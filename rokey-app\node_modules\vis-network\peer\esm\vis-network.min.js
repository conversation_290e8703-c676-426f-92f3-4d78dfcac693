/**
 * vis-network
 * https://visjs.github.io/vis-network/
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 9.1.12
 * @date    2025-06-10T17:34:31.193Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
import{isDataViewLike as t,DataSet as e}from"vis-data/peer/esm/vis-data.js";var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var s=function(t){return t&&t.Math===Math&&t},n=s("object"==typeof globalThis&&globalThis)||s("object"==typeof window&&window)||s("object"==typeof self&&self)||s("object"==typeof i&&i)||function(){return this}()||i||Function("return this")(),r=function(t){try{return!!t()}catch(t){return!0}},a=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),h=a,d=Function.prototype,l=d.apply,c=d.call,u="object"==typeof Reflect&&Reflect.apply||(h?c.bind(l):function(){return c.apply(l,arguments)}),p=a,g=Function.prototype,f=g.call,m=p&&g.bind.bind(f,f),y=p?m:function(t){return function(){return f.apply(t,arguments)}},b=y,v=b({}.toString),w=b("".slice),_=function(t){return w(v(t),8,-1)},x=_,E=y,O=function(t){if("Function"===x(t))return E(t)},C="object"==typeof document&&document.all,k={all:C,IS_HTMLDDA:void 0===C&&void 0!==C},S=k.all,T=k.IS_HTMLDDA?function(t){return"function"==typeof t||t===S}:function(t){return"function"==typeof t},M={},D=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),P=a,I=Function.prototype.call,B=P?I.bind(I):function(){return I.apply(I,arguments)},F={},z={}.propertyIsEnumerable,N=Object.getOwnPropertyDescriptor,A=N&&!z.call({1:2},1);F.f=A?function(t){var e=N(this,t);return!!e&&e.enumerable}:z;var R,j,L=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},H=r,W=_,V=Object,q=y("".split),U=H((function(){return!V("z").propertyIsEnumerable(0)}))?function(t){return"String"===W(t)?q(t,""):V(t)}:V,Y=function(t){return null==t},X=Y,K=TypeError,G=function(t){if(X(t))throw new K("Can't call method on "+t);return t},Z=U,Q=G,$=function(t){return Z(Q(t))},J=T,tt=k.all,et=k.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:J(t)||t===tt}:function(t){return"object"==typeof t?null!==t:J(t)},it={},ot=it,st=n,nt=T,rt=function(t){return nt(t)?t:void 0},at=function(t,e){return arguments.length<2?rt(ot[t])||rt(st[t]):ot[t]&&ot[t][e]||st[t]&&st[t][e]},ht=y({}.isPrototypeOf),dt="undefined"!=typeof navigator&&String(navigator.userAgent)||"",lt=n,ct=dt,ut=lt.process,pt=lt.Deno,gt=ut&&ut.versions||pt&&pt.version,ft=gt&&gt.v8;ft&&(j=(R=ft.split("."))[0]>0&&R[0]<4?1:+(R[0]+R[1])),!j&&ct&&(!(R=ct.match(/Edge\/(\d+)/))||R[1]>=74)&&(R=ct.match(/Chrome\/(\d+)/))&&(j=+R[1]);var mt=j,yt=mt,bt=r,vt=n.String,wt=!!Object.getOwnPropertySymbols&&!bt((function(){var t=Symbol("symbol detection");return!vt(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&yt&&yt<41})),_t=wt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,xt=at,Et=T,Ot=ht,Ct=Object,kt=_t?function(t){return"symbol"==typeof t}:function(t){var e=xt("Symbol");return Et(e)&&Ot(e.prototype,Ct(t))},St=String,Tt=function(t){try{return St(t)}catch(t){return"Object"}},Mt=T,Dt=Tt,Pt=TypeError,It=function(t){if(Mt(t))return t;throw new Pt(Dt(t)+" is not a function")},Bt=It,Ft=Y,zt=function(t,e){var i=t[e];return Ft(i)?void 0:Bt(i)},Nt=B,At=T,Rt=et,jt=TypeError,Lt={exports:{}},Ht=n,Wt=Object.defineProperty,Vt=function(t,e){try{Wt(Ht,t,{value:e,configurable:!0,writable:!0})}catch(i){Ht[t]=e}return e},qt="__core-js_shared__",Ut=n[qt]||Vt(qt,{}),Yt=Ut;(Lt.exports=function(t,e){return Yt[t]||(Yt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.33.0",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Xt=Lt.exports,Kt=G,Gt=Object,Zt=function(t){return Gt(Kt(t))},Qt=Zt,$t=y({}.hasOwnProperty),Jt=Object.hasOwn||function(t,e){return $t(Qt(t),e)},te=y,ee=0,ie=Math.random(),oe=te(1..toString),se=function(t){return"Symbol("+(void 0===t?"":t)+")_"+oe(++ee+ie,36)},ne=Xt,re=Jt,ae=se,he=wt,de=_t,le=n.Symbol,ce=ne("wks"),ue=de?le.for||le:le&&le.withoutSetter||ae,pe=function(t){return re(ce,t)||(ce[t]=he&&re(le,t)?le[t]:ue("Symbol."+t)),ce[t]},ge=B,fe=et,me=kt,ye=zt,be=function(t,e){var i,o;if("string"===e&&At(i=t.toString)&&!Rt(o=Nt(i,t)))return o;if(At(i=t.valueOf)&&!Rt(o=Nt(i,t)))return o;if("string"!==e&&At(i=t.toString)&&!Rt(o=Nt(i,t)))return o;throw new jt("Can't convert object to primitive value")},ve=TypeError,we=pe("toPrimitive"),_e=function(t,e){if(!fe(t)||me(t))return t;var i,o=ye(t,we);if(o){if(void 0===e&&(e="default"),i=ge(o,t,e),!fe(i)||me(i))return i;throw new ve("Can't convert object to primitive value")}return void 0===e&&(e="number"),be(t,e)},xe=kt,Ee=function(t){var e=_e(t,"string");return xe(e)?e:e+""},Oe=et,Ce=n.document,ke=Oe(Ce)&&Oe(Ce.createElement),Se=function(t){return ke?Ce.createElement(t):{}},Te=Se,Me=!D&&!r((function(){return 7!==Object.defineProperty(Te("div"),"a",{get:function(){return 7}}).a})),De=D,Pe=B,Ie=F,Be=L,Fe=$,ze=Ee,Ne=Jt,Ae=Me,Re=Object.getOwnPropertyDescriptor;M.f=De?Re:function(t,e){if(t=Fe(t),e=ze(e),Ae)try{return Re(t,e)}catch(t){}if(Ne(t,e))return Be(!Pe(Ie.f,t,e),t[e])};var je=r,Le=T,He=/#|\.prototype\./,We=function(t,e){var i=qe[Ve(t)];return i===Ye||i!==Ue&&(Le(e)?je(e):!!e)},Ve=We.normalize=function(t){return String(t).replace(He,".").toLowerCase()},qe=We.data={},Ue=We.NATIVE="N",Ye=We.POLYFILL="P",Xe=We,Ke=It,Ge=a,Ze=O(O.bind),Qe=function(t,e){return Ke(t),void 0===e?t:Ge?Ze(t,e):function(){return t.apply(e,arguments)}},$e={},Je=D&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),ti=et,ei=String,ii=TypeError,oi=function(t){if(ti(t))return t;throw new ii(ei(t)+" is not an object")},si=D,ni=Me,ri=Je,ai=oi,hi=Ee,di=TypeError,li=Object.defineProperty,ci=Object.getOwnPropertyDescriptor,ui="enumerable",pi="configurable",gi="writable";$e.f=si?ri?function(t,e,i){if(ai(t),e=hi(e),ai(i),"function"==typeof t&&"prototype"===e&&"value"in i&&gi in i&&!i[gi]){var o=ci(t,e);o&&o[gi]&&(t[e]=i.value,i={configurable:pi in i?i[pi]:o[pi],enumerable:ui in i?i[ui]:o[ui],writable:!1})}return li(t,e,i)}:li:function(t,e,i){if(ai(t),e=hi(e),ai(i),ni)try{return li(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new di("Accessors not supported");return"value"in i&&(t[e]=i.value),t};var fi=$e,mi=L,yi=D?function(t,e,i){return fi.f(t,e,mi(1,i))}:function(t,e,i){return t[e]=i,t},bi=n,vi=u,wi=O,_i=T,xi=M.f,Ei=Xe,Oi=it,Ci=Qe,ki=yi,Si=Jt,Ti=function(t){var e=function(i,o,s){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(i);case 2:return new t(i,o)}return new t(i,o,s)}return vi(t,this,arguments)};return e.prototype=t.prototype,e},Mi=function(t,e){var i,o,s,n,r,a,h,d,l,c=t.target,u=t.global,p=t.stat,g=t.proto,f=u?bi:p?bi[c]:(bi[c]||{}).prototype,m=u?Oi:Oi[c]||ki(Oi,c,{})[c],y=m.prototype;for(n in e)o=!(i=Ei(u?n:c+(p?".":"#")+n,t.forced))&&f&&Si(f,n),a=m[n],o&&(h=t.dontCallGetSet?(l=xi(f,n))&&l.value:f[n]),r=o&&h?h:e[n],o&&typeof a==typeof r||(d=t.bind&&o?Ci(r,bi):t.wrap&&o?Ti(r):g&&_i(r)?wi(r):r,(t.sham||r&&r.sham||a&&a.sham)&&ki(d,"sham",!0),ki(m,n,d),g&&(Si(Oi,s=c+"Prototype")||ki(Oi,s,{}),ki(Oi[s],n,r),t.real&&y&&(i||!y[n])&&ki(y,n,r)))},Di=Math.ceil,Pi=Math.floor,Ii=Math.trunc||function(t){var e=+t;return(e>0?Pi:Di)(e)},Bi=function(t){var e=+t;return e!=e||0===e?0:Ii(e)},Fi=Bi,zi=Math.max,Ni=Math.min,Ai=function(t,e){var i=Fi(t);return i<0?zi(i+e,0):Ni(i,e)},Ri=Bi,ji=Math.min,Li=function(t){return t>0?ji(Ri(t),9007199254740991):0},Hi=function(t){return Li(t.length)},Wi=$,Vi=Ai,qi=Hi,Ui=function(t){return function(e,i,o){var s,n=Wi(e),r=qi(n),a=Vi(o,r);if(t&&i!=i){for(;r>a;)if((s=n[a++])!=s)return!0}else for(;r>a;a++)if((t||a in n)&&n[a]===i)return t||a||0;return!t&&-1}},Yi={includes:Ui(!0),indexOf:Ui(!1)},Xi={},Ki=Jt,Gi=$,Zi=Yi.indexOf,Qi=Xi,$i=y([].push),Ji=function(t,e){var i,o=Gi(t),s=0,n=[];for(i in o)!Ki(Qi,i)&&Ki(o,i)&&$i(n,i);for(;e.length>s;)Ki(o,i=e[s++])&&(~Zi(n,i)||$i(n,i));return n},to=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],eo=Ji,io=to,oo=Object.keys||function(t){return eo(t,io)},so={};so.f=Object.getOwnPropertySymbols;var no=D,ro=y,ao=B,ho=r,lo=oo,co=so,uo=F,po=Zt,go=U,fo=Object.assign,mo=Object.defineProperty,yo=ro([].concat),bo=!fo||ho((function(){if(no&&1!==fo({b:1},fo(mo({},"a",{enumerable:!0,get:function(){mo(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol("assign detection"),o="abcdefghijklmnopqrst";return t[i]=7,o.split("").forEach((function(t){e[t]=t})),7!==fo({},t)[i]||lo(fo({},e)).join("")!==o}))?function(t,e){for(var i=po(t),o=arguments.length,s=1,n=co.f,r=uo.f;o>s;)for(var a,h=go(arguments[s++]),d=n?yo(lo(h),n(h)):lo(h),l=d.length,c=0;l>c;)a=d[c++],no&&!ao(r,h,a)||(i[a]=h[a]);return i}:fo,vo=bo;Mi({target:"Object",stat:!0,arity:2,forced:Object.assign!==vo},{assign:vo});var wo=o(it.Object.assign),_o=y([].slice),xo=y,Eo=It,Oo=et,Co=Jt,ko=_o,So=a,To=Function,Mo=xo([].concat),Do=xo([].join),Po={},Io=So?To.bind:function(t){var e=Eo(this),i=e.prototype,o=ko(arguments,1),s=function(){var i=Mo(o,ko(arguments));return this instanceof s?function(t,e,i){if(!Co(Po,e)){for(var o=[],s=0;s<e;s++)o[s]="a["+s+"]";Po[e]=To("C,a","return new C("+Do(o,",")+")")}return Po[e](t,i)}(e,i.length,i):e.apply(t,i)};return Oo(i)&&(s.prototype=i),s},Bo=Io;Mi({target:"Function",proto:!0,forced:Function.bind!==Bo},{bind:Bo});var Fo=it,zo=function(t){return Fo[t+"Prototype"]},No=zo("Function").bind,Ao=ht,Ro=No,jo=Function.prototype,Lo=function(t){var e=t.bind;return t===jo||Ao(jo,t)&&e===jo.bind?Ro:e},Ho=o(Lo);function Wo(t,e,i,o){t.beginPath(),t.arc(e,i,o,0,2*Math.PI,!1),t.closePath()}function Vo(t,e,i,o,s,n){const r=Math.PI/180;o-2*n<0&&(n=o/2),s-2*n<0&&(n=s/2),t.beginPath(),t.moveTo(e+n,i),t.lineTo(e+o-n,i),t.arc(e+o-n,i+n,n,270*r,360*r,!1),t.lineTo(e+o,i+s-n),t.arc(e+o-n,i+s-n,n,0,90*r,!1),t.lineTo(e+n,i+s),t.arc(e+n,i+s-n,n,90*r,180*r,!1),t.lineTo(e,i+n),t.arc(e+n,i+n,n,180*r,270*r,!1),t.closePath()}function qo(t,e,i,o,s){const n=.5522848,r=o/2*n,a=s/2*n,h=e+o,d=i+s,l=e+o/2,c=i+s/2;t.beginPath(),t.moveTo(e,c),t.bezierCurveTo(e,c-a,l-r,i,l,i),t.bezierCurveTo(l+r,i,h,c-a,h,c),t.bezierCurveTo(h,c+a,l+r,d,l,d),t.bezierCurveTo(l-r,d,e,c+a,e,c),t.closePath()}function Uo(t,e,i,o,s){const n=s*(1/3),r=.5522848,a=o/2*r,h=n/2*r,d=e+o,l=i+n,c=e+o/2,u=i+n/2,p=i+(s-n/2),g=i+s;t.beginPath(),t.moveTo(d,u),t.bezierCurveTo(d,u+h,c+a,l,c,l),t.bezierCurveTo(c-a,l,e,u+h,e,u),t.bezierCurveTo(e,u-h,c-a,i,c,i),t.bezierCurveTo(c+a,i,d,u-h,d,u),t.lineTo(d,p),t.bezierCurveTo(d,p+h,c+a,g,c,g),t.bezierCurveTo(c-a,g,e,p+h,e,p),t.lineTo(e,u)}function Yo(t,e,i,o,s,n){t.beginPath(),t.moveTo(e,i);const r=n.length,a=o-e,h=s-i,d=h/a;let l=Math.sqrt(a*a+h*h),c=0,u=!0,p=0,g=+n[0];for(;l>=.1;)g=+n[c++%r],g>l&&(g=l),p=Math.sqrt(g*g/(1+d*d)),p=a<0?-p:p,e+=p,i+=d*p,!0===u?t.lineTo(e,i):t.moveTo(e,i),l-=g,u=!u}const Xo={circle:Wo,dashedLine:Yo,database:Uo,diamond:function(t,e,i,o){t.beginPath(),t.lineTo(e,i+o),t.lineTo(e+o,i),t.lineTo(e,i-o),t.lineTo(e-o,i),t.closePath()},ellipse:qo,ellipse_vis:qo,hexagon:function(t,e,i,o){t.beginPath();const s=2*Math.PI/6;t.moveTo(e+o,i);for(let n=1;n<6;n++)t.lineTo(e+o*Math.cos(s*n),i+o*Math.sin(s*n));t.closePath()},roundRect:Vo,square:function(t,e,i,o){t.beginPath(),t.rect(e-o,i-o,2*o,2*o),t.closePath()},star:function(t,e,i,o){t.beginPath(),i+=.1*(o*=.82);for(let s=0;s<10;s++){const n=s%2==0?1.3*o:.5*o;t.lineTo(e+n*Math.sin(2*s*Math.PI/10),i-n*Math.cos(2*s*Math.PI/10))}t.closePath()},triangle:function(t,e,i,o){t.beginPath(),i+=.275*(o*=1.15);const s=2*o,n=s/2,r=Math.sqrt(3)/6*s,a=Math.sqrt(s*s-n*n);t.moveTo(e,i-(a-r)),t.lineTo(e+n,i+r),t.lineTo(e-n,i+r),t.lineTo(e,i-(a-r)),t.closePath()},triangleDown:function(t,e,i,o){t.beginPath(),i-=.275*(o*=1.15);const s=2*o,n=s/2,r=Math.sqrt(3)/6*s,a=Math.sqrt(s*s-n*n);t.moveTo(e,i+(a-r)),t.lineTo(e+n,i-r),t.lineTo(e-n,i-r),t.lineTo(e,i+(a-r)),t.closePath()}};var Ko={exports:{}};!function(t){function e(t){if(t)return function(t){for(var i in e.prototype)t[i]=e.prototype[i];return t}(t)}Ko.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function i(){this.off(t,i),e.apply(this,arguments)}return i.fn=e,this.on(t,i),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var i,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var s=0;s<o.length;s++)if((i=o[s])===e||i.fn===e){o.splice(s,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),i=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(i){o=0;for(var s=(i=i.slice(0)).length;o<s;++o)i[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length}}();var Go=o(Ko.exports),Zo=_,Qo=Array.isArray||function(t){return"Array"===Zo(t)},$o=TypeError,Jo=function(t){if(t>9007199254740991)throw $o("Maximum allowed index exceeded");return t},ts=Ee,es=$e,is=L,os=function(t,e,i){var o=ts(e);o in t?es.f(t,o,is(0,i)):t[o]=i},ss={};ss[pe("toStringTag")]="z";var ns="[object z]"===String(ss),rs=ns,as=T,hs=_,ds=pe("toStringTag"),ls=Object,cs="Arguments"===hs(function(){return arguments}()),us=rs?hs:function(t){var e,i,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=ls(t),ds))?i:cs?hs(e):"Object"===(o=hs(e))&&as(e.callee)?"Arguments":o},ps=T,gs=Ut,fs=y(Function.toString);ps(gs.inspectSource)||(gs.inspectSource=function(t){return fs(t)});var ms=gs.inspectSource,ys=y,bs=r,vs=T,ws=us,_s=ms,xs=function(){},Es=[],Os=at("Reflect","construct"),Cs=/^\s*(?:class|function)\b/,ks=ys(Cs.exec),Ss=!Cs.test(xs),Ts=function(t){if(!vs(t))return!1;try{return Os(xs,Es,t),!0}catch(t){return!1}},Ms=function(t){if(!vs(t))return!1;switch(ws(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Ss||!!ks(Cs,_s(t))}catch(t){return!0}};Ms.sham=!0;var Ds=!Os||bs((function(){var t;return Ts(Ts.call)||!Ts(Object)||!Ts((function(){t=!0}))||t}))?Ms:Ts,Ps=Qo,Is=Ds,Bs=et,Fs=pe("species"),zs=Array,Ns=function(t){var e;return Ps(t)&&(e=t.constructor,(Is(e)&&(e===zs||Ps(e.prototype))||Bs(e)&&null===(e=e[Fs]))&&(e=void 0)),void 0===e?zs:e},As=function(t,e){return new(Ns(t))(0===e?0:e)},Rs=r,js=mt,Ls=pe("species"),Hs=function(t){return js>=51||!Rs((function(){var e=[];return(e.constructor={})[Ls]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ws=Mi,Vs=r,qs=Qo,Us=et,Ys=Zt,Xs=Hi,Ks=Jo,Gs=os,Zs=As,Qs=Hs,$s=mt,Js=pe("isConcatSpreadable"),tn=$s>=51||!Vs((function(){var t=[];return t[Js]=!1,t.concat()[0]!==t})),en=function(t){if(!Us(t))return!1;var e=t[Js];return void 0!==e?!!e:qs(t)};Ws({target:"Array",proto:!0,arity:1,forced:!tn||!Qs("concat")},{concat:function(t){var e,i,o,s,n,r=Ys(this),a=Zs(r,0),h=0;for(e=-1,o=arguments.length;e<o;e++)if(en(n=-1===e?r:arguments[e]))for(s=Xs(n),Ks(h+s),i=0;i<s;i++,h++)i in n&&Gs(a,h,n[i]);else Ks(h+1),Gs(a,h++,n);return a.length=h,a}});var on=us,sn=String,nn=function(t){if("Symbol"===on(t))throw new TypeError("Cannot convert a Symbol value to a string");return sn(t)},rn={},an=D,hn=Je,dn=$e,ln=oi,cn=$,un=oo;rn.f=an&&!hn?Object.defineProperties:function(t,e){ln(t);for(var i,o=cn(e),s=un(e),n=s.length,r=0;n>r;)dn.f(t,i=s[r++],o[i]);return t};var pn,gn=at("document","documentElement"),fn=se,mn=Xt("keys"),yn=function(t){return mn[t]||(mn[t]=fn(t))},bn=oi,vn=rn,wn=to,_n=Xi,xn=gn,En=Se,On="prototype",Cn="script",kn=yn("IE_PROTO"),Sn=function(){},Tn=function(t){return"<"+Cn+">"+t+"</"+Cn+">"},Mn=function(t){t.write(Tn("")),t.close();var e=t.parentWindow.Object;return t=null,e},Dn=function(){try{pn=new ActiveXObject("htmlfile")}catch(t){}var t,e,i;Dn="undefined"!=typeof document?document.domain&&pn?Mn(pn):(e=En("iframe"),i="java"+Cn+":",e.style.display="none",xn.appendChild(e),e.src=String(i),(t=e.contentWindow.document).open(),t.write(Tn("document.F=Object")),t.close(),t.F):Mn(pn);for(var o=wn.length;o--;)delete Dn[On][wn[o]];return Dn()};_n[kn]=!0;var Pn=Object.create||function(t,e){var i;return null!==t?(Sn[On]=bn(t),i=new Sn,Sn[On]=null,i[kn]=t):i=Dn(),void 0===e?i:vn.f(i,e)},In={},Bn=Ji,Fn=to.concat("length","prototype");In.f=Object.getOwnPropertyNames||function(t){return Bn(t,Fn)};var zn={},Nn=Ai,An=Hi,Rn=os,jn=Array,Ln=Math.max,Hn=function(t,e,i){for(var o=An(t),s=Nn(e,o),n=Nn(void 0===i?o:i,o),r=jn(Ln(n-s,0)),a=0;s<n;s++,a++)Rn(r,a,t[s]);return r.length=a,r},Wn=_,Vn=$,qn=In.f,Un=Hn,Yn="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];zn.f=function(t){return Yn&&"Window"===Wn(t)?function(t){try{return qn(t)}catch(t){return Un(Yn)}}(t):qn(Vn(t))};var Xn=yi,Kn=function(t,e,i,o){return o&&o.enumerable?t[e]=i:Xn(t,e,i),t},Gn=$e,Zn=function(t,e,i){return Gn.f(t,e,i)},Qn={},$n=pe;Qn.f=$n;var Jn,tr,er,ir=it,or=Jt,sr=Qn,nr=$e.f,rr=function(t){var e=ir.Symbol||(ir.Symbol={});or(e,t)||nr(e,t,{value:sr.f(t)})},ar=B,hr=at,dr=pe,lr=Kn,cr=function(){var t=hr("Symbol"),e=t&&t.prototype,i=e&&e.valueOf,o=dr("toPrimitive");e&&!e[o]&&lr(e,o,(function(t){return ar(i,this)}),{arity:1})},ur=us,pr=ns?{}.toString:function(){return"[object "+ur(this)+"]"},gr=ns,fr=$e.f,mr=yi,yr=Jt,br=pr,vr=pe("toStringTag"),wr=function(t,e,i,o){if(t){var s=i?t:t.prototype;yr(s,vr)||fr(s,vr,{configurable:!0,value:e}),o&&!gr&&mr(s,"toString",br)}},_r=T,xr=n.WeakMap,Er=_r(xr)&&/native code/.test(String(xr)),Or=Er,Cr=n,kr=et,Sr=yi,Tr=Jt,Mr=Ut,Dr=yn,Pr=Xi,Ir="Object already initialized",Br=Cr.TypeError,Fr=Cr.WeakMap;if(Or||Mr.state){var zr=Mr.state||(Mr.state=new Fr);zr.get=zr.get,zr.has=zr.has,zr.set=zr.set,Jn=function(t,e){if(zr.has(t))throw new Br(Ir);return e.facade=t,zr.set(t,e),e},tr=function(t){return zr.get(t)||{}},er=function(t){return zr.has(t)}}else{var Nr=Dr("state");Pr[Nr]=!0,Jn=function(t,e){if(Tr(t,Nr))throw new Br(Ir);return e.facade=t,Sr(t,Nr,e),e},tr=function(t){return Tr(t,Nr)?t[Nr]:{}},er=function(t){return Tr(t,Nr)}}var Ar={set:Jn,get:tr,has:er,enforce:function(t){return er(t)?tr(t):Jn(t,{})},getterFor:function(t){return function(e){var i;if(!kr(e)||(i=tr(e)).type!==t)throw new Br("Incompatible receiver, "+t+" required");return i}}},Rr=Qe,jr=U,Lr=Zt,Hr=Hi,Wr=As,Vr=y([].push),qr=function(t){var e=1===t,i=2===t,o=3===t,s=4===t,n=6===t,r=7===t,a=5===t||n;return function(h,d,l,c){for(var u,p,g=Lr(h),f=jr(g),m=Rr(d,l),y=Hr(f),b=0,v=c||Wr,w=e?v(h,y):i||r?v(h,0):void 0;y>b;b++)if((a||b in f)&&(p=m(u=f[b],b,g),t))if(e)w[b]=p;else if(p)switch(t){case 3:return!0;case 5:return u;case 6:return b;case 2:Vr(w,u)}else switch(t){case 4:return!1;case 7:Vr(w,u)}return n?-1:o||s?s:w}},Ur={forEach:qr(0),map:qr(1),filter:qr(2),some:qr(3),every:qr(4),find:qr(5),findIndex:qr(6),filterReject:qr(7)},Yr=Mi,Xr=n,Kr=B,Gr=y,Zr=D,Qr=wt,$r=r,Jr=Jt,ta=ht,ea=oi,ia=$,oa=Ee,sa=nn,na=L,ra=Pn,aa=oo,ha=In,da=zn,la=so,ca=M,ua=$e,pa=rn,ga=F,fa=Kn,ma=Zn,ya=Xt,ba=Xi,va=se,wa=pe,_a=Qn,xa=rr,Ea=cr,Oa=wr,Ca=Ar,ka=Ur.forEach,Sa=yn("hidden"),Ta="Symbol",Ma="prototype",Da=Ca.set,Pa=Ca.getterFor(Ta),Ia=Object[Ma],Ba=Xr.Symbol,Fa=Ba&&Ba[Ma],za=Xr.RangeError,Na=Xr.TypeError,Aa=Xr.QObject,Ra=ca.f,ja=ua.f,La=da.f,Ha=ga.f,Wa=Gr([].push),Va=ya("symbols"),qa=ya("op-symbols"),Ua=ya("wks"),Ya=!Aa||!Aa[Ma]||!Aa[Ma].findChild,Xa=function(t,e,i){var o=Ra(Ia,e);o&&delete Ia[e],ja(t,e,i),o&&t!==Ia&&ja(Ia,e,o)},Ka=Zr&&$r((function(){return 7!==ra(ja({},"a",{get:function(){return ja(this,"a",{value:7}).a}})).a}))?Xa:ja,Ga=function(t,e){var i=Va[t]=ra(Fa);return Da(i,{type:Ta,tag:t,description:e}),Zr||(i.description=e),i},Za=function(t,e,i){t===Ia&&Za(qa,e,i),ea(t);var o=oa(e);return ea(i),Jr(Va,o)?(i.enumerable?(Jr(t,Sa)&&t[Sa][o]&&(t[Sa][o]=!1),i=ra(i,{enumerable:na(0,!1)})):(Jr(t,Sa)||ja(t,Sa,na(1,{})),t[Sa][o]=!0),Ka(t,o,i)):ja(t,o,i)},Qa=function(t,e){ea(t);var i=ia(e),o=aa(i).concat(eh(i));return ka(o,(function(e){Zr&&!Kr($a,i,e)||Za(t,e,i[e])})),t},$a=function(t){var e=oa(t),i=Kr(Ha,this,e);return!(this===Ia&&Jr(Va,e)&&!Jr(qa,e))&&(!(i||!Jr(this,e)||!Jr(Va,e)||Jr(this,Sa)&&this[Sa][e])||i)},Ja=function(t,e){var i=ia(t),o=oa(e);if(i!==Ia||!Jr(Va,o)||Jr(qa,o)){var s=Ra(i,o);return!s||!Jr(Va,o)||Jr(i,Sa)&&i[Sa][o]||(s.enumerable=!0),s}},th=function(t){var e=La(ia(t)),i=[];return ka(e,(function(t){Jr(Va,t)||Jr(ba,t)||Wa(i,t)})),i},eh=function(t){var e=t===Ia,i=La(e?qa:ia(t)),o=[];return ka(i,(function(t){!Jr(Va,t)||e&&!Jr(Ia,t)||Wa(o,Va[t])})),o};Qr||(fa(Fa=(Ba=function(){if(ta(Fa,this))throw new Na("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?sa(arguments[0]):void 0,e=va(t),i=function(t){this===Ia&&Kr(i,qa,t),Jr(this,Sa)&&Jr(this[Sa],e)&&(this[Sa][e]=!1);var o=na(1,t);try{Ka(this,e,o)}catch(t){if(!(t instanceof za))throw t;Xa(this,e,o)}};return Zr&&Ya&&Ka(Ia,e,{configurable:!0,set:i}),Ga(e,t)})[Ma],"toString",(function(){return Pa(this).tag})),fa(Ba,"withoutSetter",(function(t){return Ga(va(t),t)})),ga.f=$a,ua.f=Za,pa.f=Qa,ca.f=Ja,ha.f=da.f=th,la.f=eh,_a.f=function(t){return Ga(wa(t),t)},Zr&&ma(Fa,"description",{configurable:!0,get:function(){return Pa(this).description}})),Yr({global:!0,constructor:!0,wrap:!0,forced:!Qr,sham:!Qr},{Symbol:Ba}),ka(aa(Ua),(function(t){xa(t)})),Yr({target:Ta,stat:!0,forced:!Qr},{useSetter:function(){Ya=!0},useSimple:function(){Ya=!1}}),Yr({target:"Object",stat:!0,forced:!Qr,sham:!Zr},{create:function(t,e){return void 0===e?ra(t):Qa(ra(t),e)},defineProperty:Za,defineProperties:Qa,getOwnPropertyDescriptor:Ja}),Yr({target:"Object",stat:!0,forced:!Qr},{getOwnPropertyNames:th}),Ea(),Oa(Ba,Ta),ba[Sa]=!0;var ih=wt&&!!Symbol.for&&!!Symbol.keyFor,oh=Mi,sh=at,nh=Jt,rh=nn,ah=Xt,hh=ih,dh=ah("string-to-symbol-registry"),lh=ah("symbol-to-string-registry");oh({target:"Symbol",stat:!0,forced:!hh},{for:function(t){var e=rh(t);if(nh(dh,e))return dh[e];var i=sh("Symbol")(e);return dh[e]=i,lh[i]=e,i}});var ch=Mi,uh=Jt,ph=kt,gh=Tt,fh=ih,mh=Xt("symbol-to-string-registry");ch({target:"Symbol",stat:!0,forced:!fh},{keyFor:function(t){if(!ph(t))throw new TypeError(gh(t)+" is not a symbol");if(uh(mh,t))return mh[t]}});var yh=Qo,bh=T,vh=_,wh=nn,_h=y([].push),xh=Mi,Eh=at,Oh=u,Ch=B,kh=y,Sh=r,Th=T,Mh=kt,Dh=_o,Ph=function(t){if(bh(t))return t;if(yh(t)){for(var e=t.length,i=[],o=0;o<e;o++){var s=t[o];"string"==typeof s?_h(i,s):"number"!=typeof s&&"Number"!==vh(s)&&"String"!==vh(s)||_h(i,wh(s))}var n=i.length,r=!0;return function(t,e){if(r)return r=!1,e;if(yh(this))return e;for(var o=0;o<n;o++)if(i[o]===t)return e}}},Ih=wt,Bh=String,Fh=Eh("JSON","stringify"),zh=kh(/./.exec),Nh=kh("".charAt),Ah=kh("".charCodeAt),Rh=kh("".replace),jh=kh(1..toString),Lh=/[\uD800-\uDFFF]/g,Hh=/^[\uD800-\uDBFF]$/,Wh=/^[\uDC00-\uDFFF]$/,Vh=!Ih||Sh((function(){var t=Eh("Symbol")("stringify detection");return"[null]"!==Fh([t])||"{}"!==Fh({a:t})||"{}"!==Fh(Object(t))})),qh=Sh((function(){return'"\\udf06\\ud834"'!==Fh("\udf06\ud834")||'"\\udead"'!==Fh("\udead")})),Uh=function(t,e){var i=Dh(arguments),o=Ph(e);if(Th(o)||void 0!==t&&!Mh(t))return i[1]=function(t,e){if(Th(o)&&(e=Ch(o,this,Bh(t),e)),!Mh(e))return e},Oh(Fh,null,i)},Yh=function(t,e,i){var o=Nh(i,e-1),s=Nh(i,e+1);return zh(Hh,t)&&!zh(Wh,s)||zh(Wh,t)&&!zh(Hh,o)?"\\u"+jh(Ah(t,0),16):t};Fh&&xh({target:"JSON",stat:!0,arity:3,forced:Vh||qh},{stringify:function(t,e,i){var o=Dh(arguments),s=Oh(Vh?Uh:Fh,null,o);return qh&&"string"==typeof s?Rh(s,Lh,Yh):s}});var Xh=so,Kh=Zt;Mi({target:"Object",stat:!0,forced:!wt||r((function(){Xh.f(1)}))},{getOwnPropertySymbols:function(t){var e=Xh.f;return e?e(Kh(t)):[]}}),rr("asyncIterator"),rr("hasInstance"),rr("isConcatSpreadable"),rr("iterator"),rr("match"),rr("matchAll"),rr("replace"),rr("search"),rr("species"),rr("split");var Gh=cr;rr("toPrimitive"),Gh();var Zh=at,Qh=wr;rr("toStringTag"),Qh(Zh("Symbol"),"Symbol"),rr("unscopables"),wr(n.JSON,"JSON",!0);var $h,Jh,td,ed=it.Symbol,id={},od=D,sd=Jt,nd=Function.prototype,rd=od&&Object.getOwnPropertyDescriptor,ad=sd(nd,"name"),hd={EXISTS:ad,PROPER:ad&&"something"===function(){}.name,CONFIGURABLE:ad&&(!od||od&&rd(nd,"name").configurable)},dd=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),ld=Jt,cd=T,ud=Zt,pd=dd,gd=yn("IE_PROTO"),fd=Object,md=fd.prototype,yd=pd?fd.getPrototypeOf:function(t){var e=ud(t);if(ld(e,gd))return e[gd];var i=e.constructor;return cd(i)&&e instanceof i?i.prototype:e instanceof fd?md:null},bd=r,vd=T,wd=et,_d=Pn,xd=yd,Ed=Kn,Od=pe("iterator"),Cd=!1;[].keys&&("next"in(td=[].keys())?(Jh=xd(xd(td)))!==Object.prototype&&($h=Jh):Cd=!0);var kd=!wd($h)||bd((function(){var t={};return $h[Od].call(t)!==t}));vd(($h=kd?{}:_d($h))[Od])||Ed($h,Od,(function(){return this}));var Sd={IteratorPrototype:$h,BUGGY_SAFARI_ITERATORS:Cd},Td=Sd.IteratorPrototype,Md=Pn,Dd=L,Pd=wr,Id=id,Bd=function(){return this},Fd=Mi,zd=B,Nd=hd,Ad=function(t,e,i,o){var s=e+" Iterator";return t.prototype=Md(Td,{next:Dd(+!o,i)}),Pd(t,s,!1,!0),Id[s]=Bd,t},Rd=yd,jd=wr,Ld=Kn,Hd=id,Wd=Sd,Vd=Nd.PROPER,qd=Wd.BUGGY_SAFARI_ITERATORS,Ud=pe("iterator"),Yd="keys",Xd="values",Kd="entries",Gd=function(){return this},Zd=function(t,e,i,o,s,n,r){Ad(i,e,o);var a,h,d,l=function(t){if(t===s&&f)return f;if(!qd&&t&&t in p)return p[t];switch(t){case Yd:case Xd:case Kd:return function(){return new i(this,t)}}return function(){return new i(this)}},c=e+" Iterator",u=!1,p=t.prototype,g=p[Ud]||p["@@iterator"]||s&&p[s],f=!qd&&g||l(s),m="Array"===e&&p.entries||g;if(m&&(a=Rd(m.call(new t)))!==Object.prototype&&a.next&&(jd(a,c,!0,!0),Hd[c]=Gd),Vd&&s===Xd&&g&&g.name!==Xd&&(u=!0,f=function(){return zd(g,this)}),s)if(h={values:l(Xd),keys:n?f:l(Yd),entries:l(Kd)},r)for(d in h)(qd||u||!(d in p))&&Ld(p,d,h[d]);else Fd({target:e,proto:!0,forced:qd||u},h);return r&&p[Ud]!==f&&Ld(p,Ud,f,{name:s}),Hd[e]=f,h},Qd=function(t,e){return{value:t,done:e}},$d=$,Jd=id,tl=Ar;$e.f;var el=Zd,il=Qd,ol="Array Iterator",sl=tl.set,nl=tl.getterFor(ol);el(Array,"Array",(function(t,e){sl(this,{type:ol,target:$d(t),index:0,kind:e})}),(function(){var t=nl(this),e=t.target,i=t.kind,o=t.index++;if(!e||o>=e.length)return t.target=void 0,il(void 0,!0);switch(i){case"keys":return il(o,!1);case"values":return il(e[o],!1)}return il([o,e[o]],!1)}),"values"),Jd.Arguments=Jd.Array;var rl={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},al=n,hl=us,dl=yi,ll=id,cl=pe("toStringTag");for(var ul in rl){var pl=al[ul],gl=pl&&pl.prototype;gl&&hl(gl)!==cl&&dl(gl,cl,ul),ll[ul]=ll.Array}var fl=ed,ml=o(fl),yl=Mi,bl=Qo,vl=Ds,wl=et,_l=Ai,xl=Hi,El=$,Ol=os,Cl=pe,kl=_o,Sl=Hs("slice"),Tl=Cl("species"),Ml=Array,Dl=Math.max;yl({target:"Array",proto:!0,forced:!Sl},{slice:function(t,e){var i,o,s,n=El(this),r=xl(n),a=_l(t,r),h=_l(void 0===e?r:e,r);if(bl(n)&&(i=n.constructor,(vl(i)&&(i===Ml||bl(i.prototype))||wl(i)&&null===(i=i[Tl]))&&(i=void 0),i===Ml||void 0===i))return kl(n,a,h);for(o=new(void 0===i?Ml:i)(Dl(h-a,0)),s=0;a<h;a++,s++)a in n&&Ol(o,s,n[a]);return o.length=s,o}});var Pl=zo("Array").slice,Il=ht,Bl=Pl,Fl=Array.prototype,zl=function(t){var e=t.slice;return t===Fl||Il(Fl,t)&&e===Fl.slice?Bl:e},Nl=o(zl),Al=at,Rl=In,jl=so,Ll=oi,Hl=y([].concat),Wl=Al("Reflect","ownKeys")||function(t){var e=Rl.f(Ll(t)),i=jl.f;return i?Hl(e,i(t)):e};Mi({target:"Array",stat:!0},{isArray:Qo});var Vl=o(it.Array.isArray),ql=Ur.map;Mi({target:"Array",proto:!0,forced:!Hs("map")},{map:function(t){return ql(this,t,arguments.length>1?arguments[1]:void 0)}});var Ul=zo("Array").map,Yl=ht,Xl=Ul,Kl=Array.prototype,Gl=function(t){var e=t.map;return t===Kl||Yl(Kl,t)&&e===Kl.map?Xl:e},Zl=o(Gl),Ql=Zt,$l=oo;Mi({target:"Object",stat:!0,forced:r((function(){$l(1)}))},{keys:function(t){return $l(Ql(t))}});var Jl=o(it.Object.keys),tc=Mi,ec=Date,ic=y(ec.prototype.getTime);tc({target:"Date",stat:!0},{now:function(){return ic(new ec)}});var oc=o(it.Date.now),sc=r,nc=function(t,e){var i=[][t];return!!i&&sc((function(){i.call(null,e||function(){return 1},1)}))},rc=Ur.forEach,ac=nc("forEach")?[].forEach:function(t){return rc(this,t,arguments.length>1?arguments[1]:void 0)};Mi({target:"Array",proto:!0,forced:[].forEach!==ac},{forEach:ac});var hc=zo("Array").forEach,dc=us,lc=Jt,cc=ht,uc=hc,pc=Array.prototype,gc={DOMTokenList:!0,NodeList:!0},fc=function(t){var e=t.forEach;return t===pc||cc(pc,t)&&e===pc.forEach||lc(gc,dc(t))?uc:e},mc=o(fc),yc=Mi,bc=Qo,vc=y([].reverse),wc=[1,2];yc({target:"Array",proto:!0,forced:String(wc)===String(wc.reverse())},{reverse:function(){return bc(this)&&(this.length=this.length),vc(this)}});var _c=zo("Array").reverse,xc=ht,Ec=_c,Oc=Array.prototype,Cc=function(t){var e=t.reverse;return t===Oc||xc(Oc,t)&&e===Oc.reverse?Ec:e},kc=o(Cc),Sc=D,Tc=Qo,Mc=TypeError,Dc=Object.getOwnPropertyDescriptor,Pc=Sc&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),Ic=Tt,Bc=TypeError,Fc=function(t,e){if(!delete t[e])throw new Bc("Cannot delete property "+Ic(e)+" of "+Ic(t))},zc=Mi,Nc=Zt,Ac=Ai,Rc=Bi,jc=Hi,Lc=Pc?function(t,e){if(Tc(t)&&!Dc(t,"length").writable)throw new Mc("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},Hc=Jo,Wc=As,Vc=os,qc=Fc,Uc=Hs("splice"),Yc=Math.max,Xc=Math.min;zc({target:"Array",proto:!0,forced:!Uc},{splice:function(t,e){var i,o,s,n,r,a,h=Nc(this),d=jc(h),l=Ac(t,d),c=arguments.length;for(0===c?i=o=0:1===c?(i=0,o=d-l):(i=c-2,o=Xc(Yc(Rc(e),0),d-l)),Hc(d+i-o),s=Wc(h,o),n=0;n<o;n++)(r=l+n)in h&&Vc(s,n,h[r]);if(s.length=o,i<o){for(n=l;n<d-o;n++)a=n+i,(r=n+o)in h?h[a]=h[r]:qc(h,a);for(n=d;n>d-o+i;n--)qc(h,n-1)}else if(i>o)for(n=d-o;n>l;n--)a=n+i-1,(r=n+o-1)in h?h[a]=h[r]:qc(h,a);for(n=0;n<i;n++)h[n+l]=arguments[n+2];return Lc(h,d-o+i),s}});var Kc=zo("Array").splice,Gc=ht,Zc=Kc,Qc=Array.prototype,$c=function(t){var e=t.splice;return t===Qc||Gc(Qc,t)&&e===Qc.splice?Zc:e},Jc=o($c),tu=Yi.includes;Mi({target:"Array",proto:!0,forced:r((function(){return!Array(1).includes()}))},{includes:function(t){return tu(this,t,arguments.length>1?arguments[1]:void 0)}});var eu=zo("Array").includes,iu=et,ou=_,su=pe("match"),nu=function(t){var e;return iu(t)&&(void 0!==(e=t[su])?!!e:"RegExp"===ou(t))},ru=TypeError,au=pe("match"),hu=Mi,du=function(t){if(nu(t))throw new ru("The method doesn't accept regular expressions");return t},lu=G,cu=nn,uu=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[au]=!1,"/./"[t](e)}catch(t){}}return!1},pu=y("".indexOf);hu({target:"String",proto:!0,forced:!uu("includes")},{includes:function(t){return!!~pu(cu(lu(this)),cu(du(t)),arguments.length>1?arguments[1]:void 0)}});var gu=zo("String").includes,fu=ht,mu=eu,yu=gu,bu=Array.prototype,vu=String.prototype,wu=function(t){var e=t.includes;return t===bu||fu(bu,t)&&e===bu.includes?mu:"string"==typeof t||t===vu||fu(vu,t)&&e===vu.includes?yu:e},_u=o(wu),xu=Zt,Eu=yd,Ou=dd;Mi({target:"Object",stat:!0,forced:r((function(){Eu(1)})),sham:!Ou},{getPrototypeOf:function(t){return Eu(xu(t))}});var Cu=o(it.Object.getPrototypeOf),ku=zo("Array").concat,Su=ht,Tu=ku,Mu=Array.prototype,Du=function(t){var e=t.concat;return t===Mu||Su(Mu,t)&&e===Mu.concat?Tu:e},Pu=o(Du),Iu=Ur.filter;Mi({target:"Array",proto:!0,forced:!Hs("filter")},{filter:function(t){return Iu(this,t,arguments.length>1?arguments[1]:void 0)}});var Bu=zo("Array").filter,Fu=ht,zu=Bu,Nu=Array.prototype,Au=function(t){var e=t.filter;return t===Nu||Fu(Nu,t)&&e===Nu.filter?zu:e},Ru=o(Au),ju=D,Lu=r,Hu=y,Wu=yd,Vu=oo,qu=$,Uu=Hu(F.f),Yu=Hu([].push),Xu=ju&&Lu((function(){var t=Object.create(null);return t[2]=2,!Uu(t,2)})),Ku=function(t){return function(e){for(var i,o=qu(e),s=Vu(o),n=Xu&&null===Wu(o),r=s.length,a=0,h=[];r>a;)i=s[a++],ju&&!(n?i in o:Uu(o,i))||Yu(h,t?[i,o[i]]:o[i]);return h}},Gu={entries:Ku(!0),values:Ku(!1)}.values;Mi({target:"Object",stat:!0},{values:function(t){return Gu(t)}});var Zu=o(it.Object.values),Qu="\t\n\v\f\r                　\u2028\u2029\ufeff",$u=G,Ju=nn,tp=Qu,ep=y("".replace),ip=RegExp("^["+tp+"]+"),op=RegExp("(^|[^"+tp+"])["+tp+"]+$"),sp=function(t){return function(e){var i=Ju($u(e));return 1&t&&(i=ep(i,ip,"")),2&t&&(i=ep(i,op,"$1")),i}},np={start:sp(1),end:sp(2),trim:sp(3)},rp=n,ap=r,hp=y,dp=nn,lp=np.trim,cp=Qu,up=rp.parseInt,pp=rp.Symbol,gp=pp&&pp.iterator,fp=/^[+-]?0x/i,mp=hp(fp.exec),yp=8!==up(cp+"08")||22!==up(cp+"0x16")||gp&&!ap((function(){up(Object(gp))}))?function(t,e){var i=lp(dp(t));return up(i,e>>>0||(mp(fp,i)?16:10))}:up;Mi({global:!0,forced:parseInt!==yp},{parseInt:yp});var bp=o(it.parseInt),vp=Mi,wp=Yi.indexOf,_p=nc,xp=O([].indexOf),Ep=!!xp&&1/xp([1],1,-0)<0;vp({target:"Array",proto:!0,forced:Ep||!_p("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Ep?xp(this,t,e)||0:wp(this,t,e)}});var Op=zo("Array").indexOf,Cp=ht,kp=Op,Sp=Array.prototype,Tp=function(t){var e=t.indexOf;return t===Sp||Cp(Sp,t)&&e===Sp.indexOf?kp:e},Mp=o(Tp);Mi({target:"Object",stat:!0,sham:!D},{create:Pn});var Dp=it.Object,Pp=o((function(t,e){return Dp.create(t,e)})),Ip=it,Bp=u;Ip.JSON||(Ip.JSON={stringify:JSON.stringify});var Fp=o((function(t,e,i){return Bp(Ip.JSON.stringify,null,arguments)})),zp="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,Np=TypeError,Ap=n,Rp=u,jp=T,Lp=zp,Hp=dt,Wp=_o,Vp=function(t,e){if(t<e)throw new Np("Not enough arguments");return t},qp=Ap.Function,Up=/MSIE .\./.test(Hp)||Lp&&function(){var t=Ap.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),Yp=function(t,e){var i=e?2:1;return Up?function(o,s){var n=Vp(arguments.length,1)>i,r=jp(o)?o:qp(o),a=n?Wp(arguments,i):[],h=n?function(){Rp(r,this,a)}:r;return e?t(h,s):t(h)}:t},Xp=Mi,Kp=n,Gp=Yp(Kp.setInterval,!0);Xp({global:!0,bind:!0,forced:Kp.setInterval!==Gp},{setInterval:Gp});var Zp=Mi,Qp=n,$p=Yp(Qp.setTimeout,!0);Zp({global:!0,bind:!0,forced:Qp.setTimeout!==$p},{setTimeout:$p});var Jp=o(it.setTimeout),tg=Zt,eg=Ai,ig=Hi,og=function(t){for(var e=tg(this),i=ig(e),o=arguments.length,s=eg(o>1?arguments[1]:void 0,i),n=o>2?arguments[2]:void 0,r=void 0===n?i:eg(n,i);r>s;)e[s++]=t;return e};Mi({target:"Array",proto:!0},{fill:og});var sg,ng=zo("Array").fill,rg=ht,ag=ng,hg=Array.prototype,dg=function(t){var e=t.fill;return t===hg||rg(hg,t)&&e===hg.fill?ag:e},lg=o(dg);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
 * http://naver.github.io/egjs
 *
 * Forked By Naver egjs
 * Copyright (c) hammerjs
 * Licensed under the MIT license */
function cg(){return cg=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t},cg.apply(this,arguments)}function ug(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function pg(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}sg="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var o=arguments[i];if(null!=o)for(var s in o)o.hasOwnProperty(s)&&(e[s]=o[s])}return e}:Object.assign;var gg,fg=sg,mg=["","webkit","Moz","MS","ms","o"],yg="undefined"==typeof document?{style:{}}:document.createElement("div"),bg=Math.round,vg=Math.abs,wg=Date.now;function _g(t,e){for(var i,o,s=e[0].toUpperCase()+e.slice(1),n=0;n<mg.length;){if((o=(i=mg[n])?i+s:e)in t)return o;n++}}gg="undefined"==typeof window?{}:window;var xg=_g(yg.style,"touchAction"),Eg=void 0!==xg;var Og="compute",Cg="auto",kg="manipulation",Sg="none",Tg="pan-x",Mg="pan-y",Dg=function(){if(!Eg)return!1;var t={},e=gg.CSS&&gg.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(i){return t[i]=!e||gg.CSS.supports("touch-action",i)})),t}(),Pg="ontouchstart"in gg,Ig=void 0!==_g(gg,"PointerEvent"),Bg=Pg&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),Fg="touch",zg="mouse",Ng=25,Ag=1,Rg=4,jg=8,Lg=1,Hg=2,Wg=4,Vg=8,qg=16,Ug=Hg|Wg,Yg=Vg|qg,Xg=Ug|Yg,Kg=["x","y"],Gg=["clientX","clientY"];function Zg(t,e,i){var o;if(t)if(t.forEach)t.forEach(e,i);else if(void 0!==t.length)for(o=0;o<t.length;)e.call(i,t[o],o,t),o++;else for(o in t)t.hasOwnProperty(o)&&e.call(i,t[o],o,t)}function Qg(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function $g(t,e){return t.indexOf(e)>-1}var Jg=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Og&&(t=this.compute()),Eg&&this.manager.element.style&&Dg[t]&&(this.manager.element.style[xg]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return Zg(this.manager.recognizers,(function(e){Qg(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if($g(t,Sg))return Sg;var e=$g(t,Tg),i=$g(t,Mg);return e&&i?Sg:e||i?e?Tg:Mg:$g(t,kg)?kg:Cg}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var o=this.actions,s=$g(o,Sg)&&!Dg[Sg],n=$g(o,Mg)&&!Dg[Mg],r=$g(o,Tg)&&!Dg[Tg];if(s){var a=1===t.pointers.length,h=t.distance<2,d=t.deltaTime<250;if(a&&h&&d)return}if(!r||!n)return s||n&&i&Ug||r&&i&Yg?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function tf(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function ef(t){var e=t.length;if(1===e)return{x:bg(t[0].clientX),y:bg(t[0].clientY)};for(var i=0,o=0,s=0;s<e;)i+=t[s].clientX,o+=t[s].clientY,s++;return{x:bg(i/e),y:bg(o/e)}}function of(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:bg(t.pointers[i].clientX),clientY:bg(t.pointers[i].clientY)},i++;return{timeStamp:wg(),pointers:e,center:ef(e),deltaX:t.deltaX,deltaY:t.deltaY}}function sf(t,e,i){i||(i=Kg);var o=e[i[0]]-t[i[0]],s=e[i[1]]-t[i[1]];return Math.sqrt(o*o+s*s)}function nf(t,e,i){i||(i=Kg);var o=e[i[0]]-t[i[0]],s=e[i[1]]-t[i[1]];return 180*Math.atan2(s,o)/Math.PI}function rf(t,e){return t===e?Lg:vg(t)>=vg(e)?t<0?Hg:Wg:e<0?Vg:qg}function af(t,e,i){return{x:e/t||0,y:i/t||0}}function hf(t,e){var i=t.session,o=e.pointers,s=o.length;i.firstInput||(i.firstInput=of(e)),s>1&&!i.firstMultiple?i.firstMultiple=of(e):1===s&&(i.firstMultiple=!1);var n=i.firstInput,r=i.firstMultiple,a=r?r.center:n.center,h=e.center=ef(o);e.timeStamp=wg(),e.deltaTime=e.timeStamp-n.timeStamp,e.angle=nf(a,h),e.distance=sf(a,h),function(t,e){var i=e.center,o=t.offsetDelta||{},s=t.prevDelta||{},n=t.prevInput||{};e.eventType!==Ag&&n.eventType!==Rg||(s=t.prevDelta={x:n.deltaX||0,y:n.deltaY||0},o=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=s.x+(i.x-o.x),e.deltaY=s.y+(i.y-o.y)}(i,e),e.offsetDirection=rf(e.deltaX,e.deltaY);var d,l,c=af(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=c.x,e.overallVelocityY=c.y,e.overallVelocity=vg(c.x)>vg(c.y)?c.x:c.y,e.scale=r?(d=r.pointers,sf((l=o)[0],l[1],Gg)/sf(d[0],d[1],Gg)):1,e.rotation=r?function(t,e){return nf(e[1],e[0],Gg)+nf(t[1],t[0],Gg)}(r.pointers,o):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,o,s,n,r=t.lastInterval||e,a=e.timeStamp-r.timeStamp;if(e.eventType!==jg&&(a>Ng||void 0===r.velocity)){var h=e.deltaX-r.deltaX,d=e.deltaY-r.deltaY,l=af(a,h,d);o=l.x,s=l.y,i=vg(l.x)>vg(l.y)?l.x:l.y,n=rf(h,d),t.lastInterval=e}else i=r.velocity,o=r.velocityX,s=r.velocityY,n=r.direction;e.velocity=i,e.velocityX=o,e.velocityY=s,e.direction=n}(i,e);var u,p=t.element,g=e.srcEvent;tf(u=g.composedPath?g.composedPath()[0]:g.path?g.path[0]:g.target,p)&&(p=u),e.target=p}function df(t,e,i){var o=i.pointers.length,s=i.changedPointers.length,n=e&Ag&&o-s==0,r=e&(Rg|jg)&&o-s==0;i.isFirst=!!n,i.isFinal=!!r,n&&(t.session={}),i.eventType=e,hf(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function lf(t){return t.trim().split(/\s+/g)}function cf(t,e,i){Zg(lf(e),(function(e){t.addEventListener(e,i,!1)}))}function uf(t,e,i){Zg(lf(e),(function(e){t.removeEventListener(e,i,!1)}))}function pf(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var gf=function(){function t(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){Qg(t.options.enable,[t])&&i.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&cf(this.element,this.evEl,this.domHandler),this.evTarget&&cf(this.target,this.evTarget,this.domHandler),this.evWin&&cf(pf(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&uf(this.element,this.evEl,this.domHandler),this.evTarget&&uf(this.target,this.evTarget,this.domHandler),this.evWin&&uf(pf(this.element),this.evWin,this.domHandler)},t}();function ff(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var o=0;o<t.length;){if(i&&t[o][i]==e||!i&&t[o]===e)return o;o++}return-1}var mf={pointerdown:Ag,pointermove:2,pointerup:Rg,pointercancel:jg,pointerout:jg},yf={2:Fg,3:"pen",4:zg,5:"kinect"},bf="pointerdown",vf="pointermove pointerup pointercancel";gg.MSPointerEvent&&!gg.PointerEvent&&(bf="MSPointerDown",vf="MSPointerMove MSPointerUp MSPointerCancel");var wf=function(t){function e(){var i,o=e.prototype;return o.evEl=bf,o.evWin=vf,(i=t.apply(this,arguments)||this).store=i.manager.session.pointerEvents=[],i}return ug(e,t),e.prototype.handler=function(t){var e=this.store,i=!1,o=t.type.toLowerCase().replace("ms",""),s=mf[o],n=yf[t.pointerType]||t.pointerType,r=n===Fg,a=ff(e,t.pointerId,"pointerId");s&Ag&&(0===t.button||r)?a<0&&(e.push(t),a=e.length-1):s&(Rg|jg)&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,s,{pointers:e,changedPointers:[t],pointerType:n,srcEvent:t}),i&&e.splice(a,1))},e}(gf);function _f(t){return Array.prototype.slice.call(t,0)}function xf(t,e,i){for(var o=[],s=[],n=0;n<t.length;){var r=e?t[n][e]:t[n];ff(s,r)<0&&o.push(t[n]),s[n]=r,n++}return i&&(o=e?o.sort((function(t,i){return t[e]>i[e]})):o.sort()),o}var Ef={touchstart:Ag,touchmove:2,touchend:Rg,touchcancel:jg},Of=function(t){function e(){var i;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).targetIds={},i}return ug(e,t),e.prototype.handler=function(t){var e=Ef[t.type],i=Cf.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Fg,srcEvent:t})},e}(gf);function Cf(t,e){var i,o,s=_f(t.touches),n=this.targetIds;if(e&(2|Ag)&&1===s.length)return n[s[0].identifier]=!0,[s,s];var r=_f(t.changedTouches),a=[],h=this.target;if(o=s.filter((function(t){return tf(t.target,h)})),e===Ag)for(i=0;i<o.length;)n[o[i].identifier]=!0,i++;for(i=0;i<r.length;)n[r[i].identifier]&&a.push(r[i]),e&(Rg|jg)&&delete n[r[i].identifier],i++;return a.length?[xf(o.concat(a),"identifier",!0),a]:void 0}var kf={mousedown:Ag,mousemove:2,mouseup:Rg},Sf=function(t){function e(){var i,o=e.prototype;return o.evEl="mousedown",o.evWin="mousemove mouseup",(i=t.apply(this,arguments)||this).pressed=!1,i}return ug(e,t),e.prototype.handler=function(t){var e=kf[t.type];e&Ag&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=Rg),this.pressed&&(e&Rg&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:zg,srcEvent:t}))},e}(gf),Tf=2500;function Mf(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY},o=this.lastTouches;this.lastTouches.push(i);setTimeout((function(){var t=o.indexOf(i);t>-1&&o.splice(t,1)}),Tf)}}function Df(t,e){t&Ag?(this.primaryTouch=e.changedPointers[0].identifier,Mf.call(this,e)):t&(Rg|jg)&&Mf.call(this,e)}function Pf(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var s=this.lastTouches[o],n=Math.abs(e-s.x),r=Math.abs(i-s.y);if(n<=25&&r<=25)return!0}return!1}var If=function(){return function(t){function e(e,i){var o;return(o=t.call(this,e,i)||this).handler=function(t,e,i){var s=i.pointerType===Fg,n=i.pointerType===zg;if(!(n&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(s)Df.call(pg(pg(o)),e,i);else if(n&&Pf.call(pg(pg(o)),i))return;o.callback(t,e,i)}},o.touch=new Of(o.manager,o.handler),o.mouse=new Sf(o.manager,o.handler),o.primaryTouch=null,o.lastTouches=[],o}return ug(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(gf)}();function Bf(t,e,i){return!!Array.isArray(t)&&(Zg(t,i[e],i),!0)}var Ff=32,zf=1;function Nf(t,e){var i=e.manager;return i?i.get(t):t}function Af(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Rf=function(){function t(t){void 0===t&&(t={}),this.options=cg({enable:!0},t),this.id=zf++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return fg(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Bf(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Nf(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return Bf(t,"dropRecognizeWith",this)||(t=Nf(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Bf(t,"requireFailure",this))return this;var e=this.requireFail;return-1===ff(e,t=Nf(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Bf(t,"dropRequireFailure",this))return this;t=Nf(t,this);var e=ff(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,i=this.state;function o(i){e.manager.emit(i,t)}i<8&&o(e.options.event+Af(i)),o(e.options.event),t.additionalEvent&&o(t.additionalEvent),i>=8&&o(e.options.event+Af(i))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Ff},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=fg({},t);if(!Qg(this.options.enable,[this,e]))return this.reset(),void(this.state=Ff);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),jf=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,cg({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,i.pCenter=!1,i._timer=null,i._input=null,i.count=0,i}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){return[kg]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,s=t.distance<i.threshold,n=t.deltaTime<i.time;if(this.reset(),t.eventType&Ag&&0===this.count)return this.failTimeout();if(s&&n&&o){if(t.eventType!==Rg)return this.failTimeout();var r=!this.pTime||t.timeStamp-this.pTime<i.interval,a=!this.pCenter||sf(this.pCenter,t.center)<i.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,a&&r?this.count+=1:this.count=1,this._input=t,0===this.count%i.taps)return this.hasRequireFailures()?(this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),i.interval),2):8}return Ff},i.failTimeout=function(){var t=this;return this._timer=setTimeout((function(){t.state=Ff}),this.options.interval),Ff},i.reset=function(){clearTimeout(this._timer)},i.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(Rf),Lf=function(t){function e(e){return void 0===e&&(e={}),t.call(this,cg({pointers:1},e))||this}ug(e,t);var i=e.prototype;return i.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},i.process=function(t){var e=this.state,i=t.eventType,o=6&e,s=this.attrTest(t);return o&&(i&jg||!s)?16|e:o||s?i&Rg?8|e:2&e?4|e:2:Ff},e}(Rf);function Hf(t){return t===qg?"down":t===Vg?"up":t===Hg?"left":t===Wg?"right":""}var Wf=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,cg({event:"pan",threshold:10,pointers:1,direction:Xg},e))||this).pX=null,i.pY=null,i}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){var t=this.options.direction,e=[];return t&Ug&&e.push(Mg),t&Yg&&e.push(Tg),e},i.directionTest=function(t){var e=this.options,i=!0,o=t.distance,s=t.direction,n=t.deltaX,r=t.deltaY;return s&e.direction||(e.direction&Ug?(s=0===n?Lg:n<0?Hg:Wg,i=n!==this.pX,o=Math.abs(t.deltaX)):(s=0===r?Lg:r<0?Vg:qg,i=r!==this.pY,o=Math.abs(t.deltaY))),t.direction=s,i&&o>e.threshold&&s&e.direction},i.attrTest=function(t){return Lf.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},i.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var i=Hf(e.direction);i&&(e.additionalEvent=this.options.event+i),t.prototype.emit.call(this,e)},e}(Lf),Vf=function(t){function e(e){return void 0===e&&(e={}),t.call(this,cg({event:"swipe",threshold:10,velocity:.3,direction:Ug|Yg,pointers:1},e))||this}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){return Wf.prototype.getTouchAction.call(this)},i.attrTest=function(e){var i,o=this.options.direction;return o&(Ug|Yg)?i=e.overallVelocity:o&Ug?i=e.overallVelocityX:o&Yg&&(i=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&o&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&vg(i)>this.options.velocity&&e.eventType&Rg},i.emit=function(t){var e=Hf(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(Lf),qf=function(t){function e(e){return void 0===e&&(e={}),t.call(this,cg({event:"pinch",threshold:0,pointers:2},e))||this}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Sg]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},i.emit=function(e){if(1!==e.scale){var i=e.scale<1?"in":"out";e.additionalEvent=this.options.event+i}t.prototype.emit.call(this,e)},e}(Lf),Uf=function(t){function e(e){return void 0===e&&(e={}),t.call(this,cg({event:"rotate",threshold:0,pointers:2},e))||this}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Sg]},i.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(Lf),Yf=function(t){function e(e){var i;return void 0===e&&(e={}),(i=t.call(this,cg({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,i._input=null,i}ug(e,t);var i=e.prototype;return i.getTouchAction=function(){return[Cg]},i.process=function(t){var e=this,i=this.options,o=t.pointers.length===i.pointers,s=t.distance<i.threshold,n=t.deltaTime>i.time;if(this._input=t,!s||!o||t.eventType&(Rg|jg)&&!n)this.reset();else if(t.eventType&Ag)this.reset(),this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),i.time);else if(t.eventType&Rg)return 8;return Ff},i.reset=function(){clearTimeout(this._timer)},i.emit=function(t){8===this.state&&(t&&t.eventType&Rg?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=wg(),this.manager.emit(this.options.event,this._input)))},e}(Rf),Xf={domEvents:!1,touchAction:Og,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},Kf=[[Uf,{enable:!1}],[qf,{enable:!1},["rotate"]],[Vf,{direction:Ug}],[Wf,{direction:Ug},["swipe"]],[jf],[jf,{event:"doubletap",taps:2},["tap"]],[Yf]];function Gf(t,e){var i,o=t.element;o.style&&(Zg(t.options.cssProps,(function(s,n){i=_g(o.style,n),e?(t.oldCssProps[i]=o.style[i],o.style[i]=s):o.style[i]=t.oldCssProps[i]||""})),e||(t.oldCssProps={}))}var Zf=function(){function t(t,e){var i,o=this;this.options=fg({},Xf,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(Ig?wf:Bg?Of:Pg?If:Sf))(i,df),this.touchAction=new Jg(this,this.options.touchAction),Gf(this,!0),Zg(this.options.recognizers,(function(t){var e=o.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}var e=t.prototype;return e.set=function(t){return fg(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var o=this.recognizers,s=e.curRecognizer;(!s||s&&8&s.state)&&(e.curRecognizer=null,s=null);for(var n=0;n<o.length;)i=o[n],2===e.stopped||s&&i!==s&&!i.canRecognizeWith(s)?i.reset():i.recognize(t),!s&&14&i.state&&(e.curRecognizer=i,s=i),n++}},e.get=function(t){if(t instanceof Rf)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event===t)return e[i];return null},e.add=function(t){if(Bf(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(Bf(t,"remove",this))return this;var e=this.get(t);if(t){var i=this.recognizers,o=ff(i,e);-1!==o&&(i.splice(o,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var i=this.handlers;return Zg(lf(t),(function(t){i[t]=i[t]||[],i[t].push(e)})),this},e.off=function(t,e){if(void 0===t)return this;var i=this.handlers;return Zg(lf(t),(function(t){e?i[t]&&i[t].splice(ff(i[t],e),1):delete i[t]})),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var i=document.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(i&&i.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var o=0;o<i.length;)i[o](e),o++}},e.destroy=function(){this.element&&Gf(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),Qf={touchstart:Ag,touchmove:2,touchend:Rg,touchcancel:jg},$f=function(t){function e(){var i,o=e.prototype;return o.evTarget="touchstart",o.evWin="touchstart touchmove touchend touchcancel",(i=t.apply(this,arguments)||this).started=!1,i}return ug(e,t),e.prototype.handler=function(t){var e=Qf[t.type];if(e===Ag&&(this.started=!0),this.started){var i=Jf.call(this,t,e);e&(Rg|jg)&&i[0].length-i[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:Fg,srcEvent:t})}},e}(gf);function Jf(t,e){var i=_f(t.touches),o=_f(t.changedTouches);return e&(Rg|jg)&&(i=xf(i.concat(o),"identifier",!0)),[i,o]}function tm(t,e,i){var o="DEPRECATED METHOD: "+e+"\n"+i+" AT \n";return function(){var e=new Error("get-stack-trace"),i=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",s=window.console&&(window.console.warn||window.console.log);return s&&s.call(window.console,o,i),t.apply(this,arguments)}}var em=tm((function(t,e,i){for(var o=Object.keys(e),s=0;s<o.length;)(!i||i&&void 0===t[o[s]])&&(t[o[s]]=e[o[s]]),s++;return t}),"extend","Use `assign`."),im=tm((function(t,e){return em(t,e,!0)}),"merge","Use `assign`.");function om(t,e,i){var o,s=e.prototype;(o=t.prototype=Object.create(s)).constructor=t,o._super=s,i&&fg(o,i)}function sm(t,e){return function(){return t.apply(e,arguments)}}var nm=function(){var t=function(t,e){return void 0===e&&(e={}),new Zf(t,cg({recognizers:Kf.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=Xg,t.DIRECTION_DOWN=qg,t.DIRECTION_LEFT=Hg,t.DIRECTION_RIGHT=Wg,t.DIRECTION_UP=Vg,t.DIRECTION_HORIZONTAL=Ug,t.DIRECTION_VERTICAL=Yg,t.DIRECTION_NONE=Lg,t.DIRECTION_DOWN=qg,t.INPUT_START=Ag,t.INPUT_MOVE=2,t.INPUT_END=Rg,t.INPUT_CANCEL=jg,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Ff,t.Manager=Zf,t.Input=gf,t.TouchAction=Jg,t.TouchInput=Of,t.MouseInput=Sf,t.PointerEventInput=wf,t.TouchMouseInput=If,t.SingleTouchInput=$f,t.Recognizer=Rf,t.AttrRecognizer=Lf,t.Tap=jf,t.Pan=Wf,t.Swipe=Vf,t.Pinch=qf,t.Rotate=Uf,t.Press=Yf,t.on=cf,t.off=uf,t.each=Zg,t.merge=im,t.extend=em,t.bindFn=sm,t.assign=fg,t.inherit=om,t.bindFn=sm,t.prefixed=_g,t.toArray=_f,t.inArray=ff,t.uniqueArray=xf,t.splitStr=lf,t.boolOrFn=Qg,t.hasParent=tf,t.addEventListeners=cf,t.removeEventListeners=uf,t.defaults=fg({},Xf,{preset:Kf}),t}();nm.defaults;var rm=nm;function am(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(t){let[e,i,o]=function(){const t=function(){let t=4022871197;return function(e){const i=e.toString();for(let e=0;e<i.length;e++){t+=i.charCodeAt(e);let o=.02519603282416938*t;t=o>>>0,o-=t,o*=t,t=o>>>0,o-=t,t+=4294967296*o}return 2.3283064365386963e-10*(t>>>0)}}();let e=t(" "),i=t(" "),o=t(" ");for(let s=0;s<arguments.length;s++)e-=t(s<0||arguments.length<=s?void 0:arguments[s]),e<0&&(e+=1),i-=t(s<0||arguments.length<=s?void 0:arguments[s]),i<0&&(i+=1),o-=t(s<0||arguments.length<=s?void 0:arguments[s]),o<0&&(o+=1);return[e,i,o]}(t),s=1;const n=()=>{const t=2091639*e+2.3283064365386963e-10*s;return e=i,i=o,o=t-(s=0|t)};return n.uint32=()=>4294967296*n(),n.fract53=()=>n()+11102230246251565e-32*(2097152*n()|0),n.algorithm="Alea",n.seed=t,n.version="0.9",n}(e.length?e:[oc()])}ml("DELETE");const hm="undefined"!=typeof window?window.Hammer||rm:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function dm(t){var e;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push((()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)}));const i=hm(this._dom.overlay);i.on("tap",Ho(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push((()=>{i.destroy()}));const o=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];mc(o).call(o,(t=>{i.on(t,(t=>{t.srcEvent.stopPropagation()}))})),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push((()=>{document.body.removeEventListener("click",this._onClick)}))),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}Go(dm.prototype),dm.current=null,dm.prototype.destroy=function(){this.deactivate();for(const i of kc(t=Jc(e=this._cleanupQueue).call(e,0)).call(t)){var t,e;i()}},dm.prototype.activate=function(){dm.current&&dm.current.deactivate(),dm.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},dm.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},dm.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};const lm=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,cm=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,um=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,pm=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function gm(t){if(t)for(;!0===t.hasChildNodes();){const e=t.firstChild;e&&(gm(e),t.removeChild(e))}}function fm(t){return t instanceof String||"string"==typeof t}function mm(t){return"object"==typeof t&&null!==t}function ym(t,e,i,o){let s=!1;!0===o&&(s=null===e[i]&&void 0!==t[i]),s?delete t[i]:t[i]=e[i]}function bm(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(const o in t)if(void 0!==e[o])if(null===e[o]||"object"!=typeof e[o])ym(t,e,o,i);else{const s=t[o],n=e[o];mm(s)&&mm(n)&&bm(s,n,i)}}function vm(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Vl(i))throw new TypeError("Arrays are not supported by deepExtend");for(let s=0;s<t.length;s++){const n=t[s];if(Object.prototype.hasOwnProperty.call(i,n))if(i[n]&&i[n].constructor===Object)void 0===e[n]&&(e[n]={}),e[n].constructor===Object?_m(e[n],i[n],!1,o):ym(e,i,n,o);else{if(Vl(i[n]))throw new TypeError("Arrays are not supported by deepExtend");ym(e,i,n,o)}}return e}function wm(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Vl(i))throw new TypeError("Arrays are not supported by deepExtend");for(const s in i)if(Object.prototype.hasOwnProperty.call(i,s)&&!_u(t).call(t,s))if(i[s]&&i[s].constructor===Object)void 0===e[s]&&(e[s]={}),e[s].constructor===Object?_m(e[s],i[s]):ym(e,i,s,o);else if(Vl(i[s])){e[s]=[];for(let t=0;t<i[s].length;t++)e[s].push(i[s][t])}else ym(e,i,s,o);return e}function _m(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)||!0===i)if("object"==typeof e[n]&&null!==e[n]&&Cu(e[n])===Object.prototype)void 0===t[n]?t[n]=_m({},e[n],i):"object"==typeof t[n]&&null!==t[n]&&Cu(t[n])===Object.prototype?_m(t[n],e[n],i):ym(t,e,n,o);else if(Vl(e[n])){var s;t[n]=Nl(s=e[n]).call(s)}else ym(t,e,n,o);return t}function xm(t,e){return[...t,e]}function Em(t){return t.getBoundingClientRect().top}function Om(t,e){if(Vl(t)){const i=t.length;for(let o=0;o<i;o++)e(t[o],o,t)}else for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)}function Cm(t){let e;switch(t.length){case 3:case 4:return e=cm.exec(t),e?{r:bp(e[1]+e[1],16),g:bp(e[2]+e[2],16),b:bp(e[3]+e[3],16)}:null;case 6:case 7:return e=lm.exec(t),e?{r:bp(e[1],16),g:bp(e[2],16),b:bp(e[3],16)}:null;default:return null}}function km(t,e){if(_u(t).call(t,"rgba"))return t;if(_u(t).call(t,"rgb")){const i=t.substr(Mp(t).call(t,"(")+1).replace(")","").split(",");return"rgba("+i[0]+","+i[1]+","+i[2]+","+e+")"}{const i=Cm(t);return null==i?t:"rgba("+i.r+","+i.g+","+i.b+","+e+")"}}function Sm(t,e,i){var o;return"#"+Nl(o=((1<<24)+(t<<16)+(e<<8)+i).toString(16)).call(o,1)}function Tm(t,e){if(fm(t)){let e=t;if(Bm(e)){var i;const t=Zl(i=e.substr(4).substr(0,e.length-5).split(",")).call(i,(function(t){return bp(t)}));e=Sm(t[0],t[1],t[2])}if(!0===Im(e)){const t=function(t){const e=Cm(t);if(!e)throw new TypeError("'".concat(t,"' is not a valid color."));return Mm(e.r,e.g,e.b)}(e),i={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},o={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},s=Pm(o.h,o.s,o.v),n=Pm(i.h,i.s,i.v);return{background:e,border:s,highlight:{background:n,border:s},hover:{background:n,border:s}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}if(e){return{background:t.background||e.background,border:t.border||e.border,highlight:fm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:fm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}}return{background:t.background||void 0,border:t.border||void 0,highlight:fm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:fm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function Mm(t,e,i){t/=255,e/=255,i/=255;const o=Math.min(t,Math.min(e,i)),s=Math.max(t,Math.max(e,i));if(o===s)return{h:0,s:0,v:o};return{h:60*((t===o?3:i===o?1:5)-(t===o?e-i:i===o?t-e:i-t)/(s-o))/360,s:(s-o)/s,v:s}}function Dm(t,e,i){let o,s,n;const r=Math.floor(6*t),a=6*t-r,h=i*(1-e),d=i*(1-a*e),l=i*(1-(1-a)*e);switch(r%6){case 0:o=i,s=l,n=h;break;case 1:o=d,s=i,n=h;break;case 2:o=h,s=i,n=l;break;case 3:o=h,s=d,n=i;break;case 4:o=l,s=h,n=i;break;case 5:o=i,s=h,n=d}return{r:Math.floor(255*o),g:Math.floor(255*s),b:Math.floor(255*n)}}function Pm(t,e,i){const o=Dm(t,e,i);return Sm(o.r,o.g,o.b)}function Im(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function Bm(t){return um.test(t)}function Fm(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=Pp(t);for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&"object"==typeof t[i]&&(e[i]=Fm(t[i]));return e}function zm(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const s=function(t){return null!=t},n=function(t){return null!==t&&"object"==typeof t};if(!n(t))throw new Error("Parameter mergeTarget must be an object");if(!n(e))throw new Error("Parameter options must be an object");if(!s(i))throw new Error("Parameter option must have a value");if(!n(o))throw new Error("Parameter globalOptions must be an object");const r=e[i],a=n(o)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(o)?o[i]:void 0,h=a?a.enabled:void 0;if(void 0===r)return;if("boolean"==typeof r)return n(t[i])||(t[i]={}),void(t[i].enabled=r);if(null===r&&!n(t[i])){if(!s(a))return;t[i]=Pp(a)}if(!n(r))return;let d=!0;void 0!==r.enabled?d=r.enabled:void 0!==h&&(d=a.enabled),function(t,e,i){n(t[i])||(t[i]={});const o=e[i],s=t[i];for(const t in o)Object.prototype.hasOwnProperty.call(o,t)&&(s[t]=o[t])}(t,e,i),t[i].enabled=d}const Nm={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};function Am(t,e){let i;Vl(e)||(e=[e]);for(const o of t)if(o){i=o[e[0]];for(let t=1;t<e.length;t++)i&&(i=i[e[t]]);if(void 0!==i)break}return i}const Rm={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let jm=class{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return Rm[t]}setColor(t){let e,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("none"===t)return;const o=this._isColorString(t);if(void 0!==o&&(t=o),!0===fm(t)){if(!0===Bm(t)){const i=t.substr(4).substr(0,t.length-5).split(",");e={r:i[0],g:i[1],b:i[2],a:1}}else if(!0===function(t){return pm.test(t)}(t)){const i=t.substr(5).substr(0,t.length-6).split(",");e={r:i[0],g:i[1],b:i[2],a:i[3]}}else if(!0===Im(t)){const i=Cm(t);e={r:i.r,g:i.g,b:i.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const i=void 0!==t.a?t.a:"1.0";e={r:t.r,g:t.g,b:t.b,a:i}}if(void 0===e)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+Fp(t));this._setColor(e,i)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(){!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.previousColor=wo({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",Jp((()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)}),0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t){!0===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(this.initialColor=wo({},t)),this.color=t;const e=Mm(t.r,t.g,t.b),i=2*Math.PI,o=this.r*e.s,s=this.centerCoordinates.x+o*Math.sin(i*e.h),n=this.centerCoordinates.y+o*Math.cos(i*e.h);this.colorPickerSelector.style.left=s-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=n-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=Mm(this.color.r,this.color.g,this.color.b);e.v=t/100;const i=Dm(e.h,e.s,e.v);i.a=this.color.a,this.color=i,this._updatePicker()}_updatePicker(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color;const e=Mm(t.r,t.g,t.b),i=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1)),i.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const o=this.colorPickerCanvas.clientWidth,s=this.colorPickerCanvas.clientHeight;i.clearRect(0,0,o,s),i.putImageData(this.hueCircle,0,0),i.fillStyle="rgba(0,0,0,"+(1-e.v)+")",i.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),lg(i).call(i),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){var t,e,i,o;if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const s=this;this.opacityRange.onchange=function(){s._setOpacity(this.value)},this.opacityRange.oninput=function(){s._setOpacity(this.value)},this.brightnessRange.onchange=function(){s._setBrightness(this.value)},this.brightnessRange.oninput=function(){s._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=Ho(t=this._hide).call(t,this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=Ho(e=this._apply).call(e,this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=Ho(i=this._save).call(i,this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=Ho(o=this._loadLast).call(o,this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new hm(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",(t=>{t.isFirst&&this._moveSelector(t)})),this.hammer.on("tap",(t=>{this._moveSelector(t)})),this.hammer.on("panstart",(t=>{this._moveSelector(t)})),this.hammer.on("panmove",(t=>{this._moveSelector(t)})),this.hammer.on("panend",(t=>{this._moveSelector(t)}))}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;let o,s,n,r;t.clearRect(0,0,e,i),this.centerCoordinates={x:.5*e,y:.5*i},this.r=.49*e;const a=2*Math.PI/360,h=1/360,d=1/this.r;let l;for(n=0;n<360;n++)for(r=0;r<this.r;r++)o=this.centerCoordinates.x+r*Math.sin(a*n),s=this.centerCoordinates.y+r*Math.cos(a*n),l=Dm(n*h,r*d,1),t.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",t.fillRect(o-.5,s-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,i)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),i=t.center.x-e.left,o=t.center.y-e.top,s=.5*this.colorPickerDiv.clientHeight,n=.5*this.colorPickerDiv.clientWidth,r=i-n,a=o-s,h=Math.atan2(r,a),d=.98*Math.min(Math.sqrt(r*r+a*a),n),l=Math.cos(h)*d+s,c=Math.sin(h)*d+n;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=c-.5*this.colorPickerSelector.clientWidth+"px";let u=h/(2*Math.PI);u=u<0?u+1:u;const p=d/this.r,g=Mm(this.color.r,this.color.g,this.color.b);g.h=u,g.s=p;const f=Dm(g.h,g.s,g.v);f.a=this.color.a,this.color=f,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function Lm(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];if(e.length<1)throw new TypeError("Invalid arguments.");if(1===e.length)return document.createTextNode(e[0]);{const t=document.createElement(e[0]);return t.appendChild(Lm(...Nl(e).call(e,1))),t}}let Hm,Wm=!1;const Vm="background: #FFeeee; color: #dd0000";const qm=dm,Um=class{constructor(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>!1;this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=s,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},wo(this.options,this.defaultOptions),this.configureOptions=i,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new jm(o),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Vl(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==Ru(t)&&(this.options.filter=Ru(t)),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===Ru(this.options)&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=Ru(this.options);let e=0,i=!1;for(const o in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,o)&&(this.allowCreation=!1,i=!1,"function"==typeof t?(i=t(o,[]),i=i||this._handleObject(this.configureOptions[o],[o],!0)):!0!==t&&-1===Mp(t).call(t,o)||(i=!0),!1!==i&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(o),this._handleObject(this.configureOptions[o],[o])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let i=0;i<t.length;i++){if(void 0===e[t[i]]){e=void 0;break}e=e[t[i]]}return e}_makeItem(t){if(!0===this.allowCreation){const s=document.createElement("div");s.className="vis-configuration vis-config-item vis-config-s"+t.length;for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];return mc(i).call(i,(t=>{s.appendChild(t)})),this.domElements.push(s),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=document.createElement("div");if(o.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===i){for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild(Lm("i","b",t))}else o.innerText=t+":";return o}_makeDropdown(t,e,i){const o=document.createElement("select");o.className="vis-configuration vis-config-select";let s=0;void 0!==e&&-1!==Mp(t).call(t,e)&&(s=Mp(t).call(t,e));for(let e=0;e<t.length;e++){const i=document.createElement("option");i.value=t[e],e===s&&(i.selected="selected"),i.innerText=t[e],o.appendChild(i)}const n=this;o.onchange=function(){n._update(this.value,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}_makeRange(t,e,i){const o=t[0],s=t[1],n=t[2],r=t[3],a=document.createElement("input");a.className="vis-configuration vis-config-range";try{a.type="range",a.min=s,a.max=n}catch(t){}a.step=r;let h="",d=0;if(void 0!==e){const t=1.2;e<0&&e*t<s?(a.min=Math.ceil(e*t),d=a.min,h="range increased"):e/t<s&&(a.min=Math.ceil(e/t),d=a.min,h="range increased"),e*t>n&&1!==n&&(a.max=Math.ceil(e*t),d=a.max,h="range increased"),a.value=e}else a.value=o;const l=document.createElement("input");l.className="vis-configuration vis-config-rangeinput",l.value=a.value;const c=this;a.onchange=function(){l.value=this.value,c._update(Number(this.value),i)},a.oninput=function(){l.value=this.value};const u=this._makeLabel(i[i.length-1],i),p=this._makeItem(i,u,a,l);""!==h&&this.popupHistory[p]!==d&&(this.popupHistory[p]=d,this._setupPopup(h,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const i=document.createElement("div");i.id="vis-configuration-popup",i.className="vis-configuration-popup",i.innerText=t,i.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:i,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=Jp((()=>{this.popupDiv.html.style.opacity=0}),1500),this.popupDiv.deleteTimeout=Jp((()=>{this._removePopup()}),1800)}}_makeCheckbox(t,e,i){const o=document.createElement("input");o.type="checkbox",o.className="vis-configuration vis-config-checkbox",o.checked=t,void 0!==e&&(o.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:i,value:e}):this.changedOptions.push({path:i,value:e})));const s=this;o.onchange=function(){s._update(this.checked,i)};const n=this._makeLabel(i[i.length-1],i);this._makeItem(i,n,o)}_makeTextInput(t,e,i){const o=document.createElement("input");o.type="text",o.className="vis-configuration vis-config-text",o.value=e,e!==t&&this.changedOptions.push({path:i,value:e});const s=this;o.onchange=function(){s._update(this.value,i)};const n=this._makeLabel(i[i.length-1],i);this._makeItem(i,n,o)}_makeColorField(t,e,i){const o=t[1],s=document.createElement("div");"none"!==(e=void 0===e?o:e)?(s.className="vis-configuration vis-config-colorBlock",s.style.backgroundColor=e):s.className="vis-configuration vis-config-colorBlock none",e=void 0===e?o:e,s.onclick=()=>{this._showColorPicker(e,s,i)};const n=this._makeLabel(i[i.length-1],i);this._makeItem(i,n,s)}_showColorPicker(t,e,i){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback((t=>{const o="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=o,this._update(o,i)})),this.colorPicker.setCloseCallback((()=>{e.onclick=()=>{this._showColorPicker(t,e,i)}}))}_handleObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=!1;const s=Ru(this.options);let n=!1;for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){o=!0;const a=t[r],h=xm(e,r);if("function"==typeof s&&(o=s(r,e),!1===o&&!Vl(a)&&"string"!=typeof a&&"boolean"!=typeof a&&a instanceof Object&&(this.allowCreation=!1,o=this._handleObject(a,h,!0),this.allowCreation=!1===i)),!1!==o){n=!0;const t=this._getValue(h);if(Vl(a))this._handleArray(a,t,h);else if("string"==typeof a)this._makeTextInput(a,t,h);else if("boolean"==typeof a)this._makeCheckbox(a,t,h);else if(a instanceof Object){if(!this.hideOption(e,r,this.moduleOptions))if(void 0!==a.enabled){const t=xm(h,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(r,h,!0);this._makeItem(h,t),n=this._handleObject(a,h)||n}else this._makeCheckbox(a,e,h)}else{const t=this._makeLabel(r,h,!0);this._makeItem(h,t),n=this._handleObject(a,h)||n}}else console.error("dont know how to handle",a,r,h)}}return n}_handleArray(t,e,i){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,i),t[1]!==e&&this.changedOptions.push({path:i,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:Number(e)}))}_update(t,e){const i=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",i),this.initialized=!0,this.parent.setOptions(i)}_constructOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=i;t="false"!==(t="true"===t||t)&&t;for(let i=0;i<e.length;i++)"global"!==e[i]&&(void 0===o[e[i]]&&(o[e[i]]={}),i!==e.length-1?o=o[e[i]]:o[e[i]]=t);return i}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(Lm("pre","const options = "+Fp(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},Ym=hm,Xm=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=bp(t),this.y=bp(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,i=this.frame.parentNode.clientHeight,o=this.frame.parentNode.clientWidth;let s=0,n=0;if("flip"==this.overflowMethod){let i=!1,r=!0;this.y-t<this.padding&&(r=!1),this.x+e>o-this.padding&&(i=!0),s=i?this.x-e:this.x,n=r?this.y-t:this.y}else n=this.y-t,n+t+this.padding>i&&(n=i-t-this.padding),n<this.padding&&(n=this.padding),s=this.x,s+e+this.padding>o&&(s=o-e-this.padding),s<this.padding&&(s=this.padding);this.frame.style.left=s+"px",this.frame.style.top=n+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},Km=Vm,Gm=class t{static validate(e,i,o){Wm=!1,Hm=i;let s=i;return void 0!==o&&(s=i[o]),t.parse(e,s,[]),Wm}static parse(e,i,o){for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.check(s,e,i,o)}static check(e,i,o,s){if(void 0===o[e]&&void 0===o.__any__)return void t.getSuggestion(e,o,s);let n=e,r=!0;void 0===o[e]&&void 0!==o.__any__&&(n="__any__",r="object"===t.getType(i[e]));let a=o[n];r&&void 0!==a.__type__&&(a=a.__type__),t.checkFields(e,i,o,n,a,s)}static checkFields(e,i,o,s,n,r){const a=function(i){console.error("%c"+i+t.printLocation(r,e),Vm)},h=t.getType(i[e]),d=n[h];void 0!==d?"array"===t.getType(d)&&-1===Mp(d).call(d,i[e])?(a('Invalid option detected in "'+e+'". Allowed values are:'+t.print(d)+' not "'+i[e]+'". '),Wm=!0):"object"===h&&"__any__"!==s&&(r=xm(r,e),t.parse(i[e],o[s],r)):void 0===n.any&&(a('Invalid type received for "'+e+'". Expected: '+t.print(Jl(n))+". Received ["+h+'] "'+i[e]+'"'),Wm=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Vl(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,i,o){const s=t.findInOptions(e,i,o,!1),n=t.findInOptions(e,Hm,[],!0);let r;r=void 0!==s.indexMatch?" in "+t.printLocation(s.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+s.indexMatch+'"?\n\n':n.distance<=4&&s.distance>n.distance?" in "+t.printLocation(s.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(n.path,n.closestMatch,""):s.distance<=8?'. Did you mean "'+s.closestMatch+'"?'+t.printLocation(s.path,e):". Did you mean one of these: "+t.print(Jl(i))+t.printLocation(o,e),console.error('%cUnknown option detected: "'+e+'"'+r,Vm),Wm=!0}static findInOptions(e,i,o){let s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=1e9,r="",a=[];const h=e.toLowerCase();let d;for(const u in i){let p;if(void 0!==i[u].__type__&&!0===s){const s=t.findInOptions(e,i[u],xm(o,u));n>s.distance&&(r=s.closestMatch,a=s.path,n=s.distance,d=s.indexMatch)}else{var l;-1!==Mp(l=u.toLowerCase()).call(l,h)&&(d=u),p=t.levenshteinDistance(e,u),n>p&&(r=u,a=Nl(c=o).call(c),n=p)}}var c;return{closestMatch:r,path:a,distance:n,indexMatch:d}}static printLocation(t,e){let i="\n\n"+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Problem value found at: \n")+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)i+="  ";i+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)i+="  ";i+=e+"\n";for(let e=0;e<t.length+1;e++){for(let o=0;o<t.length-e;o++)i+="  ";i+="}\n"}return i+"\n\n"}static print(t){return Fp(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const i=[];let o,s;for(o=0;o<=e.length;o++)i[o]=[o];for(s=0;s<=t.length;s++)i[0][s]=s;for(o=1;o<=e.length;o++)for(s=1;s<=t.length;s++)e.charAt(o-1)==t.charAt(s-1)?i[o][s]=i[o-1][s-1]:i[o][s]=Math.min(i[o-1][s-1]+1,Math.min(i[o][s-1]+1,i[o-1][s]+1));return i[e.length][t.length]}};function Zm(t){return ey=t,function(){var t={};iy=0,void(oy=ey.charAt(0)),gy(),"strict"===sy&&(t.strict=!0,gy());"graph"!==sy&&"digraph"!==sy||(t.type=sy,gy());ny===Jm.IDENTIFIER&&(t.id=sy,gy());if("{"!=sy)throw wy("Angle bracket { expected");if(gy(),fy(t),"}"!=sy)throw wy("Angle bracket } expected");if(gy(),""!==sy)throw wy("End of file expected");return gy(),delete t.node,delete t.edge,delete t.graph,t}()}var Qm={fontsize:"font.size",fontcolor:"font.color",labelfontcolor:"font.color",fontname:"font.face",color:["color.border","color.background"],fillcolor:"color.background",tooltip:"title",labeltooltip:"title"},$m=Pp(Qm);$m.color="color.color",$m.style="dashes";var Jm={NULL:0,DELIMITER:1,IDENTIFIER:2,UNKNOWN:3},ty={"{":!0,"}":!0,"[":!0,"]":!0,";":!0,"=":!0,",":!0,"->":!0,"--":!0},ey="",iy=0,oy="",sy="",ny=Jm.NULL;function ry(){iy++,oy=ey.charAt(iy)}function ay(){return ey.charAt(iy+1)}function hy(t){var e=t.charCodeAt(0);return e<47?35===e||46===e:e<59?e>47:e<91?e>64:e<96?95===e:e<123&&e>96}function dy(t,e){if(t||(t={}),e)for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function ly(t,e,i){for(var o=e.split("."),s=t;o.length;){var n=o.shift();o.length?(s[n]||(s[n]={}),s=s[n]):s[n]=i}}function cy(t,e){for(var i,o,s=null,n=[t],r=t;r.parent;)n.push(r.parent),r=r.parent;if(r.nodes)for(i=0,o=r.nodes.length;i<o;i++)if(e.id===r.nodes[i].id){s=r.nodes[i];break}for(s||(s={id:e.id},t.node&&(s.attr=dy(s.attr,t.node))),i=n.length-1;i>=0;i--){var a,h=n[i];h.nodes||(h.nodes=[]),-1===Mp(a=h.nodes).call(a,s)&&h.nodes.push(s)}e.attr&&(s.attr=dy(s.attr,e.attr))}function uy(t,e){if(t.edges||(t.edges=[]),t.edges.push(e),t.edge){var i=dy({},t.edge);e.attr=dy(i,e.attr)}}function py(t,e,i,o,s){var n={from:e,to:i,type:o};return t.edge&&(n.attr=dy({},t.edge)),n.attr=dy(n.attr||{},s),null!=s&&s.hasOwnProperty("arrows")&&null!=s.arrows&&(n.arrows={to:{enabled:!0,type:s.arrows.type}},s.arrows=null),n}function gy(){for(ny=Jm.NULL,sy="";" "===oy||"\t"===oy||"\n"===oy||"\r"===oy;)ry();do{var t=!1;if("#"===oy){for(var e=iy-1;" "===ey.charAt(e)||"\t"===ey.charAt(e);)e--;if("\n"===ey.charAt(e)||""===ey.charAt(e)){for(;""!=oy&&"\n"!=oy;)ry();t=!0}}if("/"===oy&&"/"===ay()){for(;""!=oy&&"\n"!=oy;)ry();t=!0}if("/"===oy&&"*"===ay()){for(;""!=oy;){if("*"===oy&&"/"===ay()){ry(),ry();break}ry()}t=!0}for(;" "===oy||"\t"===oy||"\n"===oy||"\r"===oy;)ry()}while(t);if(""!==oy){var i=oy+ay();if(ty[i])return ny=Jm.DELIMITER,sy=i,ry(),void ry();if(ty[oy])return ny=Jm.DELIMITER,sy=oy,void ry();if(hy(oy)||"-"===oy){for(sy+=oy,ry();hy(oy);)sy+=oy,ry();return"false"===sy?sy=!1:"true"===sy?sy=!0:isNaN(Number(sy))||(sy=Number(sy)),void(ny=Jm.IDENTIFIER)}if('"'===oy){for(ry();""!=oy&&('"'!=oy||'"'===oy&&'"'===ay());)'"'===oy?(sy+=oy,ry()):"\\"===oy&&"n"===ay()?(sy+="\n",ry()):sy+=oy,ry();if('"'!=oy)throw wy('End of string " expected');return ry(),void(ny=Jm.IDENTIFIER)}for(ny=Jm.UNKNOWN;""!=oy;)sy+=oy,ry();throw new SyntaxError('Syntax error in part "'+_y(sy,30)+'"')}ny=Jm.DELIMITER}function fy(t){for(;""!==sy&&"}"!=sy;)my(t),";"===sy&&gy()}function my(t){var e=yy(t);if(e)by(t,e);else{var i=function(t){if("node"===sy)return gy(),t.node=vy(),"node";if("edge"===sy)return gy(),t.edge=vy(),"edge";if("graph"===sy)return gy(),t.graph=vy(),"graph";return null}(t);if(!i){if(ny!=Jm.IDENTIFIER)throw wy("Identifier expected");var o=sy;if(gy(),"="===sy){if(gy(),ny!=Jm.IDENTIFIER)throw wy("Identifier expected");t[o]=sy,gy()}else!function(t,e){var i={id:e},o=vy();o&&(i.attr=o);cy(t,i),by(t,e)}(t,o)}}}function yy(t){var e=null;if("subgraph"===sy&&((e={}).type="subgraph",gy(),ny===Jm.IDENTIFIER&&(e.id=sy,gy())),"{"===sy){if(gy(),e||(e={}),e.parent=t,e.node=t.node,e.edge=t.edge,e.graph=t.graph,fy(e),"}"!=sy)throw wy("Angle bracket } expected");gy(),delete e.node,delete e.edge,delete e.graph,delete e.parent,t.subgraphs||(t.subgraphs=[]),t.subgraphs.push(e)}return e}function by(t,e){for(;"->"===sy||"--"===sy;){var i,o=sy;gy();var s=yy(t);if(s)i=s;else{if(ny!=Jm.IDENTIFIER)throw wy("Identifier or subgraph expected");cy(t,{id:i=sy}),gy()}uy(t,py(t,e,i,o,vy())),e=i}}function vy(){for(var t,e,i=null,o={dashed:!0,solid:!1,dotted:[1,5]},s={dot:"circle",box:"box",crow:"crow",curve:"curve",icurve:"inv_curve",normal:"triangle",inv:"inv_triangle",diamond:"diamond",tee:"bar",vee:"vee"},n=new Array,r=new Array;"["===sy;){for(gy(),i={};""!==sy&&"]"!=sy;){if(ny!=Jm.IDENTIFIER)throw wy("Attribute name expected");var a=sy;if(gy(),"="!=sy)throw wy("Equal sign = expected");if(gy(),ny!=Jm.IDENTIFIER)throw wy("Attribute value expected");var h=sy;"style"===a&&(h=o[h]),"arrowhead"===a&&(a="arrows",h={to:{enabled:!0,type:s[h]}}),"arrowtail"===a&&(a="arrows",h={from:{enabled:!0,type:s[h]}}),n.push({attr:i,name:a,value:h}),r.push(a),gy(),","==sy&&gy()}if("]"!=sy)throw wy("Bracket ] expected");gy()}if(_u(r).call(r,"dir")){var d={arrows:{}};for(t=0;t<n.length;t++)if("arrows"===n[t].name)if(null!=n[t].value.to)d.arrows.to=t;else{if(null==n[t].value.from)throw wy("Invalid value of arrows");d.arrows.from=t}else"dir"===n[t].name&&(d.dir=t);var l,c,u=n[d.dir].value;if(!_u(r).call(r,"arrows"))if("both"===u)n.push({attr:n[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=n.length-1,n.push({attr:n[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=n.length-1;else if("forward"===u)n.push({attr:n[d.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),d.arrows.to=n.length-1;else if("back"===u)n.push({attr:n[d.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),d.arrows.from=n.length-1;else{if("none"!==u)throw wy('Invalid dir type "'+u+'"');n.push({attr:n[d.dir].attr,name:"arrows",value:""}),d.arrows.to=n.length-1}if("both"===u)d.arrows.to&&d.arrows.from?(c=n[d.arrows.to].value.to.type,l=n[d.arrows.from].value.from.type,n[d.arrows.to]={attr:n[d.arrows.to].attr,name:n[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}},Jc(n).call(n,d.arrows.from,1)):d.arrows.to?(c=n[d.arrows.to].value.to.type,l="arrow",n[d.arrows.to]={attr:n[d.arrows.to].attr,name:n[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l=n[d.arrows.from].value.from.type,n[d.arrows.from]={attr:n[d.arrows.from].attr,name:n[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}});else if("back"===u)d.arrows.to&&d.arrows.from?(c="",l=n[d.arrows.from].value.from.type,n[d.arrows.from]={attr:n[d.arrows.from].attr,name:n[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.to?(c="",l="arrow",d.arrows.from=d.arrows.to,n[d.arrows.from]={attr:n[d.arrows.from].attr,name:n[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="",l=n[d.arrows.from].value.from.type,n[d.arrows.to]={attr:n[d.arrows.from].attr,name:n[d.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),n[d.arrows.from]={attr:n[d.arrows.from].attr,name:n[d.arrows.from].name,value:{from:{enabled:!0,type:n[d.arrows.from].value.from.type}}};else if("none"===u){var p;n[p=d.arrows.to?d.arrows.to:d.arrows.from]={attr:n[p].attr,name:n[p].name,value:""}}else{if("forward"!==u)throw wy('Invalid dir type "'+u+'"');d.arrows.to&&d.arrows.from||d.arrows.to?(c=n[d.arrows.to].value.to.type,l="",n[d.arrows.to]={attr:n[d.arrows.to].attr,name:n[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):d.arrows.from&&(c="arrow",l="",d.arrows.to=d.arrows.from,n[d.arrows.to]={attr:n[d.arrows.to].attr,name:n[d.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),n[d.arrows.to]={attr:n[d.arrows.to].attr,name:n[d.arrows.to].name,value:{to:{enabled:!0,type:n[d.arrows.to].value.to.type}}}}Jc(n).call(n,d.dir,1)}if(_u(r).call(r,"penwidth")){var g=[];for(e=n.length,t=0;t<e;t++)"width"!==n[t].name&&("penwidth"===n[t].name&&(n[t].name="width"),g.push(n[t]));n=g}for(e=n.length,t=0;t<e;t++)ly(n[t].attr,n[t].name,n[t].value);return i}function wy(t){return new SyntaxError(t+', got "'+_y(sy,30)+'" (char '+iy+")")}function _y(t,e){return t.length<=e?t:t.substr(0,27)+"..."}function xy(t,e,i){for(var o=e.split("."),s=o.pop(),n=t,r=0;r<o.length;r++){var a=o[r];a in n||(n[a]={}),n=n[a]}return n[s]=i,t}function Ey(t,e){var i={};for(var o in t)if(t.hasOwnProperty(o)){var s=e[o];Vl(s)?mc(s).call(s,(function(e){xy(i,e,t[o])})):xy(i,"string"==typeof s?s:o,t[o])}return i}function Oy(t){var e,i=Zm(t),o={nodes:[],edges:[],options:{}};i.nodes&&mc(e=i.nodes).call(e,(function(t){var e={id:t.id,label:String(t.label||t.id)};dy(e,Ey(t.attr,Qm)),e.image&&(e.shape="image"),o.nodes.push(e)}));if(i.edges){var s,n=function(t){var e={from:t.from,to:t.to};return dy(e,Ey(t.attr,$m)),null==e.arrows&&"->"===t.type&&(e.arrows="to"),e};mc(s=i.edges).call(s,(function(t){var e,i,s,r,a,h,d;(e=t.from instanceof Object?t.from.nodes:{id:t.from},i=t.to instanceof Object?t.to.nodes:{id:t.to},t.from instanceof Object&&t.from.edges)&&mc(s=t.from.edges).call(s,(function(t){var e=n(t);o.edges.push(e)}));(a=i,h=function(e,i){var s=py(o,e.id,i.id,t.type,t.attr),r=n(s);o.edges.push(r)},Vl(r=e)?mc(r).call(r,(function(t){Vl(a)?mc(a).call(a,(function(e){h(t,e)})):h(t,a)})):Vl(a)?mc(a).call(a,(function(t){h(r,t)})):h(r,a),t.to instanceof Object&&t.to.edges)&&mc(d=t.to.edges).call(d,(function(t){var e=n(t);o.edges.push(e)}))}))}return i.attr&&(o.options=i.attr),o}var Cy=Object.freeze({__proto__:null,DOTToGraph:Oy,parseDOT:Zm});function ky(t,e){var i;const o={edges:{inheritColor:!1},nodes:{fixed:!1,parseColor:!1}};null!=e&&(null!=e.fixed&&(o.nodes.fixed=e.fixed),null!=e.parseColor&&(o.nodes.parseColor=e.parseColor),null!=e.inheritColor&&(o.edges.inheritColor=e.inheritColor));const s=t.edges,n=Zl(s).call(s,(t=>{const e={from:t.source,id:t.id,to:t.target};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),"Directed"===t.type&&(e.arrows="to"),t.color&&!1===o.edges.inheritColor&&(e.color=t.color),e}));return{nodes:Zl(i=t.nodes).call(i,(t=>{const e={id:t.id,fixed:o.nodes.fixed&&null!=t.x&&null!=t.y};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.size&&(e.size=t.size),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),null!=t.title&&(e.title=t.title),null!=t.x&&(e.x=t.x),null!=t.y&&(e.y=t.y),null!=t.color&&(!0===o.nodes.parseColor?e.color=t.color:e.color={background:t.color,border:t.color,highlight:{background:t.color,border:t.color},hover:{background:t.color,border:t.color}}),e})),edges:n}}var Sy=Object.freeze({__proto__:null,parseGephi:ky});var Ty=Object.freeze({__proto__:null,cn:{addDescription:"单击空白处放置新节点。",addEdge:"添加连接线",addNode:"添加节点",back:"返回",close:"關閉",createEdgeError:"无法将连接线连接到群集。",del:"删除选定",deleteClusterError:"无法删除群集。",edgeDescription:"单击某个节点并将该连接线拖动到另一个节点以连接它们。",edit:"编辑",editClusterError:"无法编辑群集。",editEdge:"编辑连接线",editEdgeDescription:"单击控制节点并将它们拖到节点上连接。",editNode:"编辑节点"},cs:{addDescription:"Kluknutím do prázdného prostoru můžete přidat nový vrchol.",addEdge:"Přidat hranu",addNode:"Přidat vrchol",back:"Zpět",close:"Zavřít",createEdgeError:"Nelze připojit hranu ke shluku.",del:"Smazat výběr",deleteClusterError:"Nelze mazat shluky.",edgeDescription:"Přetažením z jednoho vrcholu do druhého můžete spojit tyto vrcholy novou hranou.",edit:"Upravit",editClusterError:"Nelze upravovat shluky.",editEdge:"Upravit hranu",editEdgeDescription:"Přetažením kontrolního vrcholu hrany ji můžete připojit k jinému vrcholu.",editNode:"Upravit vrchol"},de:{addDescription:"Klicke auf eine freie Stelle, um einen neuen Knoten zu plazieren.",addEdge:"Kante hinzufügen",addNode:"Knoten hinzufügen",back:"Zurück",close:"Schließen",createEdgeError:"Es ist nicht möglich, Kanten mit Clustern zu verbinden.",del:"Lösche Auswahl",deleteClusterError:"Cluster können nicht gelöscht werden.",edgeDescription:"Klicke auf einen Knoten und ziehe die Kante zu einem anderen Knoten, um diese zu verbinden.",edit:"Editieren",editClusterError:"Cluster können nicht editiert werden.",editEdge:"Kante editieren",editEdgeDescription:"Klicke auf die Verbindungspunkte und ziehe diese auf einen Knoten, um sie zu verbinden.",editNode:"Knoten editieren"},en:{addDescription:"Click in an empty space to place a new node.",addEdge:"Add Edge",addNode:"Add Node",back:"Back",close:"Close",createEdgeError:"Cannot link edges to a cluster.",del:"Delete selected",deleteClusterError:"Clusters cannot be deleted.",edgeDescription:"Click on a node and drag the edge to another node to connect them.",edit:"Edit",editClusterError:"Clusters cannot be edited.",editEdge:"Edit Edge",editEdgeDescription:"Click on the control points and drag them to a node to connect to it.",editNode:"Edit Node"},es:{addDescription:"Haga clic en un lugar vacío para colocar un nuevo nodo.",addEdge:"Añadir arista",addNode:"Añadir nodo",back:"Atrás",close:"Cerrar",createEdgeError:"No se puede conectar una arista a un grupo.",del:"Eliminar selección",deleteClusterError:"No es posible eliminar grupos.",edgeDescription:"Haga clic en un nodo y arrastre la arista hacia otro nodo para conectarlos.",edit:"Editar",editClusterError:"No es posible editar grupos.",editEdge:"Editar arista",editEdgeDescription:"Haga clic en un punto de control y arrastrelo a un nodo para conectarlo.",editNode:"Editar nodo"},fr:{addDescription:"Cliquez dans un endroit vide pour placer un nœud.",addEdge:"Ajouter un lien",addNode:"Ajouter un nœud",back:"Retour",close:"Fermer",createEdgeError:"Impossible de créer un lien vers un cluster.",del:"Effacer la sélection",deleteClusterError:"Les clusters ne peuvent pas être effacés.",edgeDescription:"Cliquez sur un nœud et glissez le lien vers un autre nœud pour les connecter.",edit:"Éditer",editClusterError:"Les clusters ne peuvent pas être édités.",editEdge:"Éditer le lien",editEdgeDescription:"Cliquez sur les points de contrôle et glissez-les pour connecter un nœud.",editNode:"Éditer le nœud"},it:{addDescription:"Clicca per aggiungere un nuovo nodo",addEdge:"Aggiungi un vertice",addNode:"Aggiungi un nodo",back:"Indietro",close:"Chiudere",createEdgeError:"Non si possono collegare vertici ad un cluster",del:"Cancella la selezione",deleteClusterError:"I cluster non possono essere cancellati",edgeDescription:"Clicca su un nodo e trascinalo ad un altro nodo per connetterli.",edit:"Modifica",editClusterError:"I clusters non possono essere modificati.",editEdge:"Modifica il vertice",editEdgeDescription:"Clicca sui Punti di controllo e trascinali ad un nodo per connetterli.",editNode:"Modifica il nodo"},nl:{addDescription:"Klik op een leeg gebied om een nieuwe node te maken.",addEdge:"Link toevoegen",addNode:"Node toevoegen",back:"Terug",close:"Sluiten",createEdgeError:"Kan geen link maken naar een cluster.",del:"Selectie verwijderen",deleteClusterError:"Clusters kunnen niet worden verwijderd.",edgeDescription:"Klik op een node en sleep de link naar een andere node om ze te verbinden.",edit:"Wijzigen",editClusterError:"Clusters kunnen niet worden aangepast.",editEdge:"Link wijzigen",editEdgeDescription:"Klik op de verbindingspunten en sleep ze naar een node om daarmee te verbinden.",editNode:"Node wijzigen"},pt:{addDescription:"Clique em um espaço em branco para adicionar um novo nó",addEdge:"Adicionar aresta",addNode:"Adicionar nó",back:"Voltar",close:"Fechar",createEdgeError:"Não foi possível linkar arestas a um cluster.",del:"Remover selecionado",deleteClusterError:"Clusters não puderam ser removidos.",edgeDescription:"Clique em um nó e arraste a aresta até outro nó para conectá-los",edit:"Editar",editClusterError:"Clusters não puderam ser editados.",editEdge:"Editar aresta",editEdgeDescription:"Clique nos pontos de controle e os arraste para um nó para conectá-los",editNode:"Editar nó"},ru:{addDescription:"Кликните в свободное место, чтобы добавить новый узел.",addEdge:"Добавить ребро",addNode:"Добавить узел",back:"Назад",close:"Закрывать",createEdgeError:"Невозможно соединить ребра в кластер.",del:"Удалить выбранное",deleteClusterError:"Кластеры не могут быть удалены",edgeDescription:"Кликните на узел и протяните ребро к другому узлу, чтобы соединить их.",edit:"Редактировать",editClusterError:"Кластеры недоступны для редактирования.",editEdge:"Редактировать ребро",editEdgeDescription:"Кликните на контрольные точки и перетащите их в узел, чтобы подключиться к нему.",editNode:"Редактировать узел"},uk:{addDescription:"Kлікніть на вільне місце, щоб додати новий вузол.",addEdge:"Додати край",addNode:"Додати вузол",back:"Назад",close:"Закрити",createEdgeError:"Не можливо об'єднати краї в групу.",del:"Видалити обране",deleteClusterError:"Групи не можуть бути видалені.",edgeDescription:"Клікніть на вузол і перетягніть край до іншого вузла, щоб їх з'єднати.",edit:"Редагувати",editClusterError:"Групи недоступні для редагування.",editEdge:"Редагувати край",editEdgeDescription:"Клікніть на контрольні точки і перетягніть їх у вузол, щоб підключитися до нього.",editNode:"Редагувати вузол"}});class My{constructor(){this.NUM_ITERATIONS=4,this.image=new Image,this.canvas=document.createElement("canvas")}init(){if(this.initialized())return;this.src=this.image.src;const t=this.image.width,e=this.image.height;this.width=t,this.height=e;const i=Math.floor(e/2),o=Math.floor(e/4),s=Math.floor(e/8),n=Math.floor(e/16),r=Math.floor(t/2),a=Math.floor(t/4),h=Math.floor(t/8),d=Math.floor(t/16);this.canvas.width=3*a,this.canvas.height=i,this.coordinates=[[0,0,r,i],[r,0,a,o],[r,o,h,s],[5*h,o,d,n]],this._fillMipMap()}initialized(){return void 0!==this.coordinates}_fillMipMap(){const t=this.canvas.getContext("2d"),e=this.coordinates[0];t.drawImage(this.image,e[0],e[1],e[2],e[3]);for(let e=1;e<this.NUM_ITERATIONS;e++){const i=this.coordinates[e-1],o=this.coordinates[e];t.drawImage(this.canvas,i[0],i[1],i[2],i[3],o[0],o[1],o[2],o[3])}}drawImageAtPosition(t,e,i,o,s,n){if(this.initialized())if(e>2){e*=.5;let r=0;for(;e>2&&r<this.NUM_ITERATIONS;)e*=.5,r+=1;r>=this.NUM_ITERATIONS&&(r=this.NUM_ITERATIONS-1);const a=this.coordinates[r];t.drawImage(this.canvas,a[0],a[1],a[2],a[3],i,o,s,n)}else t.drawImage(this.image,i,o,s,n)}}class Dy{constructor(t){this.images={},this.imageBroken={},this.callback=t}_tryloadBrokenUrl(t,e,i){void 0!==t&&void 0!==i&&(void 0!==e?(i.image.onerror=()=>{console.error("Could not load brokenImage:",e)},i.image.src=e):console.warn("No broken url image defined"))}_redrawWithImage(t){this.callback&&this.callback(t)}load(t,e){const i=this.images[t];if(i)return i;const o=new My;return this.images[t]=o,o.image.onload=()=>{this._fixImageCoordinates(o.image),o.init(),this._redrawWithImage(o)},o.image.onerror=()=>{console.error("Could not load image:",t),this._tryloadBrokenUrl(t,e,o)},o.image.src=t,o}_fixImageCoordinates(t){0===t.width&&(document.body.appendChild(t),t.width=t.offsetWidth,t.height=t.offsetHeight,document.body.removeChild(t))}}var Py={exports:{}},Iy=r((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),By=r,Fy=et,zy=_,Ny=Iy,Ay=Object.isExtensible,Ry=By((function(){Ay(1)}))||Ny?function(t){return!!Fy(t)&&((!Ny||"ArrayBuffer"!==zy(t))&&(!Ay||Ay(t)))}:Ay,jy=!r((function(){return Object.isExtensible(Object.preventExtensions({}))})),Ly=Mi,Hy=y,Wy=Xi,Vy=et,qy=Jt,Uy=$e.f,Yy=In,Xy=zn,Ky=Ry,Gy=jy,Zy=!1,Qy=se("meta"),$y=0,Jy=function(t){Uy(t,Qy,{value:{objectID:"O"+$y++,weakData:{}}})},tb=Py.exports={enable:function(){tb.enable=function(){},Zy=!0;var t=Yy.f,e=Hy([].splice),i={};i[Qy]=1,t(i).length&&(Yy.f=function(i){for(var o=t(i),s=0,n=o.length;s<n;s++)if(o[s]===Qy){e(o,s,1);break}return o},Ly({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Xy.f}))},fastKey:function(t,e){if(!Vy(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!qy(t,Qy)){if(!Ky(t))return"F";if(!e)return"E";Jy(t)}return t[Qy].objectID},getWeakData:function(t,e){if(!qy(t,Qy)){if(!Ky(t))return!0;if(!e)return!1;Jy(t)}return t[Qy].weakData},onFreeze:function(t){return Gy&&Zy&&Ky(t)&&!qy(t,Qy)&&Jy(t),t}};Wy[Qy]=!0;var eb=Py.exports,ib=id,ob=pe("iterator"),sb=Array.prototype,nb=us,rb=zt,ab=Y,hb=id,db=pe("iterator"),lb=function(t){if(!ab(t))return rb(t,db)||rb(t,"@@iterator")||hb[nb(t)]},cb=B,ub=It,pb=oi,gb=Tt,fb=lb,mb=TypeError,yb=B,bb=oi,vb=zt,wb=Qe,_b=B,xb=oi,Eb=Tt,Ob=function(t){return void 0!==t&&(ib.Array===t||sb[ob]===t)},Cb=Hi,kb=ht,Sb=function(t,e){var i=arguments.length<2?fb(t):e;if(ub(i))return pb(cb(i,t));throw new mb(gb(t)+" is not iterable")},Tb=lb,Mb=function(t,e,i){var o,s;bb(t);try{if(!(o=vb(t,"return"))){if("throw"===e)throw i;return i}o=yb(o,t)}catch(t){s=!0,o=t}if("throw"===e)throw i;if(s)throw o;return bb(o),i},Db=TypeError,Pb=function(t,e){this.stopped=t,this.result=e},Ib=Pb.prototype,Bb=function(t,e,i){var o,s,n,r,a,h,d,l=i&&i.that,c=!(!i||!i.AS_ENTRIES),u=!(!i||!i.IS_RECORD),p=!(!i||!i.IS_ITERATOR),g=!(!i||!i.INTERRUPTED),f=wb(e,l),m=function(t){return o&&Mb(o,"normal",t),new Pb(!0,t)},y=function(t){return c?(xb(t),g?f(t[0],t[1],m):f(t[0],t[1])):g?f(t,m):f(t)};if(u)o=t.iterator;else if(p)o=t;else{if(!(s=Tb(t)))throw new Db(Eb(t)+" is not iterable");if(Ob(s)){for(n=0,r=Cb(t);r>n;n++)if((a=y(t[n]))&&kb(Ib,a))return a;return new Pb(!1)}o=Sb(t,s)}for(h=u?t.next:o.next;!(d=_b(h,o)).done;){try{a=y(d.value)}catch(t){Mb(o,"throw",t)}if("object"==typeof a&&a&&kb(Ib,a))return a}return new Pb(!1)},Fb=ht,zb=TypeError,Nb=function(t,e){if(Fb(e,t))return t;throw new zb("Incorrect invocation")},Ab=Mi,Rb=n,jb=eb,Lb=r,Hb=yi,Wb=Bb,Vb=Nb,qb=T,Ub=et,Yb=Y,Xb=wr,Kb=$e.f,Gb=Ur.forEach,Zb=D,Qb=Ar.set,$b=Ar.getterFor,Jb=function(t,e,i){var o,s=-1!==t.indexOf("Map"),n=-1!==t.indexOf("Weak"),r=s?"set":"add",a=Rb[t],h=a&&a.prototype,d={};if(Zb&&qb(a)&&(n||h.forEach&&!Lb((function(){(new a).entries().next()})))){var l=(o=e((function(e,i){Qb(Vb(e,l),{type:t,collection:new a}),Yb(i)||Wb(i,e[r],{that:e,AS_ENTRIES:s})}))).prototype,c=$b(t);Gb(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var e="add"===t||"set"===t;!(t in h)||n&&"clear"===t||Hb(l,t,(function(i,o){var s=c(this).collection;if(!e&&n&&!Ub(i))return"get"===t&&void 0;var r=s[t](0===i?0:i,o);return e?this:r}))})),n||Kb(l,"size",{configurable:!0,get:function(){return c(this).collection.size}})}else o=i.getConstructor(e,t,s,r),jb.enable();return Xb(o,t,!1,!0),d[t]=o,Ab({global:!0,forced:!0},d),n||i.setStrong(o,t,s),o},tv=Kn,ev=function(t,e,i){for(var o in e)i&&i.unsafe&&t[o]?t[o]=e[o]:tv(t,o,e[o],i);return t},iv=at,ov=Zn,sv=D,nv=pe("species"),rv=Pn,av=Zn,hv=ev,dv=Qe,lv=Nb,cv=Y,uv=Bb,pv=Zd,gv=Qd,fv=function(t){var e=iv(t);sv&&e&&!e[nv]&&ov(e,nv,{configurable:!0,get:function(){return this}})},mv=D,yv=eb.fastKey,bv=Ar.set,vv=Ar.getterFor,wv={getConstructor:function(t,e,i,o){var s=t((function(t,s){lv(t,n),bv(t,{type:e,index:rv(null),first:void 0,last:void 0,size:0}),mv||(t.size=0),cv(s)||uv(s,t[o],{that:t,AS_ENTRIES:i})})),n=s.prototype,r=vv(e),a=function(t,e,i){var o,s,n=r(t),a=h(t,e);return a?a.value=i:(n.last=a={index:s=yv(e,!0),key:e,value:i,previous:o=n.last,next:void 0,removed:!1},n.first||(n.first=a),o&&(o.next=a),mv?n.size++:t.size++,"F"!==s&&(n.index[s]=a)),t},h=function(t,e){var i,o=r(t),s=yv(e);if("F"!==s)return o.index[s];for(i=o.first;i;i=i.next)if(i.key===e)return i};return hv(n,{clear:function(){for(var t=r(this),e=t.index,i=t.first;i;)i.removed=!0,i.previous&&(i.previous=i.previous.next=void 0),delete e[i.index],i=i.next;t.first=t.last=void 0,mv?t.size=0:this.size=0},delete:function(t){var e=this,i=r(e),o=h(e,t);if(o){var s=o.next,n=o.previous;delete i.index[o.index],o.removed=!0,n&&(n.next=s),s&&(s.previous=n),i.first===o&&(i.first=s),i.last===o&&(i.last=n),mv?i.size--:e.size--}return!!o},forEach:function(t){for(var e,i=r(this),o=dv(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:i.first;)for(o(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!h(this,t)}}),hv(n,i?{get:function(t){var e=h(this,t);return e&&e.value},set:function(t,e){return a(this,0===t?0:t,e)}}:{add:function(t){return a(this,t=0===t?0:t,t)}}),mv&&av(n,"size",{configurable:!0,get:function(){return r(this).size}}),s},setStrong:function(t,e,i){var o=e+" Iterator",s=vv(e),n=vv(o);pv(t,e,(function(t,e){bv(this,{type:o,target:t,state:s(t),kind:e,last:void 0})}),(function(){for(var t=n(this),e=t.kind,i=t.last;i&&i.removed;)i=i.previous;return t.target&&(t.last=i=i?i.next:t.state.first)?gv("keys"===e?i.key:"values"===e?i.value:[i.key,i.value],!1):(t.target=void 0,gv(void 0,!0))}),i?"entries":"values",!i,!0),fv(e)}};Jb("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),wv);var _v=y,xv=Bi,Ev=nn,Ov=G,Cv=_v("".charAt),kv=_v("".charCodeAt),Sv=_v("".slice),Tv=function(t){return function(e,i){var o,s,n=Ev(Ov(e)),r=xv(i),a=n.length;return r<0||r>=a?t?"":void 0:(o=kv(n,r))<55296||o>56319||r+1===a||(s=kv(n,r+1))<56320||s>57343?t?Cv(n,r):o:t?Sv(n,r,r+2):s-56320+(o-55296<<10)+65536}},Mv={codeAt:Tv(!1),charAt:Tv(!0)}.charAt,Dv=nn,Pv=Ar,Iv=Zd,Bv=Qd,Fv="String Iterator",zv=Pv.set,Nv=Pv.getterFor(Fv);Iv(String,"String",(function(t){zv(this,{type:Fv,string:Dv(t),index:0})}),(function(){var t,e=Nv(this),i=e.string,o=e.index;return o>=i.length?Bv(void 0,!0):(t=Mv(i,o),e.index+=t.length,Bv(t,!1))}));var Av=o(it.Map);class Rv{constructor(){this.clear(),this._defaultIndex=0,this._groupIndex=0,this._defaultGroups=[{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},{border:"#FFA500",background:"#FFFF00",highlight:{border:"#FFA500",background:"#FFFFA3"},hover:{border:"#FFA500",background:"#FFFFA3"}},{border:"#FA0A10",background:"#FB7E81",highlight:{border:"#FA0A10",background:"#FFAFB1"},hover:{border:"#FA0A10",background:"#FFAFB1"}},{border:"#41A906",background:"#7BE141",highlight:{border:"#41A906",background:"#A1EC76"},hover:{border:"#41A906",background:"#A1EC76"}},{border:"#E129F0",background:"#EB7DF4",highlight:{border:"#E129F0",background:"#F0B3F5"},hover:{border:"#E129F0",background:"#F0B3F5"}},{border:"#7C29F0",background:"#AD85E4",highlight:{border:"#7C29F0",background:"#D3BDF0"},hover:{border:"#7C29F0",background:"#D3BDF0"}},{border:"#C37F00",background:"#FFA807",highlight:{border:"#C37F00",background:"#FFCA66"},hover:{border:"#C37F00",background:"#FFCA66"}},{border:"#4220FB",background:"#6E6EFD",highlight:{border:"#4220FB",background:"#9B9BFD"},hover:{border:"#4220FB",background:"#9B9BFD"}},{border:"#FD5A77",background:"#FFC0CB",highlight:{border:"#FD5A77",background:"#FFD1D9"},hover:{border:"#FD5A77",background:"#FFD1D9"}},{border:"#4AD63A",background:"#C2FABC",highlight:{border:"#4AD63A",background:"#E6FFE3"},hover:{border:"#4AD63A",background:"#E6FFE3"}},{border:"#990000",background:"#EE0000",highlight:{border:"#BB0000",background:"#FF3333"},hover:{border:"#BB0000",background:"#FF3333"}},{border:"#FF6000",background:"#FF6000",highlight:{border:"#FF6000",background:"#FF6000"},hover:{border:"#FF6000",background:"#FF6000"}},{border:"#97C2FC",background:"#2B7CE9",highlight:{border:"#D2E5FF",background:"#2B7CE9"},hover:{border:"#D2E5FF",background:"#2B7CE9"}},{border:"#399605",background:"#255C03",highlight:{border:"#399605",background:"#255C03"},hover:{border:"#399605",background:"#255C03"}},{border:"#B70054",background:"#FF007E",highlight:{border:"#B70054",background:"#FF007E"},hover:{border:"#B70054",background:"#FF007E"}},{border:"#AD85E4",background:"#7C29F0",highlight:{border:"#D3BDF0",background:"#7C29F0"},hover:{border:"#D3BDF0",background:"#7C29F0"}},{border:"#4557FA",background:"#000EA1",highlight:{border:"#6E6EFD",background:"#000EA1"},hover:{border:"#6E6EFD",background:"#000EA1"}},{border:"#FFC0CB",background:"#FD5A77",highlight:{border:"#FFD1D9",background:"#FD5A77"},hover:{border:"#FFD1D9",background:"#FD5A77"}},{border:"#C2FABC",background:"#74D66A",highlight:{border:"#E6FFE3",background:"#74D66A"},hover:{border:"#E6FFE3",background:"#74D66A"}},{border:"#EE0000",background:"#990000",highlight:{border:"#FF3333",background:"#BB0000"},hover:{border:"#FF3333",background:"#BB0000"}}],this.options={},this.defaultOptions={useDefaultGroups:!0},wo(this.options,this.defaultOptions)}setOptions(t){const e=["useDefaultGroups"];if(void 0!==t)for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&-1===Mp(e).call(e,i)){const e=t[i];this.add(i,e)}}clear(){this._groups=new Av,this._groupNames=[]}get(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this._groups.get(t);if(void 0===i&&e)if(!1===this.options.useDefaultGroups&&this._groupNames.length>0){const e=this._groupIndex%this._groupNames.length;++this._groupIndex,i={},i.color=this._groups.get(this._groupNames[e]),this._groups.set(t,i)}else{const e=this._defaultIndex%this._defaultGroups.length;this._defaultIndex++,i={},i.color=this._defaultGroups[e],this._groups.set(t,i)}return i}add(t,e){return this._groups.has(t)||this._groupNames.push(t),this._groups.set(t,e),e}}Mi({target:"Number",stat:!0},{isNaN:function(t){return t!=t}});var jv=o(it.Number.isNaN),Lv=n.isFinite,Hv=Number.isFinite||function(t){return"number"==typeof t&&Lv(t)};Mi({target:"Number",stat:!0},{isFinite:Hv});var Wv=o(it.Number.isFinite),Vv=Ur.some;Mi({target:"Array",proto:!0,forced:!nc("some")},{some:function(t){return Vv(this,t,arguments.length>1?arguments[1]:void 0)}});var qv=zo("Array").some,Uv=ht,Yv=qv,Xv=Array.prototype,Kv=function(t){var e=t.some;return t===Xv||Uv(Xv,t)&&e===Xv.some?Yv:e},Gv=o(Kv),Zv={exports:{}},Qv=Mi,$v=D,Jv=$e.f;Qv({target:"Object",stat:!0,forced:Object.defineProperty!==Jv,sham:!$v},{defineProperty:Jv});var tw=it.Object,ew=Zv.exports=function(t,e,i){return tw.defineProperty(t,e,i)};tw.defineProperty.sham&&(ew.sham=!0);var iw=Zv.exports,ow=o(iw),sw=pe,nw=$e.f,rw=sw("metadata"),aw=Function.prototype;void 0===aw[rw]&&nw(aw,rw,{value:null}),rr("asyncDispose"),rr("dispose"),rr("metadata");var hw=fl,dw=y,lw=at("Symbol"),cw=lw.keyFor,uw=dw(lw.prototype.valueOf),pw=lw.isRegisteredSymbol||function(t){try{return void 0!==cw(uw(t))}catch(t){return!1}};Mi({target:"Symbol",stat:!0},{isRegisteredSymbol:pw});for(var gw=Xt,fw=at,mw=y,yw=kt,bw=pe,vw=fw("Symbol"),ww=vw.isWellKnownSymbol,_w=fw("Object","getOwnPropertyNames"),xw=mw(vw.prototype.valueOf),Ew=gw("wks"),Ow=0,Cw=_w(vw),kw=Cw.length;Ow<kw;Ow++)try{var Sw=Cw[Ow];yw(vw[Sw])&&bw(Sw)}catch(t){}var Tw=function(t){if(ww&&ww(t))return!0;try{for(var e=xw(t),i=0,o=_w(Ew),s=o.length;i<s;i++)if(Ew[o[i]]==e)return!0}catch(t){}return!1};Mi({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Tw}),rr("matcher"),rr("observable"),Mi({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:pw}),Mi({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Tw}),rr("metadataKey"),rr("patternMatch"),rr("replaceAll");var Mw=o(hw),Dw=o(Qn.f("iterator"));function Pw(t){return Pw="function"==typeof Mw&&"symbol"==typeof Dw?function(t){return typeof t}:function(t){return t&&"function"==typeof Mw&&t.constructor===Mw&&t!==Mw.prototype?"symbol":typeof t},Pw(t)}var Iw=o(Qn.f("toPrimitive"));function Bw(t){var e=function(t,e){if("object"!==Pw(t)||null===t)return t;var i=t[Iw];if(void 0!==i){var o=i.call(t,e||"default");if("object"!==Pw(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Pw(e)?e:String(e)}function Fw(t,e,i){return(e=Bw(e))in t?ow(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var zw=n,Nw=r,Aw=nn,Rw=np.trim,jw=Qu,Lw=y("".charAt),Hw=zw.parseFloat,Ww=zw.Symbol,Vw=Ww&&Ww.iterator,qw=1/Hw(jw+"-0")!=-1/0||Vw&&!Nw((function(){Hw(Object(Vw))}))?function(t){var e=Rw(Aw(t)),i=Hw(e);return 0===i&&"-"===Lw(e,0)?-0:i}:Hw;Mi({global:!0,forced:parseFloat!==qw},{parseFloat:qw});var Uw=o(it.parseFloat),Yw=Mi,Xw=r,Kw=zn.f;Yw({target:"Object",stat:!0,forced:Xw((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:Kw});var Gw=it.Object,Zw=function(t){return Gw.getOwnPropertyNames(t)},Qw=o(Zw),$w=o(it.Object.getOwnPropertySymbols),Jw={exports:{}},t_=Mi,e_=r,i_=$,o_=M.f,s_=D;t_({target:"Object",stat:!0,forced:!s_||e_((function(){o_(1)})),sham:!s_},{getOwnPropertyDescriptor:function(t,e){return o_(i_(t),e)}});var n_=it.Object,r_=Jw.exports=function(t,e){return n_.getOwnPropertyDescriptor(t,e)};n_.getOwnPropertyDescriptor.sham&&(r_.sham=!0);var a_=o(Jw.exports),h_=Wl,d_=$,l_=M,c_=os;Mi({target:"Object",stat:!0,sham:!D},{getOwnPropertyDescriptors:function(t){for(var e,i,o=d_(t),s=l_.f,n=h_(o),r={},a=0;n.length>a;)void 0!==(i=s(o,e=n[a++]))&&c_(r,e,i);return r}});var u_=o(it.Object.getOwnPropertyDescriptors),p_={exports:{}},g_=Mi,f_=D,m_=rn.f;g_({target:"Object",stat:!0,forced:Object.defineProperties!==m_,sham:!f_},{defineProperties:m_});var y_=it.Object,b_=p_.exports=function(t,e){return y_.defineProperties(t,e)};y_.defineProperties.sham&&(b_.sham=!0);var v_=o(p_.exports),w_=o(iw);function __(t,e){const i=["node","edge","label"];let o=!0;const s=Am(e,"chosen");if("boolean"==typeof s)o=s;else if("object"==typeof s){if(-1===Mp(i).call(i,t))throw new Error("choosify: subOption '"+t+"' should be one of '"+i.join("', '")+"'");const s=Am(e,["chosen",t]);"boolean"!=typeof s&&"function"!=typeof s||(o=s)}return o}function x_(t,e,i){if(t.width<=0||t.height<=0)return!1;if(void 0!==i){const t={x:e.x-i.x,y:e.y-i.y};if(0!==i.angle){const o=-i.angle;e={x:Math.cos(o)*t.x-Math.sin(o)*t.y,y:Math.sin(o)*t.x+Math.cos(o)*t.y}}else e=t}const o=t.x+t.width,s=t.y+t.width;return t.left<e.x&&o>e.x&&t.top<e.y&&s>e.y}function E_(t){return"string"==typeof t&&""!==t}function O_(t,e,i,o){let s=o.x,n=o.y;if("function"==typeof o.distanceToBorder){const i=o.distanceToBorder(t,e),r=Math.sin(e)*i,a=Math.cos(e)*i;a===i?(s+=i,n=o.y):r===i?(s=o.x,n-=i):(s+=a,n-=r)}else o.shape.width>o.shape.height?(s=o.x+.5*o.shape.width,n=o.y-i):(s=o.x+i,n=o.y-.5*o.shape.height);return{x:s,y:n}}var C_=zo("Array").values,k_=us,S_=Jt,T_=ht,M_=C_,D_=Array.prototype,P_={DOMTokenList:!0,NodeList:!0},I_=function(t){var e=t.values;return t===D_||T_(D_,t)&&e===D_.values||S_(P_,k_(t))?M_:e},B_=o(I_);class F_{constructor(t){this.measureText=t,this.current=0,this.width=0,this.height=0,this.lines=[]}_add(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"normal";void 0===this.lines[t]&&(this.lines[t]={width:0,height:0,blocks:[]});let o=e;void 0!==e&&""!==e||(o=" ");const s=this.measureText(o,i),n=wo({},B_(s));n.text=e,n.width=s.width,n.mod=i,void 0!==e&&""!==e||(n.width=0),this.lines[t].blocks.push(n),this.lines[t].width+=n.width}curWidth(){const t=this.lines[this.current];return void 0===t?0:t.width}append(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e)}newLine(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal";this._add(this.current,t,e),this.current++}determineLineHeights(){for(let t=0;t<this.lines.length;t++){const e=this.lines[t];let i=0;if(void 0!==e.blocks)for(let t=0;t<e.blocks.length;t++){const o=e.blocks[t];i<o.height&&(i=o.height)}e.height=i}}determineLabelSize(){let t=0,e=0;for(let i=0;i<this.lines.length;i++){const o=this.lines[i];o.width>t&&(t=o.width),e+=o.height}this.width=t,this.height=e}removeEmptyBlocks(){const t=[];for(let e=0;e<this.lines.length;e++){const i=this.lines[e];if(0===i.blocks.length)continue;if(e===this.lines.length-1&&0===i.width)continue;const o={};let s;wo(o,i),o.blocks=[];const n=[];for(let t=0;t<i.blocks.length;t++){const e=i.blocks[t];0!==e.width?n.push(e):void 0===s&&(s=e)}0===n.length&&void 0!==s&&n.push(s),o.blocks=n,t.push(o)}return t}finalize(){this.determineLineHeights(),this.determineLabelSize();const t=this.removeEmptyBlocks();return{width:this.width,height:this.height,lines:t}}}const z_={"<b>":/<b>/,"<i>":/<i>/,"<code>":/<code>/,"</b>":/<\/b>/,"</i>":/<\/i>/,"</code>":/<\/code>/,"*":/\*/,_:/_/,"`":/`/,afterBold:/[^*]/,afterItal:/[^_]/,afterMono:/[^`]/};class N_{constructor(t){this.text=t,this.bold=!1,this.ital=!1,this.mono=!1,this.spacing=!1,this.position=0,this.buffer="",this.modStack=[],this.blocks=[]}mod(){return 0===this.modStack.length?"normal":this.modStack[0]}modName(){return 0===this.modStack.length?"normal":"mono"===this.modStack[0]?"mono":this.bold&&this.ital?"boldital":this.bold?"bold":this.ital?"ital":void 0}emitBlock(){this.spacing&&(this.add(" "),this.spacing=!1),this.buffer.length>0&&(this.blocks.push({text:this.buffer,mod:this.modName()}),this.buffer="")}add(t){" "===t&&(this.spacing=!0),this.spacing&&(this.buffer+=" ",this.spacing=!1)," "!=t&&(this.buffer+=t)}parseWS(t){return!!/[ \t]/.test(t)&&(this.mono?this.add(t):this.spacing=!0,!0)}setTag(t){this.emitBlock(),this[t]=!0,this.modStack.unshift(t)}unsetTag(t){this.emitBlock(),this[t]=!1,this.modStack.shift()}parseStartTag(t,e){return!(this.mono||this[t]||!this.match(e))&&(this.setTag(t),!0)}match(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const[i,o]=this.prepareRegExp(t),s=i.test(this.text.substr(this.position,o));return s&&e&&(this.position+=o-1),s}parseEndTag(t,e,i){let o=this.mod()===t;return o="mono"===t?o&&this.mono:o&&!this.mono,!(!o||!this.match(e))&&(void 0!==i?(this.position===this.text.length-1||this.match(i,!1))&&this.unsetTag(t):this.unsetTag(t),!0)}replace(t,e){return!!this.match(t)&&(this.add(e),this.position+=length-1,!0)}prepareRegExp(t){let e,i;if(t instanceof RegExp)i=t,e=1;else{const o=z_[t];i=void 0!==o?o:new RegExp(t),e=t.length}return[i,e]}}class A_{constructor(t,e,i,o){this.ctx=t,this.parent=e,this.selected=i,this.hover=o;this.lines=new F_(((e,s)=>{if(void 0===e)return 0;const n=this.parent.getFormattingValues(t,i,o,s);let r=0;if(""!==e){r=this.ctx.measureText(e).width}return{width:r,values:n}}))}process(t){if(!E_(t))return this.lines.finalize();const e=this.parent.fontOptions;t=(t=t.replace(/\r\n/g,"\n")).replace(/\r/g,"\n");const i=String(t).split("\n"),o=i.length;if(e.multi)for(let t=0;t<o;t++){const o=this.splitBlocks(i[t],e.multi);if(void 0!==o)if(0!==o.length){if(e.maxWdt>0)for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.splitStringIntoLines(i,e,!0)}else for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.lines.append(i,e)}this.lines.newLine()}else this.lines.newLine("")}else if(e.maxWdt>0)for(let t=0;t<o;t++)this.splitStringIntoLines(i[t]);else for(let t=0;t<o;t++)this.lines.newLine(i[t]);return this.lines.finalize()}decodeMarkupSystem(t){let e="none";return"markdown"===t||"md"===t?e="markdown":!0!==t&&"html"!==t||(e="html"),e}splitHtmlBlocks(t){const e=new N_(t),i=t=>{if(/&/.test(t)){return e.replace(e.text,"&lt;","<")||e.replace(e.text,"&amp;","&")||e.add("&"),!0}return!1};for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||/</.test(t)&&(e.parseStartTag("bold","<b>")||e.parseStartTag("ital","<i>")||e.parseStartTag("mono","<code>")||e.parseEndTag("bold","</b>")||e.parseEndTag("ital","</i>")||e.parseEndTag("mono","</code>"))||i(t)||e.add(t),e.position++}return e.emitBlock(),e.blocks}splitMarkdownBlocks(t){const e=new N_(t);let i=!0;const o=t=>!!/\\/.test(t)&&(e.position<this.text.length+1&&(e.position++,t=this.text.charAt(e.position),/ \t/.test(t)?e.spacing=!0:(e.add(t),i=!1)),!0);for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||o(t)||(i||e.spacing)&&(e.parseStartTag("bold","*")||e.parseStartTag("ital","_")||e.parseStartTag("mono","`"))||e.parseEndTag("bold","*","afterBold")||e.parseEndTag("ital","_","afterItal")||e.parseEndTag("mono","`","afterMono")||(e.add(t),i=!1),e.position++}return e.emitBlock(),e.blocks}splitBlocks(t,e){const i=this.decodeMarkupSystem(e);return"none"===i?[{text:t,mod:"normal"}]:"markdown"===i?this.splitMarkdownBlocks(t):"html"===i?this.splitHtmlBlocks(t):void 0}overMaxWidth(t){const e=this.ctx.measureText(t).width;return this.lines.curWidth()+e>this.parent.fontOptions.maxWdt}getLongestFit(t){let e="",i=0;for(;i<t.length;){const o=e+(""===e?"":" ")+t[i];if(this.overMaxWidth(o))break;e=o,i++}return i}getLongestFitWord(t){let e=0;for(;e<t.length&&!this.overMaxWidth(Nl(t).call(t,0,e));)e++;return e}splitStringIntoLines(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"normal",i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.parent.getFormattingValues(this.ctx,this.selected,this.hover,e);let o=(t=(t=t.replace(/^( +)/g,"$1\r")).replace(/([^\r][^ ]*)( +)/g,"$1\r$2\r")).split("\r");for(;o.length>0;){let t=this.getLongestFit(o);if(0===t){const t=o[0],i=this.getLongestFitWord(t);this.lines.newLine(Nl(t).call(t,0,i),e),o[0]=Nl(t).call(t,i)}else{let s=t;" "===o[t-1]?t--:" "===o[s]&&s++;const n=Nl(o).call(o,0,t).join("");t==o.length&&i?this.lines.append(n,e):this.lines.newLine(n,e),o=Nl(o).call(o,s)}}}}const R_=["bold","ital","boldital","mono"];class j_{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.body=t,this.pointToSelf=!1,this.baseSize=void 0,this.fontOptions={},this.setOptions(e),this.size={top:0,left:0,width:0,height:0,yLine:0},this.isEdgeLabel=i}setOptions(t){if(this.elementOptions=t,this.initFontOptions(t.font),E_(t.label)?this.labelDirty=!0:t.label=void 0,void 0!==t.font&&null!==t.font)if("string"==typeof t.font)this.baseSize=this.fontOptions.size;else if("object"==typeof t.font){const e=t.font.size;void 0!==e&&(this.baseSize=e)}}initFontOptions(t){Om(R_,(t=>{this.fontOptions[t]={}})),j_.parseFontString(this.fontOptions,t)?this.fontOptions.vadjust=0:Om(t,((t,e)=>{null!=t&&"object"!=typeof t&&(this.fontOptions[e]=t)}))}static parseFontString(t,e){if(!e||"string"!=typeof e)return!1;const i=e.split(" ");return t.size=+i[0].replace("px",""),t.face=i[1],t.color=i[2],!0}constrain(t){const e={constrainWidth:!1,maxWdt:-1,minWdt:-1,constrainHeight:!1,minHgt:-1,valign:"middle"},i=Am(t,"widthConstraint");if("number"==typeof i)e.maxWdt=Number(i),e.minWdt=Number(i);else if("object"==typeof i){const i=Am(t,["widthConstraint","maximum"]);"number"==typeof i&&(e.maxWdt=Number(i));const o=Am(t,["widthConstraint","minimum"]);"number"==typeof o&&(e.minWdt=Number(o))}const o=Am(t,"heightConstraint");if("number"==typeof o)e.minHgt=Number(o);else if("object"==typeof o){const i=Am(t,["heightConstraint","minimum"]);"number"==typeof i&&(e.minHgt=Number(i));const o=Am(t,["heightConstraint","valign"]);"string"==typeof o&&("top"!==o&&"bottom"!==o||(e.valign=o))}return e}update(t,e){this.setOptions(t,!0),this.propagateFonts(e),_m(this.fontOptions,this.constrain(e)),this.fontOptions.chooser=__("label",e)}adjustSizes(t){const e=t?t.right+t.left:0;this.fontOptions.constrainWidth&&(this.fontOptions.maxWdt-=e,this.fontOptions.minWdt-=e);const i=t?t.top+t.bottom:0;this.fontOptions.constrainHeight&&(this.fontOptions.minHgt-=i)}addFontOptionsToPile(t,e){for(let i=0;i<e.length;++i)this.addFontToPile(t,e[i])}addFontToPile(t,e){if(void 0===e)return;if(void 0===e.font||null===e.font)return;const i=e.font;t.push(i)}getBasicOptions(t){const e={};for(let i=0;i<t.length;++i){let o=t[i];const s={};j_.parseFontString(s,o)&&(o=s),Om(o,((t,i)=>{void 0!==t&&(Object.prototype.hasOwnProperty.call(e,i)||(-1!==Mp(R_).call(R_,i)?e[i]={}:e[i]=t))}))}return e}getFontOption(t,e,i){let o;for(let s=0;s<t.length;++s){const n=t[s];if(Object.prototype.hasOwnProperty.call(n,e)){if(o=n[e],null==o)continue;const t={};if(j_.parseFontString(t,o)&&(o=t),Object.prototype.hasOwnProperty.call(o,i))return o[i]}}if(Object.prototype.hasOwnProperty.call(this.fontOptions,i))return this.fontOptions[i];throw new Error("Did not find value for multi-font for property: '"+i+"'")}getFontOptions(t,e){const i={},o=["color","size","face","mod","vadjust"];for(let s=0;s<o.length;++s){const n=o[s];i[n]=this.getFontOption(t,e,n)}return i}propagateFonts(t){const e=[];this.addFontOptionsToPile(e,t),this.fontOptions=this.getBasicOptions(e);for(let t=0;t<R_.length;++t){const i=R_[t],o=this.fontOptions[i];Om(this.getFontOptions(e,i),((t,e)=>{o[e]=t})),o.size=Number(o.size),o.vadjust=Number(o.vadjust)}}draw(t,e,i,o,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";if(void 0===this.elementOptions.label)return;let r=this.fontOptions.size*this.body.view.scale;this.elementOptions.label&&r<this.elementOptions.scaling.label.drawThreshold-1||(r>=this.elementOptions.scaling.label.maxVisible&&(r=Number(this.elementOptions.scaling.label.maxVisible)/this.body.view.scale),this.calculateLabelSize(t,o,s,e,i,n),this._drawBackground(t),this._drawText(t,e,this.size.yLine,n,r))}_drawBackground(t){if(void 0!==this.fontOptions.background&&"none"!==this.fontOptions.background){t.fillStyle=this.fontOptions.background;const e=this.getSize();t.fillRect(e.left,e.top,e.width,e.height)}}_drawText(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"middle",s=arguments.length>4?arguments[4]:void 0;[e,i]=this._setAlignment(t,e,i,o),t.textAlign="left",e-=this.size.width/2,this.fontOptions.valign&&this.size.height>this.size.labelHeight&&("top"===this.fontOptions.valign&&(i-=(this.size.height-this.size.labelHeight)/2),"bottom"===this.fontOptions.valign&&(i+=(this.size.height-this.size.labelHeight)/2));for(let o=0;o<this.lineCount;o++){const n=this.lines[o];if(n&&n.blocks){let o=0;this.isEdgeLabel||"center"===this.fontOptions.align?o+=(this.size.width-n.width)/2:"right"===this.fontOptions.align&&(o+=this.size.width-n.width);for(let r=0;r<n.blocks.length;r++){const a=n.blocks[r];t.font=a.font;const[h,d]=this._getColor(a.color,s,a.strokeColor);a.strokeWidth>0&&(t.lineWidth=a.strokeWidth,t.strokeStyle=d,t.lineJoin="round"),t.fillStyle=h,a.strokeWidth>0&&t.strokeText(a.text,e+o,i+a.vadjust),t.fillText(a.text,e+o,i+a.vadjust),o+=a.width}i+=n.height}}}_setAlignment(t,e,i,o){if(this.isEdgeLabel&&"horizontal"!==this.fontOptions.align&&!1===this.pointToSelf){e=0,i=0;const o=2;"top"===this.fontOptions.align?(t.textBaseline="alphabetic",i-=2*o):"bottom"===this.fontOptions.align?(t.textBaseline="hanging",i+=2*o):t.textBaseline="middle"}else t.textBaseline=o;return[e,i]}_getColor(t,e,i){let o=t||"#000000",s=i||"#ffffff";if(e<=this.elementOptions.scaling.label.drawThreshold){const t=Math.max(0,Math.min(1,1-(this.elementOptions.scaling.label.drawThreshold-e)));o=km(o,t),s=km(s,t)}return[o,s]}getTextSize(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this._processLabel(t,e,i),{width:this.size.width,height:this.size.height,lineCount:this.lineCount}}getSize(){let t=this.size.left,e=this.size.top-1;if(this.isEdgeLabel){const i=.5*-this.size.width;switch(this.fontOptions.align){case"middle":t=i,e=.5*-this.size.height;break;case"top":t=i,e=-(this.size.height+2);break;case"bottom":t=i,e=2}}return{left:t,top:e,width:this.size.width,height:this.size.height}}calculateLabelSize(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"middle";this._processLabel(t,e,i),this.size.left=o-.5*this.size.width,this.size.top=s-.5*this.size.height,this.size.yLine=s+.5*(1-this.lineCount)*this.fontOptions.size,"hanging"===n&&(this.size.top+=.5*this.fontOptions.size,this.size.top+=4,this.size.yLine+=4)}getFormattingValues(t,e,i,o){const s=function(t,e,i){return"normal"===e?"mod"===i?"":t[i]:void 0!==t[e][i]?t[e][i]:t[i]},n={color:s(this.fontOptions,o,"color"),size:s(this.fontOptions,o,"size"),face:s(this.fontOptions,o,"face"),mod:s(this.fontOptions,o,"mod"),vadjust:s(this.fontOptions,o,"vadjust"),strokeWidth:this.fontOptions.strokeWidth,strokeColor:this.fontOptions.strokeColor};(e||i)&&("normal"===o&&!0===this.fontOptions.chooser&&this.elementOptions.labelHighlightBold?n.mod="bold":"function"==typeof this.fontOptions.chooser&&this.fontOptions.chooser(n,this.elementOptions.id,e,i));let r="";return void 0!==n.mod&&""!==n.mod&&(r+=n.mod+" "),r+=n.size+"px "+n.face,t.font=r.replace(/"/g,""),n.font=t.font,n.height=n.size,n}differentState(t,e){return t!==this.selectedState||e!==this.hoverState}_processLabelText(t,e,i,o){return new A_(t,this,e,i).process(o)}_processLabel(t,e,i){if(!1===this.labelDirty&&!this.differentState(e,i))return;const o=this._processLabelText(t,e,i,this.elementOptions.label);this.fontOptions.minWdt>0&&o.width<this.fontOptions.minWdt&&(o.width=this.fontOptions.minWdt),this.size.labelHeight=o.height,this.fontOptions.minHgt>0&&o.height<this.fontOptions.minHgt&&(o.height=this.fontOptions.minHgt),this.lines=o.lines,this.lineCount=o.lines.length,this.size.width=o.width,this.size.height=o.height,this.selectedState=e,this.hoverState=i,this.labelDirty=!1}visible(){if(0===this.size.width||0===this.size.height||void 0===this.elementOptions.label)return!1;return!(this.fontOptions.size*this.body.view.scale<this.elementOptions.scaling.label.drawThreshold-1)}}class L_{constructor(t,e,i){this.body=e,this.labelModule=i,this.setOptions(t),this.top=void 0,this.left=void 0,this.height=void 0,this.width=void 0,this.radius=void 0,this.margin=void 0,this.refreshNeeded=!0,this.boundingBox={top:0,left:0,right:0,bottom:0}}setOptions(t){this.options=t}_setMargins(t){this.margin={},this.options.margin&&("object"==typeof this.options.margin?(this.margin.top=this.options.margin.top,this.margin.right=this.options.margin.right,this.margin.bottom=this.options.margin.bottom,this.margin.left=this.options.margin.left):(this.margin.top=this.options.margin,this.margin.right=this.options.margin,this.margin.bottom=this.options.margin,this.margin.left=this.options.margin)),t.adjustSizes(this.margin)}_distanceToBorder(t,e){const i=this.options.borderWidth;return t&&this.resize(t),Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}enableShadow(t,e){e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}enableBorderDashes(t,e){if(!1!==e.borderDashes)if(void 0!==t.setLineDash){let i=e.borderDashes;!0===i&&(i=[5,15]),t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1}disableBorderDashes(t,e){!1!==e.borderDashes&&(void 0!==t.setLineDash?t.setLineDash([0]):(console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1))}needsRefresh(t,e){return!0===this.refreshNeeded?(this.refreshNeeded=!1,!0):void 0===this.width||this.labelModule.differentState(t,e)}initContextForDraw(t,e){const i=e.borderWidth/this.body.view.scale;t.lineWidth=Math.min(this.width,i),t.strokeStyle=e.borderColor,t.fillStyle=e.color}performStroke(t,e){const i=e.borderWidth/this.body.view.scale;t.save(),i>0&&(this.enableBorderDashes(t,e),t.stroke(),this.disableBorderDashes(t,e)),t.restore()}performFill(t,e){t.save(),t.fillStyle=e.color,this.enableShadow(t,e),lg(t).call(t),this.disableShadow(t,e),t.restore(),this.performStroke(t,e)}_addBoundingBoxMargin(t){this.boundingBox.left-=t,this.boundingBox.top-=t,this.boundingBox.bottom+=t,this.boundingBox.right+=t}_updateBoundingBox(t,e,i,o,s){void 0!==i&&this.resize(i,o,s),this.left=t-this.width/2,this.top=e-this.height/2,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}updateBoundingBox(t,e,i,o,s){this._updateBoundingBox(t,e,i,o,s)}getDimensionsFromLabel(t,e,i){this.textSize=this.labelModule.getTextSize(t,e,i);let o=this.textSize.width,s=this.textSize.height;return 0===o&&(o=14,s=14),{width:o,height:s}}}let H_=class extends L_{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.width=o.width+this.margin.right+this.margin.left,this.height=o.height+this.margin.top+this.margin.bottom,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,n),Vo(t,this.left,this.top,this.width,this.height,n.borderRadius),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s)}updateBoundingBox(t,e,i,o,s){this._updateBoundingBox(t,e,i,o,s);const n=this.options.shapeProperties.borderRadius;this._addBoundingBoxMargin(n)}distanceToBorder(t,e){t&&this.resize(t);const i=this.options.borderWidth;return Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}};class W_ extends L_{constructor(t,e,i){super(t,e,i),this.labelOffset=0,this.selected=!1}setOptions(t,e,i){this.options=t,void 0===e&&void 0===i||this.setImages(e,i)}setImages(t,e){e&&this.selected?(this.imageObj=e,this.imageObjAlt=t):(this.imageObj=t,this.imageObjAlt=e)}switchImages(t){const e=t&&!this.selected||!t&&this.selected;if(this.selected=t,void 0!==this.imageObjAlt&&e){const t=this.imageObj;this.imageObj=this.imageObjAlt,this.imageObjAlt=t}}_getImagePadding(){const t={top:0,right:0,bottom:0,left:0};if(this.options.imagePadding){const e=this.options.imagePadding;"object"==typeof e?(t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left):(t.top=e,t.right=e,t.bottom=e,t.left=e)}return t}_resizeImage(){let t,e;if(!1===this.options.shapeProperties.useImageSize){let i=1,o=1;this.imageObj.width&&this.imageObj.height&&(this.imageObj.width>this.imageObj.height?i=this.imageObj.width/this.imageObj.height:o=this.imageObj.height/this.imageObj.width),t=2*this.options.size*i,e=2*this.options.size*o}else{const i=this._getImagePadding();t=this.imageObj.width+i.left+i.right,e=this.imageObj.height+i.top+i.bottom}this.width=t,this.height=e,this.radius=.5*this.width}_drawRawCircle(t,e,i,o){this.initContextForDraw(t,o),Wo(t,e,i,o.size),this.performFill(t,o)}_drawImageAtPosition(t,e){if(0!=this.imageObj.width){t.globalAlpha=void 0!==e.opacity?e.opacity:1,this.enableShadow(t,e);let i=1;!0===this.options.shapeProperties.interpolation&&(i=this.imageObj.width/this.width/this.body.view.scale);const o=this._getImagePadding(),s=this.left+o.left,n=this.top+o.top,r=this.width-o.left-o.right,a=this.height-o.top-o.bottom;this.imageObj.drawImageAtPosition(t,i,s,n,r,a),this.disableShadow(t,e)}}_drawImageLabel(t,e,i,o,s){let n=0;if(void 0!==this.height){n=.5*this.height;const e=this.labelModule.getTextSize(t,o,s);e.lineCount>=1&&(n+=e.height/2)}const r=i+n;this.options.label&&(this.labelOffset=n),this.labelModule.draw(t,e,r,o,s,"hanging")}}let V_=class extends W_{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i),s=Math.max(o.width+this.margin.right+this.margin.left,o.height+this.margin.top+this.margin.bottom);this.options.size=s/2,this.width=s,this.height=s,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this._drawRawCircle(t,e,i,n),this.updateBoundingBox(e,i),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,i,o,s)}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size}distanceToBorder(t){return t&&this.resize(t),.5*this.width}};class q_ extends W_{constructor(t,e,i,o,s){super(t,e,i),this.setImages(o,s)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,this.height=t,void(this.radius=.5*this.width)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,s,n){this.switchImages(o),this.resize();let r=e,a=i;"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),this._drawRawCircle(t,r,a,n),t.save(),t.clip(),this._drawImageAtPosition(t,n),t.restore(),this._drawImageLabel(t,r,a,o,s),this.updateBoundingBox(e,i)}updateBoundingBox(t,e){"top-left"===this.options.shapeProperties.coordinateOrigin?(this.boundingBox.top=e,this.boundingBox.left=t,this.boundingBox.right=t+2*this.options.size,this.boundingBox.bottom=e+2*this.options.size):(this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size),this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset)}distanceToBorder(t){return t&&this.resize(t),.5*this.width}}class U_ extends L_{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{size:this.options.size};if(this.needsRefresh(e,i)){var s,n;this.labelModule.getTextSize(t,e,i);const r=2*o.size;this.width=null!==(s=this.customSizeWidth)&&void 0!==s?s:r,this.height=null!==(n=this.customSizeHeight)&&void 0!==n?n:r,this.radius=.5*this.width}}_drawShape(t,e,i,o,s,n,r,a){var h;return this.resize(t,n,r,a),this.left=o-this.width/2,this.top=s-this.height/2,this.initContextForDraw(t,a),(h=e,Object.prototype.hasOwnProperty.call(Xo,h)?Xo[h]:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),o=1;o<e;o++)i[o-1]=arguments[o];CanvasRenderingContext2D.prototype[h].call(t,i)})(t,o,s,a.size),this.performFill(t,a),void 0!==this.options.icon&&void 0!==this.options.icon.code&&(t.font=(n?"bold ":"")+this.height/2+"px "+(this.options.icon.face||"FontAwesome"),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",t.fillText(this.options.icon.code,o,s)),{drawExternalLabel:()=>{if(void 0!==this.options.label){this.labelModule.calculateLabelSize(t,n,r,o,s,"hanging");const e=s+.5*this.height+.5*this.labelModule.size.height;this.labelModule.draw(t,o,e,n,r,"hanging")}this.updateBoundingBox(o,s)}}}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height))}}function Y_(t,e){var i=Jl(t);if($w){var o=$w(t);e&&(o=Ru(o).call(o,(function(e){return a_(t,e).enumerable}))),i.push.apply(i,o)}return i}function X_(t){for(var e=1;e<arguments.length;e++){var i,o,s=null!=arguments[e]?arguments[e]:{};e%2?mc(i=Y_(Object(s),!0)).call(i,(function(e){Fw(t,e,s[e])})):u_?v_(t,u_(s)):mc(o=Y_(Object(s))).call(o,(function(e){w_(t,e,a_(s,e))}))}return t}class K_ extends U_{constructor(t,e,i,o){super(t,e,i,o),this.ctxRenderer=o}draw(t,e,i,o,s,n){this.resize(t,o,s,n),this.left=e-this.width/2,this.top=i-this.height/2,t.save();const r=this.ctxRenderer({ctx:t,id:this.options.id,x:e,y:i,state:{selected:o,hover:s},style:X_({},n),label:this.options.label});if(null!=r.drawNode&&r.drawNode(),t.restore(),r.drawExternalLabel){const e=r.drawExternalLabel;r.drawExternalLabel=()=>{t.save(),e(),t.restore()}}return r.nodeDimensions&&(this.customSizeWidth=r.nodeDimensions.width,this.customSizeHeight=r.nodeDimensions.height),r}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class G_ extends L_{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i).width+this.margin.right+this.margin.left;this.width=o,this.height=o,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,n),Uo(t,e-this.width/2,i-this.height/2,this.width,this.height),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let Z_=class extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"diamond",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class Q_ extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"circle",2,e,i,o,s,n)}distanceToBorder(t){return t&&this.resize(t),this.options.size}}class $_ extends L_{constructor(t,e,i){super(t,e,i)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.height=2*o.height,this.width=o.width+o.height,this.radius=.5*this.width}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-.5*this.width,this.top=i-.5*this.height,this.initContextForDraw(t,n),qo(t,this.left,this.top,this.width,this.height),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,e,i,o,s)}distanceToBorder(t,e){t&&this.resize(t);const i=.5*this.width,o=.5*this.height,s=Math.sin(e)*i,n=Math.cos(e)*o;return i*o/Math.sqrt(s*s+n*n)}}class J_ extends L_{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.iconSize={width:Number(this.options.icon.size),height:Number(this.options.icon.size)},this.width=this.iconSize.width+this.margin.right+this.margin.left,this.height=this.iconSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,s,n){return this.resize(t,o,s),this.options.icon.size=this.options.icon.size||50,this.left=e-this.width/2,this.top=i-this.height/2,this._icon(t,e,i,o,s,n),{drawExternalLabel:()=>{if(void 0!==this.options.label){const e=5;this.labelModule.draw(t,this.left+this.iconSize.width/2+this.margin.left,i+this.height/2+e,o)}this.updateBoundingBox(e,i)}}}updateBoundingBox(t,e){if(this.boundingBox.top=e-.5*this.options.icon.size,this.boundingBox.left=t-.5*this.options.icon.size,this.boundingBox.right=t+.5*this.options.icon.size,this.boundingBox.bottom=e+.5*this.options.icon.size,void 0!==this.options.label&&this.labelModule.size.width>0){const t=5;this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+t)}}_icon(t,e,i,o,s,n){const r=Number(this.options.icon.size);void 0!==this.options.icon.code?(t.font=[null!=this.options.icon.weight?this.options.icon.weight:o?"bold":"",(null!=this.options.icon.weight&&o?5:0)+r+"px",this.options.icon.face].join(" "),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",this.enableShadow(t,n),t.fillText(this.options.icon.code,e,i),this.disableShadow(t,n)):console.error("When using the icon shape, you need to define the code in the icon options object. This can be done per node or globally.")}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let tx=class extends W_{constructor(t,e,i,o,s){super(t,e,i),this.setImages(o,s)}resize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selected,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.hover;if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,void(this.height=t)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,s,n){t.save(),this.switchImages(o),this.resize();let r=e,a=i;if("top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),!0===this.options.shapeProperties.useBorderWithImage){const e=this.options.borderWidth,i=this.options.borderWidthSelected||2*this.options.borderWidth,r=(o?i:e)/this.body.view.scale;t.lineWidth=Math.min(this.width,r),t.beginPath();let a=o?this.options.color.highlight.border:s?this.options.color.hover.border:this.options.color.border,h=o?this.options.color.highlight.background:s?this.options.color.hover.background:this.options.color.background;void 0!==n.opacity&&(a=km(a,n.opacity),h=km(h,n.opacity)),t.strokeStyle=a,t.fillStyle=h,t.rect(this.left-.5*t.lineWidth,this.top-.5*t.lineWidth,this.width+t.lineWidth,this.height+t.lineWidth),lg(t).call(t),this.performStroke(t,n),t.closePath()}this._drawImageAtPosition(t,n),this._drawImageLabel(t,r,a,o,s),this.updateBoundingBox(e,i),t.restore()}updateBoundingBox(t,e){this.resize(),"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=t,this.top=e):(this.left=t-this.width/2,this.top=e-this.height/2),this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset))}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class ex extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"square",2,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class ix extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"hexagon",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class ox extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"star",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class sx extends L_{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.textSize=this.labelModule.getTextSize(t,e,i),this.width=this.textSize.width+this.margin.right+this.margin.left,this.height=this.textSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.enableShadow(t,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s),this.disableShadow(t,n),this.updateBoundingBox(e,i,t,o,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let nx=class extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"triangle",3,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class rx extends U_{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"triangleDown",3,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}function ax(t,e){var i=Jl(t);if($w){var o=$w(t);e&&(o=Ru(o).call(o,(function(e){return a_(t,e).enumerable}))),i.push.apply(i,o)}return i}function hx(t){for(var e=1;e<arguments.length;e++){var i,o,s=null!=arguments[e]?arguments[e]:{};e%2?mc(i=ax(Object(s),!0)).call(i,(function(e){Fw(t,e,s[e])})):u_?v_(t,u_(s)):mc(o=ax(Object(s))).call(o,(function(e){w_(t,e,a_(s,e))}))}return t}class dx{constructor(t,e,i,o,s,n){this.options=Fm(s),this.globalOptions=s,this.defaultOptions=n,this.body=e,this.edges=[],this.id=void 0,this.imagelist=i,this.grouplist=o,this.x=void 0,this.y=void 0,this.baseSize=this.options.size,this.baseFontSize=this.options.font.size,this.predefinedPosition=!1,this.selected=!1,this.hover=!1,this.labelModule=new j_(this.body,this.options,!1),this.setOptions(t)}attachEdge(t){var e;-1===Mp(e=this.edges).call(e,t)&&this.edges.push(t)}detachEdge(t){var e;const i=Mp(e=this.edges).call(e,t);var o;-1!=i&&Jc(o=this.edges).call(o,i,1)}setOptions(t){const e=this.options.shape;if(!t)return;if(void 0!==t.color&&(this._localColor=t.color),void 0!==t.id&&(this.id=t.id),void 0===this.id)throw new Error("Node must have an id");dx.checkMass(t,this.id),void 0!==t.x&&(null===t.x?(this.x=void 0,this.predefinedPosition=!1):(this.x=bp(t.x),this.predefinedPosition=!0)),void 0!==t.y&&(null===t.y?(this.y=void 0,this.predefinedPosition=!1):(this.y=bp(t.y),this.predefinedPosition=!0)),void 0!==t.size&&(this.baseSize=t.size),void 0!==t.value&&(t.value=Uw(t.value)),dx.parseOptions(this.options,t,!0,this.globalOptions,this.grouplist);const i=[t,this.options,this.defaultOptions];return this.chooser=__("node",i),this._load_images(),this.updateLabelModule(t),void 0!==t.opacity&&dx.checkOpacity(t.opacity)&&(this.options.opacity=t.opacity),this.updateShape(e),void 0!==t.hidden||void 0!==t.physics}_load_images(){if(("circularImage"===this.options.shape||"image"===this.options.shape)&&void 0===this.options.image)throw new Error("Option image must be defined for node type '"+this.options.shape+"'");if(void 0!==this.options.image){if(void 0===this.imagelist)throw new Error("Internal Error: No images provided");if("string"==typeof this.options.image)this.imageObj=this.imagelist.load(this.options.image,this.options.brokenImage,this.id);else{if(void 0===this.options.image.unselected)throw new Error("No unselected image provided");this.imageObj=this.imagelist.load(this.options.image.unselected,this.options.brokenImage,this.id),void 0!==this.options.image.selected?this.imageObjAlt=this.imagelist.load(this.options.image.selected,this.options.brokenImage,this.id):this.imageObjAlt=void 0}}}static checkOpacity(t){return 0<=t&&t<=1}static checkCoordinateOrigin(t){return void 0===t||"center"===t||"top-left"===t}static updateGroupOptions(t,e,i){var o;if(void 0===i)return;const s=t.group;if(void 0!==e&&void 0!==e.group&&s!==e.group)throw new Error("updateGroupOptions: group values in options don't match.");if(!("number"==typeof s||"string"==typeof s&&""!=s))return;const n=i.get(s);void 0!==n.opacity&&void 0===e.opacity&&(dx.checkOpacity(n.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+n.opacity),n.opacity=void 0));const r=Ru(o=Qw(e)).call(o,(t=>null!=e[t]));r.push("font"),wm(r,t,n),t.color=Tm(t.color)}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4?arguments[4]:void 0;if(wm(["color","fixed","shadow"],t,e,i),dx.checkMass(e),void 0!==t.opacity&&(dx.checkOpacity(t.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity),t.opacity=void 0)),void 0!==e.opacity&&(dx.checkOpacity(e.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+e.opacity),e.opacity=void 0)),e.shapeProperties&&!dx.checkCoordinateOrigin(e.shapeProperties.coordinateOrigin)&&console.error("Invalid option for node coordinateOrigin, found: "+e.shapeProperties.coordinateOrigin),zm(t,e,"shadow",o),void 0!==e.color&&null!==e.color){const i=Tm(e.color);bm(t.color,i)}else!0===i&&null===e.color&&(t.color=Fm(o.color));void 0!==e.fixed&&null!==e.fixed&&("boolean"==typeof e.fixed?(t.fixed.x=e.fixed,t.fixed.y=e.fixed):(void 0!==e.fixed.x&&"boolean"==typeof e.fixed.x&&(t.fixed.x=e.fixed.x),void 0!==e.fixed.y&&"boolean"==typeof e.fixed.y&&(t.fixed.y=e.fixed.y))),!0===i&&null===e.font&&(t.font=Fm(o.font)),dx.updateGroupOptions(t,e,s),void 0!==e.scaling&&zm(t.scaling,e.scaling,"label",o.scaling)}getFormattingValues(){const t={color:this.options.color.background,opacity:this.options.opacity,borderWidth:this.options.borderWidth,borderColor:this.options.color.border,size:this.options.size,borderDashes:this.options.shapeProperties.borderDashes,borderRadius:this.options.shapeProperties.borderRadius,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y};if(this.selected||this.hover?!0===this.chooser?this.selected?(null!=this.options.borderWidthSelected?t.borderWidth=this.options.borderWidthSelected:t.borderWidth*=2,t.color=this.options.color.highlight.background,t.borderColor=this.options.color.highlight.border,t.shadow=this.options.shadow.enabled):this.hover&&(t.color=this.options.color.hover.background,t.borderColor=this.options.color.hover.border,t.shadow=this.options.shadow.enabled):"function"==typeof this.chooser&&(this.chooser(t,this.options.id,this.selected,this.hover),!1===t.shadow&&(t.shadowColor===this.options.shadow.color&&t.shadowSize===this.options.shadow.size&&t.shadowX===this.options.shadow.x&&t.shadowY===this.options.shadow.y||(t.shadow=!0))):t.shadow=this.options.shadow.enabled,void 0!==this.options.opacity){const e=this.options.opacity;t.borderColor=km(t.borderColor,e),t.color=km(t.color,e),t.shadowColor=km(t.shadowColor,e)}return t}updateLabelModule(t){void 0!==this.options.label&&null!==this.options.label||(this.options.label=""),dx.updateGroupOptions(this.options,hx(hx({},t),{},{color:t&&t.color||this._localColor||void 0}),this.grouplist);const e=this.grouplist.get(this.options.group,!1),i=[t,this.options,e,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,i),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateShape(t){if(t===this.options.shape&&this.shape)this.shape.setOptions(this.options,this.imageObj,this.imageObjAlt);else switch(this.options.shape){case"box":this.shape=new H_(this.options,this.body,this.labelModule);break;case"circle":this.shape=new V_(this.options,this.body,this.labelModule);break;case"circularImage":this.shape=new q_(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"custom":this.shape=new K_(this.options,this.body,this.labelModule,this.options.ctxRenderer);break;case"database":this.shape=new G_(this.options,this.body,this.labelModule);break;case"diamond":this.shape=new Z_(this.options,this.body,this.labelModule);break;case"dot":this.shape=new Q_(this.options,this.body,this.labelModule);break;case"ellipse":default:this.shape=new $_(this.options,this.body,this.labelModule);break;case"icon":this.shape=new J_(this.options,this.body,this.labelModule);break;case"image":this.shape=new tx(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"square":this.shape=new ex(this.options,this.body,this.labelModule);break;case"hexagon":this.shape=new ix(this.options,this.body,this.labelModule);break;case"star":this.shape=new ox(this.options,this.body,this.labelModule);break;case"text":this.shape=new sx(this.options,this.body,this.labelModule);break;case"triangle":this.shape=new nx(this.options,this.body,this.labelModule);break;case"triangleDown":this.shape=new rx(this.options,this.body,this.labelModule)}this.needsRefresh()}select(){this.selected=!0,this.needsRefresh()}unselect(){this.selected=!1,this.needsRefresh()}needsRefresh(){this.shape.refreshNeeded=!0}getTitle(){return this.options.title}distanceToBorder(t,e){return this.shape.distanceToBorder(t,e)}isFixed(){return this.options.fixed.x&&this.options.fixed.y}isSelected(){return this.selected}getValue(){return this.options.value}getLabelSize(){return this.labelModule.size()}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),s=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.size=this.options.scaling.min+o*s}else this.options.size=this.baseSize,this.options.font.size=this.baseFontSize;this.updateLabelModule()}draw(t){const e=this.getFormattingValues();return this.shape.draw(t,this.x,this.y,this.selected,this.hover,e)||{}}updateBoundingBox(t){this.shape.updateBoundingBox(this.x,this.y,t)}resize(t){const e=this.getFormattingValues();this.shape.resize(t,this.selected,this.hover,e)}getItemsOnPoint(t){const e=[];return this.labelModule.visible()&&x_(this.labelModule.getSize(),t)&&e.push({nodeId:this.id,labelId:0}),x_(this.shape.boundingBox,t)&&e.push({nodeId:this.id}),e}isOverlappingWith(t){return this.shape.left<t.right&&this.shape.left+this.shape.width>t.left&&this.shape.top<t.bottom&&this.shape.top+this.shape.height>t.top}isBoundingBoxOverlappingWith(t){return this.shape.boundingBox.left<t.right&&this.shape.boundingBox.right>t.left&&this.shape.boundingBox.top<t.bottom&&this.shape.boundingBox.bottom>t.top}static checkMass(t,e){if(void 0!==t.mass&&t.mass<=0){let i="";void 0!==e&&(i=" in node id: "+e),console.error("%cNegative or zero mass disallowed"+i+", setting mass to 1.",Km),t.mass=1}}}class lx{constructor(t,e,i,o){var s;if(this.body=t,this.images=e,this.groups=i,this.layoutEngine=o,this.body.functions.createNode=Ho(s=this.create).call(s,this),this.nodesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items,e.data,e.oldData)},remove:(t,e)=>{this.remove(e.items)}},this.defaultOptions={borderWidth:1,borderWidthSelected:void 0,brokenImage:void 0,color:{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},opacity:void 0,fixed:{x:!1,y:!1},font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:0,strokeColor:"#ffffff",align:"center",vadjust:0,multi:!1,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"monospace",vadjust:2}},group:void 0,hidden:!1,icon:{face:"FontAwesome",code:void 0,size:50,color:"#2B7CE9"},image:void 0,imagePadding:{top:0,right:0,bottom:0,left:0},label:void 0,labelHighlightBold:!0,level:void 0,margin:{top:5,right:5,bottom:5,left:5},mass:1,physics:!0,scaling:{min:10,max:30,label:{enabled:!1,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},shape:"ellipse",shapeProperties:{borderDashes:!1,borderRadius:6,interpolation:!0,useImageSize:!1,useBorderWithImage:!1,coordinateOrigin:"center"},size:25,title:void 0,value:void 0,x:void 0,y:void 0},this.defaultOptions.mass<=0)throw"Internal error: mass in defaultOptions of NodesHandler may not be zero or negative";this.options=Fm(this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e;this.body.emitter.on("refreshNodes",Ho(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",Ho(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",(()=>{Om(this.nodesListeners,((t,e)=>{this.body.data.nodes&&this.body.data.nodes.off(e,t)})),delete this.body.functions.createNode,delete this.nodesListeners.add,delete this.nodesListeners.update,delete this.nodesListeners.remove,delete this.nodesListeners}))}setOptions(t){if(void 0!==t){if(dx.parseOptions(this.options,t),void 0!==t.opacity&&(jv(t.opacity)||!Wv(t.opacity)||t.opacity<0||t.opacity>1?console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity):this.options.opacity=t.opacity),void 0!==t.shape)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].updateShape();if(void 0!==t.font||void 0!==t.widthConstraint||void 0!==t.heightConstraint)for(const t of Jl(this.body.nodes))this.body.nodes[t].updateLabelModule(),this.body.nodes[t].needsRefresh();if(void 0!==t.size)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].needsRefresh();void 0===t.hidden&&void 0===t.physics||this.body.emitter.emit("_dataChanged")}}setData(i){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const s=this.body.data.nodes;if(t("id",i))this.body.data.nodes=i;else if(Vl(i))this.body.data.nodes=new e,this.body.data.nodes.add(i);else{if(i)throw new TypeError("Array or DataSet expected");this.body.data.nodes=new e}if(s&&Om(this.nodesListeners,(function(t,e){s.off(e,t)})),this.body.nodes={},this.body.data.nodes){const t=this;Om(this.nodesListeners,(function(e,i){t.body.data.nodes.on(i,e)}));const e=this.body.data.nodes.getIds();this.add(e,!0)}!1===o&&this.body.emitter.emit("_dataChanged")}add(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const o=[];for(let i=0;i<t.length;i++){e=t[i];const s=this.body.data.nodes.get(e),n=this.create(s);o.push(n),this.body.nodes[e]=n}this.layoutEngine.positionInitially(o),!1===i&&this.body.emitter.emit("_dataChanged")}update(t,e,i){const o=this.body.nodes;let s=!1;for(let i=0;i<t.length;i++){const n=t[i];let r=o[n];const a=e[i];void 0!==r?r.setOptions(a)&&(s=!0):(s=!0,r=this.create(a),o[n]=r)}s||void 0===i||(s=Gv(e).call(e,(function(t,e){const o=i[e];return o&&o.level!==t.level}))),!0===s?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}remove(t){const e=this.body.nodes;for(let i=0;i<t.length;i++){delete e[t[i]]}this.body.emitter.emit("_dataChanged")}create(t){return new(arguments.length>1&&void 0!==arguments[1]?arguments[1]:dx)(t,this.body,this.images,this.groups,this.options,this.defaultOptions)}refresh(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Om(this.body.nodes,((e,i)=>{const o=this.body.data.nodes.get(i);void 0!==o&&(!0===t&&e.setOptions({x:null,y:null}),e.setOptions({fixed:!1}),e.setOptions(o))}))}getPositions(t){const e={};if(void 0!==t){if(!0===Vl(t)){for(let i=0;i<t.length;i++)if(void 0!==this.body.nodes[t[i]]){const o=this.body.nodes[t[i]];e[t[i]]={x:Math.round(o.x),y:Math.round(o.y)}}}else if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];e[t]={x:Math.round(i.x),y:Math.round(i.y)}}}else for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];e[this.body.nodeIndices[t]]={x:Math.round(i.x),y:Math.round(i.y)}}return e}getPosition(t){if(null==t)throw new TypeError("No id was specified for getPosition method.");if(null==this.body.nodes[t])throw new ReferenceError("NodeId provided for getPosition does not exist. Provided: ".concat(t));return{x:Math.round(this.body.nodes[t].x),y:Math.round(this.body.nodes[t].y)}}storePositions(){const t=[],e=this.body.data.nodes.getDataSet();for(const i of e.get()){const e=i.id,o=this.body.nodes[e],s=Math.round(o.x),n=Math.round(o.y);i.x===s&&i.y===n||t.push({id:e,x:s,y:n})}e.update(t)}getBoundingBox(t){if(void 0!==this.body.nodes[t])return this.body.nodes[t].shape.boundingBox}getConnectedNodes(t,e){const i=[];if(void 0!==this.body.nodes[t]){const o=this.body.nodes[t],s={};for(let t=0;t<o.edges.length;t++){const n=o.edges[t];"to"!==e&&n.toId==o.id?void 0===s[n.fromId]&&(i.push(n.fromId),s[n.fromId]=!0):"from"!==e&&n.fromId==o.id&&void 0===s[n.toId]&&(i.push(n.toId),s[n.toId]=!0)}}return i}getConnectedEdges(t){const e=[];if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];for(let t=0;t<i.edges.length;t++)e.push(i.edges[t].id)}else console.error("NodeId provided for getConnectedEdges does not exist. Provided: ",t);return e}moveNode(t,e,i){void 0!==this.body.nodes[t]?(this.body.nodes[t].x=Number(e),this.body.nodes[t].y=Number(i),Jp((()=>{this.body.emitter.emit("startSimulation")}),0)):console.error("Node id supplied to moveNode does not exist. Provided: ",t)}}var cx=Mi,ux=Math.hypot,px=Math.abs,gx=Math.sqrt;cx({target:"Math",stat:!0,arity:2,forced:!!ux&&ux(1/0,NaN)!==1/0},{hypot:function(t,e){for(var i,o,s=0,n=0,r=arguments.length,a=0;n<r;)a<(i=px(arguments[n++]))?(s=s*(o=a/i)*o+1,a=i):s+=i>0?(o=i/a)*o:i;return a===1/0?1/0:a*gx(s)}});var fx=o(it.Math.hypot);class mx{static transform(t,e){Vl(t)||(t=[t]);const i=e.point.x,o=e.point.y,s=e.angle,n=e.length;for(let e=0;e<t.length;++e){const r=t[e],a=r.x*Math.cos(s)-r.y*Math.sin(s),h=r.x*Math.sin(s)+r.y*Math.cos(s);r.x=i+n*a,r.y=o+n*h}}static drawPath(t,e){t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;++i)t.lineTo(e[i].x,e[i].y);t.closePath()}}let yx,bx=class extends mx{static draw(t,e){if(e.image){t.save(),t.translate(e.point.x,e.point.y),t.rotate(Math.PI/2+e.angle);const i=null!=e.imageWidth?e.imageWidth:e.image.width,o=null!=e.imageHeight?e.imageHeight:e.image.height;e.image.drawImageAtPosition(t,1,-i/2,0,i,o),t.restore()}return!1}};class vx extends mx{static draw(t,e){const i=[{x:0,y:0},{x:-1,y:.3},{x:-.9,y:0},{x:-1,y:-.3}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class wx{static draw(t,e){const i=[{x:-1,y:0},{x:0,y:.3},{x:-.4,y:0},{x:0,y:-.3}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class _x{static draw(t,e){const i={x:-.4,y:0};mx.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,s=e.angle-o/2,n=e.angle+o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,s,n,!1),t.stroke(),!0}}class xx{static draw(t,e){const i={x:-.3,y:0};mx.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,s=e.angle+o/2,n=e.angle+3*o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,s,n,!1),t.stroke(),!0}}class Ex{static draw(t,e){const i=[{x:.02,y:0},{x:-1,y:.3},{x:-1,y:-.3}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Ox{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-1,y:0}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Cx{static draw(t,e){const i={x:-.4,y:0};return mx.transform(i,e),Wo(t,i.x,i.y,.4*e.length),!0}}class kx{static draw(t,e){const i=[{x:0,y:.5},{x:0,y:-.5},{x:-.15,y:-.5},{x:-.15,y:.5}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Sx{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-.6,y:-.3},{x:-.6,y:.3}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Tx{static draw(t,e){const i=[{x:0,y:0},{x:-.5,y:-.3},{x:-1,y:0},{x:-.5,y:.3}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Mx{static draw(t,e){const i=[{x:-1,y:.3},{x:-.5,y:0},{x:-1,y:-.3},{x:0,y:0}];return mx.transform(i,e),mx.drawPath(t,i),!0}}class Dx{static draw(t,e){let i;switch(e.type&&(i=e.type.toLowerCase()),i){case"image":return bx.draw(t,e);case"circle":return Cx.draw(t,e);case"box":return Sx.draw(t,e);case"crow":return wx.draw(t,e);case"curve":return _x.draw(t,e);case"diamond":return Tx.draw(t,e);case"inv_curve":return xx.draw(t,e);case"triangle":return Ex.draw(t,e);case"inv_triangle":return Ox.draw(t,e);case"bar":return kx.draw(t,e);case"vee":return Mx.draw(t,e);default:return vx.draw(t,e)}}}function Px(t,e){var i=Jl(t);if($w){var o=$w(t);e&&(o=Ru(o).call(o,(function(e){return a_(t,e).enumerable}))),i.push.apply(i,o)}return i}function Ix(t){for(var e=1;e<arguments.length;e++){var i,o,s=null!=arguments[e]?arguments[e]:{};e%2?mc(i=Px(Object(s),!0)).call(i,(function(e){Fw(t,e,s[e])})):u_?v_(t,u_(s)):mc(o=Px(Object(s))).call(o,(function(e){w_(t,e,a_(s,e))}))}return t}class Bx{constructor(t,e,i){this._body=e,this._labelModule=i,this.color={},this.colorDirty=!0,this.hoverWidth=1.5,this.selectionWidth=2,this.setOptions(t),this.fromPoint=this.from,this.toPoint=this.to}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to]}cleanup(){return!1}setOptions(t){this.options=t,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.id=this.options.id}drawLine(t,e,i,o){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.getViaNode();t.strokeStyle=this.getColor(t,e),t.lineWidth=e.width,!1!==e.dashes?this._drawDashedLine(t,e,s):this._drawLine(t,e,s)}_drawLine(t,e,i,o,s){if(this.from!=this.to)this._line(t,e,i,o,s);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}}_drawDashedLine(t,e,i,o,s){t.lineCap="round";const n=Vl(e.dashes)?e.dashes:[5,5];if(void 0!==t.setLineDash){if(t.save(),t.setLineDash(n),t.lineDashOffset=0,this.from!=this.to)this._line(t,e,i);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}t.setLineDash([0]),t.lineDashOffset=0,t.restore()}else{if(this.from!=this.to)Yo(t,this.from.x,this.from.y,this.to.x,this.to.y,n);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}}findBorderPosition(t,e,i){return this.from!=this.to?this._findBorderPosition(t,e,i):this._findBorderPositionCircle(t,e,i)}findBorderPositions(t){if(this.from!=this.to)return{from:this._findBorderPosition(this.from,t),to:this._findBorderPosition(this.to,t)};{var e;const[i,o]=Nl(e=this._getCircleData(t)).call(e,0,2);return{from:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.25,high:.6,direction:-1}),to:this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:.6,high:.8,direction:1})}}}_getCircleData(t){const e=this.options.selfReference.size;void 0!==t&&void 0===this.from.shape.width&&this.from.shape.resize(t);const i=O_(t,this.options.selfReference.angle,e,this.from);return[i.x,i.y,e]}_pointOnCircle(t,e,i,o){const s=2*o*Math.PI;return{x:t+i*Math.cos(s),y:e-i*Math.sin(s)}}_findBorderPositionCircle(t,e,i){const o=i.x,s=i.y;let n=i.low,r=i.high;const a=i.direction,h=this.options.selfReference.size;let d,l=.5*(n+r),c=0;!0===this.options.arrowStrikethrough&&(-1===a?c=this.options.endPointOffset.from:1===a&&(c=this.options.endPointOffset.to));let u=0;do{l=.5*(n+r),d=this._pointOnCircle(o,s,h,l);const i=Math.atan2(t.y-d.y,t.x-d.x),p=t.distanceToBorder(e,i)+c-Math.sqrt(Math.pow(d.x-t.x,2)+Math.pow(d.y-t.y,2));if(Math.abs(p)<.05)break;p>0?a>0?n=l:r=l:a>0?r=l:n=l,++u}while(n<=r&&u<10);return Ix(Ix({},d),{},{t:l})}getLineWidth(t,e){return!0===t?Math.max(this.selectionWidth,.3/this._body.view.scale):!0===e?Math.max(this.hoverWidth,.3/this._body.view.scale):Math.max(this.options.width,.3/this._body.view.scale)}getColor(t,e){if(!1!==e.inheritsColor){if("both"===e.inheritsColor&&this.from.id!==this.to.id){const i=t.createLinearGradient(this.from.x,this.from.y,this.to.x,this.to.y);let o=this.from.options.color.highlight.border,s=this.to.options.color.highlight.border;return!1===this.from.selected&&!1===this.to.selected?(o=km(this.from.options.color.border,e.opacity),s=km(this.to.options.color.border,e.opacity)):!0===this.from.selected&&!1===this.to.selected?s=this.to.options.color.border:!1===this.from.selected&&!0===this.to.selected&&(o=this.from.options.color.border),i.addColorStop(0,o),i.addColorStop(1,s),i}return"to"===e.inheritsColor?km(this.to.options.color.border,e.opacity):km(this.from.options.color.border,e.opacity)}return km(e.color,e.opacity)}_circle(t,e,i,o,s){this.enableShadow(t,e);let n=0,r=2*Math.PI;if(!this.options.selfReference.renderBehindTheNode){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,a=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:-1}),h=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:1});n=Math.atan2(a.y-o,a.x-i),r=Math.atan2(h.y-o,h.x-i)}t.beginPath(),t.arc(i,o,s,n,r,!1),t.stroke(),this.disableShadow(t,e)}getDistanceToEdge(t,e,i,o,s,n){if(this.from!=this.to)return this._getDistanceToEdge(t,e,i,o,s,n);{const[t,e,i]=this._getCircleData(void 0),o=t-s,r=e-n;return Math.abs(Math.sqrt(o*o+r*r)-i)}}_getDistanceToLine(t,e,i,o,s,n){const r=i-t,a=o-e;let h=((s-t)*r+(n-e)*a)/(r*r+a*a);h>1?h=1:h<0&&(h=0);const d=t+h*r-s,l=e+h*a-n;return Math.sqrt(d*d+l*l)}getArrowData(t,e,i,o,s,n){let r,a,h,d,l,c,u;const p=n.width;"from"===e?(h=this.from,d=this.to,l=n.fromArrowScale<0,c=Math.abs(n.fromArrowScale),u=n.fromArrowType):"to"===e?(h=this.to,d=this.from,l=n.toArrowScale<0,c=Math.abs(n.toArrowScale),u=n.toArrowType):(h=this.to,d=this.from,l=n.middleArrowScale<0,c=Math.abs(n.middleArrowScale),u=n.middleArrowType);const g=15*c+3*p;if(h!=d){const o=g/fx(h.x-d.x,h.y-d.y);if("middle"!==e)if(!0===this.options.smooth.enabled){const s=this._findBorderPosition(h,t,{via:i}),n=this.getPoint(s.t+o*("from"===e?1:-1),i);r=Math.atan2(s.y-n.y,s.x-n.x),a=s}else r=Math.atan2(h.y-d.y,h.x-d.x),a=this._findBorderPosition(h,t);else{const t=(l?-o:o)/2,e=this.getPoint(.5+t,i),s=this.getPoint(.5-t,i);r=Math.atan2(e.y-s.y,e.x-s.x),a=this.getPoint(.5,i)}}else{const[i,o,s]=this._getCircleData(t);if("from"===e){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,n=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:-1});r=-2*n.t*Math.PI+1.5*Math.PI+.1*Math.PI,a=n}else if("to"===e){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,n=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:1});r=-2*n.t*Math.PI+1.5*Math.PI-1.1*Math.PI,a=n}else{const t=this.options.selfReference.angle/(2*Math.PI);a=this._pointOnCircle(i,o,s,t),r=-2*t*Math.PI+1.5*Math.PI+.1*Math.PI}}return{point:a,core:{x:a.x-.9*g*Math.cos(r),y:a.y-.9*g*Math.sin(r)},angle:r,length:g,type:u}}drawArrowHead(t,e,i,o,s){t.strokeStyle=this.getColor(t,e),t.fillStyle=t.strokeStyle,t.lineWidth=e.width;Dx.draw(t,s)&&(this.enableShadow(t,e),lg(t).call(t),this.disableShadow(t,e))}enableShadow(t,e){!0===e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){!0===e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}drawBackground(t,e){if(!1!==e.background){const i={strokeStyle:t.strokeStyle,lineWidth:t.lineWidth,dashes:t.dashes};t.strokeStyle=e.backgroundColor,t.lineWidth=e.backgroundSize,this.setStrokeDashed(t,e.backgroundDashes),t.stroke(),t.strokeStyle=i.strokeStyle,t.lineWidth=i.lineWidth,t.dashes=i.dashes,this.setStrokeDashed(t,e.dashes)}}setStrokeDashed(t,e){if(!1!==e)if(void 0!==t.setLineDash){const i=Vl(e)?e:[5,5];t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.");else void 0!==t.setLineDash?t.setLineDash([]):console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.")}}function Fx(t,e){var i=Jl(t);if($w){var o=$w(t);e&&(o=Ru(o).call(o,(function(e){return a_(t,e).enumerable}))),i.push.apply(i,o)}return i}function zx(t){for(var e=1;e<arguments.length;e++){var i,o,s=null!=arguments[e]?arguments[e]:{};e%2?mc(i=Fx(Object(s),!0)).call(i,(function(e){Fw(t,e,s[e])})):u_?v_(t,u_(s)):mc(o=Fx(Object(s))).call(o,(function(e){w_(t,e,a_(s,e))}))}return t}class Nx extends Bx{constructor(t,e,i){super(t,e,i)}_findBorderPositionBezier(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._getViaCoordinates();let o,s,n=!1,r=1,a=0,h=this.to,d=this.options.endPointOffset?this.options.endPointOffset.to:0;t.id===this.from.id&&(h=this.from,n=!0,d=this.options.endPointOffset?this.options.endPointOffset.from:0),!1===this.options.arrowStrikethrough&&(d=0);let l=0;do{s=.5*(a+r),o=this.getPoint(s,i);const t=Math.atan2(h.y-o.y,h.x-o.x),c=h.distanceToBorder(e,t)+d-Math.sqrt(Math.pow(o.x-h.x,2)+Math.pow(o.y-h.y,2));if(Math.abs(c)<.2)break;c<0?!1===n?a=s:r=s:!1===n?r=s:a=s,++l}while(a<=r&&l<10);return zx(zx({},o),{},{t:s})}_getDistanceToBezierEdge(t,e,i,o,s,n,r){let a,h,d,l,c,u=1e9,p=t,g=e;for(h=1;h<10;h++)d=.1*h,l=Math.pow(1-d,2)*t+2*d*(1-d)*r.x+Math.pow(d,2)*i,c=Math.pow(1-d,2)*e+2*d*(1-d)*r.y+Math.pow(d,2)*o,h>0&&(a=this._getDistanceToLine(p,g,l,c,s,n),u=a<u?a:u),p=l,g=c;return u}_bezierCurve(t,e,i,o){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),null!=i&&null!=i.x?null!=o&&null!=o.x?t.bezierCurveTo(i.x,i.y,o.x,o.y,this.toPoint.x,this.toPoint.y):t.quadraticCurveTo(i.x,i.y,this.toPoint.x,this.toPoint.y):t.lineTo(this.toPoint.x,this.toPoint.y),this.drawBackground(t,e),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){return this._getViaCoordinates()}}class Ax extends Nx{constructor(t,e,i){super(t,e,i),this.via=this.via,this._boundFunction=()=>{this.positionBezierNode()},this._body.emitter.on("_repositionBezierNodes",this._boundFunction)}setOptions(t){super.setOptions(t);let e=!1;this.options.physics!==t.physics&&(e=!0),this.options=t,this.id=this.options.id,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.setupSupportNode(),this.connect(),!0===e&&(this.via.setOptions({physics:this.options.physics}),this.positionBezierNode())}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],void 0===this.from||void 0===this.to||!1===this.options.physics||this.from.id===this.to.id?this.via.setOptions({physics:!1}):this.via.setOptions({physics:!0})}cleanup(){return this._body.emitter.off("_repositionBezierNodes",this._boundFunction),void 0!==this.via&&(delete this._body.nodes[this.via.id],this.via=void 0,!0)}setupSupportNode(){if(void 0===this.via){const t="edgeId:"+this.id,e=this._body.functions.createNode({id:t,shape:"circle",physics:!0,hidden:!0});this._body.nodes[t]=e,this.via=e,this.via.parentEdgeId=this.id,this.positionBezierNode()}}positionBezierNode(){void 0!==this.via&&void 0!==this.from&&void 0!==this.to?(this.via.x=.5*(this.from.x+this.to.x),this.via.y=.5*(this.from.y+this.to.y)):void 0!==this.via&&(this.via.x=0,this.via.y=0)}_line(t,e,i){this._bezierCurve(t,e,i)}_getViaCoordinates(){return this.via}getViaNode(){return this.via}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.via;if(this.from===this.to){const[e,i,o]=this._getCircleData(),s=2*Math.PI*(1-t);return{x:e+o*Math.sin(s),y:i+o-o*(1-Math.cos(s))}}return{x:Math.pow(1-t,2)*this.fromPoint.x+2*t*(1-t)*e.x+Math.pow(t,2)*this.toPoint.x,y:Math.pow(1-t,2)*this.fromPoint.y+2*t*(1-t)*e.y+Math.pow(t,2)*this.toPoint.y}}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e,this.via)}_getDistanceToEdge(t,e,i,o,s,n){return this._getDistanceToBezierEdge(t,e,i,o,s,n,this.via)}}class Rx extends Nx{constructor(t,e,i){super(t,e,i)}_line(t,e,i){this._bezierCurve(t,e,i)}getViaNode(){return this._getViaCoordinates()}_getViaCoordinates(){const t=this.options.smooth.roundness,e=this.options.smooth.type;let i=Math.abs(this.from.x-this.to.x),o=Math.abs(this.from.y-this.to.y);if("discrete"===e||"diagonalCross"===e){let s,n;s=n=i<=o?t*o:t*i,this.from.x>this.to.x&&(s=-s),this.from.y>=this.to.y&&(n=-n);let r=this.from.x+s,a=this.from.y+n;return"discrete"===e&&(i<=o?r=i<t*o?this.from.x:r:a=o<t*i?this.from.y:a),{x:r,y:a}}if("straightCross"===e){let e=(1-t)*i,s=(1-t)*o;return i<=o?(e=0,this.from.y<this.to.y&&(s=-s)):(this.from.x<this.to.x&&(e=-e),s=0),{x:this.to.x+e,y:this.to.y+s}}if("horizontal"===e){let e=(1-t)*i;return this.from.x<this.to.x&&(e=-e),{x:this.to.x+e,y:this.from.y}}if("vertical"===e){let e=(1-t)*o;return this.from.y<this.to.y&&(e=-e),{x:this.from.x,y:this.to.y+e}}if("curvedCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),s=Math.PI,n=(Math.atan2(o,i)+(.5*t+.5)*s)%(2*s);return{x:this.from.x+(.5*t+.5)*e*Math.sin(n),y:this.from.y+(.5*t+.5)*e*Math.cos(n)}}if("curvedCCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),s=Math.PI,n=(Math.atan2(o,i)+(.5*-t+.5)*s)%(2*s);return{x:this.from.x+(.5*t+.5)*e*Math.sin(n),y:this.from.y+(.5*t+.5)*e*Math.cos(n)}}{let e,s;e=s=i<=o?t*o:t*i,this.from.x>this.to.x&&(e=-e),this.from.y>=this.to.y&&(s=-s);let n=this.from.x+e,r=this.from.y+s;return i<=o?n=this.from.x<=this.to.x?this.to.x<n?this.to.x:n:this.to.x>n?this.to.x:n:r=this.from.y>=this.to.y?this.to.y>r?this.to.y:r:this.to.y<r?this.to.y:r,{x:n,y:r}}}_findBorderPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this._findBorderPositionBezier(t,e,i.via)}_getDistanceToEdge(t,e,i,o,s,n){let r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge(t,e,i,o,s,n,r)}getPoint(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const i=t;return{x:Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*e.x+Math.pow(i,2)*this.toPoint.x,y:Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*e.y+Math.pow(i,2)*this.toPoint.y}}}class jx extends Nx{constructor(t,e,i){super(t,e,i)}_getDistanceToBezierEdge2(t,e,i,o,s,n,r,a){let h=1e9,d=t,l=e;const c=[0,0,0,0];for(let u=1;u<10;u++){const p=.1*u;c[0]=Math.pow(1-p,3),c[1]=3*p*Math.pow(1-p,2),c[2]=3*Math.pow(p,2)*(1-p),c[3]=Math.pow(p,3);const g=c[0]*t+c[1]*r.x+c[2]*a.x+c[3]*i,f=c[0]*e+c[1]*r.y+c[2]*a.y+c[3]*o;if(u>0){const t=this._getDistanceToLine(d,l,g,f,s,n);h=t<h?t:h}d=g,l=f}return h}}class Lx extends jx{constructor(t,e,i){super(t,e,i)}_line(t,e,i){const o=i[0],s=i[1];this._bezierCurve(t,e,o,s)}_getViaCoordinates(){const t=this.from.x-this.to.x,e=this.from.y-this.to.y;let i,o,s,n;const r=this.options.smooth.roundness;return(Math.abs(t)>Math.abs(e)||!0===this.options.smooth.forceDirection||"horizontal"===this.options.smooth.forceDirection)&&"vertical"!==this.options.smooth.forceDirection?(o=this.from.y,n=this.to.y,i=this.from.x-r*t,s=this.to.x+r*t):(o=this.from.y-r*e,n=this.to.y+r*e,i=this.from.x,s=this.to.x),[{x:i,y:o},{x:s,y:n}]}getViaNode(){return this._getViaCoordinates()}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e)}_getDistanceToEdge(t,e,i,o,s,n){let[r,a]=arguments.length>6&&void 0!==arguments[6]?arguments[6]:this._getViaCoordinates();return this._getDistanceToBezierEdge2(t,e,i,o,s,n,r,a)}getPoint(t){let[e,i]=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getViaCoordinates();const o=t,s=[Math.pow(1-o,3),3*o*Math.pow(1-o,2),3*Math.pow(o,2)*(1-o),Math.pow(o,3)];return{x:s[0]*this.fromPoint.x+s[1]*e.x+s[2]*i.x+s[3]*this.toPoint.x,y:s[0]*this.fromPoint.y+s[1]*e.y+s[2]*i.y+s[3]*this.toPoint.y}}}class Hx extends Bx{constructor(t,e,i){super(t,e,i)}_line(t,e){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),t.lineTo(this.toPoint.x,this.toPoint.y),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){}getPoint(t){return{x:(1-t)*this.fromPoint.x+t*this.toPoint.x,y:(1-t)*this.fromPoint.y+t*this.toPoint.y}}_findBorderPosition(t,e){let i=this.to,o=this.from;t.id===this.from.id&&(i=this.from,o=this.to);const s=Math.atan2(i.y-o.y,i.x-o.x),n=i.x-o.x,r=i.y-o.y,a=Math.sqrt(n*n+r*r),h=(a-t.distanceToBorder(e,s))/a;return{x:(1-h)*o.x+h*i.x,y:(1-h)*o.y+h*i.y,t:0}}_getDistanceToEdge(t,e,i,o,s,n){return this._getDistanceToLine(t,e,i,o,s,n)}}class Wx{constructor(t,e,i,o,s){if(void 0===e)throw new Error("No body provided");this.options=Fm(o),this.globalOptions=o,this.defaultOptions=s,this.body=e,this.imagelist=i,this.id=void 0,this.fromId=void 0,this.toId=void 0,this.selected=!1,this.hover=!1,this.labelDirty=!0,this.baseWidth=this.options.width,this.baseFontSize=this.options.font.size,this.from=void 0,this.to=void 0,this.edgeType=void 0,this.connected=!1,this.labelModule=new j_(this.body,this.options,!0),this.setOptions(t)}setOptions(t){if(!t)return;let e=void 0!==t.physics&&this.options.physics!==t.physics||void 0!==t.hidden&&(this.options.hidden||!1)!==(t.hidden||!1)||void 0!==t.from&&this.options.from!==t.from||void 0!==t.to&&this.options.to!==t.to;Wx.parseOptions(this.options,t,!0,this.globalOptions),void 0!==t.id&&(this.id=t.id),void 0!==t.from&&(this.fromId=t.from),void 0!==t.to&&(this.toId=t.to),void 0!==t.title&&(this.title=t.title),void 0!==t.value&&(t.value=Uw(t.value));const i=[t,this.options,this.defaultOptions];return this.chooser=__("edge",i),this.updateLabelModule(t),e=this.updateEdgeType()||e,this._setInteractionWidths(),this.connect(),e}static parseOptions(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(vm(["endPointOffset","arrowStrikethrough","id","from","hidden","hoverWidth","labelHighlightBold","length","line","opacity","physics","scaling","selectionWidth","selfReferenceSize","selfReference","to","title","value","width","font","chosen","widthConstraint"],t,e,i),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.from&&(Wv(e.endPointOffset.from)?t.endPointOffset.from=e.endPointOffset.from:(t.endPointOffset.from=void 0!==o.endPointOffset.from?o.endPointOffset.from:0,console.error("endPointOffset.from is not a valid number"))),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.to&&(Wv(e.endPointOffset.to)?t.endPointOffset.to=e.endPointOffset.to:(t.endPointOffset.to=void 0!==o.endPointOffset.to?o.endPointOffset.to:0,console.error("endPointOffset.to is not a valid number"))),E_(e.label)?t.label=e.label:E_(t.label)||(t.label=void 0),zm(t,e,"smooth",o),zm(t,e,"shadow",o),zm(t,e,"background",o),void 0!==e.dashes&&null!==e.dashes?t.dashes=e.dashes:!0===i&&null===e.dashes&&(t.dashes=Pp(o.dashes)),void 0!==e.scaling&&null!==e.scaling?(void 0!==e.scaling.min&&(t.scaling.min=e.scaling.min),void 0!==e.scaling.max&&(t.scaling.max=e.scaling.max),zm(t.scaling,e.scaling,"label",o.scaling)):!0===i&&null===e.scaling&&(t.scaling=Pp(o.scaling)),void 0!==e.arrows&&null!==e.arrows)if("string"==typeof e.arrows){const i=e.arrows.toLowerCase();t.arrows.to.enabled=-1!=Mp(i).call(i,"to"),t.arrows.middle.enabled=-1!=Mp(i).call(i,"middle"),t.arrows.from.enabled=-1!=Mp(i).call(i,"from")}else{if("object"!=typeof e.arrows)throw new Error("The arrow newOptions can only be an object or a string. Refer to the documentation. You used:"+Fp(e.arrows));zm(t.arrows,e.arrows,"to",o.arrows),zm(t.arrows,e.arrows,"middle",o.arrows),zm(t.arrows,e.arrows,"from",o.arrows)}else!0===i&&null===e.arrows&&(t.arrows=Pp(o.arrows));if(void 0!==e.color&&null!==e.color){const n=fm(e.color)?{color:e.color,highlight:e.color,hover:e.color,inherit:!1,opacity:1}:e.color,r=t.color;if(s)_m(r,o.color,!1,i);else for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&delete r[t];if(fm(r))r.color=r,r.highlight=r,r.hover=r,r.inherit=!1,void 0===n.opacity&&(r.opacity=1);else{let t=!1;void 0!==n.color&&(r.color=n.color,t=!0),void 0!==n.highlight&&(r.highlight=n.highlight,t=!0),void 0!==n.hover&&(r.hover=n.hover,t=!0),void 0!==n.inherit&&(r.inherit=n.inherit),void 0!==n.opacity&&(r.opacity=Math.min(1,Math.max(0,n.opacity))),!0===t?r.inherit=!1:void 0===r.inherit&&(r.inherit="from")}}else!0===i&&null===e.color&&(t.color=Fm(o.color));!0===i&&null===e.font&&(t.font=Fm(o.font)),Object.prototype.hasOwnProperty.call(e,"selfReferenceSize")&&(console.warn("The selfReferenceSize property has been deprecated. Please use selfReference property instead. The selfReference can be set like thise selfReference:{size:30, angle:Math.PI / 4}"),t.selfReference.size=e.selfReferenceSize)}getFormattingValues(){const t=!0===this.options.arrows.to||!0===this.options.arrows.to.enabled,e=!0===this.options.arrows.from||!0===this.options.arrows.from.enabled,i=!0===this.options.arrows.middle||!0===this.options.arrows.middle.enabled,o=this.options.color.inherit,s={toArrow:t,toArrowScale:this.options.arrows.to.scaleFactor,toArrowType:this.options.arrows.to.type,toArrowSrc:this.options.arrows.to.src,toArrowImageWidth:this.options.arrows.to.imageWidth,toArrowImageHeight:this.options.arrows.to.imageHeight,middleArrow:i,middleArrowScale:this.options.arrows.middle.scaleFactor,middleArrowType:this.options.arrows.middle.type,middleArrowSrc:this.options.arrows.middle.src,middleArrowImageWidth:this.options.arrows.middle.imageWidth,middleArrowImageHeight:this.options.arrows.middle.imageHeight,fromArrow:e,fromArrowScale:this.options.arrows.from.scaleFactor,fromArrowType:this.options.arrows.from.type,fromArrowSrc:this.options.arrows.from.src,fromArrowImageWidth:this.options.arrows.from.imageWidth,fromArrowImageHeight:this.options.arrows.from.imageHeight,arrowStrikethrough:this.options.arrowStrikethrough,color:o?void 0:this.options.color.color,inheritsColor:o,opacity:this.options.color.opacity,hidden:this.options.hidden,length:this.options.length,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y,dashes:this.options.dashes,width:this.options.width,background:this.options.background.enabled,backgroundColor:this.options.background.color,backgroundSize:this.options.background.size,backgroundDashes:this.options.background.dashes};if(this.selected||this.hover)if(!0===this.chooser){if(this.selected){const t=this.options.selectionWidth;"function"==typeof t?s.width=t(s.width):"number"==typeof t&&(s.width+=t),s.width=Math.max(s.width,.3/this.body.view.scale),s.color=this.options.color.highlight,s.shadow=this.options.shadow.enabled}else if(this.hover){const t=this.options.hoverWidth;"function"==typeof t?s.width=t(s.width):"number"==typeof t&&(s.width+=t),s.width=Math.max(s.width,.3/this.body.view.scale),s.color=this.options.color.hover,s.shadow=this.options.shadow.enabled}}else"function"==typeof this.chooser&&(this.chooser(s,this.options.id,this.selected,this.hover),void 0!==s.color&&(s.inheritsColor=!1),!1===s.shadow&&(s.shadowColor===this.options.shadow.color&&s.shadowSize===this.options.shadow.size&&s.shadowX===this.options.shadow.x&&s.shadowY===this.options.shadow.y||(s.shadow=!0)));else s.shadow=this.options.shadow.enabled,s.width=Math.max(s.width,.3/this.body.view.scale);return s}updateLabelModule(t){const e=[t,this.options,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,e),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateEdgeType(){const t=this.options.smooth;let e=!1,i=!0;return void 0!==this.edgeType&&((this.edgeType instanceof Ax&&!0===t.enabled&&"dynamic"===t.type||this.edgeType instanceof Lx&&!0===t.enabled&&"cubicBezier"===t.type||this.edgeType instanceof Rx&&!0===t.enabled&&"dynamic"!==t.type&&"cubicBezier"!==t.type||this.edgeType instanceof Hx&&!1===t.type.enabled)&&(i=!1),!0===i&&(e=this.cleanup())),!0===i?!0===t.enabled?"dynamic"===t.type?(e=!0,this.edgeType=new Ax(this.options,this.body,this.labelModule)):"cubicBezier"===t.type?this.edgeType=new Lx(this.options,this.body,this.labelModule):this.edgeType=new Rx(this.options,this.body,this.labelModule):this.edgeType=new Hx(this.options,this.body,this.labelModule):this.edgeType.setOptions(this.options),e}connect(){this.disconnect(),this.from=this.body.nodes[this.fromId]||void 0,this.to=this.body.nodes[this.toId]||void 0,this.connected=void 0!==this.from&&void 0!==this.to,!0===this.connected?(this.from.attachEdge(this),this.to.attachEdge(this)):(this.from&&this.from.detachEdge(this),this.to&&this.to.detachEdge(this)),this.edgeType.connect()}disconnect(){this.from&&(this.from.detachEdge(this),this.from=void 0),this.to&&(this.to.detachEdge(this),this.to=void 0),this.connected=!1}getTitle(){return this.title}isSelected(){return this.selected}getValue(){return this.options.value}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),s=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.width=this.options.scaling.min+o*s}else this.options.width=this.baseWidth,this.options.font.size=this.baseFontSize;this._setInteractionWidths(),this.updateLabelModule()}_setInteractionWidths(){"function"==typeof this.options.hoverWidth?this.edgeType.hoverWidth=this.options.hoverWidth(this.options.width):this.edgeType.hoverWidth=this.options.hoverWidth+this.options.width,"function"==typeof this.options.selectionWidth?this.edgeType.selectionWidth=this.options.selectionWidth(this.options.width):this.edgeType.selectionWidth=this.options.selectionWidth+this.options.width}draw(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode();this.edgeType.drawLine(t,e,this.selected,this.hover,i),this.drawLabel(t,i)}drawArrows(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode(),o={};this.edgeType.fromPoint=this.edgeType.from,this.edgeType.toPoint=this.edgeType.to,e.fromArrow&&(o.from=this.edgeType.getArrowData(t,"from",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.fromPoint=o.from.core),e.fromArrowSrc&&(o.from.image=this.imagelist.load(e.fromArrowSrc)),e.fromArrowImageWidth&&(o.from.imageWidth=e.fromArrowImageWidth),e.fromArrowImageHeight&&(o.from.imageHeight=e.fromArrowImageHeight)),e.toArrow&&(o.to=this.edgeType.getArrowData(t,"to",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.toPoint=o.to.core),e.toArrowSrc&&(o.to.image=this.imagelist.load(e.toArrowSrc)),e.toArrowImageWidth&&(o.to.imageWidth=e.toArrowImageWidth),e.toArrowImageHeight&&(o.to.imageHeight=e.toArrowImageHeight)),e.middleArrow&&(o.middle=this.edgeType.getArrowData(t,"middle",i,this.selected,this.hover,e),e.middleArrowSrc&&(o.middle.image=this.imagelist.load(e.middleArrowSrc)),e.middleArrowImageWidth&&(o.middle.imageWidth=e.middleArrowImageWidth),e.middleArrowImageHeight&&(o.middle.imageHeight=e.middleArrowImageHeight)),e.fromArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.from),e.middleArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.middle),e.toArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.to)}drawLabel(t,e){if(void 0!==this.options.label){const i=this.from,o=this.to;let s;if(this.labelModule.differentState(this.selected,this.hover)&&this.labelModule.getTextSize(t,this.selected,this.hover),i.id!=o.id){this.labelModule.pointToSelf=!1,s=this.edgeType.getPoint(.5,e),t.save();const i=this._getRotation(t);0!=i.angle&&(t.translate(i.x,i.y),t.rotate(i.angle)),this.labelModule.draw(t,s.x,s.y,this.selected,this.hover),t.restore()}else{this.labelModule.pointToSelf=!0;const e=O_(t,this.options.selfReference.angle,this.options.selfReference.size,i);s=this._pointOnCircle(e.x,e.y,this.options.selfReference.size,this.options.selfReference.angle),this.labelModule.draw(t,s.x,s.y,this.selected,this.hover)}}}getItemsOnPoint(t){const e=[];if(this.labelModule.visible()){const i=this._getRotation();x_(this.labelModule.getSize(),t,i)&&e.push({edgeId:this.id,labelId:0})}const i={left:t.x,top:t.y};return this.isOverlappingWith(i)&&e.push({edgeId:this.id}),e}isOverlappingWith(t){if(this.connected){const e=10,i=this.from.x,o=this.from.y,s=this.to.x,n=this.to.y,r=t.left,a=t.top;return this.edgeType.getDistanceToEdge(i,o,s,n,r,a)<e}return!1}_getRotation(t){const e=this.edgeType.getViaNode(),i=this.edgeType.getPoint(.5,e);void 0!==t&&this.labelModule.calculateLabelSize(t,this.selected,this.hover,i.x,i.y);const o={x:i.x,y:this.labelModule.size.yLine,angle:0};if(!this.labelModule.visible())return o;if("horizontal"===this.options.font.align)return o;const s=this.from.y-this.to.y,n=this.from.x-this.to.x;let r=Math.atan2(s,n);return(r<-1&&n<0||r>0&&n<0)&&(r+=Math.PI),o.angle=r,o}_pointOnCircle(t,e,i,o){return{x:t+i*Math.cos(o),y:e-i*Math.sin(o)}}select(){this.selected=!0}unselect(){this.selected=!1}cleanup(){return this.edgeType.cleanup()}remove(){this.cleanup(),this.disconnect(),delete this.body.edges[this.id]}endPointsValid(){return void 0!==this.body.nodes[this.fromId]&&void 0!==this.body.nodes[this.toId]}}class Vx{constructor(t,e,i){var o;this.body=t,this.images=e,this.groups=i,this.body.functions.createEdge=Ho(o=this.create).call(o,this),this.edgesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items)},remove:(t,e)=>{this.remove(e.items)}},this.options={},this.defaultOptions={arrows:{to:{enabled:!1,scaleFactor:1,type:"arrow"},middle:{enabled:!1,scaleFactor:1,type:"arrow"},from:{enabled:!1,scaleFactor:1,type:"arrow"}},endPointOffset:{from:0,to:0},arrowStrikethrough:!0,color:{color:"#848484",highlight:"#848484",hover:"#848484",inherit:"from",opacity:1},dashes:!1,font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:2,strokeColor:"#ffffff",align:"horizontal",multi:!1,vadjust:0,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"courier new",vadjust:2}},hidden:!1,hoverWidth:1.5,label:void 0,labelHighlightBold:!0,length:void 0,physics:!0,scaling:{min:1,max:15,label:{enabled:!0,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},selectionWidth:1.5,selfReference:{size:20,angle:Math.PI/4,renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},background:{enabled:!1,color:"rgba(111,111,111,1)",size:10,dashes:!1},smooth:{enabled:!0,type:"dynamic",forceDirection:"none",roundness:.5},title:void 0,width:1,value:void 0},_m(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t,e,i=this;this.body.emitter.on("_forceDisableDynamicCurves",(function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];"dynamic"===t&&(t="continuous");let o=!1;for(const e in i.body.edges)if(Object.prototype.hasOwnProperty.call(i.body.edges,e)){const s=i.body.edges[e],n=i.body.data.edges.get(e);if(null!=n){const e=n.smooth;void 0!==e&&!0===e.enabled&&"dynamic"===e.type&&(void 0===t?s.setOptions({smooth:!1}):s.setOptions({smooth:{type:t}}),o=!0)}}!0===e&&!0===o&&i.body.emitter.emit("_dataChanged")})),this.body.emitter.on("_dataUpdated",(()=>{this.reconnectEdges()})),this.body.emitter.on("refreshEdges",Ho(t=this.refresh).call(t,this)),this.body.emitter.on("refresh",Ho(e=this.refresh).call(e,this)),this.body.emitter.on("destroy",(()=>{Om(this.edgesListeners,((t,e)=>{this.body.data.edges&&this.body.data.edges.off(e,t)})),delete this.body.functions.createEdge,delete this.edgesListeners.add,delete this.edgesListeners.update,delete this.edgesListeners.remove,delete this.edgesListeners}))}setOptions(t){if(void 0!==t){Wx.parseOptions(this.options,t,!0,this.defaultOptions,!0);let e=!1;if(void 0!==t.smooth)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&(e=this.body.edges[t].updateEdgeType()||e);if(void 0!==t.font)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&this.body.edges[t].updateLabelModule();void 0===t.hidden&&void 0===t.physics&&!0!==e||this.body.emitter.emit("_dataChanged")}}setData(i){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const s=this.body.data.edges;if(t("id",i))this.body.data.edges=i;else if(Vl(i))this.body.data.edges=new e,this.body.data.edges.add(i);else{if(i)throw new TypeError("Array or DataSet expected");this.body.data.edges=new e}if(s&&Om(this.edgesListeners,((t,e)=>{s.off(e,t)})),this.body.edges={},this.body.data.edges){Om(this.edgesListeners,((t,e)=>{this.body.data.edges.on(e,t)}));const t=this.body.data.edges.getIds();this.add(t,!0)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===o&&this.body.emitter.emit("_dataChanged")}add(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.edges,o=this.body.data.edges;for(let e=0;e<t.length;e++){const s=t[e],n=i[s];n&&n.disconnect();const r=o.get(s,{showInternalIds:!0});i[s]=this.create(r)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}update(t){const e=this.body.edges,i=this.body.data.edges;let o=!1;for(let s=0;s<t.length;s++){const n=t[s],r=i.get(n),a=e[n];void 0!==a?(a.disconnect(),o=a.setOptions(r)||o,a.connect()):(this.body.edges[n]=this.create(r),o=!0)}!0===o?(this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),this.body.emitter.emit("_dataChanged")):this.body.emitter.emit("_dataUpdated")}remove(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(0===t.length)return;const i=this.body.edges;Om(t,(t=>{const e=i[t];void 0!==e&&e.remove()})),e&&this.body.emitter.emit("_dataChanged")}refresh(){Om(this.body.edges,((t,e)=>{const i=this.body.data.edges.get(e);void 0!==i&&t.setOptions(i)}))}create(t){return new Wx(t,this.body,this.images,this.options,this.defaultOptions)}reconnectEdges(){let t;const e=this.body.nodes,i=this.body.edges;for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(e[t].edges=[]);for(t in i)if(Object.prototype.hasOwnProperty.call(i,t)){const e=i[t];e.from=null,e.to=null,e.connect()}}getConnectedNodes(t){const e=[];if(void 0!==this.body.edges[t]){const i=this.body.edges[t];void 0!==i.fromId&&e.push(i.fromId),void 0!==i.toId&&e.push(i.toId)}return e}_updateState(){this._addMissingEdges(),this._removeInvalidEdges()}_removeInvalidEdges(){const t=[];Om(this.body.edges,((e,i)=>{const o=this.body.nodes[e.toId],s=this.body.nodes[e.fromId];void 0!==o&&!0===o.isCluster||void 0!==s&&!0===s.isCluster||void 0!==o&&void 0!==s||t.push(i)})),this.remove(t,!1)}_addMissingEdges(){const t=this.body.data.edges;if(null==t)return;const e=this.body.edges,i=[];mc(t).call(t,((t,o)=>{void 0===e[o]&&i.push(o)})),this.add(i,!0)}}class qx{constructor(t,e,i){this.body=t,this.physicsBody=e,this.barnesHutTree,this.setOptions(i),this._rng=am("BARNES HUT SOLVER")}setOptions(t){this.options=t,this.thetaInversed=1/this.options.theta,this.overlapAvoidanceFactor=1-Math.max(0,Math.min(1,this.options.avoidOverlap))}solve(){if(0!==this.options.gravitationalConstant&&this.physicsBody.physicsNodeIndices.length>0){let t;const e=this.body.nodes,i=this.physicsBody.physicsNodeIndices,o=i.length,s=this._formBarnesHutTree(e,i);this.barnesHutTree=s;for(let n=0;n<o;n++)t=e[i[n]],t.options.mass>0&&this._getForceContributions(s.root,t)}}_getForceContributions(t,e){this._getForceContribution(t.children.NW,e),this._getForceContribution(t.children.NE,e),this._getForceContribution(t.children.SW,e),this._getForceContribution(t.children.SE,e)}_getForceContribution(t,e){if(t.childrenCount>0){const i=t.centerOfMass.x-e.x,o=t.centerOfMass.y-e.y,s=Math.sqrt(i*i+o*o);s*t.calcSize>this.thetaInversed?this._calculateForces(s,i,o,e,t):4===t.childrenCount?this._getForceContributions(t,e):t.children.data.id!=e.id&&this._calculateForces(s,i,o,e,t)}}_calculateForces(t,e,i,o,s){0===t&&(e=t=.1),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const n=this.options.gravitationalConstant*s.mass*o.options.mass/Math.pow(t,3),r=e*n,a=i*n;this.physicsBody.forces[o.id].x+=r,this.physicsBody.forces[o.id].y+=a}_formBarnesHutTree(t,e){let i;const o=e.length;let s=t[e[0]].x,n=t[e[0]].y,r=t[e[0]].x,a=t[e[0]].y;for(let i=1;i<o;i++){const o=t[e[i]],h=o.x,d=o.y;o.options.mass>0&&(h<s&&(s=h),h>r&&(r=h),d<n&&(n=d),d>a&&(a=d))}const h=Math.abs(r-s)-Math.abs(a-n);h>0?(n-=.5*h,a+=.5*h):(s+=.5*h,r-=.5*h);const d=Math.max(1e-5,Math.abs(r-s)),l=.5*d,c=.5*(s+r),u=.5*(n+a),p={root:{centerOfMass:{x:0,y:0},mass:0,range:{minX:c-l,maxX:c+l,minY:u-l,maxY:u+l},size:d,calcSize:1/d,children:{data:null},maxWidth:0,level:0,childrenCount:4}};this._splitBranch(p.root);for(let s=0;s<o;s++)i=t[e[s]],i.options.mass>0&&this._placeInTree(p.root,i);return p}_updateBranchMass(t,e){const i=t.centerOfMass,o=t.mass+e.options.mass,s=1/o;i.x=i.x*t.mass+e.x*e.options.mass,i.x*=s,i.y=i.y*t.mass+e.y*e.options.mass,i.y*=s,t.mass=o;const n=Math.max(Math.max(e.height,e.radius),e.width);t.maxWidth=t.maxWidth<n?n:t.maxWidth}_placeInTree(t,e,i){1==i&&void 0!==i||this._updateBranchMass(t,e);const o=t.children.NW.range;let s;s=o.maxX>e.x?o.maxY>e.y?"NW":"SW":o.maxY>e.y?"NE":"SE",this._placeInRegion(t,e,s)}_placeInRegion(t,e,i){const o=t.children[i];switch(o.childrenCount){case 0:o.children.data=e,o.childrenCount=1,this._updateBranchMass(o,e);break;case 1:o.children.data.x===e.x&&o.children.data.y===e.y?(e.x+=this._rng(),e.y+=this._rng()):(this._splitBranch(o),this._placeInTree(o,e));break;case 4:this._placeInTree(o,e)}}_splitBranch(t){let e=null;1===t.childrenCount&&(e=t.children.data,t.mass=0,t.centerOfMass.x=0,t.centerOfMass.y=0),t.childrenCount=4,t.children.data=null,this._insertRegion(t,"NW"),this._insertRegion(t,"NE"),this._insertRegion(t,"SW"),this._insertRegion(t,"SE"),null!=e&&this._placeInTree(t,e)}_insertRegion(t,e){let i,o,s,n;const r=.5*t.size;switch(e){case"NW":i=t.range.minX,o=t.range.minX+r,s=t.range.minY,n=t.range.minY+r;break;case"NE":i=t.range.minX+r,o=t.range.maxX,s=t.range.minY,n=t.range.minY+r;break;case"SW":i=t.range.minX,o=t.range.minX+r,s=t.range.minY+r,n=t.range.maxY;break;case"SE":i=t.range.minX+r,o=t.range.maxX,s=t.range.minY+r,n=t.range.maxY}t.children[e]={centerOfMass:{x:0,y:0},mass:0,range:{minX:i,maxX:o,minY:s,maxY:n},size:.5*t.size,calcSize:2*t.calcSize,children:{data:null},maxWidth:0,level:t.level+1,childrenCount:0}}_debug(t,e){void 0!==this.barnesHutTree&&(t.lineWidth=1,this._drawBranch(this.barnesHutTree.root,t,e))}_drawBranch(t,e,i){void 0===i&&(i="#FF0000"),4===t.childrenCount&&(this._drawBranch(t.children.NW,e),this._drawBranch(t.children.NE,e),this._drawBranch(t.children.SE,e),this._drawBranch(t.children.SW,e)),e.strokeStyle=i,e.beginPath(),e.moveTo(t.range.minX,t.range.minY),e.lineTo(t.range.maxX,t.range.minY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.minY),e.lineTo(t.range.maxX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.maxY),e.lineTo(t.range.minX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.minX,t.range.maxY),e.lineTo(t.range.minX,t.range.minY),e.stroke()}}class Ux{constructor(t,e,i){this._rng=am("REPULSION SOLVER"),this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,s,n,r,a;const h=this.body.nodes,d=this.physicsBody.physicsNodeIndices,l=this.physicsBody.forces,c=this.options.nodeDistance,u=-2/3/c;for(let p=0;p<d.length-1;p++){r=h[d[p]];for(let g=p+1;g<d.length;g++)a=h[d[g]],t=a.x-r.x,e=a.y-r.y,i=Math.sqrt(t*t+e*e),0===i&&(i=.1*this._rng(),t=i),i<2*c&&(n=i<.5*c?1:u*i+1.3333333333333333,n/=i,o=t*n,s=e*n,l[r.id].x-=o,l[r.id].y-=s,l[a.id].x+=o,l[a.id].y+=s)}}}class Yx{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t,this.overlapAvoidanceFactor=Math.max(0,Math.min(1,this.options.avoidOverlap||0))}solve(){const t=this.body.nodes,e=this.physicsBody.physicsNodeIndices,i=this.physicsBody.forces,o=this.options.nodeDistance;for(let s=0;s<e.length-1;s++){const n=t[e[s]];for(let r=s+1;r<e.length;r++){const s=t[e[r]];if(n.level===s.level){const t=o+this.overlapAvoidanceFactor*((n.shape.radius||0)/2+(s.shape.radius||0)/2),e=s.x-n.x,r=s.y-n.y,a=Math.sqrt(e*e+r*r),h=.05;let d;d=a<t?-Math.pow(h*a,2)+Math.pow(h*t,2):0,0!==a&&(d/=a);const l=e*d,c=r*d;i[n.id].x-=l,i[n.id].y-=c,i[s.id].x+=l,i[s.id].y+=c}}}}}class Xx{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e;const i=this.physicsBody.physicsEdgeIndices,o=this.body.edges;let s,n,r;for(let a=0;a<i.length;a++)e=o[i[a]],!0===e.connected&&e.toId!==e.fromId&&void 0!==this.body.nodes[e.toId]&&void 0!==this.body.nodes[e.fromId]&&(void 0!==e.edgeType.via?(t=void 0===e.options.length?this.options.springLength:e.options.length,s=e.to,n=e.edgeType.via,r=e.from,this._calculateSpringForce(s,n,.5*t),this._calculateSpringForce(n,r,.5*t)):(t=void 0===e.options.length?1.5*this.options.springLength:e.options.length,this._calculateSpringForce(e.from,e.to,t)))}_calculateSpringForce(t,e,i){const o=t.x-e.x,s=t.y-e.y,n=Math.max(Math.sqrt(o*o+s*s),.01),r=this.options.springConstant*(i-n)/n,a=o*r,h=s*r;void 0!==this.physicsBody.forces[t.id]&&(this.physicsBody.forces[t.id].x+=a,this.physicsBody.forces[t.id].y+=h),void 0!==this.physicsBody.forces[e.id]&&(this.physicsBody.forces[e.id].x-=a,this.physicsBody.forces[e.id].y-=h)}}class Kx{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,s,n,r,a;const h=this.body.edges,d=.5,l=this.physicsBody.physicsEdgeIndices,c=this.physicsBody.physicsNodeIndices,u=this.physicsBody.forces;for(let t=0;t<c.length;t++){const e=c[t];u[e].springFx=0,u[e].springFy=0}for(let c=0;c<l.length;c++)e=h[l[c]],!0===e.connected&&(t=void 0===e.options.length?this.options.springLength:e.options.length,i=e.from.x-e.to.x,o=e.from.y-e.to.y,a=Math.sqrt(i*i+o*o),a=0===a?.01:a,r=this.options.springConstant*(t-a)/a,s=i*r,n=o*r,e.to.level!=e.from.level?(void 0!==u[e.toId]&&(u[e.toId].springFx-=s,u[e.toId].springFy-=n),void 0!==u[e.fromId]&&(u[e.fromId].springFx+=s,u[e.fromId].springFy+=n)):(void 0!==u[e.toId]&&(u[e.toId].x-=d*s,u[e.toId].y-=d*n),void 0!==u[e.fromId]&&(u[e.fromId].x+=d*s,u[e.fromId].y+=d*n)));let p,g;r=1;for(let t=0;t<c.length;t++){const e=c[t];p=Math.min(r,Math.max(-r,u[e].springFx)),g=Math.min(r,Math.max(-r,u[e].springFy)),u[e].x+=p,u[e].y+=g}let f=0,m=0;for(let t=0;t<c.length;t++){const e=c[t];f+=u[e].x,m+=u[e].y}const y=f/c.length,b=m/c.length;for(let t=0;t<c.length;t++){const e=c[t];u[e].x-=y,u[e].y-=b}}}class Gx{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o;const s=this.body.nodes,n=this.physicsBody.physicsNodeIndices,r=this.physicsBody.forces;for(let a=0;a<n.length;a++){o=s[n[a]],t=-o.x,e=-o.y,i=Math.sqrt(t*t+e*e),this._calculateForces(i,t,e,r,o)}}_calculateForces(t,e,i,o,s){const n=0===t?0:this.options.centralGravity/t;o[s.id].x=e*n,o[s.id].y=i*n}}class Zx extends qx{constructor(t,e,i){super(t,e,i),this._rng=am("FORCE ATLAS 2 BASED REPULSION SOLVER")}_calculateForces(t,e,i,o,s){0===t&&(e=t=.1*this._rng()),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const n=o.edges.length+1,r=this.options.gravitationalConstant*s.mass*o.options.mass*n/Math.pow(t,2),a=e*r,h=i*r;this.physicsBody.forces[o.id].x+=a,this.physicsBody.forces[o.id].y+=h}}class Qx extends Gx{constructor(t,e,i){super(t,e,i)}_calculateForces(t,e,i,o,s){if(t>0){const t=s.edges.length+1,n=this.options.centralGravity*t*s.options.mass;o[s.id].x=e*n,o[s.id].y=i*n}}}class $x{constructor(t){this.body=t,this.physicsBody={physicsNodeIndices:[],physicsEdgeIndices:[],forces:{},velocities:{}},this.physicsEnabled=!0,this.simulationInterval=1e3/60,this.requiresTimeout=!0,this.previousStates={},this.referenceState={},this.freezeCache={},this.renderTimer=void 0,this.adaptiveTimestep=!1,this.adaptiveTimestepEnabled=!1,this.adaptiveCounter=0,this.adaptiveInterval=3,this.stabilized=!1,this.startedStabilization=!1,this.stabilizationIterations=0,this.ready=!1,this.options={},this.defaultOptions={enabled:!0,barnesHut:{theta:.5,gravitationalConstant:-2e3,centralGravity:.3,springLength:95,springConstant:.04,damping:.09,avoidOverlap:0},forceAtlas2Based:{theta:.5,gravitationalConstant:-50,centralGravity:.01,springConstant:.08,springLength:100,damping:.4,avoidOverlap:0},repulsion:{centralGravity:.2,springLength:200,springConstant:.05,nodeDistance:100,damping:.09,avoidOverlap:0},hierarchicalRepulsion:{centralGravity:0,springLength:100,springConstant:.01,nodeDistance:120,damping:.09},maxVelocity:50,minVelocity:.75,solver:"barnesHut",stabilization:{enabled:!0,iterations:1e3,updateInterval:50,onlyDynamicEdges:!1,fit:!0},timestep:.5,adaptiveTimestep:!0,wind:{x:0,y:0}},wo(this.options,this.defaultOptions),this.timestep=.5,this.layoutFailed=!1,this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("initPhysics",(()=>{this.initPhysics()})),this.body.emitter.on("_layoutFailed",(()=>{this.layoutFailed=!0})),this.body.emitter.on("resetPhysics",(()=>{this.stopSimulation(),this.ready=!1})),this.body.emitter.on("disablePhysics",(()=>{this.physicsEnabled=!1,this.stopSimulation()})),this.body.emitter.on("restorePhysics",(()=>{this.setOptions(this.options),!0===this.ready&&this.startSimulation()})),this.body.emitter.on("startSimulation",(()=>{!0===this.ready&&this.startSimulation()})),this.body.emitter.on("stopSimulation",(()=>{this.stopSimulation()})),this.body.emitter.on("destroy",(()=>{this.stopSimulation(!1),this.body.emitter.off()})),this.body.emitter.on("_dataChanged",(()=>{this.updatePhysicsData()}))}setOptions(t){if(void 0!==t)if(!1===t)this.options.enabled=!1,this.physicsEnabled=!1,this.stopSimulation();else if(!0===t)this.options.enabled=!0,this.physicsEnabled=!0,this.startSimulation();else{this.physicsEnabled=!0,wm(["stabilization"],this.options,t),zm(this.options,t,"stabilization"),void 0===t.enabled&&(this.options.enabled=!0),!1===this.options.enabled&&(this.physicsEnabled=!1,this.stopSimulation());const e=this.options.wind;e&&(("number"!=typeof e.x||jv(e.x))&&(e.x=0),("number"!=typeof e.y||jv(e.y))&&(e.y=0)),this.timestep=this.options.timestep}this.init()}init(){let t;"forceAtlas2Based"===this.options.solver?(t=this.options.forceAtlas2Based,this.nodesSolver=new Zx(this.body,this.physicsBody,t),this.edgesSolver=new Xx(this.body,this.physicsBody,t),this.gravitySolver=new Qx(this.body,this.physicsBody,t)):"repulsion"===this.options.solver?(t=this.options.repulsion,this.nodesSolver=new Ux(this.body,this.physicsBody,t),this.edgesSolver=new Xx(this.body,this.physicsBody,t),this.gravitySolver=new Gx(this.body,this.physicsBody,t)):"hierarchicalRepulsion"===this.options.solver?(t=this.options.hierarchicalRepulsion,this.nodesSolver=new Yx(this.body,this.physicsBody,t),this.edgesSolver=new Kx(this.body,this.physicsBody,t),this.gravitySolver=new Gx(this.body,this.physicsBody,t)):(t=this.options.barnesHut,this.nodesSolver=new qx(this.body,this.physicsBody,t),this.edgesSolver=new Xx(this.body,this.physicsBody,t),this.gravitySolver=new Gx(this.body,this.physicsBody,t)),this.modelOptions=t}initPhysics(){!0===this.physicsEnabled&&!0===this.options.enabled?!0===this.options.stabilization.enabled?this.stabilize():(this.stabilized=!1,this.ready=!0,this.body.emitter.emit("fit",{},this.layoutFailed),this.startSimulation()):(this.ready=!0,this.body.emitter.emit("fit"))}startSimulation(){var t;!0===this.physicsEnabled&&!0===this.options.enabled?(this.stabilized=!1,this.adaptiveTimestep=!1,this.body.emitter.emit("_resizeNodes"),void 0===this.viewFunction&&(this.viewFunction=Ho(t=this.simulationStep).call(t,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))):this.body.emitter.emit("_redraw")}stopSimulation(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.stabilized=!0,!0===t&&this._emitStabilized(),void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.viewFunction=void 0,!0===t&&this.body.emitter.emit("_stopRendering"))}simulationStep(){const t=oc();this.physicsTick();(oc()-t<.4*this.simulationInterval||!0===this.runDoubleSpeed)&&!1===this.stabilized&&(this.physicsTick(),this.runDoubleSpeed=!0),!0===this.stabilized&&this.stopSimulation()}_emitStabilized(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.stabilizationIterations;(this.stabilizationIterations>1||!0===this.startedStabilization)&&Jp((()=>{this.body.emitter.emit("stabilized",{iterations:t}),this.startedStabilization=!1,this.stabilizationIterations=0}),0)}physicsStep(){this.gravitySolver.solve(),this.nodesSolver.solve(),this.edgesSolver.solve(),this.moveNodes()}adjustTimeStep(){!0===this._evaluateStepQuality()?this.timestep=1.2*this.timestep:this.timestep/1.2<this.options.timestep?this.timestep=this.options.timestep:(this.adaptiveCounter=-1,this.timestep=Math.max(this.options.timestep,this.timestep/1.2))}physicsTick(){if(this._startStabilizing(),!0!==this.stabilized){if(!0===this.adaptiveTimestep&&!0===this.adaptiveTimestepEnabled){this.adaptiveCounter%this.adaptiveInterval==0?(this.timestep=2*this.timestep,this.physicsStep(),this.revert(),this.timestep=.5*this.timestep,this.physicsStep(),this.physicsStep(),this.adjustTimeStep()):this.physicsStep(),this.adaptiveCounter+=1}else this.timestep=this.options.timestep,this.physicsStep();!0===this.stabilized&&this.revert(),this.stabilizationIterations++}}updatePhysicsData(){this.physicsBody.forces={},this.physicsBody.physicsNodeIndices=[],this.physicsBody.physicsEdgeIndices=[];const t=this.body.nodes,e=this.body.edges;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&!0===t[e].options.physics&&this.physicsBody.physicsNodeIndices.push(t[e].id);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&!0===e[t].options.physics&&this.physicsBody.physicsEdgeIndices.push(e[t].id);for(let t=0;t<this.physicsBody.physicsNodeIndices.length;t++){const e=this.physicsBody.physicsNodeIndices[t];this.physicsBody.forces[e]={x:0,y:0},void 0===this.physicsBody.velocities[e]&&(this.physicsBody.velocities[e]={x:0,y:0})}for(const e in this.physicsBody.velocities)void 0===t[e]&&delete this.physicsBody.velocities[e]}revert(){const t=Jl(this.previousStates),e=this.body.nodes,i=this.physicsBody.velocities;this.referenceState={};for(let o=0;o<t.length;o++){const s=t[o];void 0!==e[s]?!0===e[s].options.physics&&(this.referenceState[s]={positions:{x:e[s].x,y:e[s].y}},i[s].x=this.previousStates[s].vx,i[s].y=this.previousStates[s].vy,e[s].x=this.previousStates[s].x,e[s].y=this.previousStates[s].y):delete this.previousStates[s]}}_evaluateStepQuality(){let t,e,i;const o=this.body.nodes,s=this.referenceState;for(const n in this.referenceState)if(Object.prototype.hasOwnProperty.call(this.referenceState,n)&&void 0!==o[n]&&(t=o[n].x-s[n].positions.x,e=o[n].y-s[n].positions.y,i=Math.sqrt(Math.pow(t,2)+Math.pow(e,2)),i>.3))return!1;return!0}moveNodes(){const t=this.physicsBody.physicsNodeIndices;let e=0,i=0;for(let o=0;o<t.length;o++){const s=t[o],n=this._performStep(s);e=Math.max(e,n),i+=n}this.adaptiveTimestepEnabled=i/t.length<5,this.stabilized=e<this.options.minVelocity}calculateComponentVelocity(t,e,i){t+=(e-this.modelOptions.damping*t)/i*this.timestep;const o=this.options.maxVelocity||1e9;return Math.abs(t)>o&&(t=t>0?o:-o),t}_performStep(t){const e=this.body.nodes[t],i=this.physicsBody.forces[t];this.options.wind&&(i.x+=this.options.wind.x,i.y+=this.options.wind.y);const o=this.physicsBody.velocities[t];this.previousStates[t]={x:e.x,y:e.y,vx:o.x,vy:o.y},!1===e.options.fixed.x?(o.x=this.calculateComponentVelocity(o.x,i.x,e.options.mass),e.x+=o.x*this.timestep):(i.x=0,o.x=0),!1===e.options.fixed.y?(o.y=this.calculateComponentVelocity(o.y,i.y,e.options.mass),e.y+=o.y*this.timestep):(i.y=0,o.y=0);return Math.sqrt(Math.pow(o.x,2)+Math.pow(o.y,2))}_freezeNodes(){const t=this.body.nodes;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e].x&&t[e].y){const i=t[e].options.fixed;this.freezeCache[e]={x:i.x,y:i.y},i.x=!0,i.y=!0}}_restoreFrozenNodes(){const t=this.body.nodes;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.freezeCache[e]&&(t[e].options.fixed.x=this.freezeCache[e].x,t[e].options.fixed.y=this.freezeCache[e].y);this.freezeCache={}}stabilize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.stabilization.iterations;"number"!=typeof t&&(t=this.options.stabilization.iterations,console.error("The stabilize method needs a numeric amount of iterations. Switching to default: ",t)),0!==this.physicsBody.physicsNodeIndices.length?(this.adaptiveTimestep=this.options.adaptiveTimestep,this.body.emitter.emit("_resizeNodes"),this.stopSimulation(),this.stabilized=!1,this.body.emitter.emit("_blockRedraw"),this.targetIterations=t,!0===this.options.stabilization.onlyDynamicEdges&&this._freezeNodes(),this.stabilizationIterations=0,Jp((()=>this._stabilizationBatch()),0)):this.ready=!0}_startStabilizing(){return!0!==this.startedStabilization&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0,!0)}_stabilizationBatch(){const t=()=>!1===this.stabilized&&this.stabilizationIterations<this.targetIterations,e=()=>{this.body.emitter.emit("stabilizationProgress",{iterations:this.stabilizationIterations,total:this.targetIterations})};this._startStabilizing()&&e();let i=0;for(;t()&&i<this.options.stabilization.updateInterval;)this.physicsTick(),i++;var o;(e(),t())?Jp(Ho(o=this._stabilizationBatch).call(o,this),0):this._finalizeStabilization()}_finalizeStabilization(){this.body.emitter.emit("_allowRedraw"),!0===this.options.stabilization.fit&&this.body.emitter.emit("fit"),!0===this.options.stabilization.onlyDynamicEdges&&this._restoreFrozenNodes(),this.body.emitter.emit("stabilizationIterationsDone"),this.body.emitter.emit("_requestRedraw"),!0===this.stabilized?this._emitStabilized():this.startSimulation(),this.ready=!0}_drawForces(t){for(let e=0;e<this.physicsBody.physicsNodeIndices.length;e++){const i=this.physicsBody.physicsNodeIndices[e],o=this.body.nodes[i],s=this.physicsBody.forces[i],n=20,r=.03,a=Math.sqrt(Math.pow(s.x,2)+Math.pow(s.x,2)),h=Math.min(Math.max(5,a),15),d=3*h,l=Pm((180-180*Math.min(1,Math.max(0,r*a)))/360,1,1),c={x:o.x+n*s.x,y:o.y+n*s.y};t.lineWidth=h,t.strokeStyle=l,t.beginPath(),t.moveTo(o.x,o.y),t.lineTo(c.x,c.y),t.stroke();const u=Math.atan2(s.y,s.x);t.fillStyle=l,Dx.draw(t,{type:"arrow",point:c,angle:u,length:d}),lg(t).call(t)}}}const Jx=new Uint8Array(16);function tE(){if(!yx&&(yx="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!yx))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return yx(Jx)}const eE=[];for(let t=0;t<256;++t)eE.push((t+256).toString(16).slice(1));var iE={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function oE(t,e,i){if(iE.randomUUID&&!e&&!t)return iE.randomUUID();const o=(t=t||{}).random||(t.rng||tE)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,e){i=i||0;for(let t=0;t<16;++t)e[i+t]=o[t];return e}return function(t,e=0){return eE[t[e+0]]+eE[t[e+1]]+eE[t[e+2]]+eE[t[e+3]]+"-"+eE[t[e+4]]+eE[t[e+5]]+"-"+eE[t[e+6]]+eE[t[e+7]]+"-"+eE[t[e+8]]+eE[t[e+9]]+"-"+eE[t[e+10]]+eE[t[e+11]]+eE[t[e+12]]+eE[t[e+13]]+eE[t[e+14]]+eE[t[e+15]]}(o)}class sE{constructor(){}static getRange(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,s=-1e9,n=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],n>e.shape.boundingBox.left&&(n=e.shape.boundingBox.left),r<e.shape.boundingBox.right&&(r=e.shape.boundingBox.right),o>e.shape.boundingBox.top&&(o=e.shape.boundingBox.top),s<e.shape.boundingBox.bottom&&(s=e.shape.boundingBox.bottom);return 1e9===n&&-1e9===r&&1e9===o&&-1e9===s&&(o=0,s=0,n=0,r=0),{minX:n,maxX:r,minY:o,maxY:s}}static getRangeCore(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=1e9,s=-1e9,n=1e9,r=-1e9;if(i.length>0)for(let a=0;a<i.length;a++)e=t[i[a]],n>e.x&&(n=e.x),r<e.x&&(r=e.x),o>e.y&&(o=e.y),s<e.y&&(s=e.y);return 1e9===n&&-1e9===r&&1e9===o&&-1e9===s&&(o=0,s=0,n=0,r=0),{minX:n,maxX:r,minY:o,maxY:s}}static findCenter(t){return{x:.5*(t.maxX+t.minX),y:.5*(t.maxY+t.minY)}}static cloneOptions(t,e){const i={};return void 0===e||"node"===e?(_m(i,t.options,!0),i.x=t.x,i.y=t.y,i.amountOfConnections=t.edges.length):_m(i,t.options,!0),i}}class nE extends dx{constructor(t,e,i,o,s,n){super(t,e,i,o,s,n),this.isCluster=!0,this.containedNodes={},this.containedEdges={}}_openChildCluster(t){const e=this.body.nodes[t];if(void 0===this.containedNodes[t])throw new Error("node with id: "+t+" not in current cluster");if(!e.isCluster)throw new Error("node with id: "+t+" is not a cluster");delete this.containedNodes[t],Om(e.edges,(t=>{delete this.containedEdges[t.id]})),Om(e.containedNodes,((t,e)=>{this.containedNodes[e]=t})),e.containedNodes={},Om(e.containedEdges,((t,e)=>{this.containedEdges[e]=t})),e.containedEdges={},Om(e.edges,(t=>{Om(this.edges,(e=>{var i,o;const s=Mp(i=e.clusteringEdgeReplacingIds).call(i,t.id);-1!==s&&(Om(t.clusteringEdgeReplacingIds,(t=>{e.clusteringEdgeReplacingIds.push(t),this.body.edges[t].edgeReplacedById=e.id})),Jc(o=e.clusteringEdgeReplacingIds).call(o,s,1))}))})),e.edges=[]}}class rE{constructor(t){this.body=t,this.clusteredNodes={},this.clusteredEdges={},this.options={},this.defaultOptions={},wo(this.options,this.defaultOptions),this.body.emitter.on("_resetData",(()=>{this.clusteredNodes={},this.clusteredEdges={}}))}clusterByHubsize(t,e){void 0===t?t=this._getHubSize():"object"==typeof t&&(e=this._checkOptions(t),t=this._getHubSize());const i=[];for(let e=0;e<this.body.nodeIndices.length;e++){const o=this.body.nodes[this.body.nodeIndices[e]];o.edges.length>=t&&i.push(o.id)}for(let t=0;t<i.length;t++)this.clusterByConnection(i[t],e,!0);this.body.emitter.emit("_dataChanged")}cluster(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0===t.joinCondition)throw new Error("Cannot call clusterByNodeData without a joinCondition function in the options.");t=this._checkOptions(t);const i={},o={};Om(this.body.nodes,((e,s)=>{e.options&&!0===t.joinCondition(e.options)&&(i[s]=e,Om(e.edges,(t=>{void 0===this.clusteredEdges[t.id]&&(o[t.id]=t)})))})),this._cluster(i,o,t,e)}clusterByEdgeCount(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e=this._checkOptions(e);const o=[],s={};let n,r,a;for(let i=0;i<this.body.nodeIndices.length;i++){const h={},d={},l=this.body.nodeIndices[i],c=this.body.nodes[l];if(void 0===s[l]){a=0,r=[];for(let t=0;t<c.edges.length;t++)n=c.edges[t],void 0===this.clusteredEdges[n.id]&&(n.toId!==n.fromId&&a++,r.push(n));if(a===t){const t=function(t){if(void 0===e.joinCondition||null===e.joinCondition)return!0;const i=sE.cloneOptions(t);return e.joinCondition(i)};let i=!0;for(let e=0;e<r.length;e++){n=r[e];const o=this._getConnectedId(n,l);if(!t(c)){i=!1;break}d[n.id]=n,h[l]=c,h[o]=this.body.nodes[o],s[l]=!0}if(Jl(h).length>0&&Jl(d).length>0&&!0===i){const t=function(){for(let t=0;t<o.length;++t)for(const e in h)if(void 0!==o[t].nodes[e])return o[t]}();if(void 0!==t){for(const e in h)void 0===t.nodes[e]&&(t.nodes[e]=h[e]);for(const e in d)void 0===t.edges[e]&&(t.edges[e]=d[e])}else o.push({nodes:h,edges:d})}}}}for(let t=0;t<o.length;t++)this._cluster(o[t].nodes,o[t].edges,e,!1);!0===i&&this.body.emitter.emit("_dataChanged")}clusterOutliers(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(1,t,e)}clusterBridges(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clusterByEdgeCount(2,t,e)}clusterByConnection(t,e){var i;let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No nodeId supplied to clusterByConnection!");if(void 0===this.body.nodes[t])throw new Error("The nodeId given to clusterByConnection does not exist!");const s=this.body.nodes[t];void 0===(e=this._checkOptions(e,s)).clusterNodeProperties.x&&(e.clusterNodeProperties.x=s.x),void 0===e.clusterNodeProperties.y&&(e.clusterNodeProperties.y=s.y),void 0===e.clusterNodeProperties.fixed&&(e.clusterNodeProperties.fixed={},e.clusterNodeProperties.fixed.x=s.options.fixed.x,e.clusterNodeProperties.fixed.y=s.options.fixed.y);const n={},r={},a=s.id,h=sE.cloneOptions(s);n[a]=s;for(let t=0;t<s.edges.length;t++){const i=s.edges[t];if(void 0===this.clusteredEdges[i.id]){const t=this._getConnectedId(i,a);if(void 0===this.clusteredNodes[t])if(t!==a)if(void 0===e.joinCondition)r[i.id]=i,n[t]=this.body.nodes[t];else{const o=sE.cloneOptions(this.body.nodes[t]);!0===e.joinCondition(h,o)&&(r[i.id]=i,n[t]=this.body.nodes[t])}else r[i.id]=i}}const d=Zl(i=Jl(n)).call(i,(function(t){return n[t].id}));for(const t in n){if(!Object.prototype.hasOwnProperty.call(n,t))continue;const e=n[t];for(let t=0;t<e.edges.length;t++){const i=e.edges[t];Mp(d).call(d,this._getConnectedId(i,e.id))>-1&&(r[i.id]=i)}}this._cluster(n,r,e,o)}_createClusterEdges(t,e,i,o){let s,n,r,a,h,d;const l=Jl(t),c=[];for(let o=0;o<l.length;o++){n=l[o],r=t[n];for(let o=0;o<r.edges.length;o++)s=r.edges[o],void 0===this.clusteredEdges[s.id]&&(s.toId==s.fromId?e[s.id]=s:s.toId==n?(a=i.id,h=s.fromId,d=h):(a=s.toId,h=i.id,d=a),void 0===t[d]&&c.push({edge:s,fromId:h,toId:a}))}const u=[],p=function(t){for(let e=0;e<u.length;e++){const i=u[e],o=t.fromId===i.fromId&&t.toId===i.toId,s=t.fromId===i.toId&&t.toId===i.fromId;if(o||s)return i}return null};for(let t=0;t<c.length;t++){const e=c[t],i=e.edge;let s=p(e);null===s?(s=this._createClusteredEdge(e.fromId,e.toId,i,o),u.push(s)):s.clusteringEdgeReplacingIds.push(i.id),this.body.edges[i.id].edgeReplacedById=s.id,this._backupEdgeOptions(i),i.setOptions({physics:!1})}}_checkOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return void 0===t.clusterEdgeProperties&&(t.clusterEdgeProperties={}),void 0===t.clusterNodeProperties&&(t.clusterNodeProperties={}),t}_cluster(t,e,i){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const s=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.clusteredNodes[e]&&s.push(e);for(let e=0;e<s.length;++e)delete t[s[e]];if(0==Jl(t).length)return;if(1==Jl(t).length&&1!=i.clusterNodeProperties.allowSingleNodeCluster)return;let n=_m({},i.clusterNodeProperties);if(void 0!==i.processProperties){const o=[];for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const i=sE.cloneOptions(t[e]);o.push(i)}const s=[];for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"clusterEdge:"!==t.substr(0,12)){const i=sE.cloneOptions(e[t],"edge");s.push(i)}if(n=i.processProperties(n,o,s),!n)throw new Error("The processProperties function does not return properties!")}void 0===n.id&&(n.id="cluster:"+oE());const r=n.id;let a;void 0===n.label&&(n.label="cluster"),void 0===n.x&&(a=this._getClusterPosition(t),n.x=a.x),void 0===n.y&&(void 0===a&&(a=this._getClusterPosition(t)),n.y=a.y),n.id=r;const h=this.body.functions.createNode(n,nE);h.containedNodes=t,h.containedEdges=e,h.clusterEdgeProperties=i.clusterEdgeProperties,this.body.nodes[n.id]=h,this._clusterEdges(t,e,n,i.clusterEdgeProperties),n.id=void 0,!0===o&&this.body.emitter.emit("_dataChanged")}_backupEdgeOptions(t){void 0===this.clusteredEdges[t.id]&&(this.clusteredEdges[t.id]={physics:t.options.physics})}_restoreEdge(t){const e=this.clusteredEdges[t.id];void 0!==e&&(t.setOptions({physics:e.physics}),delete this.clusteredEdges[t.id])}isCluster(t){return void 0!==this.body.nodes[t]?!0===this.body.nodes[t].isCluster:(console.error("Node does not exist."),!1)}_getClusterPosition(t){const e=Jl(t);let i,o=t[e[0]].x,s=t[e[0]].x,n=t[e[0]].y,r=t[e[0]].y;for(let a=1;a<e.length;a++)i=t[e[a]],o=i.x<o?i.x:o,s=i.x>s?i.x:s,n=i.y<n?i.y:n,r=i.y>r?i.y:r;return{x:.5*(o+s),y:.5*(n+r)}}openCluster(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(void 0===t)throw new Error("No clusterNodeId supplied to openCluster.");const o=this.body.nodes[t];if(void 0===o)throw new Error("The clusterNodeId supplied to openCluster does not exist.");if(!0!==o.isCluster||void 0===o.containedNodes||void 0===o.containedEdges)throw new Error("The node:"+t+" is not a valid cluster.");const s=this.findNode(t),n=Mp(s).call(s,t)-1;if(n>=0){const e=s[n];return this.body.nodes[e]._openChildCluster(t),delete this.body.nodes[t],void(!0===i&&this.body.emitter.emit("_dataChanged"))}const r=o.containedNodes,a=o.containedEdges;if(void 0!==e&&void 0!==e.releaseFunction&&"function"==typeof e.releaseFunction){const t={},i={x:o.x,y:o.y};for(const e in r)if(Object.prototype.hasOwnProperty.call(r,e)){const i=this.body.nodes[e];t[e]={x:i.x,y:i.y}}const s=e.releaseFunction(i,t);for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];void 0!==s[t]&&(e.x=void 0===s[t].x?o.x:s[t].x,e.y=void 0===s[t].y?o.y:s[t].y)}}else Om(r,(function(t){!1===t.options.fixed.x&&(t.x=o.x),!1===t.options.fixed.y&&(t.y=o.y)}));for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)){const e=this.body.nodes[t];e.vx=o.vx,e.vy=o.vy,e.setOptions({physics:!0}),delete this.clusteredNodes[t]}const h=[];for(let t=0;t<o.edges.length;t++)h.push(o.edges[t]);for(let e=0;e<h.length;e++){const i=h[e],o=this._getConnectedId(i,t),s=this.clusteredNodes[o];for(let t=0;t<i.clusteringEdgeReplacingIds.length;t++){const e=i.clusteringEdgeReplacingIds[t],n=this.body.edges[e];if(void 0!==n)if(void 0!==s){const t=this.body.nodes[s.clusterId];t.containedEdges[n.id]=n,delete a[n.id];let e=n.fromId,i=n.toId;n.toId==o?i=s.clusterId:e=s.clusterId,this._createClusteredEdge(e,i,n,t.clusterEdgeProperties,{hidden:!1,physics:!0})}else this._restoreEdge(n)}i.remove()}for(const t in a)Object.prototype.hasOwnProperty.call(a,t)&&this._restoreEdge(a[t]);delete this.body.nodes[t],!0===i&&this.body.emitter.emit("_dataChanged")}getNodesInCluster(t){const e=[];if(!0===this.isCluster(t)){const i=this.body.nodes[t].containedNodes;for(const t in i)Object.prototype.hasOwnProperty.call(i,t)&&e.push(this.body.nodes[t].id)}return e}findNode(t){const e=[];let i,o=0;for(;void 0!==this.clusteredNodes[t]&&o<100;){if(i=this.body.nodes[t],void 0===i)return[];e.push(i.id),t=this.clusteredNodes[t].clusterId,o++}return i=this.body.nodes[t],void 0===i?[]:(e.push(i.id),kc(e).call(e),e)}updateClusteredNode(t,e){if(void 0===t)throw new Error("No clusteredNodeId supplied to updateClusteredNode.");if(void 0===e)throw new Error("No newOptions supplied to updateClusteredNode.");if(void 0===this.body.nodes[t])throw new Error("The clusteredNodeId supplied to updateClusteredNode does not exist.");this.body.nodes[t].setOptions(e),this.body.emitter.emit("_dataChanged")}updateEdge(t,e){if(void 0===t)throw new Error("No startEdgeId supplied to updateEdge.");if(void 0===e)throw new Error("No newOptions supplied to updateEdge.");if(void 0===this.body.edges[t])throw new Error("The startEdgeId supplied to updateEdge does not exist.");const i=this.getClusteredEdges(t);for(let t=0;t<i.length;t++){this.body.edges[i[t]].setOptions(e)}this.body.emitter.emit("_dataChanged")}getClusteredEdges(t){const e=[];let i=0;for(;void 0!==t&&void 0!==this.body.edges[t]&&i<100;)e.push(this.body.edges[t].id),t=this.body.edges[t].edgeReplacedById,i++;return kc(e).call(e),e}getBaseEdge(t){return this.getBaseEdges(t)[0]}getBaseEdges(t){const e=[t],i=[],o=[];let s=0;for(;e.length>0&&s<100;){const t=e.pop();if(void 0===t)continue;const n=this.body.edges[t];if(void 0===n)continue;s++;const r=n.clusteringEdgeReplacingIds;if(void 0===r)o.push(t);else for(let t=0;t<r.length;++t){const o=r[t];-1===Mp(e).call(e,r)&&-1===Mp(i).call(i,r)&&e.push(o)}i.push(t)}return o}_getConnectedId(t,e){return t.toId!=e?t.toId:(t.fromId,t.fromId)}_getHubSize(){let t=0,e=0,i=0,o=0;for(let s=0;s<this.body.nodeIndices.length;s++){const n=this.body.nodes[this.body.nodeIndices[s]];n.edges.length>o&&(o=n.edges.length),t+=n.edges.length,e+=Math.pow(n.edges.length,2),i+=1}t/=i,e/=i;const s=e-Math.pow(t,2),n=Math.sqrt(s);let r=Math.floor(t+2*n);return r>o&&(r=o),r}_createClusteredEdge(t,e,i,o,s){const n=sE.cloneOptions(i,"edge");_m(n,o),n.from=t,n.to=e,n.id="clusterEdge:"+oE(),void 0!==s&&_m(n,s);const r=this.body.functions.createEdge(n);return r.clusteringEdgeReplacingIds=[i.id],r.connect(),this.body.edges[r.id]=r,r}_clusterEdges(t,e,i,o){if(e instanceof Wx){const t=e,i={};i[t.id]=t,e=i}if(t instanceof dx){const e=t,i={};i[e.id]=e,t=i}if(null==i)throw new Error("_clusterEdges: parameter clusterNode required");void 0===o&&(o=i.clusterEdgeProperties),this._createClusterEdges(t,e,i,o);for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&void 0!==this.body.edges[t]){const e=this.body.edges[t];this._backupEdgeOptions(e),e.setOptions({physics:!1})}for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clusteredNodes[e]={clusterId:i.id,node:this.body.nodes[e]},this.body.nodes[e].setOptions({physics:!1}))}_getClusterNodeForNode(t){if(void 0===t)return;const e=this.clusteredNodes[t];if(void 0===e)return;const i=e.clusterId;return void 0!==i?this.body.nodes[i]:void 0}_filter(t,e){const i=[];return Om(t,(t=>{e(t)&&i.push(t)})),i}_updateState(){let t;const e=[],i={},o=t=>{Om(this.body.nodes,(e=>{!0===e.isCluster&&t(e)}))};for(t in this.clusteredNodes){if(!Object.prototype.hasOwnProperty.call(this.clusteredNodes,t))continue;void 0===this.body.nodes[t]&&e.push(t)}o((function(t){for(let i=0;i<e.length;i++)delete t.containedNodes[e[i]]}));for(let t=0;t<e.length;t++)delete this.clusteredNodes[e[t]];Om(this.clusteredEdges,(t=>{const e=this.body.edges[t];void 0!==e&&e.endPointsValid()||(i[t]=t)})),o((function(t){Om(t.containedEdges,((t,e)=>{t.endPointsValid()||i[e]||(i[e]=e)}))})),Om(this.body.edges,((t,e)=>{let o=!0;const s=t.clusteringEdgeReplacingIds;if(void 0!==s){let t=0;Om(s,(e=>{const i=this.body.edges[e];void 0!==i&&i.endPointsValid()&&(t+=1)})),o=t>0}t.endPointsValid()&&o||(i[e]=e)})),o((t=>{Om(i,(e=>{delete t.containedEdges[e],Om(t.edges,((o,s)=>{o.id!==e?o.clusteringEdgeReplacingIds=this._filter(o.clusteringEdgeReplacingIds,(function(t){return!i[t]})):t.edges[s]=null})),t.edges=this._filter(t.edges,(function(t){return null!==t}))}))})),Om(i,(t=>{delete this.clusteredEdges[t]})),Om(i,(t=>{delete this.body.edges[t]}));Om(Jl(this.body.edges),(t=>{const e=this.body.edges[t],i=this._isClusteredNode(e.fromId)||this._isClusteredNode(e.toId);if(i!==this._isClusteredEdge(e.id))if(i){const t=this._getClusterNodeForNode(e.fromId);void 0!==t&&this._clusterEdges(this.body.nodes[e.fromId],e,t);const i=this._getClusterNodeForNode(e.toId);void 0!==i&&this._clusterEdges(this.body.nodes[e.toId],e,i)}else delete this._clusterEdges[t],this._restoreEdge(e)}));let s=!1,n=!0;for(;n;){const t=[];o((function(e){const i=Jl(e.containedNodes).length,o=!0===e.options.allowSingleNodeCluster;(o&&i<1||!o&&i<2)&&t.push(e.id)}));for(let e=0;e<t.length;++e)this.openCluster(t[e],{},!1);n=t.length>0,s=s||n}s&&this._updateState()}_isClusteredNode(t){return void 0!==this.clusteredNodes[t]}_isClusteredEdge(t){return void 0!==this.clusteredEdges[t]}}class aE{constructor(t,e){!function(){let t;void 0!==window&&(t=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame),window.requestAnimationFrame=void 0===t?function(t){t()}:t}(),this.body=t,this.canvas=e,this.redrawRequested=!1,this.renderTimer=void 0,this.requiresTimeout=!0,this.renderingActive=!1,this.renderRequests=0,this.allowRedraw=!0,this.dragging=!1,this.zooming=!1,this.options={},this.defaultOptions={hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1},wo(this.options,this.defaultOptions),this._determineBrowserMethod(),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.on("dragStart",(()=>{this.dragging=!0})),this.body.emitter.on("dragEnd",(()=>{this.dragging=!1})),this.body.emitter.on("zoom",(()=>{this.zooming=!0,window.clearTimeout(this.zoomTimeoutId),this.zoomTimeoutId=Jp((()=>{var t;this.zooming=!1,Ho(t=this._requestRedraw).call(t,this)()}),250)})),this.body.emitter.on("_resizeNodes",(()=>{this._resizeNodes()})),this.body.emitter.on("_redraw",(()=>{!1===this.renderingActive&&this._redraw()})),this.body.emitter.on("_blockRedraw",(()=>{this.allowRedraw=!1})),this.body.emitter.on("_allowRedraw",(()=>{this.allowRedraw=!0,this.redrawRequested=!1})),this.body.emitter.on("_requestRedraw",Ho(t=this._requestRedraw).call(t,this)),this.body.emitter.on("_startRendering",(()=>{this.renderRequests+=1,this.renderingActive=!0,this._startRendering()})),this.body.emitter.on("_stopRendering",(()=>{this.renderRequests-=1,this.renderingActive=this.renderRequests>0,this.renderTimer=void 0})),this.body.emitter.on("destroy",(()=>{this.renderRequests=0,this.allowRedraw=!1,this.renderingActive=!1,!0===this.requiresTimeout?clearTimeout(this.renderTimer):window.cancelAnimationFrame(this.renderTimer),this.body.emitter.off()}))}setOptions(t){if(void 0!==t){vm(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag"],this.options,t)}}_requestNextFrame(t,e){if("undefined"==typeof window)return;let i;const o=window;return!0===this.requiresTimeout?i=Jp(t,e):o.requestAnimationFrame&&(i=o.requestAnimationFrame(t)),i}_startRendering(){var t;!0===this.renderingActive&&(void 0===this.renderTimer&&(this.renderTimer=this._requestNextFrame(Ho(t=this._renderStep).call(t,this),this.simulationInterval)))}_renderStep(){!0===this.renderingActive&&(this.renderTimer=void 0,!0===this.requiresTimeout&&this._startRendering(),this._redraw(),!1===this.requiresTimeout&&this._startRendering())}redraw(){this.body.emitter.emit("setSize"),this._redraw()}_requestRedraw(){!0!==this.redrawRequested&&!1===this.renderingActive&&!0===this.allowRedraw&&(this.redrawRequested=!0,this._requestNextFrame((()=>{this._redraw(!1)}),0))}_redraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!0===this.allowRedraw){this.body.emitter.emit("initRedraw"),this.redrawRequested=!1;const e={drawExternalLabels:null};0!==this.canvas.frame.canvas.width&&0!==this.canvas.frame.canvas.height||this.canvas.setSize(),this.canvas.setTransform();const i=this.canvas.getContext(),o=this.canvas.frame.canvas.clientWidth,s=this.canvas.frame.canvas.clientHeight;if(i.clearRect(0,0,o,s),0===this.canvas.frame.clientWidth)return;if(i.save(),i.translate(this.body.view.translation.x,this.body.view.translation.y),i.scale(this.body.view.scale,this.body.view.scale),i.beginPath(),this.body.emitter.emit("beforeDrawing",i),i.closePath(),!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawEdges(i),!1===this.dragging||!0===this.dragging&&!1===this.options.hideNodesOnDrag){const{drawExternalLabels:o}=this._drawNodes(i,t);e.drawExternalLabels=o}!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawArrows(i),null!=e.drawExternalLabels&&e.drawExternalLabels(),!1===t&&this._drawSelectionBox(i),i.beginPath(),this.body.emitter.emit("afterDrawing",i),i.closePath(),i.restore(),!0===t&&i.clearRect(0,0,o,s)}}_resizeNodes(){this.canvas.setTransform();const t=this.canvas.getContext();t.save(),t.translate(this.body.view.translation.x,this.body.view.translation.y),t.scale(this.body.view.scale,this.body.view.scale);const e=this.body.nodes;let i;for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(i=e[o],i.resize(t),i.updateBoundingBox(t,i.selected));t.restore()}_drawNodes(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.body.nodes,o=this.body.nodeIndices;let s;const n=[],r=[],a=this.canvas.DOMtoCanvas({x:-20,y:-20}),h=this.canvas.DOMtoCanvas({x:this.canvas.frame.canvas.clientWidth+20,y:this.canvas.frame.canvas.clientHeight+20}),d={top:a.y,left:a.x,bottom:h.y,right:h.x},l=[];for(let a=0;a<o.length;a++)if(s=i[o[a]],s.hover)r.push(o[a]);else if(s.isSelected())n.push(o[a]);else if(!0===e){const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else if(!0===s.isBoundingBoxOverlappingWith(d)){const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else s.updateBoundingBox(t,s.selected);let c;const u=n.length,p=r.length;for(c=0;c<u;c++){s=i[n[c]];const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}for(c=0;c<p;c++){s=i[r[c]];const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}return{drawExternalLabels:()=>{for(const t of l)t()}}}_drawEdges(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const s=e[i[o]];!0===s.connected&&s.draw(t)}}_drawArrows(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const s=e[i[o]];!0===s.connected&&s.drawArrows(t)}}_determineBrowserMethod(){if("undefined"!=typeof window){const t=navigator.userAgent.toLowerCase();this.requiresTimeout=!1,(-1!=Mp(t).call(t,"msie 9.0")||-1!=Mp(t).call(t,"safari")&&Mp(t).call(t,"chrome")<=-1)&&(this.requiresTimeout=!0)}else this.requiresTimeout=!0}_drawSelectionBox(t){if(this.body.selectionBox.show){t.beginPath();const e=this.body.selectionBox.position.end.x-this.body.selectionBox.position.start.x,i=this.body.selectionBox.position.end.y-this.body.selectionBox.position.start.y;t.rect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.fillStyle="rgba(151, 194, 252, 0.2)",t.fillRect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.strokeStyle="rgba(151, 194, 252, 1)",t.stroke()}else t.closePath()}}var hE=o(it.setInterval);function dE(t,e){e.inputHandler=function(t){t.isFirst&&e(t)},t.on("hammer.input",e.inputHandler)}function lE(t,e){return e.inputHandler=function(t){t.isFinal&&e(t)},t.on("hammer.input",e.inputHandler)}class cE{constructor(t){this.body=t,this.pixelRatio=1,this.cameraState={},this.initialized=!1,this.canvasViewCenter={},this._cleanupCallbacks=[],this.options={},this.defaultOptions={autoResize:!0,height:"100%",width:"100%"},wo(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){var t;this.body.emitter.once("resize",(t=>{0!==t.width&&(this.body.view.translation.x=.5*t.width),0!==t.height&&(this.body.view.translation.y=.5*t.height)})),this.body.emitter.on("setSize",Ho(t=this.setSize).call(t,this)),this.body.emitter.on("destroy",(()=>{this.hammerFrame.destroy(),this.hammer.destroy(),this._cleanUp()}))}setOptions(t){if(void 0!==t){vm(["width","height","autoResize"],this.options,t)}if(this._cleanUp(),!0===this.options.autoResize){var e;if(window.ResizeObserver){const t=new ResizeObserver((()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")})),{frame:e}=this;t.observe(e),this._cleanupCallbacks.push((()=>{t.unobserve(e)}))}else{const t=hE((()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")}),1e3);this._cleanupCallbacks.push((()=>{clearInterval(t)}))}const t=Ho(e=this._onResize).call(e,this);window.addEventListener("resize",t),this._cleanupCallbacks.push((()=>{window.removeEventListener("resize",t)}))}}_cleanUp(){var t,e,i;mc(t=kc(e=Jc(i=this._cleanupCallbacks).call(i,0)).call(e)).call(t,(t=>{try{t()}catch(t){console.error(t)}}))}_onResize(){this.setSize(),this.body.emitter.emit("_redraw")}_getCameraState(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.pixelRatio;!0===this.initialized&&(this.cameraState.previousWidth=this.frame.canvas.width/t,this.cameraState.previousHeight=this.frame.canvas.height/t,this.cameraState.scale=this.body.view.scale,this.cameraState.position=this.DOMtoCanvas({x:.5*this.frame.canvas.width/t,y:.5*this.frame.canvas.height/t}))}_setCameraState(){if(void 0!==this.cameraState.scale&&0!==this.frame.canvas.clientWidth&&0!==this.frame.canvas.clientHeight&&0!==this.pixelRatio&&this.cameraState.previousWidth>0&&this.cameraState.previousHeight>0){const t=this.frame.canvas.width/this.pixelRatio/this.cameraState.previousWidth,e=this.frame.canvas.height/this.pixelRatio/this.cameraState.previousHeight;let i=this.cameraState.scale;1!=t&&1!=e?i=.5*this.cameraState.scale*(t+e):1!=t?i=this.cameraState.scale*t:1!=e&&(i=this.cameraState.scale*e),this.body.view.scale=i;const o=this.DOMtoCanvas({x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight}),s={x:o.x-this.cameraState.position.x,y:o.y-this.cameraState.position.y};this.body.view.translation.x+=s.x*this.body.view.scale,this.body.view.translation.y+=s.y*this.body.view.scale}}_prepareValue(t){if("number"==typeof t)return t+"px";if("string"==typeof t){if(-1!==Mp(t).call(t,"%")||-1!==Mp(t).call(t,"px"))return t;if(-1===Mp(t).call(t,"%"))return t+"px"}throw new Error("Could not use the value supplied for width or height:"+t)}_create(){for(;this.body.container.hasChildNodes();)this.body.container.removeChild(this.body.container.firstChild);if(this.frame=document.createElement("div"),this.frame.className="vis-network",this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.tabIndex=0,this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas),this.frame.canvas.getContext)this._setPixelRatio(),this.setTransform();else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t)}this.body.container.appendChild(this.frame),this.body.view.scale=1,this.body.view.translation={x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight},this._bindHammer()}_bindHammer(){void 0!==this.hammer&&this.hammer.destroy(),this.drag={},this.pinch={},this.hammer=new Ym(this.frame.canvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.get("pan").set({threshold:5,direction:Ym.DIRECTION_ALL}),dE(this.hammer,(t=>{this.body.eventListeners.onTouch(t)})),this.hammer.on("tap",(t=>{this.body.eventListeners.onTap(t)})),this.hammer.on("doubletap",(t=>{this.body.eventListeners.onDoubleTap(t)})),this.hammer.on("press",(t=>{this.body.eventListeners.onHold(t)})),this.hammer.on("panstart",(t=>{this.body.eventListeners.onDragStart(t)})),this.hammer.on("panmove",(t=>{this.body.eventListeners.onDrag(t)})),this.hammer.on("panend",(t=>{this.body.eventListeners.onDragEnd(t)})),this.hammer.on("pinch",(t=>{this.body.eventListeners.onPinch(t)})),this.frame.canvas.addEventListener("wheel",(t=>{this.body.eventListeners.onMouseWheel(t)})),this.frame.canvas.addEventListener("mousemove",(t=>{this.body.eventListeners.onMouseMove(t)})),this.frame.canvas.addEventListener("contextmenu",(t=>{this.body.eventListeners.onContext(t)})),this.hammerFrame=new Ym(this.frame),lE(this.hammerFrame,(t=>{this.body.eventListeners.onRelease(t)}))}setSize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.options.width,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.height;t=this._prepareValue(t),e=this._prepareValue(e);let i=!1;const o=this.frame.canvas.width,s=this.frame.canvas.height,n=this.pixelRatio;if(this._setPixelRatio(),t!=this.options.width||e!=this.options.height||this.frame.style.width!=t||this.frame.style.height!=e)this._getCameraState(n),this.frame.style.width=t,this.frame.style.height=e,this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),this.options.width=t,this.options.height=e,this.canvasViewCenter={x:.5*this.frame.clientWidth,y:.5*this.frame.clientHeight},i=!0;else{const t=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),e=Math.round(this.frame.canvas.clientHeight*this.pixelRatio);this.frame.canvas.width===t&&this.frame.canvas.height===e||this._getCameraState(n),this.frame.canvas.width!==t&&(this.frame.canvas.width=t,i=!0),this.frame.canvas.height!==e&&(this.frame.canvas.height=e,i=!0)}return!0===i&&(this.body.emitter.emit("resize",{width:Math.round(this.frame.canvas.width/this.pixelRatio),height:Math.round(this.frame.canvas.height/this.pixelRatio),oldWidth:Math.round(o/this.pixelRatio),oldHeight:Math.round(s/this.pixelRatio)}),this._setCameraState()),this.initialized=!0,i}getContext(){return this.frame.canvas.getContext("2d")}_determinePixelRatio(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");let e=1;"undefined"!=typeof window&&(e=window.devicePixelRatio||1);return e/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)}_setPixelRatio(){this.pixelRatio=this._determinePixelRatio()}setTransform(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}_XconvertDOMtoCanvas(t){return(t-this.body.view.translation.x)/this.body.view.scale}_XconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.x}_YconvertDOMtoCanvas(t){return(t-this.body.view.translation.y)/this.body.view.scale}_YconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.y}canvasToDOM(t){return{x:this._XconvertCanvasToDOM(t.x),y:this._YconvertCanvasToDOM(t.y)}}DOMtoCanvas(t){return{x:this._XconvertDOMtoCanvas(t.x),y:this._YconvertDOMtoCanvas(t.y)}}}class uE{constructor(t,e){var i,o;this.body=t,this.canvas=e,this.animationSpeed=1/this.renderRefreshRate,this.animationEasingFunction="easeInOutQuint",this.easingTime=0,this.sourceScale=0,this.targetScale=0,this.sourceTranslation=0,this.targetTranslation=0,this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0,this.touchTime=0,this.viewFunction=void 0,this.body.emitter.on("fit",Ho(i=this.fit).call(i,this)),this.body.emitter.on("animationFinished",(()=>{this.body.emitter.emit("_stopRendering")})),this.body.emitter.on("unlockNode",Ho(o=this.releaseNode).call(o,this))}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=t}fit(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t=function(t,e){const i=wo({nodes:e,minZoomLevel:Number.MIN_VALUE,maxZoomLevel:1},null!=t?t:{});if(!Vl(i.nodes))throw new TypeError("Nodes has to be an array of ids.");if(0===i.nodes.length&&(i.nodes=e),!("number"==typeof i.minZoomLevel&&i.minZoomLevel>0))throw new TypeError("Min zoom level has to be a number higher than zero.");if(!("number"==typeof i.maxZoomLevel&&i.minZoomLevel<=i.maxZoomLevel))throw new TypeError("Max zoom level has to be a number higher than min zoom level.");return i}(t,this.body.nodeIndices);const i=this.canvas.frame.canvas.clientWidth,o=this.canvas.frame.canvas.clientHeight;let s,n;if(0===i||0===o)n=1,s=sE.getRange(this.body.nodes,t.nodes);else if(!0===e){let e=0;for(const t in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,t)){!0===this.body.nodes[t].predefinedPosition&&(e+=1)}if(e>.5*this.body.nodeIndices.length)return void this.fit(t,!1);s=sE.getRange(this.body.nodes,t.nodes);n=12.662/(this.body.nodeIndices.length+7.4147)+.0964822;n*=Math.min(i/600,o/600)}else{this.body.emitter.emit("_resizeNodes"),s=sE.getRange(this.body.nodes,t.nodes);const e=i/(1.1*Math.abs(s.maxX-s.minX)),r=o/(1.1*Math.abs(s.maxY-s.minY));n=e<=r?e:r}n>t.maxZoomLevel?n=t.maxZoomLevel:n<t.minZoomLevel&&(n=t.minZoomLevel);const r={position:sE.findCenter(s),scale:n,animation:t.animation};this.moveTo(r)}focus(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==this.body.nodes[t]){const i={x:this.body.nodes[t].x,y:this.body.nodes[t].y};e.position=i,e.lockedOnNode=t,this.moveTo(e)}else console.error("Node: "+t+" cannot be found.")}moveTo(t){if(void 0!==t){if(null!=t.offset){if(null!=t.offset.x){if(t.offset.x=+t.offset.x,!Wv(t.offset.x))throw new TypeError('The option "offset.x" has to be a finite number.')}else t.offset.x=0;if(null!=t.offset.y){if(t.offset.y=+t.offset.y,!Wv(t.offset.y))throw new TypeError('The option "offset.y" has to be a finite number.')}else t.offset.x=0}else t.offset={x:0,y:0};if(null!=t.position){if(null!=t.position.x){if(t.position.x=+t.position.x,!Wv(t.position.x))throw new TypeError('The option "position.x" has to be a finite number.')}else t.position.x=0;if(null!=t.position.y){if(t.position.y=+t.position.y,!Wv(t.position.y))throw new TypeError('The option "position.y" has to be a finite number.')}else t.position.x=0}else t.position=this.getViewPosition();if(null!=t.scale){if(t.scale=+t.scale,!(t.scale>0))throw new TypeError('The option "scale" has to be a number greater than zero.')}else t.scale=this.body.view.scale;void 0===t.animation&&(t.animation={duration:0}),!1===t.animation&&(t.animation={duration:0}),!0===t.animation&&(t.animation={}),void 0===t.animation.duration&&(t.animation.duration=1e3),void 0===t.animation.easingFunction&&(t.animation.easingFunction="easeInOutQuad"),this.animateView(t)}else t={}}animateView(t){if(void 0===t)return;this.animationEasingFunction=t.animation.easingFunction,this.releaseNode(),!0===t.locked&&(this.lockedOnNodeId=t.lockedOnNode,this.lockedOnNodeOffset=t.offset),0!=this.easingTime&&this._transitionRedraw(!0),this.sourceScale=this.body.view.scale,this.sourceTranslation=this.body.view.translation,this.targetScale=t.scale,this.body.view.scale=this.targetScale;const e=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i=e.x-t.position.x,o=e.y-t.position.y;var s,n;(this.targetTranslation={x:this.sourceTranslation.x+i*this.targetScale+t.offset.x,y:this.sourceTranslation.y+o*this.targetScale+t.offset.y},0===t.animation.duration)?null!=this.lockedOnNodeId?(this.viewFunction=Ho(s=this._lockedRedraw).call(s,this),this.body.emitter.on("initRedraw",this.viewFunction)):(this.body.view.scale=this.targetScale,this.body.view.translation=this.targetTranslation,this.body.emitter.emit("_requestRedraw")):(this.animationSpeed=1/(60*t.animation.duration*.001)||1/60,this.animationEasingFunction=t.animation.easingFunction,this.viewFunction=Ho(n=this._transitionRedraw).call(n,this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))}_lockedRedraw(){const t=this.body.nodes[this.lockedOnNodeId].x,e=this.body.nodes[this.lockedOnNodeId].y,i=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),o=i.x-t,s=i.y-e,n=this.body.view.translation,r={x:n.x+o*this.body.view.scale+this.lockedOnNodeOffset.x,y:n.y+s*this.body.view.scale+this.lockedOnNodeOffset.y};this.body.view.translation=r}releaseNode(){void 0!==this.lockedOnNodeId&&void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0)}_transitionRedraw(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.easingTime+=this.animationSpeed,this.easingTime=!0===t?1:this.easingTime;const e=Nm[this.animationEasingFunction](this.easingTime);if(this.body.view.scale=this.sourceScale+(this.targetScale-this.sourceScale)*e,this.body.view.translation={x:this.sourceTranslation.x+(this.targetTranslation.x-this.sourceTranslation.x)*e,y:this.sourceTranslation.y+(this.targetTranslation.y-this.sourceTranslation.y)*e},this.easingTime>=1){var i;if(this.body.emitter.off("initRedraw",this.viewFunction),this.easingTime=0,null!=this.lockedOnNodeId)this.viewFunction=Ho(i=this._lockedRedraw).call(i,this),this.body.emitter.on("initRedraw",this.viewFunction);this.body.emitter.emit("animationFinished")}}getScale(){return this.body.view.scale}getViewPosition(){return this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight})}}function pE(t){var e,i=t&&t.preventDefault||!1,o=t&&t.container||window,s={},n={keydown:{},keyup:{}},r={};for(e=97;e<=122;e++)r[String.fromCharCode(e)]={code:e-97+65,shift:!1};for(e=65;e<=90;e++)r[String.fromCharCode(e)]={code:e,shift:!0};for(e=0;e<=9;e++)r[""+e]={code:48+e,shift:!1};for(e=1;e<=12;e++)r["F"+e]={code:111+e,shift:!1};for(e=0;e<=9;e++)r["num"+e]={code:96+e,shift:!1};r["num*"]={code:106,shift:!1},r["num+"]={code:107,shift:!1},r["num-"]={code:109,shift:!1},r["num/"]={code:111,shift:!1},r["num."]={code:110,shift:!1},r.left={code:37,shift:!1},r.up={code:38,shift:!1},r.right={code:39,shift:!1},r.down={code:40,shift:!1},r.space={code:32,shift:!1},r.enter={code:13,shift:!1},r.shift={code:16,shift:void 0},r.esc={code:27,shift:!1},r.backspace={code:8,shift:!1},r.tab={code:9,shift:!1},r.ctrl={code:17,shift:!1},r.alt={code:18,shift:!1},r.delete={code:46,shift:!1},r.pageup={code:33,shift:!1},r.pagedown={code:34,shift:!1},r["="]={code:187,shift:!1},r["-"]={code:189,shift:!1},r["]"]={code:221,shift:!1},r["["]={code:219,shift:!1};var a=function(t){d(t,"keydown")},h=function(t){d(t,"keyup")},d=function(t,e){if(void 0!==n[e][t.keyCode]){for(var o=n[e][t.keyCode],s=0;s<o.length;s++)(void 0===o[s].shift||1==o[s].shift&&1==t.shiftKey||0==o[s].shift&&0==t.shiftKey)&&o[s].fn(t);1==i&&t.preventDefault()}};return s.bind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);void 0===n[i][r[t].code]&&(n[i][r[t].code]=[]),n[i][r[t].code].push({fn:e,shift:r[t].shift})},s.bindAll=function(t,e){for(var i in void 0===e&&(e="keydown"),r)r.hasOwnProperty(i)&&s.bind(i,t,e)},s.getKey=function(t){for(var e in r)if(r.hasOwnProperty(e)){if(1==t.shiftKey&&1==r[e].shift&&t.keyCode==r[e].code)return e;if(0==t.shiftKey&&0==r[e].shift&&t.keyCode==r[e].code)return e;if(t.keyCode==r[e].code&&"shift"==e)return e}return"unknown key, currently not supported"},s.unbind=function(t,e,i){if(void 0===i&&(i="keydown"),void 0===r[t])throw new Error("unsupported key: "+t);if(void 0!==e){var o=[],s=n[i][r[t].code];if(void 0!==s)for(var a=0;a<s.length;a++)s[a].fn==e&&s[a].shift==r[t].shift||o.push(n[i][r[t].code][a]);n[i][r[t].code]=o}else n[i][r[t].code]=[]},s.reset=function(){n={keydown:{},keyup:{}}},s.destroy=function(){n={keydown:{},keyup:{}},o.removeEventListener("keydown",a,!0),o.removeEventListener("keyup",h,!0)},o.addEventListener("keydown",a,!0),o.addEventListener("keyup",h,!0),s}class gE{constructor(t,e){this.body=t,this.canvas=e,this.iconsCreated=!1,this.navigationHammers=[],this.boundFunctions={},this.touchTime=0,this.activated=!1,this.body.emitter.on("activate",(()=>{this.activated=!0,this.configureKeyboardBindings()})),this.body.emitter.on("deactivate",(()=>{this.activated=!1,this.configureKeyboardBindings()})),this.body.emitter.on("destroy",(()=>{void 0!==this.keycharm&&this.keycharm.destroy()})),this.options={}}setOptions(t){void 0!==t&&(this.options=t,this.create())}create(){!0===this.options.navigationButtons?!1===this.iconsCreated&&this.loadNavigationElements():!0===this.iconsCreated&&this.cleanNavigation(),this.configureKeyboardBindings()}cleanNavigation(){if(0!=this.navigationHammers.length){for(let t=0;t<this.navigationHammers.length;t++)this.navigationHammers[t].destroy();this.navigationHammers=[]}this.navigationDOM&&this.navigationDOM.wrapper&&this.navigationDOM.wrapper.parentNode&&this.navigationDOM.wrapper.parentNode.removeChild(this.navigationDOM.wrapper),this.iconsCreated=!1}loadNavigationElements(){this.cleanNavigation(),this.navigationDOM={};const t=["up","down","left","right","zoomIn","zoomOut","zoomExtends"],e=["_moveUp","_moveDown","_moveLeft","_moveRight","_zoomIn","_zoomOut","_fit"];this.navigationDOM.wrapper=document.createElement("div"),this.navigationDOM.wrapper.className="vis-navigation",this.canvas.frame.appendChild(this.navigationDOM.wrapper);for(let s=0;s<t.length;s++){this.navigationDOM[t[s]]=document.createElement("div"),this.navigationDOM[t[s]].className="vis-button vis-"+t[s],this.navigationDOM.wrapper.appendChild(this.navigationDOM[t[s]]);const n=new Ym(this.navigationDOM[t[s]]);var i,o;if("_fit"===e[s])dE(n,Ho(i=this._fit).call(i,this));else dE(n,Ho(o=this.bindToRedraw).call(o,this,e[s]));this.navigationHammers.push(n)}const s=new Ym(this.canvas.frame);lE(s,(()=>{this._stopMovement()})),this.navigationHammers.push(s),this.iconsCreated=!0}bindToRedraw(t){var e;void 0===this.boundFunctions[t]&&(this.boundFunctions[t]=Ho(e=this[t]).call(e,this),this.body.emitter.on("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_startRendering"))}unbindFromRedraw(t){void 0!==this.boundFunctions[t]&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"),delete this.boundFunctions[t])}_fit(){(new Date).valueOf()-this.touchTime>700&&(this.body.emitter.emit("fit",{duration:700}),this.touchTime=(new Date).valueOf())}_stopMovement(){for(const t in this.boundFunctions)Object.prototype.hasOwnProperty.call(this.boundFunctions,t)&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"));this.boundFunctions={}}_moveUp(){this.body.view.translation.y+=this.options.keyboard.speed.y}_moveDown(){this.body.view.translation.y-=this.options.keyboard.speed.y}_moveLeft(){this.body.view.translation.x+=this.options.keyboard.speed.x}_moveRight(){this.body.view.translation.x-=this.options.keyboard.speed.x}_zoomIn(){const t=this.body.view.scale,e=this.body.view.scale*(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,s=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,n=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:s,y:n},this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:null})}_zoomOut(){const t=this.body.view.scale,e=this.body.view.scale/(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,s=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,n=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:s,y:n},this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:null})}configureKeyboardBindings(){var t,e,i,o,s,n,r,a,h,d,l,c,u,p,g,f,m,y,b,v,w,_,x,E;(void 0!==this.keycharm&&this.keycharm.destroy(),!0===this.options.keyboard.enabled)&&(!0===this.options.keyboard.bindToWindow?this.keycharm=pE({container:window,preventDefault:!0}):this.keycharm=pE({container:this.canvas.frame,preventDefault:!0}),this.keycharm.reset(),!0===this.activated&&(Ho(t=this.keycharm).call(t,"up",(()=>{this.bindToRedraw("_moveUp")}),"keydown"),Ho(e=this.keycharm).call(e,"down",(()=>{this.bindToRedraw("_moveDown")}),"keydown"),Ho(i=this.keycharm).call(i,"left",(()=>{this.bindToRedraw("_moveLeft")}),"keydown"),Ho(o=this.keycharm).call(o,"right",(()=>{this.bindToRedraw("_moveRight")}),"keydown"),Ho(s=this.keycharm).call(s,"=",(()=>{this.bindToRedraw("_zoomIn")}),"keydown"),Ho(n=this.keycharm).call(n,"num+",(()=>{this.bindToRedraw("_zoomIn")}),"keydown"),Ho(r=this.keycharm).call(r,"num-",(()=>{this.bindToRedraw("_zoomOut")}),"keydown"),Ho(a=this.keycharm).call(a,"-",(()=>{this.bindToRedraw("_zoomOut")}),"keydown"),Ho(h=this.keycharm).call(h,"[",(()=>{this.bindToRedraw("_zoomOut")}),"keydown"),Ho(d=this.keycharm).call(d,"]",(()=>{this.bindToRedraw("_zoomIn")}),"keydown"),Ho(l=this.keycharm).call(l,"pageup",(()=>{this.bindToRedraw("_zoomIn")}),"keydown"),Ho(c=this.keycharm).call(c,"pagedown",(()=>{this.bindToRedraw("_zoomOut")}),"keydown"),Ho(u=this.keycharm).call(u,"up",(()=>{this.unbindFromRedraw("_moveUp")}),"keyup"),Ho(p=this.keycharm).call(p,"down",(()=>{this.unbindFromRedraw("_moveDown")}),"keyup"),Ho(g=this.keycharm).call(g,"left",(()=>{this.unbindFromRedraw("_moveLeft")}),"keyup"),Ho(f=this.keycharm).call(f,"right",(()=>{this.unbindFromRedraw("_moveRight")}),"keyup"),Ho(m=this.keycharm).call(m,"=",(()=>{this.unbindFromRedraw("_zoomIn")}),"keyup"),Ho(y=this.keycharm).call(y,"num+",(()=>{this.unbindFromRedraw("_zoomIn")}),"keyup"),Ho(b=this.keycharm).call(b,"num-",(()=>{this.unbindFromRedraw("_zoomOut")}),"keyup"),Ho(v=this.keycharm).call(v,"-",(()=>{this.unbindFromRedraw("_zoomOut")}),"keyup"),Ho(w=this.keycharm).call(w,"[",(()=>{this.unbindFromRedraw("_zoomOut")}),"keyup"),Ho(_=this.keycharm).call(_,"]",(()=>{this.unbindFromRedraw("_zoomIn")}),"keyup"),Ho(x=this.keycharm).call(x,"pageup",(()=>{this.unbindFromRedraw("_zoomIn")}),"keyup"),Ho(E=this.keycharm).call(E,"pagedown",(()=>{this.unbindFromRedraw("_zoomOut")}),"keyup")))}}class fE{constructor(t,e,i){var o,s,n,r,a,h,d,l,c,u,p,g,f;this.body=t,this.canvas=e,this.selectionHandler=i,this.navigationHandler=new gE(t,e),this.body.eventListeners.onTap=Ho(o=this.onTap).call(o,this),this.body.eventListeners.onTouch=Ho(s=this.onTouch).call(s,this),this.body.eventListeners.onDoubleTap=Ho(n=this.onDoubleTap).call(n,this),this.body.eventListeners.onHold=Ho(r=this.onHold).call(r,this),this.body.eventListeners.onDragStart=Ho(a=this.onDragStart).call(a,this),this.body.eventListeners.onDrag=Ho(h=this.onDrag).call(h,this),this.body.eventListeners.onDragEnd=Ho(d=this.onDragEnd).call(d,this),this.body.eventListeners.onMouseWheel=Ho(l=this.onMouseWheel).call(l,this),this.body.eventListeners.onPinch=Ho(c=this.onPinch).call(c,this),this.body.eventListeners.onMouseMove=Ho(u=this.onMouseMove).call(u,this),this.body.eventListeners.onRelease=Ho(p=this.onRelease).call(p,this),this.body.eventListeners.onContext=Ho(g=this.onContext).call(g,this),this.touchTime=0,this.drag={},this.pinch={},this.popup=void 0,this.popupObj=void 0,this.popupTimer=void 0,this.body.functions.getPointer=Ho(f=this.getPointer).call(f,this),this.options={},this.defaultOptions={dragNodes:!0,dragView:!0,hover:!1,keyboard:{enabled:!1,speed:{x:10,y:10,zoom:.02},bindToWindow:!0,autoFocus:!0},navigationButtons:!1,tooltipDelay:300,zoomView:!0,zoomSpeed:1},wo(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("destroy",(()=>{clearTimeout(this.popupTimer),delete this.body.functions.getPointer}))}setOptions(t){if(void 0!==t){wm(["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag","keyboard","multiselect","selectable","selectConnectedEdges"],this.options,t),zm(this.options,t,"keyboard"),t.tooltip&&(wo(this.options.tooltip,t.tooltip),t.tooltip.color&&(this.options.tooltip.color=Tm(t.tooltip.color)))}this.navigationHandler.setOptions(this.options)}getPointer(t){return{x:t.x-(e=this.canvas.frame.canvas,e.getBoundingClientRect().left),y:t.y-Em(this.canvas.frame.canvas)};var e}onTouch(t){(new Date).valueOf()-this.touchTime>50&&(this.drag.pointer=this.getPointer(t.center),this.drag.pinched=!1,this.pinch.scale=this.body.view.scale,this.touchTime=(new Date).valueOf())}onTap(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect&&(t.changedPointers[0].ctrlKey||t.changedPointers[0].metaKey);this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e)}onDoubleTap(t){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("doubleClick",t,e)}onHold(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect;this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e),this.selectionHandler.generateClickEvent("hold",t,e)}onRelease(t){if((new Date).valueOf()-this.touchTime>10){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("release",t,e),this.touchTime=(new Date).valueOf()}}onContext(t){const e=this.getPointer({x:t.clientX,y:t.clientY});this.selectionHandler.generateClickEvent("oncontext",t,e)}checkSelectionChanges(t){!0===(arguments.length>1&&void 0!==arguments[1]&&arguments[1])?this.selectionHandler.selectAdditionalOnPoint(t):this.selectionHandler.selectOnPoint(t)}_determineDifference(t,e){const i=function(t,e){const i=[];for(let o=0;o<t.length;o++){const s=t[o];-1===Mp(e).call(e,s)&&i.push(s)}return i};return{nodes:i(t.nodes,e.nodes),edges:i(t.edges,e.edges)}}onDragStart(t){if(this.drag.dragging)return;void 0===this.drag.pointer&&this.onTouch(t);const e=this.selectionHandler.getNodeAt(this.drag.pointer);if(this.drag.dragging=!0,this.drag.selection=[],this.drag.translation=wo({},this.body.view.translation),this.drag.nodeId=void 0,t.srcEvent.shiftKey){this.body.selectionBox.show=!0;const e=this.getPointer(t.center);this.body.selectionBox.position.start={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)}}else if(void 0!==e&&!0===this.options.dragNodes){this.drag.nodeId=e.id,!1===e.isSelected()&&this.selectionHandler.setSelection({nodes:[e.id]}),this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer);for(const t of this.selectionHandler.getSelectedNodes()){const e={id:t.id,node:t,x:t.x,y:t.y,xFixed:t.options.fixed.x,yFixed:t.options.fixed.y};t.options.fixed.x=!0,t.options.fixed.y=!0,this.drag.selection.push(e)}}else this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer,void 0,!0)}onDrag(t){if(!0===this.drag.pinched)return;this.body.emitter.emit("unlockNode");const e=this.getPointer(t.center),i=this.drag.selection;if(i&&i.length&&!0===this.options.dragNodes){this.selectionHandler.generateClickEvent("dragging",t,e);const o=e.x-this.drag.pointer.x,s=e.y-this.drag.pointer.y;mc(i).call(i,(t=>{const e=t.node;!1===t.xFixed&&(e.x=this.canvas._XconvertDOMtoCanvas(this.canvas._XconvertCanvasToDOM(t.x)+o)),!1===t.yFixed&&(e.y=this.canvas._YconvertDOMtoCanvas(this.canvas._YconvertCanvasToDOM(t.y)+s))})),this.body.emitter.emit("startSimulation")}else{if(t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.emitter.emit("_requestRedraw")}if(!0===this.options.dragView&&!t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);const i=e.x-this.drag.pointer.x,o=e.y-this.drag.pointer.y;this.body.view.translation={x:this.drag.translation.x+i,y:this.drag.translation.y+o},this.body.emitter.emit("_requestRedraw")}}}onDragEnd(t){if(this.drag.dragging=!1,this.body.selectionBox.show){var e;this.body.selectionBox.show=!1;const i=this.body.selectionBox.position,o={minX:Math.min(i.start.x,i.end.x),minY:Math.min(i.start.y,i.end.y),maxX:Math.max(i.start.x,i.end.x),maxY:Math.max(i.start.y,i.end.y)},s=Ru(e=this.body.nodeIndices).call(e,(t=>{const e=this.body.nodes[t];return e.x>=o.minX&&e.x<=o.maxX&&e.y>=o.minY&&e.y<=o.maxY}));mc(s).call(s,(t=>this.selectionHandler.selectObject(this.body.nodes[t])));const n=this.getPointer(t.center);this.selectionHandler.commitAndEmit(n,t),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw")}else{const e=this.drag.selection;e&&e.length?(mc(e).call(e,(function(t){t.node.options.fixed.x=t.xFixed,t.node.options.fixed.y=t.yFixed})),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center)),this.body.emitter.emit("startSimulation")):(this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw"))}}onPinch(t){const e=this.getPointer(t.center);this.drag.pinched=!0,void 0===this.pinch.scale&&(this.pinch.scale=1);const i=this.pinch.scale*t.scale;this.zoom(i,e)}zoom(t,e){if(!0===this.options.zoomView){const i=this.body.view.scale;let o;t<1e-5&&(t=1e-5),t>10&&(t=10),void 0!==this.drag&&!0===this.drag.dragging&&(o=this.canvas.DOMtoCanvas(this.drag.pointer));const s=this.body.view.translation,n=t/i,r=(1-n)*e.x+s.x*n,a=(1-n)*e.y+s.y*n;if(this.body.view.scale=t,this.body.view.translation={x:r,y:a},null!=o){const t=this.canvas.canvasToDOM(o);this.drag.pointer.x=t.x,this.drag.pointer.y=t.y}this.body.emitter.emit("_requestRedraw"),i<t?this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:e}):this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:e})}}onMouseWheel(t){if(!0===this.options.zoomView){if(0!==t.deltaY){let e=this.body.view.scale;e*=1+(t.deltaY<0?1:-1)*(.1*this.options.zoomSpeed);const i=this.getPointer({x:t.clientX,y:t.clientY});this.zoom(e,i)}t.preventDefault()}}onMouseMove(t){const e=this.getPointer({x:t.clientX,y:t.clientY});let i=!1;void 0!==this.popup&&(!1===this.popup.hidden&&this._checkHidePopup(e),!1===this.popup.hidden&&(i=!0,this.popup.setPosition(e.x+3,e.y-5),this.popup.show())),this.options.keyboard.autoFocus&&!1===this.options.keyboard.bindToWindow&&!0===this.options.keyboard.enabled&&this.canvas.frame.focus(),!1===i&&(void 0!==this.popupTimer&&(clearInterval(this.popupTimer),this.popupTimer=void 0),this.drag.dragging||(this.popupTimer=Jp((()=>this._checkShowPopup(e)),this.options.tooltipDelay))),!0===this.options.hover&&this.selectionHandler.hoverObject(t,e)}_checkShowPopup(t){const e=this.canvas._XconvertDOMtoCanvas(t.x),i=this.canvas._YconvertDOMtoCanvas(t.y),o={left:e,top:i,right:e,bottom:i},s=void 0===this.popupObj?void 0:this.popupObj.id;let n=!1,r="node";if(void 0===this.popupObj){const t=this.body.nodeIndices,e=this.body.nodes;let i;const s=[];for(let r=0;r<t.length;r++)i=e[t[r]],!0===i.isOverlappingWith(o)&&(n=!0,void 0!==i.getTitle()&&s.push(t[r]));s.length>0&&(this.popupObj=e[s[s.length-1]],n=!0)}if(void 0===this.popupObj&&!1===n){const t=this.body.edgeIndices,e=this.body.edges;let i;const s=[];for(let n=0;n<t.length;n++)i=e[t[n]],!0===i.isOverlappingWith(o)&&!0===i.connected&&void 0!==i.getTitle()&&s.push(t[n]);s.length>0&&(this.popupObj=e[s[s.length-1]],r="edge")}void 0!==this.popupObj?this.popupObj.id!==s&&(void 0===this.popup&&(this.popup=new Xm(this.canvas.frame)),this.popup.popupTargetType=r,this.popup.popupTargetId=this.popupObj.id,this.popup.setPosition(t.x+3,t.y-5),this.popup.setText(this.popupObj.getTitle()),this.popup.show(),this.body.emitter.emit("showPopup",this.popupObj.id)):void 0!==this.popup&&(this.popup.hide(),this.body.emitter.emit("hidePopup"))}_checkHidePopup(t){const e=this.selectionHandler._pointerToPositionObject(t);let i=!1;if("node"===this.popup.popupTargetType){if(void 0!==this.body.nodes[this.popup.popupTargetId]&&(i=this.body.nodes[this.popup.popupTargetId].isOverlappingWith(e),!0===i)){const e=this.selectionHandler.getNodeAt(t);i=void 0!==e&&e.id===this.popup.popupTargetId}}else void 0===this.selectionHandler.getNodeAt(t)&&void 0!==this.body.edges[this.popup.popupTargetId]&&(i=this.body.edges[this.popup.popupTargetId].isOverlappingWith(e));!1===i&&(this.popupObj=void 0,this.popup.hide(),this.body.emitter.emit("hidePopup"))}}Jb("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),wv);var mE=o(it.Set),yE=y,bE=ev,vE=eb.getWeakData,wE=Nb,_E=oi,xE=Y,EE=et,OE=Bb,CE=Jt,kE=Ar.set,SE=Ar.getterFor,TE=Ur.find,ME=Ur.findIndex,DE=yE([].splice),PE=0,IE=function(t){return t.frozen||(t.frozen=new BE)},BE=function(){this.entries=[]},FE=function(t,e){return TE(t.entries,(function(t){return t[0]===e}))};BE.prototype={get:function(t){var e=FE(this,t);if(e)return e[1]},has:function(t){return!!FE(this,t)},set:function(t,e){var i=FE(this,t);i?i[1]=e:this.entries.push([t,e])},delete:function(t){var e=ME(this.entries,(function(e){return e[0]===t}));return~e&&DE(this.entries,e,1),!!~e}};var zE,NE={getConstructor:function(t,e,i,o){var s=t((function(t,s){wE(t,n),kE(t,{type:e,id:PE++,frozen:void 0}),xE(s)||OE(s,t[o],{that:t,AS_ENTRIES:i})})),n=s.prototype,r=SE(e),a=function(t,e,i){var o=r(t),s=vE(_E(e),!0);return!0===s?IE(o).set(e,i):s[o.id]=i,t};return bE(n,{delete:function(t){var e=r(this);if(!EE(t))return!1;var i=vE(t);return!0===i?IE(e).delete(t):i&&CE(i,e.id)&&delete i[e.id]},has:function(t){var e=r(this);if(!EE(t))return!1;var i=vE(t);return!0===i?IE(e).has(t):i&&CE(i,e.id)}}),bE(n,i?{get:function(t){var e=r(this);if(EE(t)){var i=vE(t);return!0===i?IE(e).get(t):i?i[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),s}},AE=jy,RE=n,jE=y,LE=ev,HE=eb,WE=Jb,VE=NE,qE=et,UE=Ar.enforce,YE=r,XE=Er,KE=Object,GE=Array.isArray,ZE=KE.isExtensible,QE=KE.isFrozen,$E=KE.isSealed,JE=KE.freeze,tO=KE.seal,eO={},iO={},oO=!RE.ActiveXObject&&"ActiveXObject"in RE,sO=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},nO=WE("WeakMap",sO,VE),rO=nO.prototype,aO=jE(rO.set);if(XE)if(oO){zE=VE.getConstructor(sO,"WeakMap",!0),HE.enable();var hO=jE(rO.delete),dO=jE(rO.has),lO=jE(rO.get);LE(rO,{delete:function(t){if(qE(t)&&!ZE(t)){var e=UE(this);return e.frozen||(e.frozen=new zE),hO(this,t)||e.frozen.delete(t)}return hO(this,t)},has:function(t){if(qE(t)&&!ZE(t)){var e=UE(this);return e.frozen||(e.frozen=new zE),dO(this,t)||e.frozen.has(t)}return dO(this,t)},get:function(t){if(qE(t)&&!ZE(t)){var e=UE(this);return e.frozen||(e.frozen=new zE),dO(this,t)?lO(this,t):e.frozen.get(t)}return lO(this,t)},set:function(t,e){if(qE(t)&&!ZE(t)){var i=UE(this);i.frozen||(i.frozen=new zE),dO(this,t)?aO(this,t,e):i.frozen.set(t,e)}else aO(this,t,e);return this}})}else AE&&YE((function(){var t=JE([]);return aO(new nO,t,1),!QE(t)}))&&LE(rO,{set:function(t,e){var i;return GE(t)&&(QE(t)?i=eO:$E(t)&&(i=iO)),aO(this,t,e),i===eO&&JE(t),i===iO&&tO(t),this}});var cO,uO,pO,gO,fO,mO=o(it.WeakMap);function yO(t,e,i,o){if("a"===i&&!o)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!o:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?o:"a"===i?o.call(t):o?o.value:e.get(t)}function bO(t,e,i,o,s){if("m"===o)throw new TypeError("Private method is not writable");if("a"===o&&!s)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?s.call(t,i):s?s.value=i:e.set(t,i),i}function vO(t,e){const i=new mE;for(const o of e)t.has(o)||i.add(o);return i}"function"==typeof SuppressedError&&SuppressedError;class wO{constructor(){cO.set(this,new mE),uO.set(this,new mE)}get size(){return yO(this,uO,"f").size}add(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)yO(this,uO,"f").add(t)}delete(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];for(const t of e)yO(this,uO,"f").delete(t)}clear(){yO(this,uO,"f").clear()}getSelection(){return[...yO(this,uO,"f")]}getChanges(){return{added:[...vO(yO(this,cO,"f"),yO(this,uO,"f"))],deleted:[...vO(yO(this,uO,"f"),yO(this,cO,"f"))],previous:[...new mE(yO(this,cO,"f"))],current:[...new mE(yO(this,uO,"f"))]}}commit(){const t=this.getChanges();bO(this,cO,yO(this,uO,"f"),"f"),bO(this,uO,new mE(yO(this,cO,"f")),"f");for(const e of t.added)e.select();for(const e of t.deleted)e.unselect();return t}}cO=new mO,uO=new mO;class _O{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};pO.set(this,new wO),gO.set(this,new wO),fO.set(this,void 0),bO(this,fO,t,"f")}get sizeNodes(){return yO(this,pO,"f").size}get sizeEdges(){return yO(this,gO,"f").size}getNodes(){return yO(this,pO,"f").getSelection()}getEdges(){return yO(this,gO,"f").getSelection()}addNodes(){yO(this,pO,"f").add(...arguments)}addEdges(){yO(this,gO,"f").add(...arguments)}deleteNodes(t){yO(this,pO,"f").delete(t)}deleteEdges(t){yO(this,gO,"f").delete(t)}clear(){yO(this,pO,"f").clear(),yO(this,gO,"f").clear()}commit(){const t={nodes:yO(this,pO,"f").commit(),edges:yO(this,gO,"f").commit()};for(var e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];return yO(this,fO,"f").call(this,t,...i),t}}pO=new mO,gO=new mO,fO=new mO;class xO{constructor(t,e){this.body=t,this.canvas=e,this._selectionAccumulator=new _O,this.hoverObj={nodes:{},edges:{}},this.options={},this.defaultOptions={multiselect:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0},wo(this.options,this.defaultOptions),this.body.emitter.on("_dataChanged",(()=>{this.updateSelection()}))}setOptions(t){if(void 0!==t){vm(["multiselect","hoverConnectedEdges","selectable","selectConnectedEdges"],this.options,t)}}selectOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);this.unselectAll(),void 0!==i&&(e=this.selectObject(i)),this.body.emitter.emit("_requestRedraw")}return e}selectAdditionalOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);void 0!==i&&(e=!0,!0===i.isSelected()?this.deselectObject(i):this.selectObject(i),this.body.emitter.emit("_requestRedraw"))}return e}_initBaseEvent(t,e){const i={};return i.pointer={DOM:{x:e.x,y:e.y},canvas:this.canvas.DOMtoCanvas(e)},i.event=t,i}generateClickEvent(t,e,i,o){let s=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const n=this._initBaseEvent(e,i);if(!0===s)n.nodes=[],n.edges=[];else{const t=this.getSelection();n.nodes=t.nodes,n.edges=t.edges}void 0!==o&&(n.previousSelection=o),"click"==t&&(n.items=this.getClickedItems(i)),void 0!==e.controlEdge&&(n.controlEdge=e.controlEdge),this.body.emitter.emit(t,n)}selectObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.options.selectConnectedEdges;return void 0!==t&&(t instanceof dx?(!0===e&&this._selectionAccumulator.addEdges(...t.edges),this._selectionAccumulator.addNodes(t)):this._selectionAccumulator.addEdges(t),!0)}deselectObject(t){!0===t.isSelected()&&(t.selected=!1,this._removeFromSelection(t))}_getAllNodesOverlappingWith(t){const e=[],i=this.body.nodes;for(let o=0;o<this.body.nodeIndices.length;o++){const s=this.body.nodeIndices[o];i[s].isOverlappingWith(t)&&e.push(s)}return e}_pointerToPositionObject(t){const e=this.canvas.DOMtoCanvas(t);return{left:e.x-1,top:e.y+1,right:e.x+1,bottom:e.y-1}}getNodeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this._pointerToPositionObject(t),o=this._getAllNodesOverlappingWith(i);return o.length>0?!0===e?this.body.nodes[o[o.length-1]]:o[o.length-1]:void 0}_getEdgesOverlappingWith(t,e){const i=this.body.edges;for(let o=0;o<this.body.edgeIndices.length;o++){const s=this.body.edgeIndices[o];i[s].isOverlappingWith(t)&&e.push(s)}}_getAllEdgesOverlappingWith(t){const e=[];return this._getEdgesOverlappingWith(t,e),e}getEdgeAt(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const i=this.canvas.DOMtoCanvas(t);let o=10,s=null;const n=this.body.edges;for(let t=0;t<this.body.edgeIndices.length;t++){const e=this.body.edgeIndices[t],r=n[e];if(r.connected){const t=r.from.x,n=r.from.y,a=r.to.x,h=r.to.y,d=r.edgeType.getDistanceToEdge(t,n,a,h,i.x,i.y);d<o&&(s=e,o=d)}}return null!==s?!0===e?this.body.edges[s]:s:void 0}_addToHover(t){t instanceof dx?this.hoverObj.nodes[t.id]=t:this.hoverObj.edges[t.id]=t}_removeFromSelection(t){t instanceof dx?(this._selectionAccumulator.deleteNodes(t),this._selectionAccumulator.deleteEdges(...t.edges)):this._selectionAccumulator.deleteEdges(t)}unselectAll(){this._selectionAccumulator.clear()}getSelectedNodeCount(){return this._selectionAccumulator.sizeNodes}getSelectedEdgeCount(){return this._selectionAccumulator.sizeEdges}_hoverConnectedEdges(t){for(let e=0;e<t.edges.length;e++){const i=t.edges[e];i.hover=!0,this._addToHover(i)}}emitBlurEvent(t,e,i){const o=this._initBaseEvent(t,e);!0===i.hover&&(i.hover=!1,i instanceof dx?(o.node=i.id,this.body.emitter.emit("blurNode",o)):(o.edge=i.id,this.body.emitter.emit("blurEdge",o)))}emitHoverEvent(t,e,i){const o=this._initBaseEvent(t,e);let s=!1;return!1===i.hover&&(i.hover=!0,this._addToHover(i),s=!0,i instanceof dx?(o.node=i.id,this.body.emitter.emit("hoverNode",o)):(o.edge=i.id,this.body.emitter.emit("hoverEdge",o))),s}hoverObject(t,e){let i=this.getNodeAt(e);void 0===i&&(i=this.getEdgeAt(e));let o=!1;for(const s in this.hoverObj.nodes)Object.prototype.hasOwnProperty.call(this.hoverObj.nodes,s)&&(void 0===i||i instanceof dx&&i.id!=s||i instanceof Wx)&&(this.emitBlurEvent(t,e,this.hoverObj.nodes[s]),delete this.hoverObj.nodes[s],o=!0);for(const s in this.hoverObj.edges)Object.prototype.hasOwnProperty.call(this.hoverObj.edges,s)&&(!0===o?(this.hoverObj.edges[s].hover=!1,delete this.hoverObj.edges[s]):(void 0===i||i instanceof Wx&&i.id!=s||i instanceof dx&&!i.hover)&&(this.emitBlurEvent(t,e,this.hoverObj.edges[s]),delete this.hoverObj.edges[s],o=!0));if(void 0!==i){const s=Jl(this.hoverObj.edges).length,n=Jl(this.hoverObj.nodes).length;(o||i instanceof Wx&&0===s&&0===n||i instanceof dx&&0===s&&0===n)&&(o=this.emitHoverEvent(t,e,i)),i instanceof dx&&!0===this.options.hoverConnectedEdges&&this._hoverConnectedEdges(i)}!0===o&&this.body.emitter.emit("_requestRedraw")}commitWithoutEmitting(){this._selectionAccumulator.commit()}commitAndEmit(t,e){let i=!1;const o=this._selectionAccumulator.commit(),s={nodes:o.nodes.previous,edges:o.edges.previous};o.edges.deleted.length>0&&(this.generateClickEvent("deselectEdge",e,t,s),i=!0),o.nodes.deleted.length>0&&(this.generateClickEvent("deselectNode",e,t,s),i=!0),o.nodes.added.length>0&&(this.generateClickEvent("selectNode",e,t),i=!0),o.edges.added.length>0&&(this.generateClickEvent("selectEdge",e,t),i=!0),!0===i&&this.generateClickEvent("select",e,t)}getSelection(){return{nodes:this.getSelectedNodeIds(),edges:this.getSelectedEdgeIds()}}getSelectedNodes(){return this._selectionAccumulator.getNodes()}getSelectedEdges(){return this._selectionAccumulator.getEdges()}getSelectedNodeIds(){var t;return Zl(t=this._selectionAccumulator.getNodes()).call(t,(t=>t.id))}getSelectedEdgeIds(){var t;return Zl(t=this._selectionAccumulator.getEdges()).call(t,(t=>t.id))}setSelection(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||!t.nodes&&!t.edges)throw new TypeError("Selection must be an object with nodes and/or edges properties");if((e.unselectAll||void 0===e.unselectAll)&&this.unselectAll(),t.nodes)for(const i of t.nodes){const t=this.body.nodes[i];if(!t)throw new RangeError('Node with id "'+i+'" not found');this.selectObject(t,e.highlightEdges)}if(t.edges)for(const e of t.edges){const t=this.body.edges[e];if(!t)throw new RangeError('Edge with id "'+e+'" not found');this.selectObject(t)}this.body.emitter.emit("_requestRedraw"),this._selectionAccumulator.commit()}selectNodes(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({nodes:t},{highlightEdges:e})}selectEdges(t){if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({edges:t})}updateSelection(){for(const t in this._selectionAccumulator.getNodes())Object.prototype.hasOwnProperty.call(this.body.nodes,t.id)||this._selectionAccumulator.deleteNodes(t);for(const t in this._selectionAccumulator.getEdges())Object.prototype.hasOwnProperty.call(this.body.edges,t.id)||this._selectionAccumulator.deleteEdges(t)}getClickedItems(t){const e=this.canvas.DOMtoCanvas(t),i=[],o=this.body.nodeIndices,s=this.body.nodes;for(let t=o.length-1;t>=0;t--){const n=s[o[t]].getItemsOnPoint(e);i.push.apply(i,n)}const n=this.body.edgeIndices,r=this.body.edges;for(let t=n.length-1;t>=0;t--){const o=r[n[t]].getItemsOnPoint(e);i.push.apply(i,o)}return i}}var EO=Hn,OO=Math.floor,CO=function(t,e){var i=t.length,o=OO(i/2);return i<8?kO(t,e):SO(t,CO(EO(t,0,o),e),CO(EO(t,o),e),e)},kO=function(t,e){for(var i,o,s=t.length,n=1;n<s;){for(o=n,i=t[n];o&&e(t[o-1],i)>0;)t[o]=t[--o];o!==n++&&(t[o]=i)}return t},SO=function(t,e,i,o){for(var s=e.length,n=i.length,r=0,a=0;r<s||a<n;)t[r+a]=r<s&&a<n?o(e[r],i[a])<=0?e[r++]:i[a++]:r<s?e[r++]:i[a++];return t},TO=CO,MO=dt.match(/firefox\/(\d+)/i),DO=!!MO&&+MO[1],PO=/MSIE|Trident/.test(dt),IO=dt.match(/AppleWebKit\/(\d+)\./),BO=!!IO&&+IO[1],FO=Mi,zO=y,NO=It,AO=Zt,RO=Hi,jO=Fc,LO=nn,HO=r,WO=TO,VO=nc,qO=DO,UO=PO,YO=mt,XO=BO,KO=[],GO=zO(KO.sort),ZO=zO(KO.push),QO=HO((function(){KO.sort(void 0)})),$O=HO((function(){KO.sort(null)})),JO=VO("sort"),tC=!HO((function(){if(YO)return YO<70;if(!(qO&&qO>3)){if(UO)return!0;if(XO)return XO<603;var t,e,i,o,s="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(o=0;o<47;o++)KO.push({k:e+o,v:i})}for(KO.sort((function(t,e){return e.v-t.v})),o=0;o<KO.length;o++)e=KO[o].k.charAt(0),s.charAt(s.length-1)!==e&&(s+=e);return"DGBEFHACIJK"!==s}}));FO({target:"Array",proto:!0,forced:QO||!$O||!JO||!tC},{sort:function(t){void 0!==t&&NO(t);var e=AO(this);if(tC)return void 0===t?GO(e):GO(e,t);var i,o,s=[],n=RO(e);for(o=0;o<n;o++)o in e&&ZO(s,e[o]);for(WO(s,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:LO(e)>LO(i)?1:-1}}(t)),i=RO(s),o=0;o<i;)e[o]=s[o++];for(;o<n;)jO(e,o++);return e}});var eC=zo("Array").sort,iC=ht,oC=eC,sC=Array.prototype,nC=function(t){var e=t.sort;return t===sC||iC(sC,t)&&e===sC.sort?oC:e},rC=o(nC),aC=It,hC=Zt,dC=U,lC=Hi,cC=TypeError,uC=function(t){return function(e,i,o,s){aC(i);var n=hC(e),r=dC(n),a=lC(n),h=t?a-1:0,d=t?-1:1;if(o<2)for(;;){if(h in r){s=r[h],h+=d;break}if(h+=d,t?h<0:a<=h)throw new cC("Reduce of empty array with no initial value")}for(;t?h>=0:a>h;h+=d)h in r&&(s=i(s,r[h],h,n));return s}},pC={left:uC(!1),right:uC(!0)},gC="process"===_(n.process),fC=pC.left;Mi({target:"Array",proto:!0,forced:!gC&&mt>79&&mt<83||!nc("reduce")},{reduce:function(t){var e=arguments.length;return fC(this,t,e,e>1?arguments[1]:void 0)}});var mC=zo("Array").reduce,yC=ht,bC=mC,vC=Array.prototype,wC=function(t){var e=t.reduce;return t===vC||yC(vC,t)&&e===vC.reduce?bC:e},_C=o(wC);class xC{abstract(){throw new Error("Can't instantiate abstract class!")}fake_use(){}curveType(){return this.abstract()}getPosition(t){return this.fake_use(t),this.abstract()}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;this.fake_use(t,e,i),this.abstract()}getTreeSize(t){return this.fake_use(t),this.abstract()}sort(t){this.fake_use(t),this.abstract()}fix(t,e){this.fake_use(t,e),this.abstract()}shift(t,e){this.fake_use(t,e),this.abstract()}}class EC extends xC{constructor(t){super(),this.layout=t}curveType(){return"horizontal"}getPosition(t){return t.x}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.x=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_x,max:e.max_x}}sort(t){rC(t).call(t,(function(t,e){return t.x-e.x}))}fix(t,e){t.y=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.y=!0}shift(t,e){this.layout.body.nodes[t].x+=e}}class OC extends xC{constructor(t){super(),this.layout=t}curveType(){return"vertical"}getPosition(t){return t.y}setPosition(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.y=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_y,max:e.max_y}}sort(t){rC(t).call(t,(function(t,e){return t.y-e.y}))}fix(t,e){t.x=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.x=!0}shift(t,e){this.layout.body.nodes[t].y+=e}}var CC=Ur.every;Mi({target:"Array",proto:!0,forced:!nc("every")},{every:function(t){return CC(this,t,arguments.length>1?arguments[1]:void 0)}});var kC=zo("Array").every,SC=ht,TC=kC,MC=Array.prototype,DC=function(t){var e=t.every;return t===MC||SC(MC,t)&&e===MC.every?TC:e},PC=o(DC);function IC(t,e){const i=new mE;return mc(t).call(t,(t=>{var e;mc(e=t.edges).call(e,(t=>{t.connected&&i.add(t)}))})),mc(i).call(i,(t=>{const i=t.from.id,o=t.to.id;null==e[i]&&(e[i]=0),(null==e[o]||e[i]>=e[o])&&(e[o]=e[i]+1)})),e}function BC(t,e,i,o){var s;const n=Pp(null),r=_C(s=[...B_(o).call(o)]).call(s,((t,e)=>t+1+e.edges.length),0),a=i+"Id",h="to"===i?1:-1;for(const[s,c]of o){if(!o.has(s)||!t(c))continue;n[s]=0;const u=[c];let p,g=0;for(;p=u.pop();){var d,l;if(!o.has(s))continue;const t=n[p.id]+h;if(mc(d=Ru(l=p.edges).call(l,(t=>t.connected&&t.to!==t.from&&t[i]!==p&&o.has(t.toId)&&o.has(t.fromId)))).call(d,(o=>{const s=o[a],r=n[s];(null==r||e(t,r))&&(n[s]=t,u.push(o[i]))})),g>r)return IC(o,n);++g}}return n}class FC{constructor(){this.childrenReference={},this.parentReference={},this.trees={},this.distributionOrdering={},this.levels={},this.distributionIndex={},this.isTree=!1,this.treeIndex=-1}addRelation(t,e){void 0===this.childrenReference[t]&&(this.childrenReference[t]=[]),this.childrenReference[t].push(e),void 0===this.parentReference[e]&&(this.parentReference[e]=[]),this.parentReference[e].push(t)}checkIfTree(){for(const t in this.parentReference)if(this.parentReference[t].length>1)return void(this.isTree=!1);this.isTree=!0}numTrees(){return this.treeIndex+1}setTreeIndex(t,e){void 0!==e&&void 0===this.trees[t.id]&&(this.trees[t.id]=e,this.treeIndex=Math.max(e,this.treeIndex))}ensureLevel(t){void 0===this.levels[t]&&(this.levels[t]=0)}getMaxLevel(t){const e={},i=t=>{if(void 0!==e[t])return e[t];let o=this.levels[t];if(this.childrenReference[t]){const e=this.childrenReference[t];if(e.length>0)for(let t=0;t<e.length;t++)o=Math.max(o,i(e[t]))}return e[t]=o,o};return i(t)}levelDownstream(t,e){void 0===this.levels[e.id]&&(void 0===this.levels[t.id]&&(this.levels[t.id]=0),this.levels[e.id]=this.levels[t.id]+1)}setMinLevelToZero(){var t;const e=new Av;let i=0;const o=rC(t=[...new mE(Zu(this.levels))]).call(t,((t,e)=>t-e));for(const t of o)e.set(t,i++);for(const t in this.levels)Object.prototype.hasOwnProperty.call(this.levels,t)&&(this.levels[t]=e.get(this.levels[t]))}getTreeSize(t,e){let i=1e9,o=-1e9,s=1e9,n=-1e9;for(const r in this.trees)if(Object.prototype.hasOwnProperty.call(this.trees,r)&&this.trees[r]===e){const e=t[r];i=Math.min(e.x,i),o=Math.max(e.x,o),s=Math.min(e.y,s),n=Math.max(e.y,n)}return{min_x:i,max_x:o,min_y:s,max_y:n}}hasSameParent(t,e){const i=this.parentReference[t.id],o=this.parentReference[e.id];if(void 0===i||void 0===o)return!1;for(let t=0;t<i.length;t++)for(let e=0;e<o.length;e++)if(i[t]==o[e])return!0;return!1}inSameSubNetwork(t,e){return this.trees[t.id]===this.trees[e.id]}getLevels(){return Jl(this.distributionOrdering)}addToOrdering(t,e){void 0===this.distributionOrdering[e]&&(this.distributionOrdering[e]=[]);let i=!1;const o=this.distributionOrdering[e];for(const e in o)if(o[e]===t){i=!0;break}i||(this.distributionOrdering[e].push(t),this.distributionIndex[t.id]=this.distributionOrdering[e].length-1)}}class zC{constructor(t){this.body=t,this._resetRNG(Math.random()+":"+oc()),this.setPhysics=!1,this.options={},this.optionsBackup={physics:{}},this.defaultOptions={randomSeed:void 0,improvedLayout:!0,clusterThreshold:150,hierarchical:{enabled:!1,levelSeparation:150,nodeSpacing:100,treeSpacing:200,blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:"UD",sortMethod:"hubsize"}},wo(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("_dataChanged",(()=>{this.setupHierarchicalLayout()})),this.body.emitter.on("_dataLoaded",(()=>{this.layoutNetwork()})),this.body.emitter.on("_resetHierarchicalLayout",(()=>{this.setupHierarchicalLayout()})),this.body.emitter.on("_adjustEdgesForHierarchicalLayout",(()=>{if(!0!==this.options.hierarchical.enabled)return;const t=this.direction.curveType();this.body.emitter.emit("_forceDisableDynamicCurves",t,!1)}))}setOptions(t,e){if(void 0!==t){const i=this.options.hierarchical,o=i.enabled;if(vm(["randomSeed","improvedLayout","clusterThreshold"],this.options,t),zm(this.options,t,"hierarchical"),void 0!==t.randomSeed&&this._resetRNG(t.randomSeed),!0===i.enabled)return!0===o&&this.body.emitter.emit("refresh",!0),"RL"===i.direction||"DU"===i.direction?i.levelSeparation>0&&(i.levelSeparation*=-1):i.levelSeparation<0&&(i.levelSeparation*=-1),this.setDirectionStrategy(),this.body.emitter.emit("_resetHierarchicalLayout"),this.adaptAllOptionsForHierarchicalLayout(e);if(!0===o)return this.body.emitter.emit("refresh"),_m(e,this.optionsBackup)}return e}_resetRNG(t){this.initialRandomSeed=t,this._rng=am(this.initialRandomSeed)}adaptAllOptionsForHierarchicalLayout(t){if(!0===this.options.hierarchical.enabled){const e=this.optionsBackup.physics;void 0===t.physics||!0===t.physics?(t.physics={enabled:void 0===e.enabled||e.enabled,solver:"hierarchicalRepulsion"},e.enabled=void 0===e.enabled||e.enabled,e.solver=e.solver||"barnesHut"):"object"==typeof t.physics?(e.enabled=void 0===t.physics.enabled||t.physics.enabled,e.solver=t.physics.solver||"barnesHut",t.physics.solver="hierarchicalRepulsion"):!1!==t.physics&&(e.solver="barnesHut",t.physics={solver:"hierarchicalRepulsion"});let i=this.direction.curveType();if(void 0===t.edges)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges={smooth:!1};else if(void 0===t.edges.smooth)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges.smooth=!1;else if("boolean"==typeof t.edges.smooth)this.optionsBackup.edges={smooth:t.edges.smooth},t.edges.smooth={enabled:t.edges.smooth,type:i};else{const e=t.edges.smooth;void 0!==e.type&&"dynamic"!==e.type&&(i=e.type),this.optionsBackup.edges={smooth:{enabled:void 0===e.enabled||e.enabled,type:void 0===e.type?"dynamic":e.type,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}},t.edges.smooth={enabled:void 0===e.enabled||e.enabled,type:i,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}}this.body.emitter.emit("_forceDisableDynamicCurves",i)}return t}positionInitially(t){if(!0!==this.options.hierarchical.enabled){this._resetRNG(this.initialRandomSeed);const e=t.length+50;for(let i=0;i<t.length;i++){const o=t[i],s=2*Math.PI*this._rng();void 0===o.x&&(o.x=e*Math.cos(s)),void 0===o.y&&(o.y=e*Math.sin(s))}}}layoutNetwork(){if(!0!==this.options.hierarchical.enabled&&!0===this.options.improvedLayout){const t=this.body.nodeIndices;let e=0;for(let i=0;i<t.length;i++){!0===this.body.nodes[t[i]].predefinedPosition&&(e+=1)}if(e<.5*t.length){const e=10;let i=0;const o=this.options.clusterThreshold,s={clusterNodeProperties:{shape:"ellipse",label:"",group:"",font:{multi:!1}},clusterEdgeProperties:{label:"",font:{multi:!1},smooth:{enabled:!1}}};if(t.length>o){const n=t.length;for(;t.length>o&&i<=e;){i+=1;const e=t.length;i%3==0?this.body.modules.clustering.clusterBridges(s):this.body.modules.clustering.clusterOutliers(s);if(e==t.length&&i%3!=0)return this._declusterAll(),this.body.emitter.emit("_layoutFailed"),void console.info("This network could not be positioned by this version of the improved layout algorithm. Please disable improvedLayout for better performance.")}this.body.modules.kamadaKawai.setOptions({springLength:Math.max(150,2*n)})}i>e&&console.info("The clustering didn't succeed within the amount of interations allowed, progressing with partial result."),this.body.modules.kamadaKawai.solve(t,this.body.edgeIndices,!0),this._shiftToCenter();const n=70;for(let e=0;e<t.length;e++){const i=this.body.nodes[t[e]];!1===i.predefinedPosition&&(i.x+=(.5-this._rng())*n,i.y+=(.5-this._rng())*n)}this._declusterAll(),this.body.emitter.emit("_repositionBezierNodes")}}}_shiftToCenter(){const t=sE.getRangeCore(this.body.nodes,this.body.nodeIndices),e=sE.findCenter(t);for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];i.x-=e.x,i.y-=e.y}}_declusterAll(){let t=!0;for(;!0===t;){t=!1;for(let e=0;e<this.body.nodeIndices.length;e++)!0===this.body.nodes[this.body.nodeIndices[e]].isCluster&&(t=!0,this.body.modules.clustering.openCluster(this.body.nodeIndices[e],{},!1));!0===t&&this.body.emitter.emit("_dataChanged")}}getSeed(){return this.initialRandomSeed}setupHierarchicalLayout(){if(!0===this.options.hierarchical.enabled&&this.body.nodeIndices.length>0){let t,e,i=!1,o=!1;for(e in this.lastNodeOnLevel={},this.hierarchical=new FC,this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,e)&&(t=this.body.nodes[e],void 0!==t.options.level?(i=!0,this.hierarchical.levels[e]=t.options.level):o=!0);if(!0===o&&!0===i)throw new Error("To use the hierarchical layout, nodes require either no predefined levels or levels have to be defined for all nodes.");{if(!0===o){const t=this.options.hierarchical.sortMethod;"hubsize"===t?this._determineLevelsByHubsize():"directed"===t?this._determineLevelsDirected():"custom"===t&&this._determineLevelsCustomCallback()}for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.hierarchical.ensureLevel(t);const t=this._getDistribution();this._generateMap(),this._placeNodesByHierarchy(t),this._condenseHierarchy(),this._shiftToCenter()}}}_condenseHierarchy(){var t=this;let e=!1;const i={},o=(t,e)=>{const i=this.hierarchical.trees;for(const o in i)Object.prototype.hasOwnProperty.call(i,o)&&i[o]===t&&this.direction.shift(o,e)},s=()=>{const t=[];for(let e=0;e<this.hierarchical.numTrees();e++)t.push(this.direction.getTreeSize(e));return t},n=(t,e)=>{if(!e[t.id]&&(e[t.id]=!0,this.hierarchical.childrenReference[t.id])){const i=this.hierarchical.childrenReference[t.id];if(i.length>0)for(let t=0;t<i.length;t++)n(this.body.nodes[i[t]],e)}},r=function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e9,o=1e9,s=1e9,n=1e9,r=-1e9;for(const a in e)if(Object.prototype.hasOwnProperty.call(e,a)){const h=t.body.nodes[a],d=t.hierarchical.levels[h.id],l=t.direction.getPosition(h),[c,u]=t._getSpaceAroundNode(h,e);o=Math.min(c,o),s=Math.min(u,s),d<=i&&(n=Math.min(l,n),r=Math.max(l,r))}return[n,r,o,s]},a=(t,e)=>{const i=this.hierarchical.getMaxLevel(t.id),o=this.hierarchical.getMaxLevel(e.id);return Math.min(i,o)},h=(t,e,i)=>{const o=this.hierarchical;for(let s=0;s<e.length;s++){const n=e[s],r=o.distributionOrdering[n];if(r.length>1)for(let e=0;e<r.length-1;e++){const s=r[e],n=r[e+1];o.hasSameParent(s,n)&&o.inSameSubNetwork(s,n)&&t(s,n,i)}}},d=function(i,o){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const h=t.direction.getPosition(i),d=t.direction.getPosition(o),l=Math.abs(d-h),c=t.options.hierarchical.nodeSpacing;if(l>c){const h={},d={};n(i,h),n(o,d);const l=a(i,o),u=r(h,l),p=r(d,l),g=u[1],f=p[0],m=p[2];if(Math.abs(g-f)>c){let i=g-f+c;i<-m+c&&(i=-m+c),i<0&&(t._shiftBlock(o.id,i),e=!0,!0===s&&t._centerParent(o))}}},l=(t,o)=>{const s=o.id,a=o.edges,h=this.hierarchical.levels[o.id],d=this.options.hierarchical.levelSeparation*this.options.hierarchical.levelSeparation,l={},c=[];for(let t=0;t<a.length;t++){const e=a[t];if(e.toId!=e.fromId){const i=e.toId==s?e.from:e.to;l[a[t].id]=i,this.hierarchical.levels[i.id]<h&&c.push(e)}}const u=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const s=this.direction.getPosition(l[e[o].id])-t;i+=s/Math.sqrt(s*s+d)}return i},p=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const s=this.direction.getPosition(l[e[o].id])-t;i-=d*Math.pow(s*s+d,-1.5)}return i},g=(t,e)=>{let i=this.direction.getPosition(o);const s={};for(let o=0;o<t;o++){const t=u(i,e),n=p(i,e),r=40;if(i-=Math.max(-r,Math.min(r,Math.round(t/n))),void 0!==s[i])break;s[i]=o}return i};let f=g(t,c);(t=>{const s=this.direction.getPosition(o);if(void 0===i[o.id]){const t={};n(o,t),i[o.id]=t}const a=r(i[o.id]),h=a[2],d=a[3],l=t-s;let c=0;l>0?c=Math.min(l,d-this.options.hierarchical.nodeSpacing):l<0&&(c=-Math.min(-l,h-this.options.hierarchical.nodeSpacing)),0!=c&&(this._shiftBlock(o.id,c),e=!0)})(f),f=g(t,a),(t=>{const i=this.direction.getPosition(o),[s,n]=this._getSpaceAroundNode(o),r=t-i;let a=i;r>0?a=Math.min(i+(n-this.options.hierarchical.nodeSpacing),t):r<0&&(a=Math.max(i-(s-this.options.hierarchical.nodeSpacing),t)),a!==i&&(this.direction.setPosition(o,a),e=!0)})(f)},c=t=>{let i=this.hierarchical.getLevels();i=kc(i).call(i);for(let o=0;o<t;o++){e=!1;for(let t=0;t<i.length;t++){const e=i[t],o=this.hierarchical.distributionOrdering[e];for(let t=0;t<o.length;t++)l(1e3,o[t])}if(!0!==e)break}},u=t=>{let i=this.hierarchical.getLevels();i=kc(i).call(i);for(let o=0;o<t&&(e=!1,h(d,i,!0),!0===e);o++);},p=()=>{for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this._centerParent(this.body.nodes[t])},g=()=>{let t=this.hierarchical.getLevels();t=kc(t).call(t);for(let e=0;e<t.length;e++){const i=t[e],o=this.hierarchical.distributionOrdering[i];for(let t=0;t<o.length;t++)this._centerParent(o[t])}};!0===this.options.hierarchical.blockShifting&&(u(5),p()),!0===this.options.hierarchical.edgeMinimization&&c(20),!0===this.options.hierarchical.parentCentralization&&g(),(()=>{const t=s();let e=0;for(let i=0;i<t.length-1;i++){e+=t[i].max-t[i+1].min+this.options.hierarchical.treeSpacing,o(i+1,e)}})()}_getSpaceAroundNode(t,e){let i=!0;void 0===e&&(i=!1);const o=this.hierarchical.levels[t.id];if(void 0!==o){const s=this.hierarchical.distributionIndex[t.id],n=this.direction.getPosition(t),r=this.hierarchical.distributionOrdering[o];let a=1e9,h=1e9;if(0!==s){const t=r[s-1];if(!0===i&&void 0===e[t.id]||!1===i){a=n-this.direction.getPosition(t)}}if(s!=r.length-1){const t=r[s+1];if(!0===i&&void 0===e[t.id]||!1===i){const e=this.direction.getPosition(t);h=Math.min(h,e-n)}}return[a,h]}return[0,0]}_centerParent(t){if(this.hierarchical.parentReference[t.id]){const e=this.hierarchical.parentReference[t.id];for(let t=0;t<e.length;t++){const i=e[t],o=this.body.nodes[i],s=this.hierarchical.childrenReference[i];if(void 0!==s){const t=this._getCenterPosition(s),e=this.direction.getPosition(o),[i,n]=this._getSpaceAroundNode(o),r=e-t;(r<0&&Math.abs(r)<n-this.options.hierarchical.nodeSpacing||r>0&&Math.abs(r)<i-this.options.hierarchical.nodeSpacing)&&this.direction.setPosition(o,t)}}}}_placeNodesByHierarchy(t){this.positionedNodes={};for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)){var e;let o=Jl(t[i]);o=this._indexArrayToNodes(o),rC(e=this.direction).call(e,o);let s=0;for(let t=0;t<o.length;t++){const e=o[t];if(void 0===this.positionedNodes[e.id]){const n=this.options.hierarchical.nodeSpacing;let r=n*s;s>0&&(r=this.direction.getPosition(o[t-1])+n),this.direction.setPosition(e,r,i),this._validatePositionAndContinue(e,i,r),s++}}}}_placeBranchNodes(t,e){var i;const o=this.hierarchical.childrenReference[t];if(void 0===o)return;const s=[];for(let t=0;t<o.length;t++)s.push(this.body.nodes[o[t]]);rC(i=this.direction).call(i,s);for(let i=0;i<s.length;i++){const o=s[i],n=this.hierarchical.levels[o.id];if(!(n>e&&void 0===this.positionedNodes[o.id]))return;{const e=this.options.hierarchical.nodeSpacing;let r;r=0===i?this.direction.getPosition(this.body.nodes[t]):this.direction.getPosition(s[i-1])+e,this.direction.setPosition(o,r,n),this._validatePositionAndContinue(o,n,r)}}const n=this._getCenterPosition(s);this.direction.setPosition(this.body.nodes[t],n,e)}_validatePositionAndContinue(t,e,i){if(this.hierarchical.isTree){if(void 0!==this.lastNodeOnLevel[e]){const o=this.direction.getPosition(this.body.nodes[this.lastNodeOnLevel[e]]);if(i-o<this.options.hierarchical.nodeSpacing){const s=o+this.options.hierarchical.nodeSpacing-i,n=this._findCommonParent(this.lastNodeOnLevel[e],t.id);this._shiftBlock(n.withChild,s)}}this.lastNodeOnLevel[e]=t.id,this.positionedNodes[t.id]=!0,this._placeBranchNodes(t.id,e)}}_indexArrayToNodes(t){const e=[];for(let i=0;i<t.length;i++)e.push(this.body.nodes[t[i]]);return e}_getDistribution(){const t={};let e,i;for(e in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,e)){i=this.body.nodes[e];const o=void 0===this.hierarchical.levels[e]?0:this.hierarchical.levels[e];this.direction.fix(i,o),void 0===t[o]&&(t[o]={}),t[o][e]=i}return t}_getActiveEdges(t){const e=[];return Om(t.edges,(t=>{var i;-1!==Mp(i=this.body.edgeIndices).call(i,t.id)&&e.push(t)})),e}_getHubSizes(){const t={};Om(this.body.nodeIndices,(e=>{const i=this.body.nodes[e],o=this._getActiveEdges(i).length;t[o]=!0}));const e=[];return Om(t,(t=>{e.push(Number(t))})),rC(e).call(e,(function(t,e){return e-t})),e}_determineLevelsByHubsize(){const t=(t,e)=>{this.hierarchical.levelDownstream(t,e)},e=this._getHubSizes();for(let i=0;i<e.length;++i){const o=e[i];if(0===o)break;Om(this.body.nodeIndices,(e=>{const i=this.body.nodes[e];o===this._getActiveEdges(i).length&&this._crawlNetwork(t,e)}))}}_determineLevelsCustomCallback(){this._crawlNetwork(((t,e,i)=>{let o=this.hierarchical.levels[t.id];void 0===o&&(o=this.hierarchical.levels[t.id]=1e5);const s=(sE.cloneOptions(t,"node"),sE.cloneOptions(e,"node"),void sE.cloneOptions(i,"edge"));this.hierarchical.levels[e.id]=o+s})),this.hierarchical.setMinLevelToZero()}_determineLevelsDirected(){var t;const e=_C(t=this.body.nodeIndices).call(t,((t,e)=>(t.set(e,this.body.nodes[e]),t)),new Av);"roots"===this.options.hierarchical.shakeTowards?this.hierarchical.levels=function(t){return BC((e=>{var i,o;return PC(i=Ru(o=e.edges).call(o,(e=>t.has(e.toId)))).call(i,(t=>t.from===e))}),((t,e)=>e<t),"to",t)}(e):this.hierarchical.levels=function(t){return BC((e=>{var i,o;return PC(i=Ru(o=e.edges).call(o,(e=>t.has(e.toId)))).call(i,(t=>t.to===e))}),((t,e)=>e>t),"from",t)}(e),this.hierarchical.setMinLevelToZero()}_generateMap(){this._crawlNetwork(((t,e)=>{this.hierarchical.levels[e.id]>this.hierarchical.levels[t.id]&&this.hierarchical.addRelation(t.id,e.id)})),this.hierarchical.checkIfTree()}_crawlNetwork(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){},e=arguments.length>1?arguments[1]:void 0;const i={},o=(e,s)=>{if(void 0===i[e.id]){let n;this.hierarchical.setTreeIndex(e,s),i[e.id]=!0;const r=this._getActiveEdges(e);for(let i=0;i<r.length;i++){const a=r[i];!0===a.connected&&(n=a.toId==e.id?a.from:a.to,e.id!=n.id&&(t(e,n,a),o(n,s)))}}};if(void 0===e){let t=0;for(let e=0;e<this.body.nodeIndices.length;e++){const s=this.body.nodeIndices[e];if(void 0===i[s]){const e=this.body.nodes[s];o(e,t),t+=1}}}else{const t=this.body.nodes[e];if(void 0===t)return void console.error("Node not found:",e);o(t)}}_shiftBlock(t,e){const i={},o=t=>{if(i[t])return;i[t]=!0,this.direction.shift(t,e);const s=this.hierarchical.childrenReference[t];if(void 0!==s)for(let t=0;t<s.length;t++)o(s[t])};o(t)}_findCommonParent(t,e){const i={},o=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let e=0;e<i.length;e++){const s=i[e];t[s]=!0,o(t,s)}},s=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let o=0;o<i.length;o++){const n=i[o];if(void 0!==t[n])return{foundParent:n,withChild:e};const r=s(t,n);if(null!==r.foundParent)return r}return{foundParent:null,withChild:e}};return o(i,t),s(i,e)}setDirectionStrategy(){const t="UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction;this.direction=t?new EC(this):new OC(this)}_getCenterPosition(t){let e=1e9,i=-1e9;for(let o=0;o<t.length;o++){let s;if(void 0!==t[o].id)s=t[o];else{const e=t[o];s=this.body.nodes[e]}const n=this.direction.getPosition(s);e=Math.min(e,n),i=Math.max(i,n)}return.5*(e+i)}}class NC{constructor(t,e,i,o){var s,n;this.body=t,this.canvas=e,this.selectionHandler=i,this.interactionHandler=o,this.editMode=!1,this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0,this._domEventListenerCleanupQueue=[],this.temporaryUIFunctions={},this.temporaryEventFunctions=[],this.touchTime=0,this.temporaryIds={nodes:[],edges:[]},this.guiEnabled=!1,this.inMode=!1,this.selectedControlNode=void 0,this.options={},this.defaultOptions={enabled:!1,initiallyActive:!1,addNode:!0,addEdge:!0,editNode:void 0,editEdge:!0,deleteNode:!0,deleteEdge:!0,controlNodeStyle:{shape:"dot",size:6,color:{background:"#ff0000",border:"#3c3c3c",highlight:{background:"#07f968",border:"#3c3c3c"}},borderWidth:2,borderWidthSelected:2}},wo(this.options,this.defaultOptions),this.body.emitter.on("destroy",(()=>{this._clean()})),this.body.emitter.on("_dataChanged",Ho(s=this._restore).call(s,this)),this.body.emitter.on("_resetData",Ho(n=this._restore).call(n,this))}_restore(){!1!==this.inMode&&(!0===this.options.initiallyActive?this.enableEditMode():this.disableEditMode())}setOptions(t,e,i){void 0!==e&&(void 0!==e.locale?this.options.locale=e.locale:this.options.locale=i.locale,void 0!==e.locales?this.options.locales=e.locales:this.options.locales=i.locales),void 0!==t&&("boolean"==typeof t?this.options.enabled=t:(this.options.enabled=!0,_m(this.options,t)),!0===this.options.initiallyActive&&(this.editMode=!0),this._setup())}toggleEditMode(){!0===this.editMode?this.disableEditMode():this.enableEditMode()}enableEditMode(){this.editMode=!0,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="block",this.closeDiv.style.display="block",this.editModeDiv.style.display="none",this.showManipulatorToolbar())}disableEditMode(){this.editMode=!1,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="none",this.closeDiv.style.display="none",this.editModeDiv.style.display="block",this._createEditButton())}showManipulatorToolbar(){if(this._clean(),this.manipulationDOM={},!0===this.guiEnabled){var t,e;this.editMode=!0,this.manipulationDiv.style.display="block",this.closeDiv.style.display="block";const i=this.selectionHandler.getSelectedNodeCount(),o=this.selectionHandler.getSelectedEdgeCount(),s=i+o,n=this.options.locales[this.options.locale];let r=!1;!1!==this.options.addNode&&(this._createAddNodeButton(n),r=!0),!1!==this.options.addEdge&&(!0===r?this._createSeperator(1):r=!0,this._createAddEdgeButton(n)),1===i&&"function"==typeof this.options.editNode?(!0===r?this._createSeperator(2):r=!0,this._createEditNodeButton(n)):1===o&&0===i&&!1!==this.options.editEdge&&(!0===r?this._createSeperator(3):r=!0,this._createEditEdgeButton(n)),0!==s&&(i>0&&!1!==this.options.deleteNode||0===i&&!1!==this.options.deleteEdge)&&(!0===r&&this._createSeperator(4),this._createDeleteButton(n)),this._bindElementEvents(this.closeDiv,Ho(t=this.toggleEditMode).call(t,this)),this._temporaryBindEvent("select",Ho(e=this.showManipulatorToolbar).call(e,this))}this.body.emitter.emit("_redraw")}addNodeMode(){var t;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addNode",!0===this.guiEnabled){var e;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.addDescription||this.options.locales.en.addDescription),this._bindElementEvents(this.closeDiv,Ho(e=this.toggleEditMode).call(e,this))}this._temporaryBindEvent("click",Ho(t=this._performAddNode).call(t,this))}editNode(){!0!==this.editMode&&this.enableEditMode(),this._clean();const t=this.selectionHandler.getSelectedNodes()[0];if(void 0!==t){if(this.inMode="editNode","function"!=typeof this.options.editNode)throw new Error("No function has been configured to handle the editing of nodes.");if(!0!==t.isCluster){const e=_m({},t.options,!1);if(e.x=t.x,e.y=t.y,2!==this.options.editNode.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editNode(e,(t=>{null!=t&&"editNode"===this.inMode&&this.body.data.nodes.getDataSet().update(t),this.showManipulatorToolbar()}))}else alert(this.options.locales[this.options.locale].editClusterError||this.options.locales.en.editClusterError)}else this.showManipulatorToolbar()}addEdgeMode(){var t,e,i,o,s;if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addEdge",!0===this.guiEnabled){var n;const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.edgeDescription||this.options.locales.en.edgeDescription),this._bindElementEvents(this.closeDiv,Ho(n=this.toggleEditMode).call(n,this))}this._temporaryBindUI("onTouch",Ho(t=this._handleConnect).call(t,this)),this._temporaryBindUI("onDragEnd",Ho(e=this._finishConnect).call(e,this)),this._temporaryBindUI("onDrag",Ho(i=this._dragControlNode).call(i,this)),this._temporaryBindUI("onRelease",Ho(o=this._finishConnect).call(o,this)),this._temporaryBindUI("onDragStart",Ho(s=this._dragStartEdge).call(s,this)),this._temporaryBindUI("onHold",(()=>{}))}editEdgeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="editEdge","object"!=typeof this.options.editEdge||"function"!=typeof this.options.editEdge.editWithoutDrag||(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0===this.edgeBeingEditedId)){if(!0===this.guiEnabled){var t;const e=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(e),this._createSeperator(),this._createDescription(e.editEdgeDescription||this.options.locales.en.editEdgeDescription),this._bindElementEvents(this.closeDiv,Ho(t=this.toggleEditMode).call(t,this))}if(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId){var e,i,o,s;const t=this.body.edges[this.edgeBeingEditedId],n=this._getNewTargetNode(t.from.x,t.from.y),r=this._getNewTargetNode(t.to.x,t.to.y);this.temporaryIds.nodes.push(n.id),this.temporaryIds.nodes.push(r.id),this.body.nodes[n.id]=n,this.body.nodeIndices.push(n.id),this.body.nodes[r.id]=r,this.body.nodeIndices.push(r.id),this._temporaryBindUI("onTouch",Ho(e=this._controlNodeTouch).call(e,this)),this._temporaryBindUI("onTap",(()=>{})),this._temporaryBindUI("onHold",(()=>{})),this._temporaryBindUI("onDragStart",Ho(i=this._controlNodeDragStart).call(i,this)),this._temporaryBindUI("onDrag",Ho(o=this._controlNodeDrag).call(o,this)),this._temporaryBindUI("onDragEnd",Ho(s=this._controlNodeDragEnd).call(s,this)),this._temporaryBindUI("onMouseMove",(()=>{})),this._temporaryBindEvent("beforeDrawing",(e=>{const i=t.edgeType.findBorderPositions(e);!1===n.selected&&(n.x=i.from.x,n.y=i.from.y),!1===r.selected&&(r.x=i.to.x,r.y=i.to.y)})),this.body.emitter.emit("_redraw")}else this.showManipulatorToolbar()}else{const t=this.body.edges[this.edgeBeingEditedId];this._performEditEdge(t.from.id,t.to.id)}}deleteSelected(){!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="delete";const t=this.selectionHandler.getSelectedNodeIds(),e=this.selectionHandler.getSelectedEdgeIds();let i;if(t.length>0){for(let e=0;e<t.length;e++)if(!0===this.body.nodes[t[e]].isCluster)return void alert(this.options.locales[this.options.locale].deleteClusterError||this.options.locales.en.deleteClusterError);"function"==typeof this.options.deleteNode&&(i=this.options.deleteNode)}else e.length>0&&"function"==typeof this.options.deleteEdge&&(i=this.options.deleteEdge);if("function"==typeof i){const o={nodes:t,edges:e};if(2!==i.length)throw new Error("The function for delete does not support two arguments (data, callback)");i(o,(t=>{null!=t&&"delete"===this.inMode?(this.body.data.edges.getDataSet().remove(t.edges),this.body.data.nodes.getDataSet().remove(t.nodes),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()):(this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar())}))}else this.body.data.edges.getDataSet().remove(e),this.body.data.nodes.getDataSet().remove(t),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()}_setup(){!0===this.options.enabled?(this.guiEnabled=!0,this._createWrappers(),!1===this.editMode?this._createEditButton():this.showManipulatorToolbar()):(this._removeManipulationDOM(),this.guiEnabled=!1)}_createWrappers(){var t,e;(void 0===this.manipulationDiv&&(this.manipulationDiv=document.createElement("div"),this.manipulationDiv.className="vis-manipulation",!0===this.editMode?this.manipulationDiv.style.display="block":this.manipulationDiv.style.display="none",this.canvas.frame.appendChild(this.manipulationDiv)),void 0===this.editModeDiv&&(this.editModeDiv=document.createElement("div"),this.editModeDiv.className="vis-edit-mode",!0===this.editMode?this.editModeDiv.style.display="none":this.editModeDiv.style.display="block",this.canvas.frame.appendChild(this.editModeDiv)),void 0===this.closeDiv)&&(this.closeDiv=document.createElement("button"),this.closeDiv.className="vis-close",this.closeDiv.setAttribute("aria-label",null!==(t=null===(e=this.options.locales[this.options.locale])||void 0===e?void 0:e.close)&&void 0!==t?t:this.options.locales.en.close),this.closeDiv.style.display=this.manipulationDiv.style.display,this.canvas.frame.appendChild(this.closeDiv))}_getNewTargetNode(t,e){const i=_m({},this.options.controlNodeStyle);i.id="targetNode"+oE(),i.hidden=!1,i.physics=!1,i.x=t,i.y=e;const o=this.body.functions.createNode(i);return o.shape.boundingBox={left:t,right:t,top:e,bottom:e},o}_createEditButton(){var t;this._clean(),this.manipulationDOM={},gm(this.editModeDiv);const e=this.options.locales[this.options.locale],i=this._createButton("editMode","vis-edit vis-edit-mode",e.edit||this.options.locales.en.edit);this.editModeDiv.appendChild(i),this._bindElementEvents(i,Ho(t=this.toggleEditMode).call(t,this))}_clean(){this.inMode=!1,!0===this.guiEnabled&&(gm(this.editModeDiv),gm(this.manipulationDiv),this._cleanupDOMEventListeners()),this._cleanupTemporaryNodesAndEdges(),this._unbindTemporaryUIs(),this._unbindTemporaryEvents(),this.body.emitter.emit("restorePhysics")}_cleanupDOMEventListeners(){for(const e of Jc(t=this._domEventListenerCleanupQueue).call(t,0)){var t;e()}}_removeManipulationDOM(){this._clean(),gm(this.manipulationDiv),gm(this.editModeDiv),gm(this.closeDiv),this.manipulationDiv&&this.canvas.frame.removeChild(this.manipulationDiv),this.editModeDiv&&this.canvas.frame.removeChild(this.editModeDiv),this.closeDiv&&this.canvas.frame.removeChild(this.closeDiv),this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0}_createSeperator(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.manipulationDOM["seperatorLineDiv"+t]=document.createElement("div"),this.manipulationDOM["seperatorLineDiv"+t].className="vis-separator-line",this.manipulationDiv.appendChild(this.manipulationDOM["seperatorLineDiv"+t])}_createAddNodeButton(t){var e;const i=this._createButton("addNode","vis-add",t.addNode||this.options.locales.en.addNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,Ho(e=this.addNodeMode).call(e,this))}_createAddEdgeButton(t){var e;const i=this._createButton("addEdge","vis-connect",t.addEdge||this.options.locales.en.addEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,Ho(e=this.addEdgeMode).call(e,this))}_createEditNodeButton(t){var e;const i=this._createButton("editNode","vis-edit",t.editNode||this.options.locales.en.editNode);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,Ho(e=this.editNode).call(e,this))}_createEditEdgeButton(t){var e;const i=this._createButton("editEdge","vis-edit",t.editEdge||this.options.locales.en.editEdge);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,Ho(e=this.editEdgeMode).call(e,this))}_createDeleteButton(t){var e;let i;i=this.options.rtl?"vis-delete-rtl":"vis-delete";const o=this._createButton("delete",i,t.del||this.options.locales.en.del);this.manipulationDiv.appendChild(o),this._bindElementEvents(o,Ho(e=this.deleteSelected).call(e,this))}_createBackButton(t){var e;const i=this._createButton("back","vis-back",t.back||this.options.locales.en.back);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,Ho(e=this.showManipulatorToolbar).call(e,this))}_createButton(t,e,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"vis-label";return this.manipulationDOM[t+"Div"]=document.createElement("button"),this.manipulationDOM[t+"Div"].className="vis-button "+e,this.manipulationDOM[t+"Label"]=document.createElement("div"),this.manipulationDOM[t+"Label"].className=o,this.manipulationDOM[t+"Label"].innerText=i,this.manipulationDOM[t+"Div"].appendChild(this.manipulationDOM[t+"Label"]),this.manipulationDOM[t+"Div"]}_createDescription(t){this.manipulationDOM.descriptionLabel=document.createElement("div"),this.manipulationDOM.descriptionLabel.className="vis-none",this.manipulationDOM.descriptionLabel.innerText=t,this.manipulationDiv.appendChild(this.manipulationDOM.descriptionLabel)}_temporaryBindEvent(t,e){this.temporaryEventFunctions.push({event:t,boundFunction:e}),this.body.emitter.on(t,e)}_temporaryBindUI(t,e){if(void 0===this.body.eventListeners[t])throw new Error("This UI function does not exist. Typo? You tried: "+t+" possible are: "+Fp(Jl(this.body.eventListeners)));this.temporaryUIFunctions[t]=this.body.eventListeners[t],this.body.eventListeners[t]=e}_unbindTemporaryUIs(){for(const t in this.temporaryUIFunctions)Object.prototype.hasOwnProperty.call(this.temporaryUIFunctions,t)&&(this.body.eventListeners[t]=this.temporaryUIFunctions[t],delete this.temporaryUIFunctions[t]);this.temporaryUIFunctions={}}_unbindTemporaryEvents(){for(let t=0;t<this.temporaryEventFunctions.length;t++){const e=this.temporaryEventFunctions[t].event,i=this.temporaryEventFunctions[t].boundFunction;this.body.emitter.off(e,i)}this.temporaryEventFunctions=[]}_bindElementEvents(t,e){const i=new Ym(t,{});dE(i,e),this._domEventListenerCleanupQueue.push((()=>{i.destroy()}));const o=t=>{let{keyCode:i,key:o}=t;"Enter"!==o&&" "!==o&&13!==i&&32!==i||e()};t.addEventListener("keyup",o,!1),this._domEventListenerCleanupQueue.push((()=>{t.removeEventListener("keyup",o,!1)}))}_cleanupTemporaryNodesAndEdges(){for(let i=0;i<this.temporaryIds.edges.length;i++){var t;this.body.edges[this.temporaryIds.edges[i]].disconnect(),delete this.body.edges[this.temporaryIds.edges[i]];const o=Mp(t=this.body.edgeIndices).call(t,this.temporaryIds.edges[i]);var e;if(-1!==o)Jc(e=this.body.edgeIndices).call(e,o,1)}for(let t=0;t<this.temporaryIds.nodes.length;t++){var i;delete this.body.nodes[this.temporaryIds.nodes[t]];const e=Mp(i=this.body.nodeIndices).call(i,this.temporaryIds.nodes[t]);var o;if(-1!==e)Jc(o=this.body.nodeIndices).call(o,e,1)}this.temporaryIds={nodes:[],edges:[]}}_controlNodeTouch(t){this.selectionHandler.unselectAll(),this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=wo({},this.body.view.translation)}_controlNodeDragStart(){const t=this.lastTouch,e=this.selectionHandler._pointerToPositionObject(t),i=this.body.nodes[this.temporaryIds.nodes[0]],o=this.body.nodes[this.temporaryIds.nodes[1]],s=this.body.edges[this.edgeBeingEditedId];this.selectedControlNode=void 0;const n=i.isOverlappingWith(e),r=o.isOverlappingWith(e);!0===n?(this.selectedControlNode=i,s.edgeType.from=i):!0===r&&(this.selectedControlNode=o,s.edgeType.to=o),void 0!==this.selectedControlNode&&this.selectionHandler.selectObject(this.selectedControlNode),this.body.emitter.emit("_redraw")}_controlNodeDrag(t){this.body.emitter.emit("disablePhysics");const e=this.body.functions.getPointer(t.center),i=this.canvas.DOMtoCanvas(e);void 0!==this.selectedControlNode?(this.selectedControlNode.x=i.x,this.selectedControlNode.y=i.y):this.interactionHandler.onDrag(t),this.body.emitter.emit("_redraw")}_controlNodeDragEnd(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e),o=this.body.edges[this.edgeBeingEditedId];if(void 0===this.selectedControlNode)return;this.selectionHandler.unselectAll();const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--)if(s[t]!==this.selectedControlNode.id){n=this.body.nodes[s[t]];break}if(void 0!==n&&void 0!==this.selectedControlNode)if(!0===n.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this.body.nodes[this.temporaryIds.nodes[0]];this.selectedControlNode.id===t.id?this._performEditEdge(n.id,o.to.id):this._performEditEdge(o.from.id,n.id)}else o.updateEdgeType(),this.body.emitter.emit("restorePhysics");this.body.emitter.emit("_redraw")}_handleConnect(t){if((new Date).valueOf()-this.touchTime>100){this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=wo({},this.body.view.translation),this.interactionHandler.drag.pointer=this.lastTouch,this.interactionHandler.drag.translation=this.lastTouch.translation;const e=this.lastTouch,i=this.selectionHandler.getNodeAt(e);if(void 0!==i)if(!0===i.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this._getNewTargetNode(i.x,i.y);this.body.nodes[t.id]=t,this.body.nodeIndices.push(t.id);const e=this.body.functions.createEdge({id:"connectionEdge"+oE(),from:i.id,to:t.id,physics:!1,smooth:{enabled:!0,type:"continuous",roundness:.5}});this.body.edges[e.id]=e,this.body.edgeIndices.push(e.id),this.temporaryIds.nodes.push(t.id),this.temporaryIds.edges.push(e.id)}this.touchTime=(new Date).valueOf()}}_dragControlNode(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--){var r;if(-1===Mp(r=this.temporaryIds.nodes).call(r,s[t])){n=this.body.nodes[s[t]];break}}if(t.controlEdge={from:o,to:n?n.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragging",t,e),void 0!==this.temporaryIds.nodes[0]){const t=this.body.nodes[this.temporaryIds.nodes[0]];t.x=this.canvas._XconvertDOMtoCanvas(e.x),t.y=this.canvas._YconvertDOMtoCanvas(e.y),this.body.emitter.emit("_redraw")}else this.interactionHandler.onDrag(t)}_finishConnect(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--){var r;if(-1===Mp(r=this.temporaryIds.nodes).call(r,s[t])){n=this.body.nodes[s[t]];break}}this._cleanupTemporaryNodesAndEdges(),void 0!==n&&(!0===n.isCluster?alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError):void 0!==this.body.nodes[o]&&void 0!==this.body.nodes[n.id]&&this._performAddEdge(o,n.id)),t.controlEdge={from:o,to:n?n.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragEnd",t,e),this.body.emitter.emit("_redraw")}_dragStartEdge(t){const e=this.lastTouch;this.selectionHandler.generateClickEvent("dragStart",t,e,void 0,!0)}_performAddNode(t){const e={id:oE(),x:t.pointer.canvas.x,y:t.pointer.canvas.y,label:"new"};if("function"==typeof this.options.addNode){if(2!==this.options.addNode.length)throw this.showManipulatorToolbar(),new Error("The function for add does not support two arguments (data,callback)");this.options.addNode(e,(t=>{null!=t&&"addNode"===this.inMode&&this.body.data.nodes.getDataSet().add(t),this.showManipulatorToolbar()}))}else this.body.data.nodes.getDataSet().add(e),this.showManipulatorToolbar()}_performAddEdge(t,e){const i={from:t,to:e};if("function"==typeof this.options.addEdge){if(2!==this.options.addEdge.length)throw new Error("The function for connect does not support two arguments (data,callback)");this.options.addEdge(i,(t=>{null!=t&&"addEdge"===this.inMode&&(this.body.data.edges.getDataSet().add(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())}))}else this.body.data.edges.getDataSet().add(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}_performEditEdge(t,e){const i={id:this.edgeBeingEditedId,from:t,to:e,label:this.body.data.edges.get(this.edgeBeingEditedId).label};let o=this.options.editEdge;if("object"==typeof o&&(o=o.editWithoutDrag),"function"==typeof o){if(2!==o.length)throw new Error("The function for edit does not support two arguments (data, callback)");o(i,(t=>{null==t||"editEdge"!==this.inMode?(this.body.edges[i.id].updateEdgeType(),this.body.emitter.emit("_redraw"),this.showManipulatorToolbar()):(this.body.data.edges.getDataSet().update(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())}))}else this.body.data.edges.getDataSet().update(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}}const AC="string",RC="boolean",jC="number",LC="array",HC="object",WC=["arrow","bar","box","circle","crow","curve","diamond","image","inv_curve","inv_triangle","triangle","vee"],VC={borderWidth:{number:jC},borderWidthSelected:{number:jC,undefined:"undefined"},brokenImage:{string:AC,undefined:"undefined"},chosen:{label:{boolean:RC,function:"function"},node:{boolean:RC,function:"function"},__type__:{object:HC,boolean:RC}},color:{border:{string:AC},background:{string:AC},highlight:{border:{string:AC},background:{string:AC},__type__:{object:HC,string:AC}},hover:{border:{string:AC},background:{string:AC},__type__:{object:HC,string:AC}},__type__:{object:HC,string:AC}},opacity:{number:jC,undefined:"undefined"},fixed:{x:{boolean:RC},y:{boolean:RC},__type__:{object:HC,boolean:RC}},font:{align:{string:AC},color:{string:AC},size:{number:jC},face:{string:AC},background:{string:AC},strokeWidth:{number:jC},strokeColor:{string:AC},vadjust:{number:jC},multi:{boolean:RC,string:AC},bold:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},boldital:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},ital:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},mono:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},__type__:{object:HC,string:AC}},group:{string:AC,number:jC,undefined:"undefined"},heightConstraint:{minimum:{number:jC},valign:{string:AC},__type__:{object:HC,boolean:RC,number:jC}},hidden:{boolean:RC},icon:{face:{string:AC},code:{string:AC},size:{number:jC},color:{string:AC},weight:{string:AC,number:jC},__type__:{object:HC}},id:{string:AC,number:jC},image:{selected:{string:AC,undefined:"undefined"},unselected:{string:AC,undefined:"undefined"},__type__:{object:HC,string:AC}},imagePadding:{top:{number:jC},right:{number:jC},bottom:{number:jC},left:{number:jC},__type__:{object:HC,number:jC}},label:{string:AC,undefined:"undefined"},labelHighlightBold:{boolean:RC},level:{number:jC,undefined:"undefined"},margin:{top:{number:jC},right:{number:jC},bottom:{number:jC},left:{number:jC},__type__:{object:HC,number:jC}},mass:{number:jC},physics:{boolean:RC},scaling:{min:{number:jC},max:{number:jC},label:{enabled:{boolean:RC},min:{number:jC},max:{number:jC},maxVisible:{number:jC},drawThreshold:{number:jC},__type__:{object:HC,boolean:RC}},customScalingFunction:{function:"function"},__type__:{object:HC}},shadow:{enabled:{boolean:RC},color:{string:AC},size:{number:jC},x:{number:jC},y:{number:jC},__type__:{object:HC,boolean:RC}},shape:{string:["custom","ellipse","circle","database","box","text","image","circularImage","diamond","dot","star","triangle","triangleDown","square","icon","hexagon"]},ctxRenderer:{function:"function"},shapeProperties:{borderDashes:{boolean:RC,array:LC},borderRadius:{number:jC},interpolation:{boolean:RC},useImageSize:{boolean:RC},useBorderWithImage:{boolean:RC},coordinateOrigin:{string:["center","top-left"]},__type__:{object:HC}},size:{number:jC},title:{string:AC,dom:"dom",undefined:"undefined"},value:{number:jC,undefined:"undefined"},widthConstraint:{minimum:{number:jC},maximum:{number:jC},__type__:{object:HC,boolean:RC,number:jC}},x:{number:jC},y:{number:jC},__type__:{object:HC}},qC={configure:{enabled:{boolean:RC},filter:{boolean:RC,string:AC,array:LC,function:"function"},container:{dom:"dom"},showButton:{boolean:RC},__type__:{object:HC,boolean:RC,string:AC,array:LC,function:"function"}},edges:{arrows:{to:{enabled:{boolean:RC},scaleFactor:{number:jC},type:{string:WC},imageHeight:{number:jC},imageWidth:{number:jC},src:{string:AC},__type__:{object:HC,boolean:RC}},middle:{enabled:{boolean:RC},scaleFactor:{number:jC},type:{string:WC},imageWidth:{number:jC},imageHeight:{number:jC},src:{string:AC},__type__:{object:HC,boolean:RC}},from:{enabled:{boolean:RC},scaleFactor:{number:jC},type:{string:WC},imageWidth:{number:jC},imageHeight:{number:jC},src:{string:AC},__type__:{object:HC,boolean:RC}},__type__:{string:["from","to","middle"],object:HC}},endPointOffset:{from:{number:jC},to:{number:jC},__type__:{object:HC,number:jC}},arrowStrikethrough:{boolean:RC},background:{enabled:{boolean:RC},color:{string:AC},size:{number:jC},dashes:{boolean:RC,array:LC},__type__:{object:HC,boolean:RC}},chosen:{label:{boolean:RC,function:"function"},edge:{boolean:RC,function:"function"},__type__:{object:HC,boolean:RC}},color:{color:{string:AC},highlight:{string:AC},hover:{string:AC},inherit:{string:["from","to","both"],boolean:RC},opacity:{number:jC},__type__:{object:HC,string:AC}},dashes:{boolean:RC,array:LC},font:{color:{string:AC},size:{number:jC},face:{string:AC},background:{string:AC},strokeWidth:{number:jC},strokeColor:{string:AC},align:{string:["horizontal","top","middle","bottom"]},vadjust:{number:jC},multi:{boolean:RC,string:AC},bold:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},boldital:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},ital:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},mono:{color:{string:AC},size:{number:jC},face:{string:AC},mod:{string:AC},vadjust:{number:jC},__type__:{object:HC,string:AC}},__type__:{object:HC,string:AC}},hidden:{boolean:RC},hoverWidth:{function:"function",number:jC},label:{string:AC,undefined:"undefined"},labelHighlightBold:{boolean:RC},length:{number:jC,undefined:"undefined"},physics:{boolean:RC},scaling:{min:{number:jC},max:{number:jC},label:{enabled:{boolean:RC},min:{number:jC},max:{number:jC},maxVisible:{number:jC},drawThreshold:{number:jC},__type__:{object:HC,boolean:RC}},customScalingFunction:{function:"function"},__type__:{object:HC}},selectionWidth:{function:"function",number:jC},selfReferenceSize:{number:jC},selfReference:{size:{number:jC},angle:{number:jC},renderBehindTheNode:{boolean:RC},__type__:{object:HC}},shadow:{enabled:{boolean:RC},color:{string:AC},size:{number:jC},x:{number:jC},y:{number:jC},__type__:{object:HC,boolean:RC}},smooth:{enabled:{boolean:RC},type:{string:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"]},roundness:{number:jC},forceDirection:{string:["horizontal","vertical","none"],boolean:RC},__type__:{object:HC,boolean:RC}},title:{string:AC,undefined:"undefined"},width:{number:jC},widthConstraint:{maximum:{number:jC},__type__:{object:HC,boolean:RC,number:jC}},value:{number:jC,undefined:"undefined"},__type__:{object:HC}},groups:{useDefaultGroups:{boolean:RC},__any__:VC,__type__:{object:HC}},interaction:{dragNodes:{boolean:RC},dragView:{boolean:RC},hideEdgesOnDrag:{boolean:RC},hideEdgesOnZoom:{boolean:RC},hideNodesOnDrag:{boolean:RC},hover:{boolean:RC},keyboard:{enabled:{boolean:RC},speed:{x:{number:jC},y:{number:jC},zoom:{number:jC},__type__:{object:HC}},bindToWindow:{boolean:RC},autoFocus:{boolean:RC},__type__:{object:HC,boolean:RC}},multiselect:{boolean:RC},navigationButtons:{boolean:RC},selectable:{boolean:RC},selectConnectedEdges:{boolean:RC},hoverConnectedEdges:{boolean:RC},tooltipDelay:{number:jC},zoomView:{boolean:RC},zoomSpeed:{number:jC},__type__:{object:HC}},layout:{randomSeed:{undefined:"undefined",number:jC,string:AC},improvedLayout:{boolean:RC},clusterThreshold:{number:jC},hierarchical:{enabled:{boolean:RC},levelSeparation:{number:jC},nodeSpacing:{number:jC},treeSpacing:{number:jC},blockShifting:{boolean:RC},edgeMinimization:{boolean:RC},parentCentralization:{boolean:RC},direction:{string:["UD","DU","LR","RL"]},sortMethod:{string:["hubsize","directed"]},shakeTowards:{string:["leaves","roots"]},__type__:{object:HC,boolean:RC}},__type__:{object:HC}},manipulation:{enabled:{boolean:RC},initiallyActive:{boolean:RC},addNode:{boolean:RC,function:"function"},addEdge:{boolean:RC,function:"function"},editNode:{function:"function"},editEdge:{editWithoutDrag:{function:"function"},__type__:{object:HC,boolean:RC,function:"function"}},deleteNode:{boolean:RC,function:"function"},deleteEdge:{boolean:RC,function:"function"},controlNodeStyle:VC,__type__:{object:HC,boolean:RC}},nodes:VC,physics:{enabled:{boolean:RC},barnesHut:{theta:{number:jC},gravitationalConstant:{number:jC},centralGravity:{number:jC},springLength:{number:jC},springConstant:{number:jC},damping:{number:jC},avoidOverlap:{number:jC},__type__:{object:HC}},forceAtlas2Based:{theta:{number:jC},gravitationalConstant:{number:jC},centralGravity:{number:jC},springLength:{number:jC},springConstant:{number:jC},damping:{number:jC},avoidOverlap:{number:jC},__type__:{object:HC}},repulsion:{centralGravity:{number:jC},springLength:{number:jC},springConstant:{number:jC},nodeDistance:{number:jC},damping:{number:jC},__type__:{object:HC}},hierarchicalRepulsion:{centralGravity:{number:jC},springLength:{number:jC},springConstant:{number:jC},nodeDistance:{number:jC},damping:{number:jC},avoidOverlap:{number:jC},__type__:{object:HC}},maxVelocity:{number:jC},minVelocity:{number:jC},solver:{string:["barnesHut","repulsion","hierarchicalRepulsion","forceAtlas2Based"]},stabilization:{enabled:{boolean:RC},iterations:{number:jC},updateInterval:{number:jC},onlyDynamicEdges:{boolean:RC},fit:{boolean:RC},__type__:{object:HC,boolean:RC}},timestep:{number:jC},adaptiveTimestep:{boolean:RC},wind:{x:{number:jC},y:{number:jC},__type__:{object:HC}},__type__:{object:HC,boolean:RC}},autoResize:{boolean:RC},clickToUse:{boolean:RC},locale:{string:AC},locales:{__any__:{any:"any"},__type__:{object:HC}},height:{string:AC},width:{string:AC},__type__:{object:HC}},UC={nodes:{borderWidth:[1,0,10,1],borderWidthSelected:[2,0,10,1],color:{border:["color","#2B7CE9"],background:["color","#97C2FC"],highlight:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]},hover:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]}},opacity:[0,0,1,.1],fixed:{x:!1,y:!1},font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[0,0,50,1],strokeColor:["color","#ffffff"]},hidden:!1,labelHighlightBold:!0,physics:!0,scaling:{min:[10,0,200,1],max:[30,0,200,1],label:{enabled:!1,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},shape:["ellipse","box","circle","database","diamond","dot","square","star","text","triangle","triangleDown","hexagon"],shapeProperties:{borderDashes:!1,borderRadius:[6,0,20,1],interpolation:!0,useImageSize:!1},size:[25,0,200,1]},edges:{arrows:{to:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},middle:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},from:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"}},endPointOffset:{from:[0,-10,10,1],to:[0,-10,10,1]},arrowStrikethrough:!0,color:{color:["color","#848484"],highlight:["color","#848484"],hover:["color","#848484"],inherit:["from","to","both",!0,!1],opacity:[1,0,1,.05]},dashes:!1,font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[2,0,50,1],strokeColor:["color","#ffffff"],align:["horizontal","top","middle","bottom"]},hidden:!1,hoverWidth:[1.5,0,5,.1],labelHighlightBold:!0,physics:!0,scaling:{min:[1,0,100,1],max:[15,0,100,1],label:{enabled:!0,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},selectionWidth:[1.5,0,5,.1],selfReferenceSize:[20,0,200,1],selfReference:{size:[20,0,200,1],angle:[Math.PI/2,-6*Math.PI,6*Math.PI,Math.PI/8],renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},smooth:{enabled:!0,type:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"],forceDirection:["horizontal","vertical","none"],roundness:[.5,0,1,.05]},width:[1,0,30,1]},layout:{hierarchical:{enabled:!1,levelSeparation:[150,20,500,5],nodeSpacing:[100,20,500,5],treeSpacing:[200,20,500,5],blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:["UD","DU","LR","RL"],sortMethod:["hubsize","directed"],shakeTowards:["leaves","roots"]}},interaction:{dragNodes:!0,dragView:!0,hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1,hover:!1,keyboard:{enabled:!1,speed:{x:[10,0,40,1],y:[10,0,40,1],zoom:[.02,0,.1,.005]},bindToWindow:!0,autoFocus:!0},multiselect:!1,navigationButtons:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0,tooltipDelay:[300,0,1e3,25],zoomView:!0,zoomSpeed:[1,.1,2,.1]},manipulation:{enabled:!1,initiallyActive:!1},physics:{enabled:!0,barnesHut:{theta:[.5,.1,1,.05],gravitationalConstant:[-2e3,-3e4,0,50],centralGravity:[.3,0,10,.05],springLength:[95,0,500,5],springConstant:[.04,0,1.2,.005],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},forceAtlas2Based:{theta:[.5,.1,1,.05],gravitationalConstant:[-50,-500,0,1],centralGravity:[.01,0,1,.005],springLength:[95,0,500,5],springConstant:[.08,0,1.2,.005],damping:[.4,0,1,.01],avoidOverlap:[0,0,1,.01]},repulsion:{centralGravity:[.2,0,10,.05],springLength:[200,0,500,5],springConstant:[.05,0,1.2,.005],nodeDistance:[100,0,500,5],damping:[.09,0,1,.01]},hierarchicalRepulsion:{centralGravity:[.2,0,10,.05],springLength:[100,0,500,5],springConstant:[.01,0,1.2,.005],nodeDistance:[120,0,500,5],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},maxVelocity:[50,0,150,1],minVelocity:[.1,.01,.5,.01],solver:["barnesHut","forceAtlas2Based","repulsion","hierarchicalRepulsion"],timestep:[.5,.01,1,.01],wind:{x:[0,-10,10,.1],y:[0,-10,10,.1]}}},YC=(t,e,i)=>{var o;return!(!_u(t).call(t,"physics")||!_u(o=UC.physics.solver).call(o,e)||i.physics.solver===e||"wind"===e)};var XC=Object.freeze({__proto__:null,allOptions:qC,configuratorHideOption:YC,configureOptions:UC});class KC{constructor(){}getDistances(t,e,i){const o={},s=t.edges;for(let t=0;t<e.length;t++){const i={};o[e[t]]=i;for(let o=0;o<e.length;o++)i[e[o]]=t==o?0:1e9}for(let t=0;t<i.length;t++){const e=s[i[t]];!0===e.connected&&void 0!==o[e.fromId]&&void 0!==o[e.toId]&&(o[e.fromId][e.toId]=1,o[e.toId][e.fromId]=1)}const n=e.length;for(let t=0;t<n;t++){const i=e[t],s=o[i];for(let t=0;t<n-1;t++){const r=e[t],a=o[r];for(let h=t+1;h<n;h++){const t=e[h],n=o[t],d=Math.min(a[t],a[i]+s[t]);a[t]=d,n[r]=d}}}return o}}class GC{constructor(t,e,i){this.body=t,this.springLength=e,this.springConstant=i,this.distanceSolver=new KC}setOptions(t){t&&(t.springLength&&(this.springLength=t.springLength),t.springConstant&&(this.springConstant=t.springConstant))}solve(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o=this.distanceSolver.getDistances(this.body,t,e);this._createL_matrix(o),this._createK_matrix(o),this._createE_matrix();let s=0;const n=Math.max(1e3,Math.min(10*this.body.nodeIndices.length,6e3));let r=1e9,a=0,h=0,d=0,l=0,c=0;for(;r>.01&&s<n;)for(s+=1,[a,r,h,d]=this._getHighestEnergyNode(i),l=r,c=0;l>1&&c<5;)c+=1,this._moveNode(a,h,d),[l,h,d]=this._getEnergy(a)}_getHighestEnergyNode(t){const e=this.body.nodeIndices,i=this.body.nodes;let o=0,s=e[0],n=0,r=0;for(let a=0;a<e.length;a++){const h=e[a];if(!0!==i[h].predefinedPosition||!0===i[h].isCluster&&!0===t||!0!==i[h].options.fixed.x||!0!==i[h].options.fixed.y){const[t,e,i]=this._getEnergy(h);o<t&&(o=t,s=h,n=e,r=i)}}return[s,o,n,r]}_getEnergy(t){const[e,i]=this.E_sums[t];return[Math.sqrt(e**2+i**2),e,i]}_moveNode(t,e,i){const o=this.body.nodeIndices,s=this.body.nodes;let n=0,r=0,a=0;const h=s[t].x,d=s[t].y,l=this.K_matrix[t],c=this.L_matrix[t];for(let e=0;e<o.length;e++){const i=o[e];if(i!==t){const t=s[i].x,e=s[i].y,o=l[i],u=c[i],p=1/((h-t)**2+(d-e)**2)**1.5;n+=o*(1-u*(d-e)**2*p),r+=o*(u*(h-t)*(d-e)*p),a+=o*(1-u*(h-t)**2*p)}}const u=(e/n+i/r)/(r/n-a/r),p=-(r*u+e)/n;s[t].x+=p,s[t].y+=u,this._updateE_matrix(t)}_createL_matrix(t){const e=this.body.nodeIndices,i=this.springLength;this.L_matrix=[];for(let o=0;o<e.length;o++){this.L_matrix[e[o]]={};for(let s=0;s<e.length;s++)this.L_matrix[e[o]][e[s]]=i*t[e[o]][e[s]]}}_createK_matrix(t){const e=this.body.nodeIndices,i=this.springConstant;this.K_matrix=[];for(let o=0;o<e.length;o++){this.K_matrix[e[o]]={};for(let s=0;s<e.length;s++)this.K_matrix[e[o]][e[s]]=i*t[e[o]][e[s]]**-2}}_createE_matrix(){const t=this.body.nodeIndices,e=this.body.nodes;this.E_matrix={},this.E_sums={};for(let e=0;e<t.length;e++)this.E_matrix[t[e]]=[];for(let i=0;i<t.length;i++){const o=t[i],s=e[o].x,n=e[o].y;let r=0,a=0;for(let h=i;h<t.length;h++){const d=t[h];if(d!==o){const t=e[d].x,l=e[d].y,c=1/Math.sqrt((s-t)**2+(n-l)**2);this.E_matrix[o][h]=[this.K_matrix[o][d]*(s-t-this.L_matrix[o][d]*(s-t)*c),this.K_matrix[o][d]*(n-l-this.L_matrix[o][d]*(n-l)*c)],this.E_matrix[d][i]=this.E_matrix[o][h],r+=this.E_matrix[o][h][0],a+=this.E_matrix[o][h][1]}}this.E_sums[o]=[r,a]}}_updateE_matrix(t){const e=this.body.nodeIndices,i=this.body.nodes,o=this.E_matrix[t],s=this.K_matrix[t],n=this.L_matrix[t],r=i[t].x,a=i[t].y;let h=0,d=0;for(let l=0;l<e.length;l++){const c=e[l];if(c!==t){const t=o[l],e=t[0],u=t[1],p=i[c].x,g=i[c].y,f=1/Math.sqrt((r-p)**2+(a-g)**2),m=s[c]*(r-p-n[c]*(r-p)*f),y=s[c]*(a-g-n[c]*(a-g)*f);o[l]=[m,y],h+=m,d+=y;const b=this.E_sums[c];b[0]+=m-e,b[1]+=y-u}}this.E_sums[t]=[h,d]}}function ZC(t,e,i){var o,s,n,r;if(!(this instanceof ZC))throw new SyntaxError("Constructor must be called with the new operator");this.options={},this.defaultOptions={locale:"en",locales:Ty,clickToUse:!1},wo(this.options,this.defaultOptions),this.body={container:t,nodes:{},nodeIndices:[],edges:{},edgeIndices:[],emitter:{on:Ho(o=this.on).call(o,this),off:Ho(s=this.off).call(s,this),emit:Ho(n=this.emit).call(n,this),once:Ho(r=this.once).call(r,this)},eventListeners:{onTap:function(){},onTouch:function(){},onDoubleTap:function(){},onHold:function(){},onDragStart:function(){},onDrag:function(){},onDragEnd:function(){},onMouseWheel:function(){},onPinch:function(){},onMouseMove:function(){},onRelease:function(){},onContext:function(){}},data:{nodes:null,edges:null},functions:{createNode:function(){},createEdge:function(){},getPointer:function(){}},modules:{},view:{scale:1,translation:{x:0,y:0}},selectionBox:{show:!1,position:{start:{x:0,y:0},end:{x:0,y:0}}}},this.bindEventListeners(),this.images=new Dy((()=>this.body.emitter.emit("_requestRedraw"))),this.groups=new Rv,this.canvas=new cE(this.body),this.selectionHandler=new xO(this.body,this.canvas),this.interactionHandler=new fE(this.body,this.canvas,this.selectionHandler),this.view=new uE(this.body,this.canvas),this.renderer=new aE(this.body,this.canvas),this.physics=new $x(this.body),this.layoutEngine=new zC(this.body),this.clustering=new rE(this.body),this.manipulation=new NC(this.body,this.canvas,this.selectionHandler,this.interactionHandler),this.nodesHandler=new lx(this.body,this.images,this.groups,this.layoutEngine),this.edgesHandler=new Vx(this.body,this.images,this.groups),this.body.modules.kamadaKawai=new GC(this.body,150,.05),this.body.modules.clustering=this.clustering,this.canvas._create(),this.setOptions(i),this.setData(e)}Go(ZC.prototype),ZC.prototype.setOptions=function(t){if(null===t&&(t=void 0),void 0!==t){!0===Gm.validate(t,qC)&&console.error("%cErrors have been found in the supplied options object.",Km);if(vm(["locale","locales","clickToUse"],this.options,t),void 0!==t.locale&&(t.locale=function(t,e){try{const[o,s]=e.split(/[-_ /]/,2),n=null!=o?o.toLowerCase():null,r=null!=s?s.toUpperCase():null;if(n&&r){const e=n+"-"+r;if(Object.prototype.hasOwnProperty.call(t,e))return e;var i;console.warn(Pu(i="Unknown variant ".concat(r," of language ")).call(i,n,"."))}if(n){const e=n;if(Object.prototype.hasOwnProperty.call(t,e))return e;console.warn("Unknown language ".concat(n))}return console.warn("Unknown locale ".concat(e,", falling back to English.")),"en"}catch(t){return console.error(t),console.warn("Unexpected error while normalizing locale ".concat(e,", falling back to English.")),"en"}}(t.locales||this.options.locales,t.locale)),t=this.layoutEngine.setOptions(t.layout,t),this.canvas.setOptions(t),this.groups.setOptions(t.groups),this.nodesHandler.setOptions(t.nodes),this.edgesHandler.setOptions(t.edges),this.physics.setOptions(t.physics),this.manipulation.setOptions(t.manipulation,t,this.options),this.interactionHandler.setOptions(t.interaction),this.renderer.setOptions(t.interaction),this.selectionHandler.setOptions(t.interaction),void 0!==t.groups&&this.body.emitter.emit("refreshNodes"),"configure"in t&&(this.configurator||(this.configurator=new Um(this,this.body.container,UC,this.canvas.pixelRatio,YC)),this.configurator.setOptions(t.configure)),this.configurator&&!0===this.configurator.options.enabled){const t={nodes:{},edges:{},layout:{},interaction:{},manipulation:{},physics:{},global:{}};_m(t.nodes,this.nodesHandler.options),_m(t.edges,this.edgesHandler.options),_m(t.layout,this.layoutEngine.options),_m(t.interaction,this.selectionHandler.options),_m(t.interaction,this.renderer.options),_m(t.interaction,this.interactionHandler.options),_m(t.manipulation,this.manipulation.options),_m(t.physics,this.physics.options),_m(t.global,this.canvas.options),_m(t.global,this.options),this.configurator.setModuleOptions(t)}void 0!==t.clickToUse?!0===t.clickToUse?void 0===this.activator&&(this.activator=new qm(this.canvas.frame),this.activator.on("change",(()=>{this.body.emitter.emit("activate")}))):(void 0!==this.activator&&(this.activator.destroy(),delete this.activator),this.body.emitter.emit("activate")):this.body.emitter.emit("activate"),this.canvas.setSize(),this.body.emitter.emit("startSimulation")}},ZC.prototype._updateVisibleIndices=function(){const t=this.body.nodes,e=this.body.edges;this.body.nodeIndices=[],this.body.edgeIndices=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clustering._isClusteredNode(e)||!1!==t[e].options.hidden||this.body.nodeIndices.push(t[e].id));for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const o=e[i],s=t[o.fromId],n=t[o.toId],r=void 0!==s&&void 0!==n;!this.clustering._isClusteredEdge(i)&&!1===o.options.hidden&&r&&!1===s.options.hidden&&!1===n.options.hidden&&this.body.edgeIndices.push(o.id)}},ZC.prototype.bindEventListeners=function(){this.body.emitter.on("_dataChanged",(()=>{this.edgesHandler._updateState(),this.body.emitter.emit("_dataUpdated")})),this.body.emitter.on("_dataUpdated",(()=>{this.clustering._updateState(),this._updateVisibleIndices(),this._updateValueRange(this.body.nodes),this._updateValueRange(this.body.edges),this.body.emitter.emit("startSimulation"),this.body.emitter.emit("_requestRedraw")}))},ZC.prototype.setData=function(t){if(this.body.emitter.emit("resetPhysics"),this.body.emitter.emit("_resetData"),this.selectionHandler.unselectAll(),t&&t.dot&&(t.nodes||t.edges))throw new SyntaxError('Data must contain either parameter "dot" or  parameter pair "nodes" and "edges", but not both.');if(this.setOptions(t&&t.options),t&&t.dot){console.warn("The dot property has been deprecated. Please use the static convertDot method to convert DOT into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertDot(dotString);");const e=Oy(t.dot);this.setData(e)}else if(t&&t.gephi){console.warn("The gephi property has been deprecated. Please use the static convertGephi method to convert gephi into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertGephi(gephiJson);");const e=ky(t.gephi);this.setData(e)}else this.nodesHandler.setData(t&&t.nodes,!0),this.edgesHandler.setData(t&&t.edges,!0),this.body.emitter.emit("_dataChanged"),this.body.emitter.emit("_dataLoaded"),this.body.emitter.emit("initPhysics")},ZC.prototype.destroy=function(){this.body.emitter.emit("destroy"),this.body.emitter.off(),this.off(),delete this.groups,delete this.canvas,delete this.selectionHandler,delete this.interactionHandler,delete this.view,delete this.renderer,delete this.physics,delete this.layoutEngine,delete this.clustering,delete this.manipulation,delete this.nodesHandler,delete this.edgesHandler,delete this.configurator,delete this.images;for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&delete this.body.nodes[t];for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&delete this.body.edges[t];gm(this.body.container)},ZC.prototype._updateValueRange=function(t){let e,i,o,s=0;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e].getValue();void 0!==n&&(i=void 0===i?n:Math.min(n,i),o=void 0===o?n:Math.max(n,o),s+=n)}if(void 0!==i&&void 0!==o)for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&t[e].setValueRange(i,o,s)},ZC.prototype.isActive=function(){return!this.activator||this.activator.active},ZC.prototype.setSize=function(){return this.canvas.setSize.apply(this.canvas,arguments)},ZC.prototype.canvasToDOM=function(){return this.canvas.canvasToDOM.apply(this.canvas,arguments)},ZC.prototype.DOMtoCanvas=function(){return this.canvas.DOMtoCanvas.apply(this.canvas,arguments)},ZC.prototype.findNode=function(){return this.clustering.findNode.apply(this.clustering,arguments)},ZC.prototype.isCluster=function(){return this.clustering.isCluster.apply(this.clustering,arguments)},ZC.prototype.openCluster=function(){return this.clustering.openCluster.apply(this.clustering,arguments)},ZC.prototype.cluster=function(){return this.clustering.cluster.apply(this.clustering,arguments)},ZC.prototype.getNodesInCluster=function(){return this.clustering.getNodesInCluster.apply(this.clustering,arguments)},ZC.prototype.clusterByConnection=function(){return this.clustering.clusterByConnection.apply(this.clustering,arguments)},ZC.prototype.clusterByHubsize=function(){return this.clustering.clusterByHubsize.apply(this.clustering,arguments)},ZC.prototype.updateClusteredNode=function(){return this.clustering.updateClusteredNode.apply(this.clustering,arguments)},ZC.prototype.getClusteredEdges=function(){return this.clustering.getClusteredEdges.apply(this.clustering,arguments)},ZC.prototype.getBaseEdge=function(){return this.clustering.getBaseEdge.apply(this.clustering,arguments)},ZC.prototype.getBaseEdges=function(){return this.clustering.getBaseEdges.apply(this.clustering,arguments)},ZC.prototype.updateEdge=function(){return this.clustering.updateEdge.apply(this.clustering,arguments)},ZC.prototype.clusterOutliers=function(){return this.clustering.clusterOutliers.apply(this.clustering,arguments)},ZC.prototype.getSeed=function(){return this.layoutEngine.getSeed.apply(this.layoutEngine,arguments)},ZC.prototype.enableEditMode=function(){return this.manipulation.enableEditMode.apply(this.manipulation,arguments)},ZC.prototype.disableEditMode=function(){return this.manipulation.disableEditMode.apply(this.manipulation,arguments)},ZC.prototype.addNodeMode=function(){return this.manipulation.addNodeMode.apply(this.manipulation,arguments)},ZC.prototype.editNode=function(){return this.manipulation.editNode.apply(this.manipulation,arguments)},ZC.prototype.editNodeMode=function(){return console.warn("Deprecated: Please use editNode instead of editNodeMode."),this.manipulation.editNode.apply(this.manipulation,arguments)},ZC.prototype.addEdgeMode=function(){return this.manipulation.addEdgeMode.apply(this.manipulation,arguments)},ZC.prototype.editEdgeMode=function(){return this.manipulation.editEdgeMode.apply(this.manipulation,arguments)},ZC.prototype.deleteSelected=function(){return this.manipulation.deleteSelected.apply(this.manipulation,arguments)},ZC.prototype.getPositions=function(){return this.nodesHandler.getPositions.apply(this.nodesHandler,arguments)},ZC.prototype.getPosition=function(){return this.nodesHandler.getPosition.apply(this.nodesHandler,arguments)},ZC.prototype.storePositions=function(){return this.nodesHandler.storePositions.apply(this.nodesHandler,arguments)},ZC.prototype.moveNode=function(){return this.nodesHandler.moveNode.apply(this.nodesHandler,arguments)},ZC.prototype.getBoundingBox=function(){return this.nodesHandler.getBoundingBox.apply(this.nodesHandler,arguments)},ZC.prototype.getConnectedNodes=function(t){return void 0!==this.body.nodes[t]?this.nodesHandler.getConnectedNodes.apply(this.nodesHandler,arguments):this.edgesHandler.getConnectedNodes.apply(this.edgesHandler,arguments)},ZC.prototype.getConnectedEdges=function(){return this.nodesHandler.getConnectedEdges.apply(this.nodesHandler,arguments)},ZC.prototype.startSimulation=function(){return this.physics.startSimulation.apply(this.physics,arguments)},ZC.prototype.stopSimulation=function(){return this.physics.stopSimulation.apply(this.physics,arguments)},ZC.prototype.stabilize=function(){return this.physics.stabilize.apply(this.physics,arguments)},ZC.prototype.getSelection=function(){return this.selectionHandler.getSelection.apply(this.selectionHandler,arguments)},ZC.prototype.setSelection=function(){return this.selectionHandler.setSelection.apply(this.selectionHandler,arguments)},ZC.prototype.getSelectedNodes=function(){return this.selectionHandler.getSelectedNodeIds.apply(this.selectionHandler,arguments)},ZC.prototype.getSelectedEdges=function(){return this.selectionHandler.getSelectedEdgeIds.apply(this.selectionHandler,arguments)},ZC.prototype.getNodeAt=function(){const t=this.selectionHandler.getNodeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},ZC.prototype.getEdgeAt=function(){const t=this.selectionHandler.getEdgeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},ZC.prototype.selectNodes=function(){return this.selectionHandler.selectNodes.apply(this.selectionHandler,arguments)},ZC.prototype.selectEdges=function(){return this.selectionHandler.selectEdges.apply(this.selectionHandler,arguments)},ZC.prototype.unselectAll=function(){this.selectionHandler.unselectAll.apply(this.selectionHandler,arguments),this.selectionHandler.commitWithoutEmitting.apply(this.selectionHandler),this.redraw()},ZC.prototype.redraw=function(){return this.renderer.redraw.apply(this.renderer,arguments)},ZC.prototype.getScale=function(){return this.view.getScale.apply(this.view,arguments)},ZC.prototype.getViewPosition=function(){return this.view.getViewPosition.apply(this.view,arguments)},ZC.prototype.fit=function(){return this.view.fit.apply(this.view,arguments)},ZC.prototype.moveTo=function(){return this.view.moveTo.apply(this.view,arguments)},ZC.prototype.focus=function(){return this.view.focus.apply(this.view,arguments)},ZC.prototype.releaseNode=function(){return this.view.releaseNode.apply(this.view,arguments)},ZC.prototype.getOptionsFromConfigurator=function(){let t={};return this.configurator&&(t=this.configurator.getOptions.apply(this.configurator)),t};const QC=Oy;export{ZC as Network,Dy as NetworkImages,Cy as networkDOTParser,Sy as networkGephiParser,XC as networkOptions,QC as parseDOTNetwork,ky as parseGephiNetwork};
//# sourceMappingURL=vis-network.min.js.map
