{"name": "vis-data", "version": "7.1.9", "description": "Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.", "homepage": "http://visjs.org/", "license": "(Apache-2.0 OR MIT)", "repository": {"type": "git", "url": "https://github.com/visjs/vis-data.git"}, "bugs": {"url": "https://github.com/visjs/vis-data/issues"}, "keywords": ["vis", "visualization", "web based", "browser based", "javascript", "chart", "linechart", "timeline", "graph", "network", "browser"], "browser": "peer/umd/vis-data.min.js", "jsnext": "esnext/esm/vis-data.js", "main": "peer/umd/vis-data.js", "module": "peer/esm/vis-data.js", "types": "declarations/index.d.ts", "files": ["LICENSE*", "declarations", "dist", "esnext", "peer", "standalone"], "funding": {"type": "opencollective", "url": "https://opencollective.com/visjs"}, "scripts": {"build": "npm run build:types && npm run build:code && npm run build:docs", "build:code": "rollup --bundleConfigAsCjs --config rollup.build.js && rollup --bundleConfigAsCjs --config rollup.config.js", "build:docs": "typedoc", "build:types": "tsc -p tsconfig.types.json", "clean": "rimraf --glob \"{declarations,dist,esnext,peer,standalone,tsdocs}/*\"", "contributors:update": "git-authors-cli", "style": "prettier --check .", "style-fix": "prettier --write .", "lint": "eslint --ext .js,.ts .", "lint-fix": "eslint --fix --ext .js,.ts .", "prepublishOnly": "npm run build", "test": "npm run test:coverage && npm run test:interop", "test:coverage": "BABEL_ENV=test-cov nyc mocha", "test:interop": "node interop.js", "test:interop:debug": "npm run test:interop -- --fail-command \"$SHELL\"", "test:unit": "BABEL_ENV=test mocha", "type-check": "tsc --noemit", "version": "npm run contributors:update && git add package.json", "watch": "rollup --bundleConfigAsCjs --watch --config rollup.config.js", "watch-dev": "npm run watch-dev", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,css,html,json,md,yml,yaml}": "prettier --write", "*.{js,ts}": "eslint --fix", ".*.{js,ts,css,html,json,md,yml,yaml}": "prettier --write", ".*.{js,ts}": "eslint --fix"}, "peerDependencies": {"uuid": "^3.4.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "vis-util": "^5.0.1"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "7.20.7", "@egjs/hammerjs": "2.0.17", "@types/chai": "4.3.11", "@types/mocha": "10.0.6", "@types/node": "^20.9.0", "@types/sinon": "17.0.2", "@types/uuid": "9.0.7", "component-emitter": "2.0.0", "eslint": "8.54.0", "git-authors-cli": "1.0.47", "husky": "8.0.3", "lint-staged": "15.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "rimraf": "5.0.5", "sazerac": "2.0.0", "sinon": "17.0.1", "snap-shot-it": "7.9.10", "typedoc": "0.25.3", "uuid": "9.0.1", "vis-dev-utils": "4.0.41", "vis-util": "5.0.7"}, "contributors": ["jos <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "wimrijn<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "macleodbroad-wf <<EMAIL>>", "<PERSON><PERSON><PERSON> Page <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<PERSON>.He<PERSON>@goeg.at>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Måns <PERSON><PERSON> <<EMAIL>>", "Brandon Mills <<EMAIL>>", "fpierrat2 <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "mdxs <van.wijger<PERSON>@mdxs.net>", "<PERSON><PERSON> <<EMAIL>>", "TimurUncountable <<EMAIL>>", "<PERSON> <93350506+<PERSON><PERSON><PERSON>@users.noreply.github.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "AoDev <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "The Gitter Badger <<EMAIL>>", "<PERSON> <<EMAIL>>", "bertolds <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Dilek Üzülmez <PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "easleydp <<EMAIL>>", "Alex<PERSON>angelov <<EMAIL>>", "fabriziofortino <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> (<PERSON>) <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <jero<PERSON><PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "justin<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "maik <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "CapitanMorgan <<EMAIL>>", "oliver <oliver@werklaptop2.(none)>", "dockstreet <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]}