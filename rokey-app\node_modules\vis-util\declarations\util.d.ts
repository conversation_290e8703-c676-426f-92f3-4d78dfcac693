/**
 * <PERSON><PERSON>, <PERSON>tura<PERSON>, Value.
 */
export interface HSV {
    /**
     * <PERSON><PERSON> \<0, 1\>.
     */
    h: number;
    /**
     * Saturation \<0, 1\>.
     */
    s: number;
    /**
     * Value \<0, 1\>.
     */
    v: number;
}
/**
 * Red, Green, Blue.
 */
export interface RGB {
    /**
     * Red \<0, 255\> integer.
     */
    r: number;
    /**
     * Green \<0, 255\> integer.
     */
    g: number;
    /**
     * <PERSON> \<0, 255\> integer.
     */
    b: number;
}
/**
 * Red, Green, Blue, Alpha.
 */
export interface RGBA {
    /**
     * Red \<0, 255\> integer.
     */
    r: number;
    /**
     * <PERSON> \<0, 255\> integer.
     */
    g: number;
    /**
     * Blue \<0, 255\> integer.
     */
    b: number;
    /**
     * Alpha \<0, 1\>.
     */
    a: number;
}
/**
 * Test whether given object is a number.
 *
 * @param value - Input value of unknown type.
 * @returns True if number, false otherwise.
 */
export declare function isNumber(value: unknown): value is number;
/**
 * Remove everything in the DOM object.
 *
 * @param DOMobject - Node whose child nodes will be recursively deleted.
 */
export declare function recursiveDOMDelete(DOMobject: Node | null | undefined): void;
/**
 * Test whether given object is a string.
 *
 * @param value - Input value of unknown type.
 * @returns True if string, false otherwise.
 */
export declare function isString(value: unknown): value is string;
/**
 * Test whether given object is a object (not primitive or null).
 *
 * @param value - Input value of unknown type.
 * @returns True if not null object, false otherwise.
 */
export declare function isObject(value: unknown): value is object;
/**
 * Test whether given object is a Date, or a String containing a Date.
 *
 * @param value - Input value of unknown type.
 * @returns True if Date instance or string date representation, false otherwise.
 */
export declare function isDate(value: unknown): value is Date | string;
/**
 * Fill an object with a possibly partially defined other object.
 *
 * Only copies values for the properties already present in a.
 * That means an object is not created on a property if only the b object has it.
 *
 * @param a - The object that will have it's properties updated.
 * @param b - The object with property updates.
 * @param allowDeletion - If true, delete properties in a that are explicitly set to null in b.
 */
export declare function fillIfDefined<T extends object>(a: T, b: Partial<T>, allowDeletion?: boolean): void;
/**
 * Copy the values of all of the enumerable own properties from one or more source objects to a
 * target object. Returns the target object.
 *
 * @param target - The target object to copy to.
 * @param source - The source object from which to copy properties.
 * @returns The target object.
 */
export declare const extend: {
    <T extends {}, U>(target: T, source: U): T & U;
    <T_1 extends {}, U_1, V>(target: T_1, source1: U_1, source2: V): T_1 & U_1 & V;
    <T_2 extends {}, U_2, V_1, W>(target: T_2, source1: U_2, source2: V_1, source3: W): T_2 & U_2 & V_1 & W;
    (target: object, ...sources: any[]): any;
};
/**
 * Extend object a with selected properties of object b or a series of objects.
 *
 * @remarks
 * Only properties with defined values are copied.
 * @param props - Properties to be copied to a.
 * @param a - The target.
 * @param others - The sources.
 * @returns Argument a.
 */
export declare function selectiveExtend(props: string[], a: any, ...others: any[]): any;
/**
 * Extend object a with selected properties of object b.
 * Only properties with defined values are copied.
 *
 * @remarks
 * Previous version of this routine implied that multiple source objects could
 * be used; however, the implementation was **wrong**. Since multiple (\>1)
 * sources weren't used anywhere in the `vis.js` code, this has been removed
 * @param props - Names of first-level properties to copy over.
 * @param a - Target object.
 * @param b - Source object.
 * @param allowDeletion - If true, delete property in a if explicitly set to null in b.
 * @returns Argument a.
 */
export declare function selectiveDeepExtend(props: string[], a: any, b: any, allowDeletion?: boolean): any;
/**
 * Extend object `a` with properties of object `b`, ignoring properties which
 * are explicitly specified to be excluded.
 *
 * @remarks
 * The properties of `b` are considered for copying. Properties which are
 * themselves objects are are also extended. Only properties with defined
 * values are copied.
 * @param propsToExclude - Names of properties which should *not* be copied.
 * @param a - Object to extend.
 * @param b - Object to take properties from for extension.
 * @param allowDeletion - If true, delete properties in a that are explicitly
 * set to null in b.
 * @returns Argument a.
 */
export declare function selectiveNotDeepExtend(propsToExclude: string[], a: any, b: any, allowDeletion?: boolean): any;
/**
 * Deep extend an object a with the properties of object b.
 *
 * @param a - Target object.
 * @param b - Source object.
 * @param protoExtend - If true, the prototype values will also be extended.
 * (That is the options objects that inherit from others will also get the
 * inherited options).
 * @param allowDeletion - If true, the values of fields that are null will be deleted.
 * @returns Argument a.
 */
export declare function deepExtend(a: any, b: any, protoExtend?: boolean, allowDeletion?: boolean): any;
/**
 * Test whether all elements in two arrays are equal.
 *
 * @param a - First array.
 * @param b - Second array.
 * @returns True if both arrays have the same length and same elements (1 = '1').
 */
export declare function equalArray(a: unknown[], b: unknown[]): boolean;
/**
 * Get the type of an object, for example exports.getType([]) returns 'Array'.
 *
 * @param object - Input value of unknown type.
 * @returns Detected type.
 */
export declare function getType(object: unknown): string;
export declare function copyAndExtendArray<T>(arr: ReadonlyArray<T>, newValue: T): T[];
export declare function copyAndExtendArray<A, V>(arr: ReadonlyArray<A>, newValue: V): (A | V)[];
/**
 * Used to extend an array and copy it. This is used to propagate paths recursively.
 *
 * @param arr - The array to be copied.
 * @returns Shallow copy of arr.
 */
export declare function copyArray<T>(arr: ReadonlyArray<T>): T[];
/**
 * Retrieve the absolute left value of a DOM element.
 *
 * @param elem - A dom element, for example a div.
 * @returns The absolute left position of this element in the browser page.
 */
export declare function getAbsoluteLeft(elem: Element): number;
/**
 * Retrieve the absolute right value of a DOM element.
 *
 * @param elem - A dom element, for example a div.
 * @returns The absolute right position of this element in the browser page.
 */
export declare function getAbsoluteRight(elem: Element): number;
/**
 * Retrieve the absolute top value of a DOM element.
 *
 * @param elem - A dom element, for example a div.
 * @returns The absolute top position of this element in the browser page.
 */
export declare function getAbsoluteTop(elem: Element): number;
/**
 * Add a className to the given elements style.
 *
 * @param elem - The element to which the classes will be added.
 * @param classNames - Space separated list of classes.
 */
export declare function addClassName(elem: Element, classNames: string): void;
/**
 * Remove a className from the given elements style.
 *
 * @param elem - The element from which the classes will be removed.
 * @param classNames - Space separated list of classes.
 */
export declare function removeClassName(elem: Element, classNames: string): void;
export declare function forEach<V>(array: undefined | null | V[], callback: (value: V, index: number, object: V[]) => void): void;
export declare function forEach<O extends object>(object: undefined | null | O, callback: <Key extends keyof O>(value: O[Key], key: Key, object: O) => void): void;
/**
 * Convert an object into an array: all objects properties are put into the array. The resulting array is unordered.
 *
 * @param o - Object that contains the properties and methods.
 * @returns An array of unordered values.
 */
export declare const toArray: {
    <T>(o: {
        [s: string]: T;
    } | ArrayLike<T>): T[];
    (o: {}): any[];
};
/**
 * Update a property in an object.
 *
 * @param object - The object whose property will be updated.
 * @param key - Name of the property to be updated.
 * @param value - The new value to be assigned.
 * @returns Whether the value was updated (true) or already strictly the same in the original object (false).
 */
export declare function updateProperty<K extends string, V>(object: Record<K, V>, key: K, value: V): boolean;
/**
 * Throttle the given function to be only executed once per animation frame.
 *
 * @param fn - The original function.
 * @returns The throttled function.
 */
export declare function throttle(fn: () => void): () => void;
/**
 * Cancels the event's default action if it is cancelable, without stopping further propagation of the event.
 *
 * @param event - The event whose default action should be prevented.
 */
export declare function preventDefault(event: Event | undefined): void;
/**
 * Get HTML element which is the target of the event.
 *
 * @param event - The event.
 * @returns The element or null if not obtainable.
 */
export declare function getTarget(event?: Event | undefined): Element | null;
/**
 * Check if given element contains given parent somewhere in the DOM tree.
 *
 * @param element - The element to be tested.
 * @param parent - The ancestor (not necessarily parent) of the element.
 * @returns True if parent is an ancestor of the element, false otherwise.
 */
export declare function hasParent(element: Element, parent: Element): boolean;
export declare const option: {
    /**
     * Convert a value into a boolean.
     *
     * @param value - Value to be converted intoboolean, a function will be executed as `(() => unknown)`.
     * @param defaultValue - If the value or the return value of the function == null then this will be returned.
     * @returns Corresponding boolean value, if none then the default value, if none then null.
     */
    asBoolean(value: unknown, defaultValue?: boolean): boolean | null;
    /**
     * Convert a value into a number.
     *
     * @param value - Value to be converted intonumber, a function will be executed as `(() => unknown)`.
     * @param defaultValue - If the value or the return value of the function == null then this will be returned.
     * @returns Corresponding **boxed** number value, if none then the default value, if none then null.
     */
    asNumber(value: unknown, defaultValue?: number): number | null;
    /**
     * Convert a value into a string.
     *
     * @param value - Value to be converted intostring, a function will be executed as `(() => unknown)`.
     * @param defaultValue - If the value or the return value of the function == null then this will be returned.
     * @returns Corresponding **boxed** string value, if none then the default value, if none then null.
     */
    asString(value: unknown, defaultValue?: string): string | null;
    /**
     * Convert a value into a size.
     *
     * @param value - Value to be converted intosize, a function will be executed as `(() => unknown)`.
     * @param defaultValue - If the value or the return value of the function == null then this will be returned.
     * @returns Corresponding string value (number + 'px'), if none then the default value, if none then null.
     */
    asSize(value: unknown, defaultValue?: string): string | null;
    /**
     * Convert a value into a DOM Element.
     *
     * @param value - Value to be converted into DOM Element, a function will be executed as `(() => unknown)`.
     * @param defaultValue - If the value or the return value of the function == null then this will be returned.
     * @returns The DOM Element, if none then the default value, if none then null.
     */
    asElement<T extends Node>(value: T | (() => T | undefined) | undefined, defaultValue: T): T | null;
};
/**
 * Convert hex color string into RGB color object.
 *
 * @remarks
 * {@link http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb}
 * @param hex - Hex color string (3 or 6 digits, with or without #).
 * @returns RGB color object.
 */
export declare function hexToRGB(hex: string): RGB | null;
/**
 * This function takes string color in hex or RGB format and adds the opacity, RGBA is passed through unchanged.
 *
 * @param color - The color string (hex, RGB, RGBA).
 * @param opacity - The new opacity.
 * @returns RGBA string, for example 'rgba(255, 0, 127, 0.3)'.
 */
export declare function overrideOpacity(color: string, opacity: number): string;
/**
 * Convert RGB \<0, 255\> into hex color string.
 *
 * @param red - Red channel.
 * @param green - Green channel.
 * @param blue - Blue channel.
 * @returns Hex color string (for example: '#0acdc0').
 */
export declare function RGBToHex(red: number, green: number, blue: number): string;
export interface ColorObject {
    background?: string;
    border?: string;
    hover?: string | {
        border?: string;
        background?: string;
    };
    highlight?: string | {
        border?: string;
        background?: string;
    };
}
export interface FullColorObject {
    background: string;
    border: string;
    hover: {
        border: string;
        background: string;
    };
    highlight: {
        border: string;
        background: string;
    };
}
export declare function parseColor(inputColor: string): FullColorObject;
export declare function parseColor(inputColor: FullColorObject): FullColorObject;
export declare function parseColor(inputColor: ColorObject): ColorObject;
export declare function parseColor(inputColor: ColorObject, defaultColor: FullColorObject): FullColorObject;
/**
 * Convert RGB \<0, 255\> into HSV object.
 *
 * @remarks
 * {@link http://www.javascripter.net/faq/rgb2hsv.htm}
 * @param red - Red channel.
 * @param green - Green channel.
 * @param blue - Blue channel.
 * @returns HSV color object.
 */
export declare function RGBToHSV(red: number, green: number, blue: number): HSV;
/**
 * Append a string with css styles to an element.
 *
 * @param element - The element that will receive new styles.
 * @param cssText - The styles to be appended.
 */
export declare function addCssText(element: HTMLElement, cssText: string): void;
/**
 * Remove a string with css styles from an element.
 *
 * @param element - The element from which styles should be removed.
 * @param cssText - The styles to be removed.
 */
export declare function removeCssText(element: HTMLElement, cssText: string): void;
/**
 * Convert HSV \<0, 1\> into RGB color object.
 *
 * @remarks
 * {@link https://gist.github.com/mjijackson/5311256}
 * @param h - Hue.
 * @param s - Saturation.
 * @param v - Value.
 * @returns RGB color object.
 */
export declare function HSVToRGB(h: number, s: number, v: number): RGB;
/**
 * Convert HSV \<0, 1\> into hex color string.
 *
 * @param h - Hue.
 * @param s - Saturation.
 * @param v - Value.
 * @returns Hex color string.
 */
export declare function HSVToHex(h: number, s: number, v: number): string;
/**
 * Convert hex color string into HSV \<0, 1\>.
 *
 * @param hex - Hex color string.
 * @returns HSV color object.
 */
export declare function hexToHSV(hex: string): HSV;
/**
 * Validate hex color string.
 *
 * @param hex - Unknown string that may contain a color.
 * @returns True if the string is valid, false otherwise.
 */
export declare function isValidHex(hex: string): boolean;
/**
 * Validate RGB color string.
 *
 * @param rgb - Unknown string that may contain a color.
 * @returns True if the string is valid, false otherwise.
 */
export declare function isValidRGB(rgb: string): boolean;
/**
 * Validate RGBA color string.
 *
 * @param rgba - Unknown string that may contain a color.
 * @returns True if the string is valid, false otherwise.
 */
export declare function isValidRGBA(rgba: string): boolean;
/**
 * This recursively redirects the prototype of JSON objects to the referenceObject.
 * This is used for default options.
 *
 * @param fields - Names of properties to be bridged.
 * @param referenceObject - The original object.
 * @returns A new object inheriting from the referenceObject.
 */
export declare function selectiveBridgeObject<F extends string, V>(fields: F[], referenceObject: Record<F, V>): Record<F, V> | null;
export declare function bridgeObject<T extends object>(referenceObject: T): T;
export declare function bridgeObject<T>(referenceObject: T): null;
/**
 * This method provides a stable sort implementation, very fast for presorted data.
 *
 * @param a - The array to be sorted (in-place).
 * @param compare - An order comparator.
 * @returns The argument a.
 */
export declare function insertSort<T>(a: T[], compare: (a: T, b: T) => number): T[];
/**
 * This is used to set the options of subobjects in the options object.
 *
 * A requirement of these subobjects is that they have an 'enabled' element
 * which is optional for the user but mandatory for the program.
 *
 * The added value here of the merge is that option 'enabled' is set as required.
 *
 * @param mergeTarget - Either this.options or the options used for the groups.
 * @param options - Options.
 * @param option - Option key in the options argument.
 * @param globalOptions - Global options, passed in to determine value of option 'enabled'.
 */
export declare function mergeOptions(mergeTarget: any, options: any, option: string, globalOptions?: any): void;
export declare function binarySearchCustom<O extends object, K1 extends keyof O, K2 extends keyof O[K1]>(orderedItems: O[], comparator: (v: O[K1][K2]) => -1 | 0 | 1, field: K1, field2: K2): number;
export declare function binarySearchCustom<O extends object, K1 extends keyof O>(orderedItems: O[], comparator: (v: O[K1]) => -1 | 0 | 1, field: K1): number;
/**
 * This function does a binary search for a specific value in a sorted array.
 * If it does not exist but is in between of two values, we return either the
 * one before or the one after, depending on user input If it is found, we
 * return the index, else -1.
 *
 * @param orderedItems - Sorted array.
 * @param target - The searched value.
 * @param field - Name of the property in items to be searched.
 * @param sidePreference - If the target is between two values, should the index of the before or the after be returned?
 * @param comparator - An optional comparator, returning -1, 0, 1 for \<, ===, \>.
 * @returns The index of found value or -1 if nothing was found.
 */
export declare function binarySearchValue<T extends string>(orderedItems: {
    [K in T]: number;
}[], target: number, field: T, sidePreference: "before" | "after", comparator?: (a: number, b: number) => -1 | 0 | 1): number;
export declare const easingFunctions: {
    /**
     * Provides no easing and no acceleration.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    linear(t: number): number;
    /**
     * Accelerate from zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInQuad(t: number): number;
    /**
     * Decelerate to zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeOutQuad(t: number): number;
    /**
     * Accelerate until halfway, then decelerate.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInOutQuad(t: number): number;
    /**
     * Accelerate from zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInCubic(t: number): number;
    /**
     * Decelerate to zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeOutCubic(t: number): number;
    /**
     * Accelerate until halfway, then decelerate.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInOutCubic(t: number): number;
    /**
     * Accelerate from zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInQuart(t: number): number;
    /**
     * Decelerate to zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeOutQuart(t: number): number;
    /**
     * Accelerate until halfway, then decelerate.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInOutQuart(t: number): number;
    /**
     * Accelerate from zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInQuint(t: number): number;
    /**
     * Decelerate to zero velocity.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeOutQuint(t: number): number;
    /**
     * Accelerate until halfway, then decelerate.
     *
     * @param t - Time.
     * @returns Value at time t.
     */
    easeInOutQuint(t: number): number;
};
/**
 * Experimentaly compute the width of the scrollbar for this browser.
 *
 * @returns The width in pixels.
 */
export declare function getScrollBarWidth(): number;
/**
 * Get the top most property value from a pile of objects.
 *
 * @param pile - Array of objects, no required format.
 * @param accessors - Array of property names.
 * For example `object['foo']['bar']` → `['foo', 'bar']`.
 * @returns Value of the property with given accessors path from the first pile item where it's not undefined.
 */
export declare function topMost(pile: any, accessors: any): any;
//# sourceMappingURL=util.d.ts.map