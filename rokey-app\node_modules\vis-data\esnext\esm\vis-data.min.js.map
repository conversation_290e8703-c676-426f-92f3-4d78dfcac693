{"version": 3, "file": "vis-data.min.js", "sources": ["../../src/data-pipe.ts", "../../src/data-interface.ts", "../../src/queue.ts", "../../src/data-set-part.ts", "../../src/data-stream.ts", "../../src/data-set.ts", "../../src/data-view.ts", "../../src/data-set-check.ts", "../../src/data-view-check.ts"], "sourcesContent": ["import { DataInterface, EventCallbacks, PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * This interface is used to control the pipe.\n */\nexport interface DataPipe {\n  /**\n   * Take all items from the source data set or data view, transform them as\n   * configured and update the target data set.\n   */\n  all(): this;\n\n  /**\n   * Start observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   *\n   * @remarks\n   * The current content of the source data set will be ignored. If you for\n   * example want to process all the items that are already there use:\n   * `pipe.all().start()`.\n   */\n  start(): this;\n\n  /**\n   * Stop observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   */\n  stop(): this;\n}\n\n/**\n * This interface is used to construct the pipe.\n */\nexport type DataPipeFactory = InstanceType<typeof DataPipeUnderConstruction>;\n\n/**\n * Create new data pipe.\n *\n * @param from - The source data set or data view.\n * @remarks\n * Example usage:\n * ```typescript\n * interface AppItem {\n *   whoami: string;\n *   appData: unknown;\n *   visData: VisItem;\n * }\n * interface VisItem {\n *   id: number;\n *   label: string;\n *   color: string;\n *   x: number;\n *   y: number;\n * }\n *\n * const ds1 = new DataSet<AppItem, \"whoami\">([], { fieldId: \"whoami\" });\n * const ds2 = new DataSet<VisItem, \"id\">();\n *\n * const pipe = createNewDataPipeFrom(ds1)\n *   .filter((item): boolean => item.enabled === true)\n *   .map<VisItem, \"id\">((item): VisItem => item.visData)\n *   .to(ds2);\n *\n * pipe.start();\n * ```\n * @returns A factory whose methods can be used to configure the pipe.\n */\nexport function createNewDataPipeFrom<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n>(from: DataInterface<SI, SP>): DataPipeUnderConstruction<SI, SP> {\n  return new DataPipeUnderConstruction(from);\n}\n\ntype Transformer<T> = (input: T[]) => T[];\n\n/**\n * Internal implementation of the pipe. This should be accessible only through\n * `createNewDataPipeFrom` from the outside.\n *\n * @typeParam SI - Source item type.\n * @typeParam SP - Source item type's id property name.\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass SimpleDataPipe<\n  SI extends PartItem<SP>,\n  SP extends string,\n  TI extends PartItem<TP>,\n  TP extends string\n> implements DataPipe\n{\n  /**\n   * Bound listeners for use with `DataInterface['on' | 'off']`.\n   */\n  private readonly _listeners: EventCallbacks<SI, SP> = {\n    add: this._add.bind(this),\n    remove: this._remove.bind(this),\n    update: this._update.bind(this),\n  };\n\n  /**\n   * Create a new data pipe.\n   *\n   * @param _source - The data set or data view that will be observed.\n   * @param _transformers - An array of transforming functions to be used to\n   * filter or transform the items in the pipe.\n   * @param _target - The data set or data view that will receive the items.\n   */\n  public constructor(\n    private readonly _source: DataInterface<SI, SP>,\n    private readonly _transformers: readonly Transformer<unknown>[],\n    private readonly _target: DataSet<TI, TP>\n  ) {}\n\n  /** @inheritDoc */\n  public all(): this {\n    this._target.update(this._transformItems(this._source.get()));\n    return this;\n  }\n\n  /** @inheritDoc */\n  public start(): this {\n    this._source.on(\"add\", this._listeners.add);\n    this._source.on(\"remove\", this._listeners.remove);\n    this._source.on(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /** @inheritDoc */\n  public stop(): this {\n    this._source.off(\"add\", this._listeners.add);\n    this._source.off(\"remove\", this._listeners.remove);\n    this._source.off(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /**\n   * Apply the transformers to the items.\n   *\n   * @param items - The items to be transformed.\n   * @returns The transformed items.\n   */\n  private _transformItems(items: unknown[]): any[] {\n    return this._transformers.reduce((items, transform): unknown[] => {\n      return transform(items);\n    }, items);\n  }\n\n  /**\n   * Handle an add event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the added items.\n   */\n  private _add(\n    _name: Parameters<EventCallbacks<SI, SP>[\"add\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"add\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.add(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle an update event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the updated items.\n   */\n  private _update(\n    _name: Parameters<EventCallbacks<SI, SP>[\"update\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"update\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.update(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle a remove event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the data of the removed items.\n   */\n  private _remove(\n    _name: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.remove(this._transformItems(payload.oldData));\n  }\n}\n\n/**\n * Internal implementation of the pipe factory. This should be accessible\n * only through `createNewDataPipeFrom` from the outside.\n *\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass DataPipeUnderConstruction<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n> {\n  /**\n   * Array transformers used to transform items within the pipe. This is typed\n   * as any for the sake of simplicity.\n   */\n  private readonly _transformers: Transformer<any>[] = [];\n\n  /**\n   * Create a new data pipe factory. This is an internal constructor that\n   * should never be called from outside of this file.\n   *\n   * @param _source - The source data set or data view for this pipe.\n   */\n  public constructor(private readonly _source: DataInterface<SI, SP>) {}\n\n  /**\n   * Filter the items.\n   *\n   * @param callback - A filtering function that returns true if given item\n   * should be piped and false if not.\n   * @returns This factory for further configuration.\n   */\n  public filter(\n    callback: (item: SI) => boolean\n  ): DataPipeUnderConstruction<SI, SP> {\n    this._transformers.push((input): unknown[] => input.filter(callback));\n    return this;\n  }\n\n  /**\n   * Map each source item to a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * corresponding mapped item.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public map<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.map(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Map each source item to zero or more items of a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * an array of corresponding mapped items.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public flatMap<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI[]\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.flatMap(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Connect this pipe to given data set.\n   *\n   * @param target - The data set that will receive the items from this pipe.\n   * @returns The pipe connected between given data sets and performing\n   * configured transformation on the processed items.\n   */\n  public to(target: DataSet<SI, SP>): DataPipe {\n    return new SimpleDataPipe(this._source, this._transformers, target);\n  }\n}\n", "import { Assignable } from \"vis-util/esnext\";\nimport { DataSet } from \"./data-set\";\nimport { DataStream } from \"./data-stream\";\n\ntype ValueOf<T> = T[keyof T];\n\n/** Valid id type. */\nexport type Id = number | string;\n/** Nullable id type. */\nexport type OptId = undefined | null | Id;\n/**\n * Determine whether a value can be used as an id.\n *\n * @param value - Input value of unknown type.\n * @returns True if the value is valid id, false otherwise.\n */\nexport function isId(value: unknown): value is Id {\n  return typeof value === \"string\" || typeof value === \"number\";\n}\n\n/**\n * Make an object deeply partial.\n */\nexport type DeepPartial<T> = T extends any[] | Function | Node\n  ? T\n  : T extends object\n  ? { [key in keyof T]?: DeepPartial<T[key]> }\n  : T;\n\n/**\n * An item that may ({@link Id}) or may not (absent, undefined or null) have an id property.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type PartItem<IdProp extends string> = Partial<Record<IdProp, OptId>>;\n/**\n * An item that has a property containing an id and all other required properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type FullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Item & Record<IdProp, Id>;\n/**\n * An item that has a property containing an id and optionally other properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type UpdateItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Assignable<FullItem<Item, IdProp>> & Record<IdProp, Id>;\n\n/**\n * Test whether an item has an id (is a {@link FullItem}).\n *\n * @param item - The item to be tested.\n * @param idProp - Name of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns True if this value is a {@link FullItem}, false otherwise.\n */\nexport function isFullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n>(item: Item, idProp: IdProp): item is FullItem<Item, IdProp> {\n  return item[idProp] != null;\n}\n\n/** Add event payload. */\nexport interface AddEventPayload {\n  /** Ids of added items. */\n  items: Id[];\n}\n/** Update event payload. */\nexport interface UpdateEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of updated items. */\n  items: Id[];\n  /** Items as they were before this update. */\n  oldData: FullItem<Item, IdProp>[];\n  /**\n   * Items as they are now.\n   *\n   * @deprecated Just get the data from the data set or data view.\n   */\n  data: FullItem<Item, IdProp>[];\n}\n/** Remove event payload. */\nexport interface RemoveEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of removed items. */\n  items: Id[];\n  /** Items as they were before their removal. */\n  oldData: FullItem<Item, IdProp>[];\n}\n\n/**\n * Map of event payload types (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloads<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  add: AddEventPayload;\n  update: UpdateEventPayload<Item, IdProp>;\n  remove: RemoveEventPayload<Item, IdProp>;\n}\n/**\n * Map of event payload types including any event (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloadsWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventPayloads<Item, IdProp> {\n  \"*\": ValueOf<EventPayloads<Item, IdProp>>;\n}\n\n/**\n * Map of event callback types (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacks<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  add(name: \"add\", payload: AddEventPayload | null, senderId?: Id | null): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  update(\n    name: \"update\",\n    payload: UpdateEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  remove(\n    name: \"remove\",\n    payload: RemoveEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n}\n/**\n * Map of event callback types including any event (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacksWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventCallbacks<Item, IdProp> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  \"*\"<N extends keyof EventCallbacks<Item, IdProp>>(\n    name: N,\n    payload: EventPayloads<Item, IdProp>[N],\n    senderId?: Id | null\n  ): void;\n}\n\n/** Available event names. */\nexport type EventName = keyof EventPayloads<never, \"\">;\n/** Available event names and '*' to listen for all. */\nexport type EventNameWithAny = keyof EventPayloadsWithAny<never, \"\">;\n\n/**\n * Data interface order parameter.\n * - A string value determines which property will be used for sorting (using < and > operators for numeric comparison).\n * - A function will be used the same way as in Array.sort.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceOrder<Item> =\n  | keyof Item\n  | ((a: Item, b: Item) => number);\n\n/**\n * Data interface get options (return type independent).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsBase<Item> {\n  /**\n   * An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items.\n   *\n   * @remarks\n   * Warning**: There is no TypeScript support for this.\n   */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface get options (returns a single item or an array).\n *\n * @remarks\n * Whether an item or and array of items is returned is determined by the type of the id(s) argument.\n * If an array of ids is requested an array of items will be returned.\n * If a single id is requested a single item (or null if the id doesn't correspond to any item) will be returned.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsArray<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as a single item (if invoked with an id) or an array of items (if invoked with an array of ids). */\n  returnType?: undefined | \"Array\";\n}\n/**\n * Data interface get options (returns an object).\n *\n * @remarks\n * The returned object has ids as keys and items as values of corresponding ids.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsObject<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as an object map (id → item). */\n  returnType: \"Object\";\n}\n/**\n * Data interface get options (returns single item, an array or object).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceGetOptions<Item> =\n  | DataInterfaceGetOptionsArray<Item>\n  | DataInterfaceGetOptionsObject<Item>;\n\n/**\n * Data interface get ids options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetIdsOptions<Item> {\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface for each options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceForEachOptions<Item> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface map oprions.\n *\n * @typeParam Original - The original item type in the data.\n * @typeParam Mapped - The type after mapping.\n */\nexport interface DataInterfaceMapOptions<Original, Mapped> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Original) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Mapped>;\n}\n\n/**\n * Common interface for data sets and data view.\n *\n * @typeParam Item - Item type that may or may not have an id (missing ids will be generated upon insertion).\n * @typeParam IdProp - Name of the property on the Item type that contains the id.\n */\nexport interface DataInterface<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n> {\n  /** The number of items. */\n  length: number;\n\n  /** The key of id property. */\n  idProp: IdProp;\n\n  /**\n   * Add a universal event listener.\n   *\n   * @remarks The `*` event is triggered when any of the events `add`, `update`, and `remove` occurs.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Add an `add` event listener.\n   *\n   * @remarks The `add` event is triggered when an item or a set of items is added, or when an item is updated while not yet existing.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Add a `remove` event listener.\n   *\n   * @remarks The `remove` event is triggered when an item or a set of items is removed.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Add an `update` event listener.\n   *\n   * @remarks The `update` event is triggered when an existing item or a set of existing items is updated.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Remove a universal event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Remove an `add` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Remove a `remove` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Remove an `update` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Get all the items.\n   *\n   * @returns An array containing all the items.\n   */\n  get(): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items.\n   */\n  get(options: DataInterfaceGetOptionsArray<Item>): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items or if requested an object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(id: Id): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item if found or null otherwise. If requested an object map with 0 to 1 items.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(ids: Id[]): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   * If requested an object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get items.\n   *\n   * @param ids - Id or ids to be returned.\n   * @param options - Options to specify iteration details.\n   * @returns The items (format is determined by ids (single or array) and the options.\n   */\n  get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /**\n   * Get the DataSet to which the instance implementing this interface is connected.\n   * In case there is a chain of multiple DataViews, the root DataSet of this chain is returned.\n   *\n   * @returns The data set that actually contains the data.\n   */\n  getDataSet(): DataSet<Item, IdProp>;\n\n  /**\n   * Get ids of items.\n   *\n   * @remarks\n   * No guarantee is given about the order of returned ids unless an ordering function is supplied.\n   * @param options - Additional configuration.\n   * @returns An array of requested ids.\n   */\n  getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[];\n\n  /**\n   * Execute a callback function for each item.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration unless an ordering function is supplied.\n   * @param callback - Executed in similar fashion to Array.forEach callback, but instead of item, index, array receives item, id.\n   * @param options - Options to specify iteration details.\n   */\n  forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void;\n\n  /**\n   * Map each item into different item and return them as an array.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration even if ordering function is supplied (the items are sorted after the mapping).\n   * @param callback - Array.map-like callback, but only with the first two params.\n   * @param options - Options to specify iteration details.\n   * @returns The mapped items.\n   */\n  map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[];\n\n  /**\n   * Stream.\n   *\n   * @param ids - Ids of the items to be included in this stream (missing are ignored), all if omitted.\n   * @returns The data stream for this data set.\n   */\n  stream(ids?: Iterable<Id>): DataStream<Item>;\n}\n", "/** Queue configuration object. */\nexport interface QueueOptions {\n  /** The queue will be flushed automatically after an inactivity of this delay in milliseconds. By default there is no automatic flushing (`null`). */\n  delay?: null | number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value is `Infinity`. */\n  max?: number;\n}\n/**\n * Queue extending options.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport interface QueueExtendOptions<T> {\n  /** A list with method names of the methods on the object to be replaced with queued ones. */\n  replace: T[];\n  /** When provided, the queue will be flushed automatically after an inactivity of this delay in milliseconds. Default value is null. */\n  delay?: number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value of max is Infinity. */\n  max?: number;\n}\n/**\n * Queue call entry.\n * - A function to be executed.\n * - An object with function, args, context (like function.bind(context, ...args)).\n */\ntype QueueCallEntry =\n  | Function\n  | {\n      fn: Function;\n      args: unknown[];\n    }\n  | {\n      fn: Function;\n      args: unknown[];\n      context: unknown;\n    };\n\ninterface QueueExtended<O> {\n  object: O;\n  methods: {\n    name: string;\n    original: unknown;\n  }[];\n}\n\n/**\n * A queue.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport class Queue<T = never> {\n  /** Delay in milliseconds. If defined the queue will be periodically flushed. */\n  public delay: null | number;\n  /** Maximum number of entries in the queue before it will be flushed. */\n  public max: number;\n\n  private readonly _queue: {\n    fn: Function;\n    args?: unknown[];\n    context?: unknown;\n  }[] = [];\n\n  private _timeout: ReturnType<typeof setTimeout> | null = null;\n  private _extended: null | QueueExtended<T> = null;\n\n  /**\n   * Construct a new Queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public constructor(options?: QueueOptions) {\n    // options\n    this.delay = null;\n    this.max = Infinity;\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Update the configuration of the queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public setOptions(options?: QueueOptions): void {\n    if (options && typeof options.delay !== \"undefined\") {\n      this.delay = options.delay;\n    }\n    if (options && typeof options.max !== \"undefined\") {\n      this.max = options.max;\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Extend an object with queuing functionality.\n   * The object will be extended with a function flush, and the methods provided in options.replace will be replaced with queued ones.\n   *\n   * @param object - The object to be extended.\n   * @param options - Additional options.\n   * @returns The created queue.\n   */\n  public static extend<O extends { flush?: () => void }, K extends string>(\n    object: O,\n    options: QueueExtendOptions<K>\n  ): Queue<O> {\n    const queue = new Queue<O>(options);\n\n    if (object.flush !== undefined) {\n      throw new Error(\"Target object already has a property flush\");\n    }\n    object.flush = (): void => {\n      queue.flush();\n    };\n\n    const methods: QueueExtended<O>[\"methods\"] = [\n      {\n        name: \"flush\",\n        original: undefined,\n      },\n    ];\n\n    if (options && options.replace) {\n      for (let i = 0; i < options.replace.length; i++) {\n        const name = options.replace[i];\n        methods.push({\n          name: name,\n          // @TODO: better solution?\n          original: (object as unknown as Record<K, () => void>)[name],\n        });\n        // @TODO: better solution?\n        queue.replace(object as unknown as Record<K, () => void>, name);\n      }\n    }\n\n    queue._extended = {\n      object: object,\n      methods: methods,\n    };\n\n    return queue;\n  }\n\n  /**\n   * Destroy the queue. The queue will first flush all queued actions, and in case it has extended an object, will restore the original object.\n   */\n  public destroy(): void {\n    this.flush();\n\n    if (this._extended) {\n      const object = this._extended.object;\n      const methods = this._extended.methods;\n      for (let i = 0; i < methods.length; i++) {\n        const method = methods[i];\n        if (method.original) {\n          // @TODO: better solution?\n          (object as any)[method.name] = method.original;\n        } else {\n          // @TODO: better solution?\n          delete (object as any)[method.name];\n        }\n      }\n      this._extended = null;\n    }\n  }\n\n  /**\n   * Replace a method on an object with a queued version.\n   *\n   * @param object - Object having the method.\n   * @param method - The method name.\n   */\n  public replace<M extends string>(\n    object: Record<M, () => void>,\n    method: M\n  ): void {\n    /* eslint-disable-next-line @typescript-eslint/no-this-alias -- Function this is necessary in the function bellow, so class this has to be saved into a variable here. */\n    const me = this;\n    const original = object[method];\n    if (!original) {\n      throw new Error(\"Method \" + method + \" undefined\");\n    }\n\n    object[method] = function (...args: unknown[]): void {\n      // add this call to the queue\n      me.queue({\n        args: args,\n        fn: original,\n        context: this,\n      });\n    };\n  }\n\n  /**\n   * Queue a call.\n   *\n   * @param entry - The function or entry to be queued.\n   */\n  public queue(entry: QueueCallEntry): void {\n    if (typeof entry === \"function\") {\n      this._queue.push({ fn: entry });\n    } else {\n      this._queue.push(entry);\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Check whether the queue needs to be flushed.\n   */\n  private _flushIfNeeded(): void {\n    // flush when the maximum is exceeded.\n    if (this._queue.length > this.max) {\n      this.flush();\n    }\n\n    // flush after a period of inactivity when a delay is configured\n    if (this._timeout != null) {\n      clearTimeout(this._timeout);\n      this._timeout = null;\n    }\n    if (this.queue.length > 0 && typeof this.delay === \"number\") {\n      this._timeout = setTimeout((): void => {\n        this.flush();\n      }, this.delay);\n    }\n  }\n\n  /**\n   * Flush all queued calls\n   */\n  public flush(): void {\n    this._queue.splice(0).forEach((entry): void => {\n      entry.fn.apply(entry.context || entry.fn, entry.args || []);\n    });\n  }\n}\n", "import {\n  DataInterface,\n  EventCallbacksWithAny,\n  EventName,\n  EventNameWithAny,\n  EventPayloads,\n  Id,\n  PartItem,\n} from \"./data-interface\";\n\ntype EventSubscribers<Item extends PartItem<IdProp>, IdProp extends string> = {\n  [Name in keyof EventCallbacksWithAny<Item, IdProp>]: (...args: any[]) => void;\n};\n\n/**\n * {@link DataSet} code that can be reused in {@link DataView} or other similar implementations of {@link DataInterface}.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport abstract class DataSetPart<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> implements Pick<DataInterface<Item, IdProp>, \"on\" | \"off\">\n{\n  private readonly _subscribers: {\n    [Name in EventNameWithAny]: EventSubscribers<Item, IdProp>[Name][];\n  } = {\n    \"*\": [],\n    add: [],\n    remove: [],\n    update: [],\n  };\n\n  protected _trigger(\n    event: \"add\",\n    payload: EventPayloads<Item, IdProp>[\"add\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"update\",\n    payload: EventPayloads<Item, IdProp>[\"update\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"remove\",\n    payload: EventPayloads<Item, IdProp>[\"remove\"],\n    senderId?: Id | null\n  ): void;\n  /**\n   * Trigger an event\n   *\n   * @param event - Event name.\n   * @param payload - Event payload.\n   * @param senderId - Id of the sender.\n   */\n  protected _trigger<Name extends EventName>(\n    event: Name,\n    payload: EventPayloads<Item, IdProp>[Name],\n    senderId?: Id | null\n  ): void {\n    if ((event as string) === \"*\") {\n      throw new Error(\"Cannot trigger event *\");\n    }\n\n    [...this._subscribers[event], ...this._subscribers[\"*\"]].forEach(\n      (subscriber): void => {\n        subscriber(event, payload, senderId != null ? senderId : null);\n      }\n    );\n  }\n\n  /** @inheritDoc */\n  public on(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Subscribe to an event, add an event listener.\n   *\n   * @remarks Non-function callbacks are ignored.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public on<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    if (typeof callback === \"function\") {\n      this._subscribers[event].push(callback);\n    }\n    // @TODO: Maybe throw for invalid callbacks?\n  }\n\n  /** @inheritDoc */\n  public off(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Unsubscribe from an event, remove an event listener.\n   *\n   * @remarks If the same callback was subscribed more than once **all** occurences will be removed.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public off<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    this._subscribers[event] = this._subscribers[event].filter(\n      (subscriber): boolean => subscriber !== callback\n    );\n  }\n\n  /**\n   * @deprecated Use on instead (PS: DataView.subscribe === DataView.on).\n   */\n  public subscribe: DataSetPart<Item, IdProp>[\"on\"] = DataSetPart.prototype.on;\n  /**\n   * @deprecated Use off instead (PS: DataView.unsubscribe === DataView.off).\n   */\n  public unsubscribe: DataSetPart<Item, IdProp>[\"off\"] =\n    DataSetPart.prototype.off;\n\n  /* develblock:start */\n  public get testLeakSubscribers(): any {\n    return this._subscribers;\n  }\n  /* develblock:end */\n}\n", "import { Id } from \"./data-interface\";\n\n/**\n * Data stream\n *\n * @remarks\n * {@link DataStream} offers an always up to date stream of items from a {@link DataSet} or {@link DataView}.\n * That means that the stream is evaluated at the time of iteration, conversion to another data type or when {@link cache} is called, not when the {@link DataStream} was created.\n * Multiple invocations of for example {@link toItemArray} may yield different results (if the data source like for example {@link DataSet} gets modified).\n * @typeParam Item - The item type this stream is going to work with.\n */\nexport class DataStream<Item> implements Iterable<[Id, Item]> {\n  private readonly _pairs: Iterable<[Id, Item]>;\n\n  /**\n   * Create a new data stream.\n   *\n   * @param pairs - The id, item pairs.\n   */\n  public constructor(pairs: Iterable<[Id, Item]>) {\n    this._pairs = pairs;\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *entries(): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of keys in the stream.\n   */\n  public *keys(): IterableIterator<Id> {\n    for (const [id] of this._pairs) {\n      yield id;\n    }\n  }\n\n  /**\n   * Return an iterable of values in the stream.\n   */\n  public *values(): IterableIterator<Item> {\n    for (const [, item] of this._pairs) {\n      yield item;\n    }\n  }\n\n  /**\n   * Return an array containing all the ids in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all ids from this stream.\n   */\n  public toIdArray(): Id[] {\n    return [...this._pairs].map((pair): Id => pair[0]);\n  }\n\n  /**\n   * Return an array containing all the items in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all items from this stream.\n   */\n  public toItemArray(): Item[] {\n    return [...this._pairs].map((pair): Item => pair[1]);\n  }\n\n  /**\n   * Return an array containing all the entries in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all entries from this stream.\n   */\n  public toEntryArray(): [Id, Item][] {\n    return [...this._pairs];\n  }\n\n  /**\n   * Return an object map containing all the items in this stream accessible by ids.\n   *\n   * @remarks\n   * In case of duplicate ids (coerced to string so `7 == '7'`) the last encoutered appears in the returned object.\n   * @returns The object map of all id → item pairs from this stream.\n   */\n  public toObjectMap(): Record<Id, Item> {\n    const map: Record<Id, Item> = Object.create(null);\n    for (const [id, item] of this._pairs) {\n      map[id] = item;\n    }\n    return map;\n  }\n\n  /**\n   * Return a map containing all the items in this stream accessible by ids.\n   *\n   * @returns The map of all id → item pairs from this stream.\n   */\n  public toMap(): Map<Id, Item> {\n    return new Map(this._pairs);\n  }\n\n  /**\n   * Return a set containing all the (unique) ids in this stream.\n   *\n   * @returns The set of all ids from this stream.\n   */\n  public toIdSet(): Set<Id> {\n    return new Set(this.toIdArray());\n  }\n\n  /**\n   * Return a set containing all the (unique) items in this stream.\n   *\n   * @returns The set of all items from this stream.\n   */\n  public toItemSet(): Set<Item> {\n    return new Set(this.toItemArray());\n  }\n\n  /**\n   * Cache the items from this stream.\n   *\n   * @remarks\n   * This method allows for items to be fetched immediatelly and used (possibly multiple times) later.\n   * It can also be used to optimize performance as {@link DataStream} would otherwise reevaluate everything upon each iteration.\n   *\n   * ## Example\n   * ```javascript\n   * const ds = new DataSet([…])\n   *\n   * const cachedStream = ds.stream()\n   *   .filter(…)\n   *   .sort(…)\n   *   .map(…)\n   *   .cached(…) // Data are fetched, processed and cached here.\n   *\n   * ds.clear()\n   * chachedStream // Still has all the items.\n   * ```\n   * @returns A new {@link DataStream} with cached items (detached from the original {@link DataSet}).\n   */\n  public cache(): DataStream<Item> {\n    return new DataStream([...this._pairs]);\n  }\n\n  /**\n   * Get the distinct values of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @typeParam T - The type of the distinct value.\n   * @returns A set of all distinct properties.\n   */\n  public distinct<T>(callback: (item: Item, id: Id) => T): Set<T> {\n    const set = new Set<T>();\n\n    for (const [id, item] of this._pairs) {\n      set.add(callback(item, id));\n    }\n\n    return set;\n  }\n\n  /**\n   * Filter the items of the stream.\n   *\n   * @param callback - The function that decides whether an item will be included.\n   * @returns A new data stream with the filtered items.\n   */\n  public filter(callback: (item: Item, id: Id) => boolean): DataStream<Item> {\n    const pairs = this._pairs;\n    return new DataStream<Item>({\n      *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n        for (const [id, item] of pairs) {\n          if (callback(item, id)) {\n            yield [id, item];\n          }\n        }\n      },\n    });\n  }\n\n  /**\n   * Execute a callback for each item of the stream.\n   *\n   * @param callback - The function that will be invoked for each item.\n   */\n  public forEach(callback: (item: Item, id: Id) => boolean): void {\n    for (const [id, item] of this._pairs) {\n      callback(item, id);\n    }\n  }\n\n  /**\n   * Map the items into a different type.\n   *\n   * @param callback - The function that does the conversion.\n   * @typeParam Mapped - The type of the item after mapping.\n   * @returns A new data stream with the mapped items.\n   */\n  public map<Mapped>(\n    callback: (item: Item, id: Id) => Mapped\n  ): DataStream<Mapped> {\n    const pairs = this._pairs;\n    return new DataStream<Mapped>({\n      *[Symbol.iterator](): IterableIterator<[Id, Mapped]> {\n        for (const [id, item] of pairs) {\n          yield [id, callback(item, id)];\n        }\n      },\n    });\n  }\n\n  /**\n   * Get the item with the maximum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the maximum if found otherwise null.\n   */\n  public max(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let maxItem: Item = curr.value[1];\n    let maxValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value > maxValue) {\n        maxValue = value;\n        maxItem = item;\n      }\n    }\n\n    return maxItem;\n  }\n\n  /**\n   * Get the item with the minimum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the minimum if found otherwise null.\n   */\n  public min(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let minItem: Item = curr.value[1];\n    let minValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value < minValue) {\n        minValue = value;\n        minItem = item;\n      }\n    }\n\n    return minItem;\n  }\n\n  /**\n   * Reduce the items into a single value.\n   *\n   * @param callback - The function that does the reduction.\n   * @param accumulator - The initial value of the accumulator.\n   * @typeParam T - The type of the accumulated value.\n   * @returns The reduced value.\n   */\n  public reduce<T>(\n    callback: (accumulator: T, item: Item, id: Id) => T,\n    accumulator: T\n  ): T {\n    for (const [id, item] of this._pairs) {\n      accumulator = callback(accumulator, item, id);\n    }\n    return accumulator;\n  }\n\n  /**\n   * Sort the items.\n   *\n   * @param callback - Item comparator.\n   * @returns A new stream with sorted items.\n   */\n  public sort(\n    callback: (itemA: Item, itemB: Item, idA: Id, idB: Id) => number\n  ): DataStream<Item> {\n    return new DataStream({\n      [Symbol.iterator]: (): IterableIterator<[Id, Item]> =>\n        [...this._pairs]\n          .sort(([idA, itemA], [idB, itemB]): number =>\n            callback(itemA, itemB, idA, idB)\n          )\n          [Symbol.iterator](),\n    });\n  }\n}\n", "import { v4 as uuid4 } from \"uuid\";\nimport { pureDeepObjectAssign } from \"vis-util/esnext\";\n\nimport {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  DataInterfaceOrder,\n  DeepPartial,\n  EventPayloads,\n  FullItem,\n  Id,\n  OptId,\n  PartItem,\n  UpdateItem,\n  isId,\n} from \"./data-interface\";\n\nimport { Queue, QueueOptions } from \"./queue\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Initial DataSet configuration object.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataSetInitialOptions<IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /**\n   * Queue data changes ('add', 'update', 'remove') and flush them at once. The queue can be flushed manually by calling `DataSet.flush()`, or can be flushed after a configured delay or maximum number of entries.\n   *\n   * When queue is true, a queue is created with default options. Options can be specified by providing an object.\n   */\n  queue?: QueueOptions | false;\n}\n/** DataSet configuration object. */\nexport interface DataSetOptions {\n  /**\n   * Queue configuration object or false if no queue should be used.\n   *\n   * - If false and there was a queue before it will be flushed and then removed.\n   * - If {@link QueueOptions} the existing queue will be reconfigured or a new queue will be created.\n   */\n  queue?: Queue | QueueOptions | false;\n}\n\n/**\n * Add an id to given item if it doesn't have one already.\n *\n * @remarks\n * The item will be modified.\n * @param item - The item that will have an id after a call to this function.\n * @param idProp - The key of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns true\n */\nfunction ensureFullItem<Item extends PartItem<IdProp>, IdProp extends string>(\n  item: Item,\n  idProp: IdProp\n): FullItem<Item, IdProp> {\n  if (item[idProp] == null) {\n    // generate an id\n    item[idProp] = uuid4() as any;\n  }\n\n  return item as FullItem<Item, IdProp>;\n}\n\n/**\n * # DataSet\n *\n * Vis.js comes with a flexible DataSet, which can be used to hold and\n * manipulate unstructured data and listen for changes in the data. The DataSet\n * is key/value based. Data items can be added, updated and removed from the\n * DataSet, and one can subscribe to changes in the DataSet. The data in the\n * DataSet can be filtered and ordered. Data can be normalized when appending it\n * to the DataSet as well.\n *\n * ## Example\n *\n * The following example shows how to use a DataSet.\n *\n * ```javascript\n * // create a DataSet\n * var options = {};\n * var data = new vis.DataSet(options);\n *\n * // add items\n * // note that the data items can contain different properties and data formats\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // subscribe to any change in the DataSet\n * data.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an existing item\n * data.update({id: 2, group: 1});\n *\n * // remove an item\n * data.remove(4);\n *\n * // get all ids\n * var ids = data.getIds();\n * console.log('ids', ids);\n *\n * // get a specific item\n * var item1 = data.get(1);\n * console.log('item1', item1);\n *\n * // retrieve a filtered subset of the data\n * var items = data.get({\n *   filter: function (item) {\n *     return item.group == 1;\n *   }\n * });\n * console.log('filtered items', items);\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataSet<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** Flush all queued calls. */\n  public flush?: () => void;\n  /** @inheritDoc */\n  public length: number;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this._idProp;\n  }\n\n  private readonly _options: DataSetInitialOptions<IdProp>;\n  private readonly _data: Map<Id, FullItem<Item, IdProp>>;\n  private readonly _idProp: IdProp;\n  private _queue: Queue<this> | null = null;\n\n  /**\n   * @param options - DataSet configuration.\n   */\n  public constructor(options?: DataSetInitialOptions<IdProp>);\n  /**\n   * @param data - An initial set of items for the new instance.\n   * @param options - DataSet configuration.\n   */\n  public constructor(data: Item[], options?: DataSetInitialOptions<IdProp>);\n  /**\n   * Construct a new DataSet.\n   *\n   * @param data - Initial data or options.\n   * @param options - Options (type error if data is also options).\n   */\n  public constructor(\n    data?: Item[] | DataSetInitialOptions<IdProp>,\n    options?: DataSetInitialOptions<IdProp>\n  ) {\n    super();\n\n    // correctly read optional arguments\n    if (data && !Array.isArray(data)) {\n      options = data;\n      data = [];\n    }\n\n    this._options = options || {};\n    this._data = new Map(); // map with data indexed by id\n    this.length = 0; // number of items in the DataSet\n    this._idProp = this._options.fieldId || (\"id\" as IdProp); // name of the field containing id\n\n    // add initial data when provided\n    if (data && data.length) {\n      this.add(data);\n    }\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Set new options.\n   *\n   * @param options - The new options.\n   */\n  public setOptions(options?: DataSetOptions): void {\n    if (options && options.queue !== undefined) {\n      if (options.queue === false) {\n        // delete queue if loaded\n        if (this._queue) {\n          this._queue.destroy();\n          this._queue = null;\n        }\n      } else {\n        // create queue and update its options\n        if (!this._queue) {\n          this._queue = Queue.extend(this, {\n            replace: [\"add\", \"update\", \"remove\"],\n          });\n        }\n\n        if (options.queue && typeof options.queue === \"object\") {\n          this._queue.setOptions(options.queue);\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a data item or an array with items.\n   *\n   * After the items are added to the DataSet, the DataSet will trigger an event `add`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet()\n   *\n   * // add items\n   * const ids = data.add([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { text: 'item without an id' }\n   * ])\n   *\n   * console.log(ids) // [1, 2, '<UUIDv4>']\n   * ```\n   *\n   * @param data - Items to be added (ids will be generated if missing).\n   * @param senderId - Sender id.\n   * @returns addedIds - Array with the ids (generated if not present) of the added items.\n   * @throws When an item with the same id as any of the added items already exists.\n   */\n  public add(data: Item | Item[], senderId?: Id | null): (string | number)[] {\n    const addedIds: Id[] = [];\n    let id: Id;\n\n    if (Array.isArray(data)) {\n      // Array\n      const idsToAdd: Id[] = data.map((d) => d[this._idProp] as Id);\n      if (idsToAdd.some((id) => this._data.has(id))) {\n        throw new Error(\"A duplicate id was found in the parameter array.\");\n      }\n      for (let i = 0, len = data.length; i < len; i++) {\n        id = this._addItem(data[i]);\n        addedIds.push(id);\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      id = this._addItem(data);\n      addedIds.push(id);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n\n    return addedIds;\n  }\n\n  /**\n   * Update existing items. When an item does not exist, it will be created.\n   *\n   * @remarks\n   * The provided properties will be merged in the existing item. When an item does not exist, it will be created.\n   *\n   * After the items are updated, the DataSet will trigger an event `add` for the added items, and an event `update`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' },\n   *   { id: 4, text: 'item 4 (new)' }\n   * ])\n   *\n   * console.log(ids) // [2, 4]\n   * ```\n   *\n   * ## Warning for TypeScript users\n   * This method may introduce partial items into the data set. Use add or updateOnly instead for better type safety.\n   * @param data - Items to be updated (if the id is already present) or added (if the id is missing).\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the added (these may be newly generated if there was no id in the item from the data) or updated items.\n   * @throws When the supplied data is neither an item nor an array of items.\n   */\n  public update(\n    data: DeepPartial<Item> | DeepPartial<Item>[],\n    senderId?: Id | null\n  ): Id[] {\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const oldData: FullItem<Item, IdProp>[] = [];\n    const updatedData: FullItem<Item, IdProp>[] = [];\n    const idProp = this._idProp;\n\n    const addOrUpdate = (item: DeepPartial<Item>): void => {\n      const origId: OptId = item[idProp];\n      if (origId != null && this._data.has(origId)) {\n        const fullItem = item as FullItem<Item, IdProp>; // it has an id, therefore it is a fullitem\n        const oldItem = Object.assign({}, this._data.get(origId));\n        // update item\n        const id = this._updateItem(fullItem);\n        updatedIds.push(id);\n        updatedData.push(fullItem);\n        oldData.push(oldItem);\n      } else {\n        // add new item\n        const id = this._addItem(item as any);\n        addedIds.push(id);\n      }\n    };\n\n    if (Array.isArray(data)) {\n      // Array\n      for (let i = 0, len = data.length; i < len; i++) {\n        if (data[i] && typeof data[i] === \"object\") {\n          addOrUpdate(data[i]);\n        } else {\n          console.warn(\n            \"Ignoring input item, which is not an object at index \" + i\n          );\n        }\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      addOrUpdate(data);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      const props = { items: updatedIds, oldData: oldData, data: updatedData };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n    }\n\n    return addedIds.concat(updatedIds);\n  }\n\n  /**\n   * Update existing items. When an item does not exist, an error will be thrown.\n   *\n   * @remarks\n   * The provided properties will be deeply merged into the existing item.\n   * When an item does not exist (id not present in the data set or absent), an error will be thrown and nothing will be changed.\n   *\n   * After the items are updated, the DataSet will trigger an event `update`.\n   * When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' },\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' }, // works\n   *   // { id: 4, text: 'item 4 (new)' }, // would throw\n   *   // { text: 'item 4 (new)' }, // would also throw\n   * ])\n   *\n   * console.log(ids) // [2]\n   * ```\n   * @param data - Updates (the id and optionally other props) to the items in this data set.\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the updated items.\n   * @throws When the supplied data is neither an item nor an array of items, when the ids are missing.\n   */\n  public updateOnly(\n    data: UpdateItem<Item, IdProp> | UpdateItem<Item, IdProp>[],\n    senderId?: Id | null\n  ): Id[] {\n    if (!Array.isArray(data)) {\n      data = [data];\n    }\n\n    const updateEventData = data\n      .map(\n        (\n          update\n        ): {\n          oldData: FullItem<Item, IdProp>;\n          update: UpdateItem<Item, IdProp>;\n        } => {\n          const oldData = this._data.get(update[this._idProp]);\n          if (oldData == null) {\n            throw new Error(\"Updating non-existent items is not allowed.\");\n          }\n          return { oldData, update };\n        }\n      )\n      .map(\n        ({\n          oldData,\n          update,\n        }): {\n          id: Id;\n          oldData: FullItem<Item, IdProp>;\n          updatedData: FullItem<Item, IdProp>;\n        } => {\n          const id = oldData[this._idProp];\n          const updatedData = pureDeepObjectAssign(oldData, update);\n\n          this._data.set(id, updatedData);\n\n          return {\n            id,\n            oldData: oldData,\n            updatedData,\n          };\n        }\n      );\n\n    if (updateEventData.length) {\n      const props: EventPayloads<Item, IdProp>[\"update\"] = {\n        items: updateEventData.map((value): Id => value.id),\n        oldData: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.oldData\n        ),\n        data: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.updatedData\n        ),\n      };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n\n      return props.items;\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>> {\n    // @TODO: Woudn't it be better to split this into multiple methods?\n\n    // parse the arguments\n    let id: Id | undefined = undefined;\n    let ids: Id[] | undefined = undefined;\n    let options: DataInterfaceGetOptions<Item> | undefined = undefined;\n    if (isId(first)) {\n      // get(id [, options])\n      id = first;\n      options = second;\n    } else if (Array.isArray(first)) {\n      // get(ids [, options])\n      ids = first;\n      options = second;\n    } else {\n      // get([, options])\n      options = first;\n    }\n\n    // determine the return type\n    const returnType =\n      options && options.returnType === \"Object\" ? \"Object\" : \"Array\";\n    // @TODO: WTF is this? Or am I missing something?\n    // var returnType\n    // if (options && options.returnType) {\n    //   var allowedValues = ['Array', 'Object']\n    //   returnType =\n    //     allowedValues.indexOf(options.returnType) == -1\n    //       ? 'Array'\n    //       : options.returnType\n    // } else {\n    //   returnType = 'Array'\n    // }\n\n    // build options\n    const filter = options && options.filter;\n    const items: FullItem<Item, IdProp>[] = [];\n    let item: undefined | FullItem<Item, IdProp> = undefined;\n    let itemIds: undefined | Id[] = undefined;\n    let itemId: undefined | Id = undefined;\n\n    // convert items\n    if (id != null) {\n      // return a single item\n      item = this._data.get(id);\n      if (item && filter && !filter(item)) {\n        item = undefined;\n      }\n    } else if (ids != null) {\n      // return a subset of items\n      for (let i = 0, len = ids.length; i < len; i++) {\n        item = this._data.get(ids[i]);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    } else {\n      // return all items\n      itemIds = [...this._data.keys()];\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        itemId = itemIds[i];\n        item = this._data.get(itemId);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    }\n\n    // order the results\n    if (options && options.order && id == undefined) {\n      this._sort(items, options.order);\n    }\n\n    // filter fields of the items\n    if (options && options.fields) {\n      const fields = options.fields;\n      if (id != undefined && item != null) {\n        item = this._filterFields(item, fields) as FullItem<Item, IdProp>;\n      } else {\n        for (let i = 0, len = items.length; i < len; i++) {\n          items[i] = this._filterFields(items[i], fields) as FullItem<\n            Item,\n            IdProp\n          >;\n        }\n      }\n    }\n\n    // return the results\n    if (returnType == \"Object\") {\n      const result: Record<string, FullItem<Item, IdProp>> = {};\n      for (let i = 0, len = items.length; i < len; i++) {\n        const resultant = items[i];\n        // @TODO: Shoudn't this be this._fieldId?\n        // result[resultant.id] = resultant\n        const id: Id = resultant[this._idProp];\n        result[id] = resultant;\n      }\n      return result;\n    } else {\n      if (id != null) {\n        // a single item\n        return item ?? null;\n      } else {\n        // just return our array\n        return items;\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    const data = this._data;\n    const filter = options && options.filter;\n    const order = options && options.order;\n    const itemIds = [...data.keys()];\n    const ids: Id[] = [];\n\n    if (filter) {\n      // get filtered items\n      if (order) {\n        // create ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            items.push(item);\n          }\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    } else {\n      // get all items\n      if (order) {\n        // create an ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          items.push(data.get(id)!);\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = data.get(id);\n          if (item != null) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    }\n\n    return ids;\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this;\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    const filter = options && options.filter;\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    if (options && options.order) {\n      // execute forEach on ordered list\n      const items: FullItem<Item, IdProp>[] = this.get(options);\n\n      for (let i = 0, len = items.length; i < len; i++) {\n        const item = items[i];\n        const id = item[this._idProp];\n        callback(item, id);\n      }\n    } else {\n      // unordered\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        const id = itemIds[i];\n        const item = this._data.get(id);\n        if (item != null && (!filter || filter(item))) {\n          callback(item, id);\n        }\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    const filter = options && options.filter;\n    const mappedItems: T[] = [];\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    // convert and filter items\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = this._data.get(id);\n      if (item != null && (!filter || filter(item))) {\n        mappedItems.push(callback(item, id));\n      }\n    }\n\n    // order items\n    if (options && options.order) {\n      this._sort(mappedItems, options.order);\n    }\n\n    return mappedItems;\n  }\n\n  private _filterFields<K extends string>(item: null, fields: K[]): null;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[]\n  ): Record<K, unknown>;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[] | Record<K, string>\n  ): any;\n  /**\n   * Filter the fields of an item.\n   *\n   * @param item - The item whose fields should be filtered.\n   * @param fields - The names of the fields that will be kept.\n   * @typeParam K - Field name type.\n   * @returns The item without any additional fields.\n   */\n  private _filterFields<K extends string>(\n    item: Record<K, unknown> | null,\n    fields: K[] | Record<K, unknown>\n  ): Record<K, unknown> | null {\n    if (!item) {\n      // item is null\n      return item;\n    }\n\n    return (\n      Array.isArray(fields)\n        ? // Use the supplied array\n          fields\n        : // Use the keys of the supplied object\n          (Object.keys(fields) as K[])\n    ).reduce<Record<string, unknown>>(\n      (filteredItem, field): Record<string, unknown> => {\n        filteredItem[field] = item[field];\n        return filteredItem;\n      },\n      {}\n    );\n  }\n\n  /**\n   * Sort the provided array with items.\n   *\n   * @param items - Items to be sorted in place.\n   * @param order - A field name or custom sort function.\n   * @typeParam T - The type of the items in the items array.\n   */\n  private _sort<T>(items: T[], order: DataInterfaceOrder<T>): void {\n    if (typeof order === \"string\") {\n      // order by provided field name\n      const name = order; // field name\n      items.sort((a, b): -1 | 0 | 1 => {\n        // @TODO: How to treat missing properties?\n        const av = (a as any)[name];\n        const bv = (b as any)[name];\n        return av > bv ? 1 : av < bv ? -1 : 0;\n      });\n    } else if (typeof order === \"function\") {\n      // order by sort function\n      items.sort(order);\n    } else {\n      // TODO: extend order by an Object {field:string, direction:string}\n      //       where direction can be 'asc' or 'desc'\n      throw new TypeError(\"Order must be a function or a string\");\n    }\n  }\n\n  /**\n   * Remove an item or multiple items by “reference” (only the id is used) or by id.\n   *\n   * The method ignores removal of non-existing items, and returns an array containing the ids of the items which are actually removed from the DataSet.\n   *\n   * After the items are removed, the DataSet will trigger an event `remove` for the removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // remove items\n   * const ids = data.remove([2, { id: 3 }, 4])\n   *\n   * console.log(ids) // [2, 3]\n   * ```\n   *\n   * @param id - One or more items or ids of items to be removed.\n   * @param senderId - Sender id.\n   * @returns The ids of the removed items.\n   */\n  public remove(id: Id | Item | (Id | Item)[], senderId?: Id | null): Id[] {\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // force everything to be an array for simplicity\n    const ids = Array.isArray(id) ? id : [id];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const item = this._remove(ids[i]);\n      if (item) {\n        const itemId: OptId = item[this._idProp];\n        if (itemId != null) {\n          removedIds.push(itemId);\n          removedItems.push(item);\n        }\n      }\n    }\n\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n\n    return removedIds;\n  }\n\n  /**\n   * Remove an item by its id or reference.\n   *\n   * @param id - Id of an item or the item itself.\n   * @returns The removed item if removed, null otherwise.\n   */\n  private _remove(id: Id | Item): FullItem<Item, IdProp> | null {\n    // @TODO: It origianlly returned the item although the docs say id.\n    // The code expects the item, so probably an error in the docs.\n    let ident: OptId;\n\n    // confirm the id to use based on the args type\n    if (isId(id)) {\n      ident = id;\n    } else if (id && typeof id === \"object\") {\n      ident = id[this._idProp]; // look for the identifier field using ._idProp\n    }\n\n    // do the removing if the item is found\n    if (ident != null && this._data.has(ident)) {\n      const item = this._data.get(ident) || null;\n      this._data.delete(ident);\n      --this.length;\n      return item;\n    }\n\n    return null;\n  }\n\n  /**\n   * Clear the entire data set.\n   *\n   * After the items are removed, the {@link DataSet} will trigger an event `remove` for all removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * @param senderId - Sender id.\n   * @returns removedIds - The ids of all removed items.\n   */\n  public clear(senderId?: Id | null): Id[] {\n    const ids = [...this._data.keys()];\n    const items: FullItem<Item, IdProp>[] = [];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      items.push(this._data.get(ids[i])!);\n    }\n\n    this._data.clear();\n    this.length = 0;\n\n    this._trigger(\"remove\", { items: ids, oldData: items }, senderId);\n\n    return ids;\n  }\n\n  /**\n   * Find the item with maximum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for max value.\n   * @returns Item containing max value, or null if no items.\n   */\n  public max(field: keyof Item): Item | null {\n    let max = null;\n    let maxField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (maxField == null || itemField > maxField)\n      ) {\n        max = item;\n        maxField = itemField;\n      }\n    }\n\n    return max || null;\n  }\n\n  /**\n   * Find the item with minimum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for min value.\n   * @returns Item containing min value, or null if no items.\n   */\n  public min(field: keyof Item): Item | null {\n    let min = null;\n    let minField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (minField == null || itemField < minField)\n      ) {\n        min = item;\n        minField = itemField;\n      }\n    }\n\n    return min || null;\n  }\n\n  public distinct<T extends keyof Item>(prop: T): Item[T][];\n  public distinct(prop: string): unknown[];\n  /**\n   * Find all distinct values of a specified field\n   *\n   * @param prop - The property name whose distinct values should be returned.\n   * @returns Unordered array containing all distinct values. Items without specified property are ignored.\n   */\n  public distinct<T extends string>(prop: T): unknown[] {\n    const data = this._data;\n    const itemIds = [...data.keys()];\n    const values: unknown[] = [];\n    let count = 0;\n\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = data.get(id);\n      const value = (item as any)[prop];\n      let exists = false;\n      for (let j = 0; j < count; j++) {\n        if (values[j] == value) {\n          exists = true;\n          break;\n        }\n      }\n      if (!exists && value !== undefined) {\n        values[count] = value;\n        count++;\n      }\n    }\n\n    return values;\n  }\n\n  /**\n   * Add a single item. Will fail when an item with the same id already exists.\n   *\n   * @param item - A new item to be added.\n   * @returns Added item's id. An id is generated when it is not present in the item.\n   */\n  private _addItem(item: Item): Id {\n    const fullItem = ensureFullItem(item, this._idProp);\n    const id = fullItem[this._idProp];\n\n    // check whether this id is already taken\n    if (this._data.has(id)) {\n      // item already exists\n      throw new Error(\n        \"Cannot add item: item with id \" + id + \" already exists\"\n      );\n    }\n\n    this._data.set(id, fullItem);\n    ++this.length;\n\n    return id;\n  }\n\n  /**\n   * Update a single item: merge with existing item.\n   * Will fail when the item has no id, or when there does not exist an item with the same id.\n   *\n   * @param update - The new item\n   * @returns The id of the updated item.\n   */\n  private _updateItem(update: FullItem<Item, IdProp>): Id {\n    const id: OptId = update[this._idProp];\n    if (id == null) {\n      throw new Error(\n        \"Cannot update item: item has no id (item: \" +\n          JSON.stringify(update) +\n          \")\"\n      );\n    }\n    const item = this._data.get(id);\n    if (!item) {\n      // item doesn't exist\n      throw new Error(\"Cannot update item: no item with id \" + id + \" found\");\n    }\n\n    this._data.set(id, { ...item, ...update });\n\n    return id;\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    if (ids) {\n      const data = this._data;\n\n      return new DataStream<Item>({\n        *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n          for (const id of ids) {\n            const item = data.get(id);\n            if (item != null) {\n              yield [id, item];\n            }\n          }\n        },\n      });\n    } else {\n      return new DataStream({\n        [Symbol.iterator]: this._data.entries.bind(this._data),\n      });\n    }\n  }\n\n  /* develblock:start */\n  public get testLeakData(): Map<Id, FullItem<Item, IdProp>> {\n    return this._data;\n  }\n  public get testLeakIdProp(): IdProp {\n    return this._idProp;\n  }\n  public get testLeakOptions(): DataSetInitialOptions<IdProp> {\n    return this._options;\n  }\n  public get testLeakQueue(): Queue<this> | null {\n    return this._queue;\n  }\n  public set testLeakQueue(v: Queue<this> | null) {\n    this._queue = v;\n  }\n  /* develblock:end */\n}\n", "import {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  EventCallbacksWithAny,\n  EventName,\n  EventPayloads,\n  FullItem,\n  Id,\n  PartItem,\n  RemoveEventPayload,\n  UpdateEventPayload,\n  isId,\n} from \"./data-interface\";\n\nimport { DataSet } from \"./data-set\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Data view options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataViewOptions<Item, IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n}\n\n/**\n * DataView\n *\n * A DataView offers a filtered and/or formatted view on a DataSet. One can subscribe to changes in a DataView, and easily get filtered or formatted data without having to specify filters and field types all the time.\n *\n * ## Example\n * ```javascript\n * // create a DataSet\n * var data = new vis.DataSet();\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // create a DataView\n * // the view will only contain items having a property group with value 1,\n * // and will only output fields id, text, and date.\n * var view = new vis.DataView(data, {\n *   filter: function (item) {\n *     return (item.group == 1);\n *   },\n *   fields: ['id', 'text', 'date']\n * });\n *\n * // subscribe to any change in the DataView\n * view.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an item in the data set\n * data.update({id: 2, group: 1});\n *\n * // get all ids in the view\n * var ids = view.getIds();\n * console.log('ids', ids); // will output [1, 2]\n *\n * // get all items in the view\n * var items = view.get();\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataView<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** @inheritDoc */\n  public length = 0;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this.getDataSet().idProp;\n  }\n\n  private readonly _listener: EventCallbacksWithAny<Item, IdProp>[\"*\"];\n  private _data!: DataInterface<Item, IdProp>; // constructor → setData\n  private readonly _ids: Set<Id> = new Set(); // ids of the items currently in memory (just contains a boolean true)\n  private readonly _options: DataViewOptions<Item, IdProp>;\n\n  /**\n   * Create a DataView.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @param options - Options to configure this data view.\n   */\n  public constructor(\n    data: DataInterface<Item, IdProp>,\n    options?: DataViewOptions<Item, IdProp>\n  ) {\n    super();\n\n    this._options = options || {};\n\n    this._listener = this._onEvent.bind(this);\n\n    this.setData(data);\n  }\n\n  // TODO: implement a function .config() to dynamically update things like configured filter\n  // and trigger changes accordingly\n\n  /**\n   * Set a data source for the view.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @remarks\n   * Note that when the data view is bound to a data set it won't be garbage\n   * collected unless the data set is too. Use `dataView.setData(null)` or\n   * `dataView.dispose()` to enable garbage collection before you lose the last\n   * reference.\n   */\n  public setData(data: DataInterface<Item, IdProp>): void {\n    if (this._data) {\n      // unsubscribe from current dataset\n      if (this._data.off) {\n        this._data.off(\"*\", this._listener);\n      }\n\n      // trigger a remove of all items in memory\n      const ids = this._data.getIds({ filter: this._options.filter });\n      const items = this._data.get(ids);\n\n      this._ids.clear();\n      this.length = 0;\n      this._trigger(\"remove\", { items: ids, oldData: items });\n    }\n\n    if (data != null) {\n      this._data = data;\n\n      // trigger an add of all added items\n      const ids = this._data.getIds({ filter: this._options.filter });\n      for (let i = 0, len = ids.length; i < len; i++) {\n        const id = ids[i];\n        this._ids.add(id);\n      }\n      this.length = ids.length;\n      this._trigger(\"add\", { items: ids });\n    } else {\n      this._data = new DataSet<Item, IdProp>();\n    }\n\n    // subscribe to new dataset\n    if (this._data.on) {\n      this._data.on(\"*\", this._listener);\n    }\n  }\n\n  /**\n   * Refresh the DataView.\n   * Useful when the DataView has a filter function containing a variable parameter.\n   */\n  public refresh(): void {\n    const ids = this._data.getIds({\n      filter: this._options.filter,\n    });\n    const oldIds = [...this._ids];\n    const newIds: Record<Id, boolean> = {};\n    const addedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // check for additions\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const id = ids[i];\n      newIds[id] = true;\n      if (!this._ids.has(id)) {\n        addedIds.push(id);\n        this._ids.add(id);\n      }\n    }\n\n    // check for removals\n    for (let i = 0, len = oldIds.length; i < len; i++) {\n      const id = oldIds[i];\n      const item = this._data.get(id);\n      if (item == null) {\n        // @TODO: Investigate.\n        // Doesn't happen during tests or examples.\n        // Is it really impossible or could it eventually happen?\n        // How to handle it if it does? The types guarantee non-nullable items.\n        console.error(\"If you see this, report it please.\");\n      } else if (!newIds[id]) {\n        removedIds.push(id);\n        removedItems.push(item);\n        this._ids.delete(id);\n      }\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    // trigger events\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds });\n    }\n    if (removedIds.length) {\n      this._trigger(\"remove\", { items: removedIds, oldData: removedItems });\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<string, FullItem<Item, IdProp>> {\n    if (this._data == null) {\n      return null;\n    }\n\n    // parse the arguments\n    let ids: Id | Id[] | null = null;\n    let options: any;\n    if (isId(first) || Array.isArray(first)) {\n      ids = first;\n      options = second;\n    } else {\n      options = first;\n    }\n\n    // extend the options with the default options and provided options\n    const viewOptions: DataInterfaceGetOptions<Item> = Object.assign(\n      {},\n      this._options,\n      options\n    );\n\n    // create a combined filter method when needed\n    const thisFilter = this._options.filter;\n    const optionsFilter = options && options.filter;\n    if (thisFilter && optionsFilter) {\n      viewOptions.filter = (item): boolean => {\n        return thisFilter(item) && optionsFilter(item);\n      };\n    }\n\n    if (ids == null) {\n      return this._data.get(viewOptions);\n    } else {\n      return this._data.get(ids, viewOptions);\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    if (this._data.length) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options != null ? options.filter : null;\n      let filter: DataInterfaceGetIdsOptions<Item>[\"filter\"];\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): boolean => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.getIds({\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | ((item: Item) => boolean);\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = function (item: Item): boolean {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      this._data.forEach(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    type Filter = NonNullable<DataInterfaceMapOptions<Item, T>[\"filter\"]>;\n\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | Filter;\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): ReturnType<Filter> => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.map(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this._data.getDataSet();\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    return this._data.stream(\n      ids || {\n        [Symbol.iterator]: this._ids.keys.bind(this._ids),\n      }\n    );\n  }\n\n  /**\n   * Render the instance unusable prior to garbage collection.\n   *\n   * @remarks\n   * The intention of this method is to help discover scenarios where the data\n   * view is being used when the programmer thinks it has been garbage collected\n   * already. It's stricter version of `dataView.setData(null)`.\n   */\n  public dispose(): void {\n    if (this._data?.off) {\n      this._data.off(\"*\", this._listener);\n    }\n\n    const message = \"This data view has already been disposed of.\";\n    const replacement = {\n      get: (): void => {\n        throw new Error(message);\n      },\n      set: (): void => {\n        throw new Error(message);\n      },\n\n      configurable: false,\n    };\n    for (const key of Reflect.ownKeys(DataView.prototype)) {\n      Object.defineProperty(this, key, replacement);\n    }\n  }\n\n  /**\n   * Event listener. Will propagate all events from the connected data set to the subscribers of the DataView, but will filter the items and only trigger when there are changes in the filtered data set.\n   *\n   * @param event - The name of the event.\n   * @param params - Parameters of the event.\n   * @param senderId - Id supplied by the sender.\n   */\n  private _onEvent<EN extends EventName>(\n    event: EN,\n    params: EventPayloads<Item, IdProp>[EN],\n    senderId?: Id | null\n  ): void {\n    if (!params || !params.items || !this._data) {\n      return;\n    }\n\n    const ids = params.items;\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const oldItems: FullItem<Item, IdProp>[] = [];\n    const updatedItems: FullItem<Item, IdProp>[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    switch (event) {\n      case \"add\":\n        // filter the ids of the added items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n          if (item) {\n            this._ids.add(id);\n            addedIds.push(id);\n          }\n        }\n\n        break;\n\n      case \"update\":\n        // determine the event from the views viewpoint: an updated\n        // item can be added, updated, or removed from this view.\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n\n          if (item) {\n            if (this._ids.has(id)) {\n              updatedIds.push(id);\n              updatedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).data[i]\n              );\n              oldItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              this._ids.add(id);\n              addedIds.push(id);\n            }\n          } else {\n            if (this._ids.has(id)) {\n              this._ids.delete(id);\n              removedIds.push(id);\n              removedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              // nothing interesting for me :-(\n            }\n          }\n        }\n\n        break;\n\n      case \"remove\":\n        // filter the ids of the removed items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          if (this._ids.has(id)) {\n            this._ids.delete(id);\n            removedIds.push(id);\n            removedItems.push(\n              (params as RemoveEventPayload<Item, IdProp>).oldData[i]\n            );\n          }\n        }\n\n        break;\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      this._trigger(\n        \"update\",\n        { items: updatedIds, oldData: oldItems, data: updatedItems },\n        senderId\n      );\n    }\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n  }\n}\n", "import { PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * Check that given value is compatible with Vis Data Set interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataSetLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataSet<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.add === \"function\" &&\n    typeof v.clear === \"function\" &&\n    typeof v.distinct === \"function\" &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.max === \"function\" &&\n    typeof v.min === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.remove === \"function\" &&\n    typeof v.setOptions === \"function\" &&\n    typeof v.stream === \"function\" &&\n    typeof v.update === \"function\" &&\n    typeof v.updateOnly === \"function\"\n  );\n}\n", "import { DataView } from \"./data-view\";\nimport { PartItem } from \"./data-interface\";\nimport { isDataSetLike } from \"./data-set-check\";\n\n/**\n * Check that given value is compatible with Vis Data View interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataViewLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataView<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.stream === \"function\" &&\n    isDataSetLike(idProp, v.getDataSet())\n  );\n}\n"], "names": ["createNewDataPipeFrom", "from", "DataPipeUnderConstruction", "SimpleDataPipe", "_source", "_transformers", "_target", "_listeners", "add", "this", "_add", "bind", "remove", "_remove", "update", "_update", "constructor", "all", "_transformItems", "get", "start", "on", "stop", "off", "items", "reduce", "transform", "_name", "payload", "oldData", "filter", "callback", "push", "input", "map", "flatMap", "to", "target", "isId", "value", "Queue", "delay", "max", "_queue", "_timeout", "_extended", "options", "Infinity", "setOptions", "_flushIfNeeded", "extend", "object", "queue", "undefined", "flush", "Error", "methods", "name", "original", "replace", "i", "length", "destroy", "method", "me", "args", "fn", "context", "entry", "clearTimeout", "setTimeout", "splice", "for<PERSON>ach", "apply", "DataSetPart", "_subscribers", "_trigger", "event", "senderId", "subscriber", "subscribe", "prototype", "unsubscribe", "DataStream", "_pairs", "pairs", "Symbol", "iterator", "id", "item", "entries", "keys", "values", "toIdArray", "pair", "toItemArray", "toEntryArray", "toObjectMap", "Object", "create", "toMap", "Map", "toIdSet", "Set", "toItemSet", "cache", "distinct", "set", "iter", "curr", "next", "done", "maxItem", "maxValue", "min", "minItem", "minValue", "accumulator", "sort", "idA", "itemA", "idB", "itemB", "DataSet", "idProp", "_idProp", "_options", "_data", "data", "super", "Array", "isArray", "fieldId", "addedIds", "d", "some", "has", "len", "_addItem", "updatedIds", "updatedData", "addOrUpdate", "origId", "fullItem", "oldItem", "assign", "_updateItem", "console", "warn", "props", "concat", "updateOnly", "updateEventData", "pureDeepObjectAssign", "first", "second", "ids", "returnType", "itemIds", "itemId", "order", "_sort", "fields", "_filterFields", "result", "resultant", "getIds", "getDataSet", "mappedItems", "filteredItem", "field", "a", "b", "av", "bv", "TypeError", "removedIds", "removedItems", "ident", "delete", "clear", "maxField", "itemField", "minField", "prop", "count", "exists", "j", "uuid4", "ensureFullItem", "JSON", "stringify", "stream", "DataView", "_listener", "_ids", "_onEvent", "setData", "refresh", "oldIds", "newIds", "error", "viewOptions", "thisFilter", "optionsFilter", "defaultFilter", "dispose", "message", "replacement", "configurable", "key", "Reflect", "ownKeys", "defineProperty", "params", "oldItems", "updatedItems", "isDataSetLike", "v", "isDataViewLike"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;qJAoEM,SAAUA,EAGdC,GACA,OAAO,IAAIC,EAA0BD,EACvC,CAaA,MAAME,EAyBeC,QACAC,cACAC,QAjBFC,WAAqC,CACpDC,IAAKC,KAAKC,KAAKC,KAAKF,MACpBG,OAAQH,KAAKI,QAAQF,KAAKF,MAC1BK,OAAQL,KAAKM,QAAQJ,KAAKF,OAW5B,WAAAO,CACmBZ,EACAC,EACAC,GAFAG,KAAOL,QAAPA,EACAK,KAAaJ,cAAbA,EACAI,KAAOH,QAAPA,CACf,CAGG,GAAAW,GAEL,OADAR,KAAKH,QAAQQ,OAAOL,KAAKS,gBAAgBT,KAAKL,QAAQe,QAC/CV,IACR,CAGM,KAAAW,GAKL,OAJAX,KAAKL,QAAQiB,GAAG,MAAOZ,KAAKF,WAAWC,KACvCC,KAAKL,QAAQiB,GAAG,SAAUZ,KAAKF,WAAWK,QAC1CH,KAAKL,QAAQiB,GAAG,SAAUZ,KAAKF,WAAWO,QAEnCL,IACR,CAGM,IAAAa,GAKL,OAJAb,KAAKL,QAAQmB,IAAI,MAAOd,KAAKF,WAAWC,KACxCC,KAAKL,QAAQmB,IAAI,SAAUd,KAAKF,WAAWK,QAC3CH,KAAKL,QAAQmB,IAAI,SAAUd,KAAKF,WAAWO,QAEpCL,IACR,CAQO,eAAAS,CAAgBM,GACtB,OAAOf,KAAKJ,cAAcoB,QAAO,CAACD,EAAOE,IAChCA,EAAUF,IAChBA,EACJ,CAQO,IAAAd,CACNiB,EACAC,GAEe,MAAXA,GAIJnB,KAAKH,QAAQE,IAAIC,KAAKS,gBAAgBT,KAAKL,QAAQe,IAAIS,EAAQJ,QAChE,CAQO,OAAAT,CACNY,EACAC,GAEe,MAAXA,GAIJnB,KAAKH,QAAQQ,OAAOL,KAAKS,gBAAgBT,KAAKL,QAAQe,IAAIS,EAAQJ,QACnE,CAQO,OAAAX,CACNc,EACAC,GAEe,MAAXA,GAIJnB,KAAKH,QAAQM,OAAOH,KAAKS,gBAAgBU,EAAQC,SAClD,EAUH,MAAM3B,EAgBgCE,QARnBC,cAAoC,GAQrD,WAAAW,CAAoCZ,GAAAK,KAAOL,QAAPA,CAAkC,CAS/D,MAAA0B,CACLC,GAGA,OADAtB,KAAKJ,cAAc2B,MAAMC,GAAqBA,EAAMH,OAAOC,KACpDtB,IACR,CAWM,GAAAyB,CACLH,GAGA,OADAtB,KAAKJ,cAAc2B,MAAMC,GAAqBA,EAAMC,IAAIH,KACjDtB,IACR,CAWM,OAAA0B,CACLJ,GAGA,OADAtB,KAAKJ,cAAc2B,MAAMC,GAAqBA,EAAME,QAAQJ,KACrDtB,IACR,CASM,EAAA2B,CAAGC,GACR,OAAO,IAAIlC,EAAeM,KAAKL,QAASK,KAAKJ,cAAegC,EAC7D,EC5QG,SAAUC,EAAKC,GACnB,MAAwB,iBAAVA,GAAuC,iBAAVA,CAC7C,OCgCaC,EAEJC,MAEAC,IAEUC,OAIX,GAEEC,SAAiD,KACjDC,UAAqC,KAO7C,WAAA7B,CAAmB8B,GAEjBrC,KAAKgC,MAAQ,KACbhC,KAAKiC,IAAMK,IAEXtC,KAAKuC,WAAWF,EACjB,CAOM,UAAAE,CAAWF,GACZA,QAAoC,IAAlBA,EAAQL,QAC5BhC,KAAKgC,MAAQK,EAAQL,OAEnBK,QAAkC,IAAhBA,EAAQJ,MAC5BjC,KAAKiC,IAAMI,EAAQJ,KAGrBjC,KAAKwC,gBACN,CAUM,aAAOC,CACZC,EACAL,GAEA,MAAMM,EAAQ,IAAIZ,EAASM,GAE3B,QAAqBO,IAAjBF,EAAOG,MACT,MAAM,IAAIC,MAAM,8CAElBJ,EAAOG,MAAQ,KACbF,EAAME,OAAO,EAGf,MAAME,EAAuC,CAC3C,CACEC,KAAM,QACNC,cAAUL,IAId,GAAIP,GAAWA,EAAQa,QACrB,IAAK,IAAIC,EAAI,EAAGA,EAAId,EAAQa,QAAQE,OAAQD,IAAK,CAC/C,MAAMH,EAAOX,EAAQa,QAAQC,GAC7BJ,EAAQxB,KAAK,CACXyB,KAAMA,EAENC,SAAWP,EAA4CM,KAGzDL,EAAMO,QAAQR,EAA4CM,EAC3D,CAQH,OALAL,EAAMP,UAAY,CAChBM,OAAQA,EACRK,QAASA,GAGJJ,CACR,CAKM,OAAAU,GAGL,GAFArD,KAAK6C,QAED7C,KAAKoC,UAAW,CAClB,MAAMM,EAAS1C,KAAKoC,UAAUM,OACxBK,EAAU/C,KAAKoC,UAAUW,QAC/B,IAAK,IAAII,EAAI,EAAGA,EAAIJ,EAAQK,OAAQD,IAAK,CACvC,MAAMG,EAASP,EAAQI,GACnBG,EAAOL,SAERP,EAAeY,EAAON,MAAQM,EAAOL,gBAG9BP,EAAeY,EAAON,KAEjC,CACDhD,KAAKoC,UAAY,IAClB,CACF,CAQM,OAAAc,CACLR,EACAY,GAGA,MAAMC,EAAKvD,KACLiD,EAAWP,EAAOY,GACxB,IAAKL,EACH,MAAM,IAAIH,MAAM,UAAYQ,EAAS,cAGvCZ,EAAOY,GAAU,YAAaE,GAE5BD,EAAGZ,MAAM,CACPa,KAAMA,EACNC,GAAIR,EACJS,QAAS1D,MAEb,CACD,CAOM,KAAA2C,CAAMgB,GACU,mBAAVA,EACT3D,KAAKkC,OAAOX,KAAK,CAAEkC,GAAIE,IAEvB3D,KAAKkC,OAAOX,KAAKoC,GAGnB3D,KAAKwC,gBACN,CAKO,cAAAA,GAEFxC,KAAKkC,OAAOkB,OAASpD,KAAKiC,KAC5BjC,KAAK6C,QAIc,MAAjB7C,KAAKmC,WACPyB,aAAa5D,KAAKmC,UAClBnC,KAAKmC,SAAW,MAEdnC,KAAK2C,MAAMS,OAAS,GAA2B,iBAAfpD,KAAKgC,QACvChC,KAAKmC,SAAW0B,YAAW,KACzB7D,KAAK6C,OAAO,GACX7C,KAAKgC,OAEX,CAKM,KAAAa,GACL7C,KAAKkC,OAAO4B,OAAO,GAAGC,SAASJ,IAC7BA,EAAMF,GAAGO,MAAML,EAAMD,SAAWC,EAAMF,GAAIE,EAAMH,MAAQ,GAAG,GAE9D,QCxNmBS,EAKHC,aAEb,CACF,IAAK,GACLnE,IAAK,GACLI,OAAQ,GACRE,OAAQ,IAyBA,QAAA8D,CACRC,EACAjD,EACAkD,GAEA,GAA0B,MAArBD,EACH,MAAM,IAAItB,MAAM,0BAGlB,IAAI9C,KAAKkE,aAAaE,MAAWpE,KAAKkE,aAAa,MAAMH,SACtDO,IACCA,EAAWF,EAAOjD,EAAqB,MAAZkD,EAAmBA,EAAW,KAAK,GAGnE,CA6BM,EAAAzD,CACLwD,EACA9C,GAEwB,mBAAbA,GACTtB,KAAKkE,aAAaE,GAAO7C,KAAKD,EAGjC,CA6BM,GAAAR,CACLsD,EACA9C,GAEAtB,KAAKkE,aAAaE,GAASpE,KAAKkE,aAAaE,GAAO/C,QACjDiD,GAAwBA,IAAehD,GAE3C,CAKMiD,UAA6CN,EAAYO,UAAU5D,GAInE6D,YACLR,EAAYO,UAAU1D,UC9Ib4D,EACMC,OAOjB,WAAApE,CAAmBqE,GACjB5E,KAAK2E,OAASC,CACf,CAKM,EAAEC,OAAOC,YACd,IAAK,MAAOC,EAAIC,KAAShF,KAAK2E,YACtB,CAACI,EAAIC,EAEd,CAKM,QAACC,GACN,IAAK,MAAOF,EAAIC,KAAShF,KAAK2E,YACtB,CAACI,EAAIC,EAEd,CAKM,KAACE,GACN,IAAK,MAAOH,KAAO/E,KAAK2E,aAChBI,CAET,CAKM,OAACI,GACN,IAAK,MAAS,CAAAH,KAAShF,KAAK2E,aACpBK,CAET,CASM,SAAAI,GACL,MAAO,IAAIpF,KAAK2E,QAAQlD,KAAK4D,GAAaA,EAAK,IAChD,CASM,WAAAC,GACL,MAAO,IAAItF,KAAK2E,QAAQlD,KAAK4D,GAAeA,EAAK,IAClD,CASM,YAAAE,GACL,MAAO,IAAIvF,KAAK2E,OACjB,CASM,WAAAa,GACL,MAAM/D,EAAwBgE,OAAOC,OAAO,MAC5C,IAAK,MAAOX,EAAIC,KAAShF,KAAK2E,OAC5BlD,EAAIsD,GAAMC,EAEZ,OAAOvD,CACR,CAOM,KAAAkE,GACL,OAAO,IAAIC,IAAI5F,KAAK2E,OACrB,CAOM,OAAAkB,GACL,OAAO,IAAIC,IAAI9F,KAAKoF,YACrB,CAOM,SAAAW,GACL,OAAO,IAAID,IAAI9F,KAAKsF,cACrB,CAwBM,KAAAU,GACL,OAAO,IAAItB,EAAW,IAAI1E,KAAK2E,QAChC,CASM,QAAAsB,CAAY3E,GACjB,MAAM4E,EAAM,IAAIJ,IAEhB,IAAK,MAAOf,EAAIC,KAAShF,KAAK2E,OAC5BuB,EAAInG,IAAIuB,EAAS0D,EAAMD,IAGzB,OAAOmB,CACR,CAQM,MAAA7E,CAAOC,GACZ,MAAMsD,EAAQ5E,KAAK2E,OACnB,OAAO,IAAID,EAAiB,CAC1B,EAAEG,OAAOC,YACP,IAAK,MAAOC,EAAIC,KAASJ,EACnBtD,EAAS0D,EAAMD,UACX,CAACA,EAAIC,GAGhB,GAEJ,CAOM,OAAAjB,CAAQzC,GACb,IAAK,MAAOyD,EAAIC,KAAShF,KAAK2E,OAC5BrD,EAAS0D,EAAMD,EAElB,CASM,GAAAtD,CACLH,GAEA,MAAMsD,EAAQ5E,KAAK2E,OACnB,OAAO,IAAID,EAAmB,CAC5B,EAAEG,OAAOC,YACP,IAAK,MAAOC,EAAIC,KAASJ,OACjB,CAACG,EAAIzD,EAAS0D,EAAMD,GAE7B,GAEJ,CAQM,GAAA9C,CAAIX,GACT,MAAM6E,EAAOnG,KAAK2E,OAAOE,OAAOC,YAChC,IAAIsB,EAAOD,EAAKE,OAChB,GAAID,EAAKE,KACP,OAAO,KAGT,IAAIC,EAAgBH,EAAKtE,MAAM,GAC3B0E,EAAmBlF,EAAS8E,EAAKtE,MAAM,GAAIsE,EAAKtE,MAAM,IAC1D,OAASsE,EAAOD,EAAKE,QAAQC,MAAM,CACjC,MAAOvB,EAAIC,GAAQoB,EAAKtE,MAClBA,EAAQR,EAAS0D,EAAMD,GACzBjD,EAAQ0E,IACVA,EAAW1E,EACXyE,EAAUvB,EAEb,CAED,OAAOuB,CACR,CAQM,GAAAE,CAAInF,GACT,MAAM6E,EAAOnG,KAAK2E,OAAOE,OAAOC,YAChC,IAAIsB,EAAOD,EAAKE,OAChB,GAAID,EAAKE,KACP,OAAO,KAGT,IAAII,EAAgBN,EAAKtE,MAAM,GAC3B6E,EAAmBrF,EAAS8E,EAAKtE,MAAM,GAAIsE,EAAKtE,MAAM,IAC1D,OAASsE,EAAOD,EAAKE,QAAQC,MAAM,CACjC,MAAOvB,EAAIC,GAAQoB,EAAKtE,MAClBA,EAAQR,EAAS0D,EAAMD,GACzBjD,EAAQ6E,IACVA,EAAW7E,EACX4E,EAAU1B,EAEb,CAED,OAAO0B,CACR,CAUM,MAAA1F,CACLM,EACAsF,GAEA,IAAK,MAAO7B,EAAIC,KAAShF,KAAK2E,OAC5BiC,EAActF,EAASsF,EAAa5B,EAAMD,GAE5C,OAAO6B,CACR,CAQM,IAAAC,CACLvF,GAEA,OAAO,IAAIoD,EAAW,CACpB,CAACG,OAAOC,UAAW,IACjB,IAAI9E,KAAK2E,QACNkC,MAAK,EAAEC,EAAKC,IAASC,EAAKC,KACzB3F,EAASyF,EAAOE,EAAOH,EAAKE,KAE7BnC,OAAOC,aAEf,ECpLG,MAAOoC,UAIHjD,EAIDpB,MAEAO,OAEP,UAAW+D,GACT,OAAOnH,KAAKoH,OACb,CAEgBC,SACAC,MACAF,QACTlF,OAA6B,KAiBrC,WAAA3B,CACEgH,EACAlF,GAEAmF,QAGID,IAASE,MAAMC,QAAQH,KACzBlF,EAAUkF,EACVA,EAAO,IAGTvH,KAAKqH,SAAWhF,GAAW,GAC3BrC,KAAKsH,MAAQ,IAAI1B,IACjB5F,KAAKoD,OAAS,EACdpD,KAAKoH,QAAUpH,KAAKqH,SAASM,SAAY,KAGrCJ,GAAQA,EAAKnE,QACfpD,KAAKD,IAAIwH,GAGXvH,KAAKuC,WAAWF,EACjB,CAOM,UAAAE,CAAWF,GACZA,QAA6BO,IAAlBP,EAAQM,SACC,IAAlBN,EAAQM,MAEN3C,KAAKkC,SACPlC,KAAKkC,OAAOmB,UACZrD,KAAKkC,OAAS,OAIXlC,KAAKkC,SACRlC,KAAKkC,OAASH,EAAMU,OAAOzC,KAAM,CAC/BkD,QAAS,CAAC,MAAO,SAAU,aAI3Bb,EAAQM,OAAkC,iBAAlBN,EAAQM,OAClC3C,KAAKkC,OAAOK,WAAWF,EAAQM,QAItC,CA4BM,GAAA5C,CAAIwH,EAAqBlD,GAC9B,MAAMuD,EAAiB,GACvB,IAAI7C,EAEJ,GAAI0C,MAAMC,QAAQH,GAAO,CAGvB,GADuBA,EAAK9F,KAAKoG,GAAMA,EAAE7H,KAAKoH,WACjCU,MAAM/C,GAAO/E,KAAKsH,MAAMS,IAAIhD,KACvC,MAAM,IAAIjC,MAAM,oDAElB,IAAK,IAAIK,EAAI,EAAG6E,EAAMT,EAAKnE,OAAQD,EAAI6E,EAAK7E,IAC1C4B,EAAK/E,KAAKiI,SAASV,EAAKpE,IACxByE,EAASrG,KAAKwD,EAEjB,KAAM,KAAIwC,GAAwB,iBAATA,EAKxB,MAAM,IAAIzE,MAAM,oBAHhBiC,EAAK/E,KAAKiI,SAASV,GACnBK,EAASrG,KAAKwD,EAGf,CAMD,OAJI6C,EAASxE,QACXpD,KAAKmE,SAAS,MAAO,CAAEpD,MAAO6G,GAAYvD,GAGrCuD,CACR,CAoCM,MAAAvH,CACLkH,EACAlD,GAEA,MAAMuD,EAAiB,GACjBM,EAAmB,GACnB9G,EAAoC,GACpC+G,EAAwC,GACxChB,EAASnH,KAAKoH,QAEdgB,EAAepD,IACnB,MAAMqD,EAAgBrD,EAAKmC,GAC3B,GAAc,MAAVkB,GAAkBrI,KAAKsH,MAAMS,IAAIM,GAAS,CAC5C,MAAMC,EAAWtD,EACXuD,EAAU9C,OAAO+C,OAAO,CAAE,EAAExI,KAAKsH,MAAM5G,IAAI2H,IAE3CtD,EAAK/E,KAAKyI,YAAYH,GAC5BJ,EAAW3G,KAAKwD,GAChBoD,EAAY5G,KAAK+G,GACjBlH,EAAQG,KAAKgH,EACd,KAAM,CAEL,MAAMxD,EAAK/E,KAAKiI,SAASjD,GACzB4C,EAASrG,KAAKwD,EACf,GAGH,GAAI0C,MAAMC,QAAQH,GAEhB,IAAK,IAAIpE,EAAI,EAAG6E,EAAMT,EAAKnE,OAAQD,EAAI6E,EAAK7E,IACtCoE,EAAKpE,IAAyB,iBAAZoE,EAAKpE,GACzBiF,EAAYb,EAAKpE,IAEjBuF,QAAQC,KACN,wDAA0DxF,OAI3D,KAAIoE,GAAwB,iBAATA,EAIxB,MAAM,IAAIzE,MAAM,oBAFhBsF,EAAYb,EAGb,CAKD,GAHIK,EAASxE,QACXpD,KAAKmE,SAAS,MAAO,CAAEpD,MAAO6G,GAAYvD,GAExC6D,EAAW9E,OAAQ,CACrB,MAAMwF,EAAQ,CAAE7H,MAAOmH,EAAY9G,QAASA,EAASmG,KAAMY,GAQ3DnI,KAAKmE,SAAS,SAAUyE,EAAOvE,EAChC,CAED,OAAOuD,EAASiB,OAAOX,EACxB,CAoCM,UAAAY,CACLvB,EACAlD,GAEKoD,MAAMC,QAAQH,KACjBA,EAAO,CAACA,IAGV,MAAMwB,EAAkBxB,EACrB9F,KAEGpB,IAKA,MAAMe,EAAUpB,KAAKsH,MAAM5G,IAAIL,EAAOL,KAAKoH,UAC3C,GAAe,MAAXhG,EACF,MAAM,IAAI0B,MAAM,+CAElB,MAAO,CAAE1B,UAASf,SAAQ,IAG7BoB,KACC,EACEL,UACAf,aAMA,MAAM0E,EAAK3D,EAAQpB,KAAKoH,SAClBe,EAAca,EAAqB5H,EAASf,GAIlD,OAFAL,KAAKsH,MAAMpB,IAAInB,EAAIoD,GAEZ,CACLpD,KACA3D,QAASA,EACT+G,cACD,IAIP,GAAIY,EAAgB3F,OAAQ,CAC1B,MAAMwF,EAA+C,CACnD7H,MAAOgI,EAAgBtH,KAAKK,GAAcA,EAAMiD,KAChD3D,QAAS2H,EAAgBtH,KACtBK,GAAkCA,EAAMV,UAE3CmG,KAAMwB,EAAgBtH,KACnBK,GAAkCA,EAAMqG,eAY7C,OAFAnI,KAAKmE,SAAS,SAAUyE,EAAOvE,GAExBuE,EAAM7H,KACd,CACC,MAAO,EAEV,CA6DM,GAAAL,CACLuI,EACAC,GASA,IAAInE,EACAoE,EACA9G,EACAR,EAAKoH,IAEPlE,EAAKkE,EACL5G,EAAU6G,GACDzB,MAAMC,QAAQuB,IAEvBE,EAAMF,EACN5G,EAAU6G,GAGV7G,EAAU4G,EAIZ,MAAMG,EACJ/G,GAAkC,WAAvBA,EAAQ+G,WAA0B,SAAW,QAcpD/H,EAASgB,GAAWA,EAAQhB,OAC5BN,EAAkC,GACxC,IAAIiE,EACAqE,EACAC,EAGJ,GAAU,MAANvE,EAEFC,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAClBC,GAAQ3D,IAAWA,EAAO2D,KAC5BA,OAAOpC,QAEJ,GAAW,MAAPuG,EAET,IAAK,IAAIhG,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IACzC6B,EAAOhF,KAAKsH,MAAM5G,IAAIyI,EAAIhG,IACd,MAAR6B,GAAkB3D,IAAUA,EAAO2D,IACrCjE,EAAMQ,KAAKyD,OAGV,CAELqE,EAAU,IAAIrJ,KAAKsH,MAAMpC,QACzB,IAAK,IAAI/B,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAC7CmG,EAASD,EAAQlG,GACjB6B,EAAOhF,KAAKsH,MAAM5G,IAAI4I,GACV,MAARtE,GAAkB3D,IAAUA,EAAO2D,IACrCjE,EAAMQ,KAAKyD,EAGhB,CAQD,GALI3C,GAAWA,EAAQkH,OAAe3G,MAANmC,GAC9B/E,KAAKwJ,MAAMzI,EAAOsB,EAAQkH,OAIxBlH,GAAWA,EAAQoH,OAAQ,CAC7B,MAAMA,EAASpH,EAAQoH,OACvB,GAAU7G,MAANmC,GAA2B,MAARC,EACrBA,EAAOhF,KAAK0J,cAAc1E,EAAMyE,QAEhC,IAAK,IAAItG,EAAI,EAAG6E,EAAMjH,EAAMqC,OAAQD,EAAI6E,EAAK7E,IAC3CpC,EAAMoC,GAAKnD,KAAK0J,cAAc3I,EAAMoC,GAAIsG,EAM7C,CAGD,GAAkB,UAAdL,EAAwB,CAC1B,MAAMO,EAAiD,CAAA,EACvD,IAAK,IAAIxG,EAAI,EAAG6E,EAAMjH,EAAMqC,OAAQD,EAAI6E,EAAK7E,IAAK,CAChD,MAAMyG,EAAY7I,EAAMoC,GAIxBwG,EADeC,EAAU5J,KAAKoH,UACjBwC,CACd,CACD,OAAOD,CACR,CACC,OAAU,MAAN5E,EAEKC,GAAQ,KAGRjE,CAGZ,CAGM,MAAA8I,CAAOxH,GACZ,MAAMkF,EAAOvH,KAAKsH,MACZjG,EAASgB,GAAWA,EAAQhB,OAC5BkI,EAAQlH,GAAWA,EAAQkH,MAC3BF,EAAU,IAAI9B,EAAKrC,QACnBiE,EAAY,GAElB,GAAI9H,EAEF,GAAIkI,EAAO,CAET,MAAMxI,EAAQ,GACd,IAAK,IAAIoC,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACb6B,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAChB,MAARC,GAAgB3D,EAAO2D,IACzBjE,EAAMQ,KAAKyD,EAEd,CAEDhF,KAAKwJ,MAAMzI,EAAOwI,GAElB,IAAK,IAAIpG,EAAI,EAAG6E,EAAMjH,EAAMqC,OAAQD,EAAI6E,EAAK7E,IAC3CgG,EAAI5H,KAAKR,EAAMoC,GAAGnD,KAAKoH,SAE1B,MAEC,IAAK,IAAIjE,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACb6B,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAChB,MAARC,GAAgB3D,EAAO2D,IACzBmE,EAAI5H,KAAKyD,EAAKhF,KAAKoH,SAEtB,MAIH,GAAImC,EAAO,CAET,MAAMxI,EAAQ,GACd,IAAK,IAAIoC,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACnBpC,EAAMQ,KAAKgG,EAAK7G,IAAIqE,GACrB,CAED/E,KAAKwJ,MAAMzI,EAAOwI,GAElB,IAAK,IAAIpG,EAAI,EAAG6E,EAAMjH,EAAMqC,OAAQD,EAAI6E,EAAK7E,IAC3CgG,EAAI5H,KAAKR,EAAMoC,GAAGnD,KAAKoH,SAE1B,MAEC,IAAK,IAAIjE,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACb6B,EAAOuC,EAAK7G,IAAIqE,GACV,MAARC,GACFmE,EAAI5H,KAAKyD,EAAKhF,KAAKoH,SAEtB,CAIL,OAAO+B,CACR,CAGM,UAAAW,GACL,OAAO9J,IACR,CAGM,OAAA+D,CACLzC,EACAe,GAEA,MAAMhB,EAASgB,GAAWA,EAAQhB,OAE5BgI,EAAU,IADHrJ,KAAKsH,MACOpC,QAEzB,GAAI7C,GAAWA,EAAQkH,MAAO,CAE5B,MAAMxI,EAAkCf,KAAKU,IAAI2B,GAEjD,IAAK,IAAIc,EAAI,EAAG6E,EAAMjH,EAAMqC,OAAQD,EAAI6E,EAAK7E,IAAK,CAChD,MAAM6B,EAAOjE,EAAMoC,GAEnB7B,EAAS0D,EADEA,EAAKhF,KAAKoH,SAEtB,CACF,MAEC,IAAK,IAAIjE,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACb6B,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAChB,MAARC,GAAkB3D,IAAUA,EAAO2D,IACrC1D,EAAS0D,EAAMD,EAElB,CAEJ,CAGM,GAAAtD,CACLH,EACAe,GAEA,MAAMhB,EAASgB,GAAWA,EAAQhB,OAC5B0I,EAAmB,GAEnBV,EAAU,IADHrJ,KAAKsH,MACOpC,QAGzB,IAAK,IAAI/B,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GACb6B,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAChB,MAARC,GAAkB3D,IAAUA,EAAO2D,IACrC+E,EAAYxI,KAAKD,EAAS0D,EAAMD,GAEnC,CAOD,OAJI1C,GAAWA,EAAQkH,OACrBvJ,KAAKwJ,MAAMO,EAAa1H,EAAQkH,OAG3BQ,CACR,CAmBO,aAAAL,CACN1E,EACAyE,GAEA,OAAKzE,GAMHyC,MAAMC,QAAQ+B,GAEVA,EAEChE,OAAOP,KAAKuE,IACjBzI,QACA,CAACgJ,EAAcC,KACbD,EAAaC,GAASjF,EAAKiF,GACpBD,IAET,CAAE,GAdKhF,CAgBV,CASO,KAAAwE,CAASzI,EAAYwI,GAC3B,GAAqB,iBAAVA,EAAoB,CAE7B,MAAMvG,EAAOuG,EACbxI,EAAM8F,MAAK,CAACqD,EAAGC,KAEb,MAAMC,EAAMF,EAAUlH,GAChBqH,EAAMF,EAAUnH,GACtB,OAAOoH,EAAKC,EAAK,EAAID,EAAKC,GAAM,EAAI,CAAC,GAExC,KAAM,IAAqB,mBAAVd,EAMhB,MAAM,IAAIe,UAAU,wCAJpBvJ,EAAM8F,KAAK0C,EAKZ,CACF,CA4BM,MAAApJ,CAAO4E,EAA+BV,GAC3C,MAAMkG,EAAmB,GACnBC,EAAyC,GAGzCrB,EAAM1B,MAAMC,QAAQ3C,GAAMA,EAAK,CAACA,GAEtC,IAAK,IAAI5B,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM6B,EAAOhF,KAAKI,QAAQ+I,EAAIhG,IAC9B,GAAI6B,EAAM,CACR,MAAMsE,EAAgBtE,EAAKhF,KAAKoH,SAClB,MAAVkC,IACFiB,EAAWhJ,KAAK+H,GAChBkB,EAAajJ,KAAKyD,GAErB,CACF,CAUD,OARIuF,EAAWnH,QACbpD,KAAKmE,SACH,SACA,CAAEpD,MAAOwJ,EAAYnJ,QAASoJ,GAC9BnG,GAIGkG,CACR,CAQO,OAAAnK,CAAQ2E,GAGd,IAAI0F,EAUJ,GAPI5I,EAAKkD,GACP0F,EAAQ1F,EACCA,GAAoB,iBAAPA,IACtB0F,EAAQ1F,EAAG/E,KAAKoH,UAIL,MAATqD,GAAiBzK,KAAKsH,MAAMS,IAAI0C,GAAQ,CAC1C,MAAMzF,EAAOhF,KAAKsH,MAAM5G,IAAI+J,IAAU,KAGtC,OAFAzK,KAAKsH,MAAMoD,OAAOD,KAChBzK,KAAKoD,OACA4B,CACR,CAED,OAAO,IACR,CAUM,KAAA2F,CAAMtG,GACX,MAAM8E,EAAM,IAAInJ,KAAKsH,MAAMpC,QACrBnE,EAAkC,GAExC,IAAK,IAAIoC,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IACzCpC,EAAMQ,KAAKvB,KAAKsH,MAAM5G,IAAIyI,EAAIhG,KAQhC,OALAnD,KAAKsH,MAAMqD,QACX3K,KAAKoD,OAAS,EAEdpD,KAAKmE,SAAS,SAAU,CAAEpD,MAAOoI,EAAK/H,QAASL,GAASsD,GAEjD8E,CACR,CAQM,GAAAlH,CAAIgI,GACT,IAAIhI,EAAM,KACN2I,EAAW,KAEf,IAAK,MAAM5F,KAAQhF,KAAKsH,MAAMnC,SAAU,CACtC,MAAM0F,EAAY7F,EAAKiF,GAEA,iBAAdY,IACM,MAAZD,GAAoBC,EAAYD,KAEjC3I,EAAM+C,EACN4F,EAAWC,EAEd,CAED,OAAO5I,GAAO,IACf,CAQM,GAAAwE,CAAIwD,GACT,IAAIxD,EAAM,KACNqE,EAAW,KAEf,IAAK,MAAM9F,KAAQhF,KAAKsH,MAAMnC,SAAU,CACtC,MAAM0F,EAAY7F,EAAKiF,GAEA,iBAAdY,IACM,MAAZC,GAAoBD,EAAYC,KAEjCrE,EAAMzB,EACN8F,EAAWD,EAEd,CAED,OAAOpE,GAAO,IACf,CAUM,QAAAR,CAA2B8E,GAChC,MAAMxD,EAAOvH,KAAKsH,MACZ+B,EAAU,IAAI9B,EAAKrC,QACnBC,EAAoB,GAC1B,IAAI6F,EAAQ,EAEZ,IAAK,IAAI7H,EAAI,EAAG6E,EAAMqB,EAAQjG,OAAQD,EAAI6E,EAAK7E,IAAK,CAClD,MAAM4B,EAAKsE,EAAQlG,GAEbrB,EADOyF,EAAK7G,IAAIqE,GACMgG,GAC5B,IAAIE,GAAS,EACb,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAOE,IACzB,GAAI/F,EAAO+F,IAAMpJ,EAAO,CACtBmJ,GAAS,EACT,KACD,CAEEA,QAAoBrI,IAAVd,IACbqD,EAAO6F,GAASlJ,EAChBkJ,IAEH,CAED,OAAO7F,CACR,CAQO,QAAA8C,CAASjD,GACf,MAAMsD,EA19BV,SACEtD,EACAmC,GAOA,OALoB,MAAhBnC,EAAKmC,KAEPnC,EAAKmC,GAAUgE,KAGVnG,CACT,CAg9BqBoG,CAAepG,EAAMhF,KAAKoH,SACrCrC,EAAKuD,EAAStI,KAAKoH,SAGzB,GAAIpH,KAAKsH,MAAMS,IAAIhD,GAEjB,MAAM,IAAIjC,MACR,iCAAmCiC,EAAK,mBAO5C,OAHA/E,KAAKsH,MAAMpB,IAAInB,EAAIuD,KACjBtI,KAAKoD,OAEA2B,CACR,CASO,WAAA0D,CAAYpI,GAClB,MAAM0E,EAAY1E,EAAOL,KAAKoH,SAC9B,GAAU,MAANrC,EACF,MAAM,IAAIjC,MACR,6CACEuI,KAAKC,UAAUjL,GACf,KAGN,MAAM2E,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAC5B,IAAKC,EAEH,MAAM,IAAIlC,MAAM,uCAAyCiC,EAAK,UAKhE,OAFA/E,KAAKsH,MAAMpB,IAAInB,EAAI,IAAKC,KAAS3E,IAE1B0E,CACR,CAGM,MAAAwG,CAAOpC,GACZ,GAAIA,EAAK,CACP,MAAM5B,EAAOvH,KAAKsH,MAElB,OAAO,IAAI5C,EAAiB,CAC1B,EAAEG,OAAOC,YACP,IAAK,MAAMC,KAAMoE,EAAK,CACpB,MAAMnE,EAAOuC,EAAK7G,IAAIqE,GACV,MAARC,SACI,CAACD,EAAIC,GAEd,CACF,GAEJ,CACC,OAAO,IAAIN,EAAW,CACpB,CAACG,OAAOC,UAAW9E,KAAKsH,MAAMrC,QAAQ/E,KAAKF,KAAKsH,QAGrD,ECxgCG,MAAOkE,UAIHvH,EAIDb,OAAS,EAEhB,UAAW+D,GACT,OAAOnH,KAAK8J,aAAa3C,MAC1B,CAEgBsE,UACTnE,MACSoE,KAAgB,IAAI5F,IACpBuB,SAQjB,WAAA9G,CACEgH,EACAlF,GAEAmF,QAEAxH,KAAKqH,SAAWhF,GAAW,GAE3BrC,KAAKyL,UAAYzL,KAAK2L,SAASzL,KAAKF,MAEpCA,KAAK4L,QAAQrE,EACd,CAeM,OAAAqE,CAAQrE,GACb,GAAIvH,KAAKsH,MAAO,CAEVtH,KAAKsH,MAAMxG,KACbd,KAAKsH,MAAMxG,IAAI,IAAKd,KAAKyL,WAI3B,MAAMtC,EAAMnJ,KAAKsH,MAAMuC,OAAO,CAAExI,OAAQrB,KAAKqH,SAAShG,SAChDN,EAAQf,KAAKsH,MAAM5G,IAAIyI,GAE7BnJ,KAAK0L,KAAKf,QACV3K,KAAKoD,OAAS,EACdpD,KAAKmE,SAAS,SAAU,CAAEpD,MAAOoI,EAAK/H,QAASL,GAChD,CAED,GAAY,MAARwG,EAAc,CAChBvH,KAAKsH,MAAQC,EAGb,MAAM4B,EAAMnJ,KAAKsH,MAAMuC,OAAO,CAAExI,OAAQrB,KAAKqH,SAAShG,SACtD,IAAK,IAAI8B,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM4B,EAAKoE,EAAIhG,GACfnD,KAAK0L,KAAK3L,IAAIgF,EACf,CACD/E,KAAKoD,OAAS+F,EAAI/F,OAClBpD,KAAKmE,SAAS,MAAO,CAAEpD,MAAOoI,GAC/B,MACCnJ,KAAKsH,MAAQ,IAAIJ,EAIflH,KAAKsH,MAAM1G,IACbZ,KAAKsH,MAAM1G,GAAG,IAAKZ,KAAKyL,UAE3B,CAMM,OAAAI,GACL,MAAM1C,EAAMnJ,KAAKsH,MAAMuC,OAAO,CAC5BxI,OAAQrB,KAAKqH,SAAShG,SAElByK,EAAS,IAAI9L,KAAK0L,MAClBK,EAA8B,CAAA,EAC9BnE,EAAiB,GACjB2C,EAAmB,GACnBC,EAAyC,GAG/C,IAAK,IAAIrH,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM4B,EAAKoE,EAAIhG,GACf4I,EAAOhH,IAAM,EACR/E,KAAK0L,KAAK3D,IAAIhD,KACjB6C,EAASrG,KAAKwD,GACd/E,KAAK0L,KAAK3L,IAAIgF,GAEjB,CAGD,IAAK,IAAI5B,EAAI,EAAG6E,EAAM8D,EAAO1I,OAAQD,EAAI6E,EAAK7E,IAAK,CACjD,MAAM4B,EAAK+G,EAAO3I,GACZ6B,EAAOhF,KAAKsH,MAAM5G,IAAIqE,GAChB,MAARC,EAKF0D,QAAQsD,MAAM,sCACJD,EAAOhH,KACjBwF,EAAWhJ,KAAKwD,GAChByF,EAAajJ,KAAKyD,GAClBhF,KAAK0L,KAAKhB,OAAO3F,GAEpB,CAED/E,KAAKoD,QAAUwE,EAASxE,OAASmH,EAAWnH,OAGxCwE,EAASxE,QACXpD,KAAKmE,SAAS,MAAO,CAAEpD,MAAO6G,IAE5B2C,EAAWnH,QACbpD,KAAKmE,SAAS,SAAU,CAAEpD,MAAOwJ,EAAYnJ,QAASoJ,GAEzD,CA6DM,GAAA9J,CACLuI,EACAC,GAMA,GAAkB,MAAdlJ,KAAKsH,MACP,OAAO,KAIT,IACIjF,EADA8G,EAAwB,KAExBtH,EAAKoH,IAAUxB,MAAMC,QAAQuB,IAC/BE,EAAMF,EACN5G,EAAU6G,GAEV7G,EAAU4G,EAIZ,MAAMgD,EAA6CxG,OAAO+C,OACxD,CAAE,EACFxI,KAAKqH,SACLhF,GAII6J,EAAalM,KAAKqH,SAAShG,OAC3B8K,EAAgB9J,GAAWA,EAAQhB,OAOzC,OANI6K,GAAcC,IAChBF,EAAY5K,OAAU2D,GACbkH,EAAWlH,IAASmH,EAAcnH,IAIlC,MAAPmE,EACKnJ,KAAKsH,MAAM5G,IAAIuL,GAEfjM,KAAKsH,MAAM5G,IAAIyI,EAAK8C,EAE9B,CAGM,MAAApC,CAAOxH,GACZ,GAAIrC,KAAKsH,MAAMlE,OAAQ,CACrB,MAAMgJ,EAAgBpM,KAAKqH,SAAShG,OAC9B8K,EAA2B,MAAX9J,EAAkBA,EAAQhB,OAAS,KACzD,IAAIA,EAcJ,OAVIA,EAFA8K,EACEC,EACQpH,GACDoH,EAAcpH,IAASmH,EAAcnH,GAGrCmH,EAGFC,EAGJpM,KAAKsH,MAAMuC,OAAO,CACvBxI,OAAQA,EACRkI,MAAOlH,GAAWA,EAAQkH,OAE7B,CACC,MAAO,EAEV,CAGM,OAAAxF,CACLzC,EACAe,GAEA,GAAIrC,KAAKsH,MAAO,CACd,MAAM8E,EAAgBpM,KAAKqH,SAAShG,OAC9B8K,EAAgB9J,GAAWA,EAAQhB,OACzC,IAAIA,EAIAA,EAFA8K,EACEC,EACO,SAAUpH,GACjB,OAAOoH,EAAcpH,IAASmH,EAAcnH,EAC9C,EAESmH,EAGFC,EAGXpM,KAAKsH,MAAMvD,QAAQzC,EAAU,CAC3BD,OAAQA,EACRkI,MAAOlH,GAAWA,EAAQkH,OAE7B,CACF,CAGM,GAAA9H,CACLH,EACAe,GAIA,GAAIrC,KAAKsH,MAAO,CACd,MAAM8E,EAAgBpM,KAAKqH,SAAShG,OAC9B8K,EAAgB9J,GAAWA,EAAQhB,OACzC,IAAIA,EAcJ,OAVIA,EAFA8K,EACEC,EACQpH,GACDoH,EAAcpH,IAASmH,EAAcnH,GAGrCmH,EAGFC,EAGJpM,KAAKsH,MAAM7F,IAAIH,EAAU,CAC9BD,OAAQA,EACRkI,MAAOlH,GAAWA,EAAQkH,OAE7B,CACC,MAAO,EAEV,CAGM,UAAAO,GACL,OAAO9J,KAAKsH,MAAMwC,YACnB,CAGM,MAAAyB,CAAOpC,GACZ,OAAOnJ,KAAKsH,MAAMiE,OAChBpC,GAAO,CACL,CAACtE,OAAOC,UAAW9E,KAAK0L,KAAKxG,KAAKhF,KAAKF,KAAK0L,OAGjD,CAUM,OAAAW,GACDrM,KAAKsH,OAAOxG,KACdd,KAAKsH,MAAMxG,IAAI,IAAKd,KAAKyL,WAG3B,MAAMa,EAAU,+CACVC,EAAc,CAClB7L,IAAK,KACH,MAAM,IAAIoC,MAAMwJ,EAAQ,EAE1BpG,IAAK,KACH,MAAM,IAAIpD,MAAMwJ,EAAQ,EAG1BE,cAAc,GAEhB,IAAK,MAAMC,KAAOC,QAAQC,QAAQnB,EAAShH,WACzCiB,OAAOmH,eAAe5M,KAAMyM,EAAKF,EAEpC,CASO,QAAAZ,CACNvH,EACAyI,EACAxI,GAEA,IAAKwI,IAAWA,EAAO9L,QAAUf,KAAKsH,MACpC,OAGF,MAAM6B,EAAM0D,EAAO9L,MACb6G,EAAiB,GACjBM,EAAmB,GACnBqC,EAAmB,GACnBuC,EAAqC,GACrCC,EAAyC,GACzCvC,EAAyC,GAE/C,OAAQpG,GACN,IAAK,MAEH,IAAK,IAAIjB,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM4B,EAAKoE,EAAIhG,GACFnD,KAAKU,IAAIqE,KAEpB/E,KAAK0L,KAAK3L,IAAIgF,GACd6C,EAASrG,KAAKwD,GAEjB,CAED,MAEF,IAAK,SAGH,IAAK,IAAI5B,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM4B,EAAKoE,EAAIhG,GACFnD,KAAKU,IAAIqE,GAGhB/E,KAAK0L,KAAK3D,IAAIhD,IAChBmD,EAAW3G,KAAKwD,GAChBgI,EAAaxL,KACVsL,EAA4CtF,KAAKpE,IAEpD2J,EAASvL,KACNsL,EAA4CzL,QAAQ+B,MAGvDnD,KAAK0L,KAAK3L,IAAIgF,GACd6C,EAASrG,KAAKwD,IAGZ/E,KAAK0L,KAAK3D,IAAIhD,KAChB/E,KAAK0L,KAAKhB,OAAO3F,GACjBwF,EAAWhJ,KAAKwD,GAChByF,EAAajJ,KACVsL,EAA4CzL,QAAQ+B,IAM5D,CAED,MAEF,IAAK,SAEH,IAAK,IAAIA,EAAI,EAAG6E,EAAMmB,EAAI/F,OAAQD,EAAI6E,EAAK7E,IAAK,CAC9C,MAAM4B,EAAKoE,EAAIhG,GACXnD,KAAK0L,KAAK3D,IAAIhD,KAChB/E,KAAK0L,KAAKhB,OAAO3F,GACjBwF,EAAWhJ,KAAKwD,GAChByF,EAAajJ,KACVsL,EAA4CzL,QAAQ+B,IAG1D,EAKLnD,KAAKoD,QAAUwE,EAASxE,OAASmH,EAAWnH,OAExCwE,EAASxE,QACXpD,KAAKmE,SAAS,MAAO,CAAEpD,MAAO6G,GAAYvD,GAExC6D,EAAW9E,QACbpD,KAAKmE,SACH,SACA,CAAEpD,MAAOmH,EAAY9G,QAAS0L,EAAUvF,KAAMwF,GAC9C1I,GAGAkG,EAAWnH,QACbpD,KAAKmE,SACH,SACA,CAAEpD,MAAOwJ,EAAYnJ,QAASoJ,GAC9BnG,EAGL,EC9iBa,SAAA2I,EAGd7F,EAAgB8F,GAChB,MACe,iBAANA,GACD,OAANA,GACA9F,IAAW8F,EAAE9F,QACI,mBAAV8F,EAAElN,KACU,mBAAZkN,EAAEtC,OACa,mBAAfsC,EAAEhH,UACY,mBAAdgH,EAAElJ,SACQ,mBAAVkJ,EAAEvM,KACe,mBAAjBuM,EAAEnD,YACW,mBAAbmD,EAAEpD,QACW,iBAAboD,EAAE7J,QACQ,mBAAV6J,EAAExL,KACQ,mBAAVwL,EAAEhL,KACQ,mBAAVgL,EAAExG,KACQ,mBAAVwG,EAAEnM,KACO,mBAATmM,EAAErM,IACW,mBAAbqM,EAAE9M,QACe,mBAAjB8M,EAAE1K,YACW,mBAAb0K,EAAE1B,QACW,mBAAb0B,EAAE5M,QACe,mBAAjB4M,EAAEnE,UAEb,CC1BgB,SAAAoE,EAGd/F,EAAgB8F,GAChB,MACe,iBAANA,GACD,OAANA,GACA9F,IAAW8F,EAAE9F,QACQ,mBAAd8F,EAAElJ,SACQ,mBAAVkJ,EAAEvM,KACe,mBAAjBuM,EAAEnD,YACW,mBAAbmD,EAAEpD,QACW,iBAAboD,EAAE7J,QACQ,mBAAV6J,EAAExL,KACQ,mBAAVwL,EAAEnM,KACO,mBAATmM,EAAErM,IACW,mBAAbqM,EAAE1B,QACTyB,EAAc7F,EAAQ8F,EAAEnD,aAE5B"}