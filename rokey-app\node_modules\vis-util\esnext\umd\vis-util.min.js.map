{"version": 3, "file": "vis-util.min.js", "sources": ["../../src/deep-object-assign.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/validator.js", "../../src/shared/index.ts", "../../src/shared/popup.js", "../../src/random/alea.ts"], "sourcesContent": ["/**\n * Use this symbol to delete properies in deepObjectAssign.\n */\nexport const DELETE = Symbol(\"DELETE\");\n\n/**\n * Turns `undefined` into `undefined | typeof DELETE` and makes everything\n * partial. Intended to be used with `deepObjectAssign`.\n */\nexport type Assignable<T> = T extends undefined\n  ?\n      | (T extends Function\n          ? T\n          : T extends object\n          ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n          : T)\n      | typeof DELETE\n  : T extends Function\n  ? T | undefined\n  : T extends object\n  ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n  : T | undefined;\n\n/**\n * Pure version of deepObjectAssign, it doesn't modify any of it's arguments.\n *\n * @param base - The base object that fullfils the whole interface T.\n * @param updates - Updates that may change or delete props.\n * @returns A brand new instance with all the supplied objects deeply merged.\n */\nexport function pureDeepObjectAssign<T>(\n  base: T,\n  ...updates: Assignable<T>[]\n): T {\n  return deepObjectAssign({} as any, base, ...updates);\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param target - The object that will be augmented using the sources.\n * @param sources - Objects to be deeply merged into the target.\n * @returns The target (same instance).\n */\nexport function deepObjectAssign<T>(target: T, ...sources: Assignable<T>[]): T;\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nexport function deepObjectAssign(...values: readonly any[]): any {\n  const merged = deepObjectAssignNonentry(...values);\n  stripDelete(merged);\n  return merged;\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @remarks\n * This doesn't strip the DELETE symbols so they may end up in the final object.\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nfunction deepObjectAssignNonentry(...values: readonly any[]): any {\n  if (values.length < 2) {\n    return values[0];\n  } else if (values.length > 2) {\n    return deepObjectAssignNonentry(\n      deepObjectAssign(values[0], values[1]),\n      ...values.slice(2)\n    );\n  }\n\n  const a = values[0];\n  const b = values[1];\n\n  if (a instanceof Date && b instanceof Date) {\n    a.setTime(b.getTime());\n    return a;\n  }\n\n  for (const prop of Reflect.ownKeys(b)) {\n    if (!Object.prototype.propertyIsEnumerable.call(b, prop)) {\n      // Ignore nonenumerable props, Object.assign() would do the same.\n    } else if (b[prop] === DELETE) {\n      delete a[prop];\n    } else if (\n      a[prop] !== null &&\n      b[prop] !== null &&\n      typeof a[prop] === \"object\" &&\n      typeof b[prop] === \"object\" &&\n      !Array.isArray(a[prop]) &&\n      !Array.isArray(b[prop])\n    ) {\n      a[prop] = deepObjectAssignNonentry(a[prop], b[prop]);\n    } else {\n      a[prop] = clone(b[prop]);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep clone given object or array. In case of primitive simply return.\n *\n * @param a - Anything.\n * @returns Deep cloned object/array or unchanged a.\n */\nfunction clone(a: any): any {\n  if (Array.isArray(a)) {\n    return a.map((value: any): any => clone(value));\n  } else if (typeof a === \"object\" && a !== null) {\n    if (a instanceof Date) {\n      return new Date(a.getTime());\n    }\n    return deepObjectAssignNonentry({}, a);\n  } else {\n    return a;\n  }\n}\n\n/**\n * Strip DELETE from given object.\n *\n * @param a - Object which may contain DELETE but won't after this is executed.\n */\nfunction stripDelete(a: any): void {\n  for (const prop of Object.keys(a)) {\n    if (a[prop] === DELETE) {\n      delete a[prop];\n    } else if (typeof a[prop] === \"object\" && a[prop] !== null) {\n      stripDelete(a[prop]);\n    }\n  }\n}\n", "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n *\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n *\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n *\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n *\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", "// utility functions\n\n// parse ASP.Net Date pattern,\n// for example '/Date(1198908717056)/' or '/Date(1198908717056-0700)/'\n// code from http://momentjs.com/\nconst ASPDateRegex = /^\\/?Date\\((-?\\d+)/i;\n\n// Color REs\nconst fullHexRE = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\nconst shortHexRE = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\nconst rgbRE =\n  /^rgb\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *\\)$/i;\nconst rgbaRE =\n  /^rgba\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *([01]|0?\\.\\d+) *\\)$/i;\n\n/**\n * Hue, Saturation, Value.\n */\nexport interface HSV {\n  /**\n   * Hue \\<0, 1\\>.\n   */\n  h: number;\n  /**\n   * Saturation \\<0, 1\\>.\n   */\n  s: number;\n  /**\n   * Value \\<0, 1\\>.\n   */\n  v: number;\n}\n\n/**\n * Red, Green, Blue.\n */\nexport interface RGB {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n}\n\n/**\n * Red, Green, Blue, Alpha.\n */\nexport interface RGBA {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n  /**\n   * Alpha \\<0, 1\\>.\n   */\n  a: number;\n}\n\n/**\n * Test whether given object is a number.\n *\n * @param value - Input value of unknown type.\n * @returns True if number, false otherwise.\n */\nexport function isNumber(value: unknown): value is number {\n  return value instanceof Number || typeof value === \"number\";\n}\n\n/**\n * Remove everything in the DOM object.\n *\n * @param DOMobject - Node whose child nodes will be recursively deleted.\n */\nexport function recursiveDOMDelete(DOMobject: Node | null | undefined): void {\n  if (DOMobject) {\n    while (DOMobject.hasChildNodes() === true) {\n      const child = DOMobject.firstChild;\n      if (child) {\n        recursiveDOMDelete(child);\n        DOMobject.removeChild(child);\n      }\n    }\n  }\n}\n\n/**\n * Test whether given object is a string.\n *\n * @param value - Input value of unknown type.\n * @returns True if string, false otherwise.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === \"string\";\n}\n\n/**\n * Test whether given object is a object (not primitive or null).\n *\n * @param value - Input value of unknown type.\n * @returns True if not null object, false otherwise.\n */\nexport function isObject(value: unknown): value is object {\n  return typeof value === \"object\" && value !== null;\n}\n\n/**\n * Test whether given object is a Date, or a String containing a Date.\n *\n * @param value - Input value of unknown type.\n * @returns True if Date instance or string date representation, false otherwise.\n */\nexport function isDate(value: unknown): value is Date | string {\n  if (value instanceof Date) {\n    return true;\n  } else if (isString(value)) {\n    // test whether this string contains a date\n    const match = ASPDateRegex.exec(value);\n    if (match) {\n      return true;\n    } else if (!isNaN(Date.parse(value))) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Copy property from b to a if property present in a.\n * If property in b explicitly set to null, delete it if `allowDeletion` set.\n *\n * Internal helper routine, should not be exported. Not added to `exports` for that reason.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param prop - Name of property to copy from b to a.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n */\nfunction copyOrDelete(\n  a: any,\n  b: any,\n  prop: string,\n  allowDeletion: boolean\n): void {\n  let doDeletion = false;\n  if (allowDeletion === true) {\n    doDeletion = b[prop] === null && a[prop] !== undefined;\n  }\n\n  if (doDeletion) {\n    delete a[prop];\n  } else {\n    a[prop] = b[prop]; // Remember, this is a reference copy!\n  }\n}\n\n/**\n * Fill an object with a possibly partially defined other object.\n *\n * Only copies values for the properties already present in a.\n * That means an object is not created on a property if only the b object has it.\n *\n * @param a - The object that will have it's properties updated.\n * @param b - The object with property updates.\n * @param allowDeletion - If true, delete properties in a that are explicitly set to null in b.\n */\nexport function fillIfDefined<T extends object>(\n  a: T,\n  b: Partial<T>,\n  allowDeletion = false\n): void {\n  // NOTE: iteration of properties of a\n  // NOTE: prototype properties iterated over as well\n  for (const prop in a) {\n    if (b[prop] !== undefined) {\n      if (b[prop] === null || typeof b[prop] !== \"object\") {\n        // Note: typeof null === 'object'\n        copyOrDelete(a, b, prop, allowDeletion);\n      } else {\n        const aProp = a[prop];\n        const bProp = b[prop];\n        if (isObject(aProp) && isObject(bProp)) {\n          fillIfDefined(aProp, bProp, allowDeletion);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Copy the values of all of the enumerable own properties from one or more source objects to a\n * target object. Returns the target object.\n *\n * @param target - The target object to copy to.\n * @param source - The source object from which to copy properties.\n * @returns The target object.\n */\nexport const extend = Object.assign;\n\n/**\n * Extend object a with selected properties of object b or a series of objects.\n *\n * @remarks\n * Only properties with defined values are copied.\n * @param props - Properties to be copied to a.\n * @param a - The target.\n * @param others - The sources.\n * @returns Argument a.\n */\nexport function selectiveExtend(\n  props: string[],\n  a: any,\n  ...others: any[]\n): any {\n  if (!Array.isArray(props)) {\n    throw new Error(\"Array with property names expected as first argument\");\n  }\n\n  for (const other of others) {\n    for (let p = 0; p < props.length; p++) {\n      const prop = props[p];\n      if (other && Object.prototype.hasOwnProperty.call(other, prop)) {\n        a[prop] = other[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object a with selected properties of object b.\n * Only properties with defined values are copied.\n *\n * @remarks\n * Previous version of this routine implied that multiple source objects could\n * be used; however, the implementation was **wrong**. Since multiple (\\>1)\n * sources weren't used anywhere in the `vis.js` code, this has been removed\n * @param props - Names of first-level properties to copy over.\n * @param a - Target object.\n * @param b - Source object.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n * @returns Argument a.\n */\nexport function selectiveDeepExtend(\n  props: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (let p = 0; p < props.length; p++) {\n    const prop = props[p];\n    if (Object.prototype.hasOwnProperty.call(b, prop)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop], false, allowDeletion);\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError(\"Arrays are not supported by deepExtend\");\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object `a` with properties of object `b`, ignoring properties which\n * are explicitly specified to be excluded.\n *\n * @remarks\n * The properties of `b` are considered for copying. Properties which are\n * themselves objects are are also extended. Only properties with defined\n * values are copied.\n * @param propsToExclude - Names of properties which should *not* be copied.\n * @param a - Object to extend.\n * @param b - Object to take properties from for extension.\n * @param allowDeletion - If true, delete properties in a that are explicitly\n * set to null in b.\n * @returns Argument a.\n */\nexport function selectiveNotDeepExtend(\n  propsToExclude: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  // NOTE: array properties have an else-below; apparently, there is a problem here.\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (const prop in b) {\n    if (!Object.prototype.hasOwnProperty.call(b, prop)) {\n      continue;\n    } // Handle local properties only\n    if (propsToExclude.includes(prop)) {\n      continue;\n    } // In exclusion list, skip\n\n    if (b[prop] && b[prop].constructor === Object) {\n      if (a[prop] === undefined) {\n        a[prop] = {};\n      }\n      if (a[prop].constructor === Object) {\n        deepExtend(a[prop], b[prop]); // NOTE: allowDeletion not propagated!\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    } else if (Array.isArray(b[prop])) {\n      a[prop] = [];\n      for (let i = 0; i < b[prop].length; i++) {\n        a[prop].push(b[prop][i]);\n      }\n    } else {\n      copyOrDelete(a, b, prop, allowDeletion);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param protoExtend - If true, the prototype values will also be extended.\n * (That is the options objects that inherit from others will also get the\n * inherited options).\n * @param allowDeletion - If true, the values of fields that are null will be deleted.\n * @returns Argument a.\n */\nexport function deepExtend(\n  a: any,\n  b: any,\n  protoExtend = false,\n  allowDeletion = false\n): any {\n  for (const prop in b) {\n    if (Object.prototype.hasOwnProperty.call(b, prop) || protoExtend === true) {\n      if (\n        typeof b[prop] === \"object\" &&\n        b[prop] !== null &&\n        Object.getPrototypeOf(b[prop]) === Object.prototype\n      ) {\n        if (a[prop] === undefined) {\n          a[prop] = deepExtend({}, b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else if (\n          typeof a[prop] === \"object\" &&\n          a[prop] !== null &&\n          Object.getPrototypeOf(a[prop]) === Object.prototype\n        ) {\n          deepExtend(a[prop], b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        a[prop] = b[prop].slice();\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Test whether all elements in two arrays are equal.\n *\n * @param a - First array.\n * @param b - Second array.\n * @returns True if both arrays have the same length and same elements (1 = '1').\n */\nexport function equalArray(a: unknown[], b: unknown[]): boolean {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0, len = a.length; i < len; i++) {\n    if (a[i] != b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Get the type of an object, for example exports.getType([]) returns 'Array'.\n *\n * @param object - Input value of unknown type.\n * @returns Detected type.\n */\nexport function getType(object: unknown): string {\n  const type = typeof object;\n\n  if (type === \"object\") {\n    if (object === null) {\n      return \"null\";\n    }\n    if (object instanceof Boolean) {\n      return \"Boolean\";\n    }\n    if (object instanceof Number) {\n      return \"Number\";\n    }\n    if (object instanceof String) {\n      return \"String\";\n    }\n    if (Array.isArray(object)) {\n      return \"Array\";\n    }\n    if (object instanceof Date) {\n      return \"Date\";\n    }\n\n    return \"Object\";\n  }\n  if (type === \"number\") {\n    return \"Number\";\n  }\n  if (type === \"boolean\") {\n    return \"Boolean\";\n  }\n  if (type === \"string\") {\n    return \"String\";\n  }\n  if (type === undefined) {\n    return \"undefined\";\n  }\n\n  return type;\n}\n\nexport function copyAndExtendArray<T>(arr: ReadonlyArray<T>, newValue: T): T[];\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[];\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - First part.\n * @param newValue - The value to be aadded into the array.\n * @returns A new array with all items from arr and newValue (which is last).\n */\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[] {\n  return [...arr, newValue];\n}\n\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - The array to be copied.\n * @returns Shallow copy of arr.\n */\nexport function copyArray<T>(arr: ReadonlyArray<T>): T[] {\n  return arr.slice();\n}\n\n/**\n * Retrieve the absolute left value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute left position of this element in the browser page.\n */\nexport function getAbsoluteLeft(elem: Element): number {\n  return elem.getBoundingClientRect().left;\n}\n\n/**\n * Retrieve the absolute right value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute right position of this element in the browser page.\n */\nexport function getAbsoluteRight(elem: Element): number {\n  return elem.getBoundingClientRect().right;\n}\n\n/**\n * Retrieve the absolute top value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute top position of this element in the browser page.\n */\nexport function getAbsoluteTop(elem: Element): number {\n  return elem.getBoundingClientRect().top;\n}\n\n/**\n * Add a className to the given elements style.\n *\n * @param elem - The element to which the classes will be added.\n * @param classNames - Space separated list of classes.\n */\nexport function addClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const newClasses = classNames.split(\" \");\n  classes = classes.concat(\n    newClasses.filter(function (className): boolean {\n      return !classes.includes(className);\n    })\n  );\n  elem.className = classes.join(\" \");\n}\n\n/**\n * Remove a className from the given elements style.\n *\n * @param elem - The element from which the classes will be removed.\n * @param classNames - Space separated list of classes.\n */\nexport function removeClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const oldClasses = classNames.split(\" \");\n  classes = classes.filter(function (className): boolean {\n    return !oldClasses.includes(className);\n  });\n  elem.className = classes.join(\" \");\n}\n\nexport function forEach<V>(\n  array: undefined | null | V[],\n  callback: (value: V, index: number, object: V[]) => void\n): void;\nexport function forEach<O extends object>(\n  object: undefined | null | O,\n  callback: <Key extends keyof O>(value: O[Key], key: Key, object: O) => void\n): void;\n/**\n * For each method for both arrays and objects.\n * In case of an array, the built-in Array.forEach() is applied (**No, it's not!**).\n * In case of an Object, the method loops over all properties of the object.\n *\n * @param object - An Object or Array to be iterated over.\n * @param callback - Array.forEach-like callback.\n */\nexport function forEach(object: any, callback: any): void {\n  if (Array.isArray(object)) {\n    // array\n    const len = object.length;\n    for (let i = 0; i < len; i++) {\n      callback(object[i], i, object);\n    }\n  } else {\n    // object\n    for (const key in object) {\n      if (Object.prototype.hasOwnProperty.call(object, key)) {\n        callback(object[key], key, object);\n      }\n    }\n  }\n}\n\n/**\n * Convert an object into an array: all objects properties are put into the array. The resulting array is unordered.\n *\n * @param o - Object that contains the properties and methods.\n * @returns An array of unordered values.\n */\nexport const toArray = Object.values;\n\n/**\n * Update a property in an object.\n *\n * @param object - The object whose property will be updated.\n * @param key - Name of the property to be updated.\n * @param value - The new value to be assigned.\n * @returns Whether the value was updated (true) or already strictly the same in the original object (false).\n */\nexport function updateProperty<K extends string, V>(\n  object: Record<K, V>,\n  key: K,\n  value: V\n): boolean {\n  if (object[key] !== value) {\n    object[key] = value;\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Throttle the given function to be only executed once per animation frame.\n *\n * @param fn - The original function.\n * @returns The throttled function.\n */\nexport function throttle(fn: () => void): () => void {\n  let scheduled = false;\n\n  return (): void => {\n    if (!scheduled) {\n      scheduled = true;\n      requestAnimationFrame((): void => {\n        scheduled = false;\n        fn();\n      });\n    }\n  };\n}\n\n/**\n * Cancels the event's default action if it is cancelable, without stopping further propagation of the event.\n *\n * @param event - The event whose default action should be prevented.\n */\nexport function preventDefault(event: Event | undefined): void {\n  if (!event) {\n    event = window.event;\n  }\n\n  if (!event) {\n    // No event, no work.\n  } else if (event.preventDefault) {\n    event.preventDefault(); // non-IE browsers\n  } else {\n    // @TODO: IE types? Does anyone care?\n    (event as any).returnValue = false; // IE browsers\n  }\n}\n\n/**\n * Get HTML element which is the target of the event.\n *\n * @param event - The event.\n * @returns The element or null if not obtainable.\n */\nexport function getTarget(\n  event: Event | undefined = window.event\n): Element | null {\n  // code from http://www.quirksmode.org/js/events_properties.html\n  // @TODO: EventTarget can be almost anything, is it okay to return only Elements?\n\n  let target: null | EventTarget = null;\n  if (!event) {\n    // No event, no target.\n  } else if (event.target) {\n    target = event.target;\n  } else if (event.srcElement) {\n    target = event.srcElement;\n  }\n\n  if (!(target instanceof Element)) {\n    return null;\n  }\n\n  if (target.nodeType != null && target.nodeType == 3) {\n    // defeat Safari bug\n    target = target.parentNode;\n    if (!(target instanceof Element)) {\n      return null;\n    }\n  }\n\n  return target;\n}\n\n/**\n * Check if given element contains given parent somewhere in the DOM tree.\n *\n * @param element - The element to be tested.\n * @param parent - The ancestor (not necessarily parent) of the element.\n * @returns True if parent is an ancestor of the element, false otherwise.\n */\nexport function hasParent(element: Element, parent: Element): boolean {\n  let elem: Node = element;\n\n  while (elem) {\n    if (elem === parent) {\n      return true;\n    } else if (elem.parentNode) {\n      elem = elem.parentNode;\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n\nexport const option = {\n  /**\n   * Convert a value into a boolean.\n   *\n   * @param value - Value to be converted intoboolean, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding boolean value, if none then the default value, if none then null.\n   */\n  asBoolean(value: unknown, defaultValue?: boolean): boolean | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return value != false;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a number.\n   *\n   * @param value - Value to be converted intonumber, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** number value, if none then the default value, if none then null.\n   */\n  asNumber(value: unknown, defaultValue?: number): number | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return Number(value) || defaultValue || null;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a string.\n   *\n   * @param value - Value to be converted intostring, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** string value, if none then the default value, if none then null.\n   */\n  asString(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return String(value);\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a size.\n   *\n   * @param value - Value to be converted intosize, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding string value (number + 'px'), if none then the default value, if none then null.\n   */\n  asSize(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (isString(value)) {\n      return value;\n    } else if (isNumber(value)) {\n      return value + \"px\";\n    } else {\n      return defaultValue || null;\n    }\n  },\n\n  /**\n   * Convert a value into a DOM Element.\n   *\n   * @param value - Value to be converted into DOM Element, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns The DOM Element, if none then the default value, if none then null.\n   */\n  asElement<T extends Node>(\n    value: T | (() => T | undefined) | undefined,\n    defaultValue: T\n  ): T | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    return value || defaultValue || null;\n  },\n};\n\n/**\n * Convert hex color string into RGB color object.\n *\n * @remarks\n * {@link http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb}\n * @param hex - Hex color string (3 or 6 digits, with or without #).\n * @returns RGB color object.\n */\nexport function hexToRGB(hex: string): RGB | null {\n  let result;\n  switch (hex.length) {\n    case 3:\n    case 4:\n      result = shortHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1] + result[1], 16),\n            g: parseInt(result[2] + result[2], 16),\n            b: parseInt(result[3] + result[3], 16),\n          }\n        : null;\n    case 6:\n    case 7:\n      result = fullHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16),\n          }\n        : null;\n    default:\n      return null;\n  }\n}\n\n/**\n * This function takes string color in hex or RGB format and adds the opacity, RGBA is passed through unchanged.\n *\n * @param color - The color string (hex, RGB, RGBA).\n * @param opacity - The new opacity.\n * @returns RGBA string, for example 'rgba(255, 0, 127, 0.3)'.\n */\nexport function overrideOpacity(color: string, opacity: number): string {\n  if (color.includes(\"rgba\")) {\n    return color;\n  } else if (color.includes(\"rgb\")) {\n    const rgb = color\n      .substr(color.indexOf(\"(\") + 1)\n      .replace(\")\", \"\")\n      .split(\",\");\n    return \"rgba(\" + rgb[0] + \",\" + rgb[1] + \",\" + rgb[2] + \",\" + opacity + \")\";\n  } else {\n    const rgb = hexToRGB(color);\n    if (rgb == null) {\n      return color;\n    } else {\n      return \"rgba(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \",\" + opacity + \")\";\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into hex color string.\n *\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns Hex color string (for example: '#0acdc0').\n */\nexport function RGBToHex(red: number, green: number, blue: number): string {\n  return (\n    \"#\" + ((1 << 24) + (red << 16) + (green << 8) + blue).toString(16).slice(1)\n  );\n}\n\nexport interface ColorObject {\n  background?: string;\n  border?: string;\n  hover?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n  highlight?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n}\nexport interface FullColorObject {\n  background: string;\n  border: string;\n  hover: {\n    border: string;\n    background: string;\n  };\n  highlight: {\n    border: string;\n    background: string;\n  };\n}\n\nexport function parseColor(inputColor: string): FullColorObject;\nexport function parseColor(inputColor: FullColorObject): FullColorObject;\nexport function parseColor(inputColor: ColorObject): ColorObject;\nexport function parseColor(\n  inputColor: ColorObject,\n  defaultColor: FullColorObject\n): FullColorObject;\n/**\n * Parse a color property into an object with border, background, and highlight colors.\n *\n * @param inputColor - Shorthand color string or input color object.\n * @param defaultColor - Full color object to fill in missing values in inputColor.\n * @returns Color object.\n */\nexport function parseColor(\n  inputColor: ColorObject | string,\n  defaultColor?: FullColorObject\n): ColorObject | FullColorObject {\n  if (isString(inputColor)) {\n    let colorStr: string = inputColor;\n    if (isValidRGB(colorStr)) {\n      const rgb = colorStr\n        .substr(4)\n        .substr(0, colorStr.length - 5)\n        .split(\",\")\n        .map(function (value): number {\n          return parseInt(value);\n        });\n      colorStr = RGBToHex(rgb[0], rgb[1], rgb[2]);\n    }\n    if (isValidHex(colorStr) === true) {\n      const hsv = hexToHSV(colorStr);\n      const lighterColorHSV = {\n        h: hsv.h,\n        s: hsv.s * 0.8,\n        v: Math.min(1, hsv.v * 1.02),\n      };\n      const darkerColorHSV = {\n        h: hsv.h,\n        s: Math.min(1, hsv.s * 1.25),\n        v: hsv.v * 0.8,\n      };\n      const darkerColorHex = HSVToHex(\n        darkerColorHSV.h,\n        darkerColorHSV.s,\n        darkerColorHSV.v\n      );\n      const lighterColorHex = HSVToHex(\n        lighterColorHSV.h,\n        lighterColorHSV.s,\n        lighterColorHSV.v\n      );\n      return {\n        background: colorStr,\n        border: darkerColorHex,\n        highlight: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n        hover: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n      };\n    } else {\n      return {\n        background: colorStr,\n        border: colorStr,\n        highlight: {\n          background: colorStr,\n          border: colorStr,\n        },\n        hover: {\n          background: colorStr,\n          border: colorStr,\n        },\n      };\n    }\n  } else {\n    if (defaultColor) {\n      const color: FullColorObject = {\n        background: inputColor.background || defaultColor.background,\n        border: inputColor.border || defaultColor.border,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                defaultColor.highlight.background,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                defaultColor.highlight.border,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) ||\n                defaultColor.hover.border,\n              background:\n                (inputColor.hover && inputColor.hover.background) ||\n                defaultColor.hover.background,\n            },\n      };\n      return color;\n    } else {\n      const color: ColorObject = {\n        background: inputColor.background || undefined,\n        border: inputColor.border || undefined,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                undefined,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                undefined,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) || undefined,\n              background:\n                (inputColor.hover && inputColor.hover.background) || undefined,\n            },\n      };\n      return color;\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into HSV object.\n *\n * @remarks\n * {@link http://www.javascripter.net/faq/rgb2hsv.htm}\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns HSV color object.\n */\nexport function RGBToHSV(red: number, green: number, blue: number): HSV {\n  red = red / 255;\n  green = green / 255;\n  blue = blue / 255;\n  const minRGB = Math.min(red, Math.min(green, blue));\n  const maxRGB = Math.max(red, Math.max(green, blue));\n\n  // Black-gray-white\n  if (minRGB === maxRGB) {\n    return { h: 0, s: 0, v: minRGB };\n  }\n\n  // Colors other than black-gray-white:\n  const d =\n    red === minRGB ? green - blue : blue === minRGB ? red - green : blue - red;\n  const h = red === minRGB ? 3 : blue === minRGB ? 1 : 5;\n  const hue = (60 * (h - d / (maxRGB - minRGB))) / 360;\n  const saturation = (maxRGB - minRGB) / maxRGB;\n  const value = maxRGB;\n  return { h: hue, s: saturation, v: value };\n}\n\ninterface CSSStyles {\n  [key: string]: string;\n}\n\n/**\n * Split a string with css styles into an object with key/values.\n *\n * @param cssText - CSS source code to split into key/value object.\n * @returns Key/value object corresponding to {@link cssText}.\n */\nfunction splitCSSText(cssText: string): CSSStyles {\n  const tmpEllement = document.createElement(\"div\");\n\n  const styles: CSSStyles = {};\n\n  tmpEllement.style.cssText = cssText;\n\n  for (let i = 0; i < tmpEllement.style.length; ++i) {\n    styles[tmpEllement.style[i]] = tmpEllement.style.getPropertyValue(\n      tmpEllement.style[i]\n    );\n  }\n\n  return styles;\n}\n\n/**\n * Append a string with css styles to an element.\n *\n * @param element - The element that will receive new styles.\n * @param cssText - The styles to be appended.\n */\nexport function addCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const [key, value] of Object.entries(cssStyle)) {\n    element.style.setProperty(key, value);\n  }\n}\n\n/**\n * Remove a string with css styles from an element.\n *\n * @param element - The element from which styles should be removed.\n * @param cssText - The styles to be removed.\n */\nexport function removeCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const key of Object.keys(cssStyle)) {\n    element.style.removeProperty(key);\n  }\n}\n\n/**\n * Convert HSV \\<0, 1\\> into RGB color object.\n *\n * @remarks\n * {@link https://gist.github.com/mjijackson/5311256}\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns RGB color object.\n */\nexport function HSVToRGB(h: number, s: number, v: number): RGB {\n  let r: undefined | number;\n  let g: undefined | number;\n  let b: undefined | number;\n\n  const i = Math.floor(h * 6);\n  const f = h * 6 - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n\n  switch (i % 6) {\n    case 0:\n      (r = v), (g = t), (b = p);\n      break;\n    case 1:\n      (r = q), (g = v), (b = p);\n      break;\n    case 2:\n      (r = p), (g = v), (b = t);\n      break;\n    case 3:\n      (r = p), (g = q), (b = v);\n      break;\n    case 4:\n      (r = t), (g = p), (b = v);\n      break;\n    case 5:\n      (r = v), (g = p), (b = q);\n      break;\n  }\n\n  return {\n    r: Math.floor((r as number) * 255),\n    g: Math.floor((g as number) * 255),\n    b: Math.floor((b as number) * 255),\n  };\n}\n\n/**\n * Convert HSV \\<0, 1\\> into hex color string.\n *\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns Hex color string.\n */\nexport function HSVToHex(h: number, s: number, v: number): string {\n  const rgb = HSVToRGB(h, s, v);\n  return RGBToHex(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Convert hex color string into HSV \\<0, 1\\>.\n *\n * @param hex - Hex color string.\n * @returns HSV color object.\n */\nexport function hexToHSV(hex: string): HSV {\n  const rgb = hexToRGB(hex);\n  if (!rgb) {\n    throw new TypeError(`'${hex}' is not a valid color.`);\n  }\n  return RGBToHSV(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Validate hex color string.\n *\n * @param hex - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidHex(hex: string): boolean {\n  const isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(hex);\n  return isOk;\n}\n\n/**\n * Validate RGB color string.\n *\n * @param rgb - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGB(rgb: string): boolean {\n  return rgbRE.test(rgb);\n}\n\n/**\n * Validate RGBA color string.\n *\n * @param rgba - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGBA(rgba: string): boolean {\n  return rgbaRE.test(rgba);\n}\n\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param fields - Names of properties to be bridged.\n * @param referenceObject - The original object.\n * @returns A new object inheriting from the referenceObject.\n */\nexport function selectiveBridgeObject<F extends string, V>(\n  fields: F[],\n  referenceObject: Record<F, V>\n): Record<F, V> | null {\n  if (referenceObject !== null && typeof referenceObject === \"object\") {\n    // !!! typeof null === 'object'\n    const objectTo = Object.create(referenceObject);\n    for (let i = 0; i < fields.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(referenceObject, fields[i])) {\n        if (typeof referenceObject[fields[i]] == \"object\") {\n          objectTo[fields[i]] = bridgeObject(referenceObject[fields[i]]);\n        }\n      }\n    }\n    return objectTo;\n  } else {\n    return null;\n  }\n}\n\nexport function bridgeObject<T extends object>(referenceObject: T): T;\nexport function bridgeObject<T>(referenceObject: T): null;\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param referenceObject - The original object.\n * @returns The Element if the referenceObject is an Element, or a new object inheriting from the referenceObject.\n */\nexport function bridgeObject<T extends object | null>(\n  referenceObject: T\n): T | null {\n  if (referenceObject === null || typeof referenceObject !== \"object\") {\n    return null;\n  }\n\n  if (referenceObject instanceof Element) {\n    // Avoid bridging DOM objects\n    return referenceObject;\n  }\n\n  const objectTo = Object.create(referenceObject);\n  for (const i in referenceObject) {\n    if (Object.prototype.hasOwnProperty.call(referenceObject, i)) {\n      if (typeof (referenceObject as any)[i] == \"object\") {\n        objectTo[i] = bridgeObject((referenceObject as any)[i]);\n      }\n    }\n  }\n\n  return objectTo;\n}\n\n/**\n * This method provides a stable sort implementation, very fast for presorted data.\n *\n * @param a - The array to be sorted (in-place).\n * @param compare - An order comparator.\n * @returns The argument a.\n */\nexport function insertSort<T>(a: T[], compare: (a: T, b: T) => number): T[] {\n  for (let i = 0; i < a.length; i++) {\n    const k = a[i];\n    let j;\n    for (j = i; j > 0 && compare(k, a[j - 1]) < 0; j--) {\n      a[j] = a[j - 1];\n    }\n    a[j] = k;\n  }\n  return a;\n}\n\n/**\n * This is used to set the options of subobjects in the options object.\n *\n * A requirement of these subobjects is that they have an 'enabled' element\n * which is optional for the user but mandatory for the program.\n *\n * The added value here of the merge is that option 'enabled' is set as required.\n *\n * @param mergeTarget - Either this.options or the options used for the groups.\n * @param options - Options.\n * @param option - Option key in the options argument.\n * @param globalOptions - Global options, passed in to determine value of option 'enabled'.\n */\nexport function mergeOptions(\n  mergeTarget: any,\n  options: any,\n  option: string,\n  globalOptions: any = {}\n): void {\n  // Local helpers\n  const isPresent = function (obj: any): boolean {\n    return obj !== null && obj !== undefined;\n  };\n\n  const isObject = function (obj: unknown): boolean {\n    return obj !== null && typeof obj === \"object\";\n  };\n\n  // https://stackoverflow.com/a/34491287/1223531\n  const isEmpty = function (obj: object): obj is {} {\n    for (const x in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, x)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  // Guards\n  if (!isObject(mergeTarget)) {\n    throw new Error(\"Parameter mergeTarget must be an object\");\n  }\n\n  if (!isObject(options)) {\n    throw new Error(\"Parameter options must be an object\");\n  }\n\n  if (!isPresent(option)) {\n    throw new Error(\"Parameter option must have a value\");\n  }\n\n  if (!isObject(globalOptions)) {\n    throw new Error(\"Parameter globalOptions must be an object\");\n  }\n\n  //\n  // Actual merge routine, separated from main logic\n  // Only a single level of options is merged. Deeper levels are ref'd. This may actually be an issue.\n  //\n  const doMerge = function (target: any, options: any, option: string): void {\n    if (!isObject(target[option])) {\n      target[option] = {};\n    }\n\n    const src = options[option];\n    const dst = target[option];\n    for (const prop in src) {\n      if (Object.prototype.hasOwnProperty.call(src, prop)) {\n        dst[prop] = src[prop];\n      }\n    }\n  };\n\n  // Local initialization\n  const srcOption = options[option];\n  const globalPassed = isObject(globalOptions) && !isEmpty(globalOptions);\n  const globalOption = globalPassed ? globalOptions[option] : undefined;\n  const globalEnabled = globalOption ? globalOption.enabled : undefined;\n\n  /////////////////////////////////////////\n  // Main routine\n  /////////////////////////////////////////\n  if (srcOption === undefined) {\n    return; // Nothing to do\n  }\n\n  if (typeof srcOption === \"boolean\") {\n    if (!isObject(mergeTarget[option])) {\n      mergeTarget[option] = {};\n    }\n\n    mergeTarget[option].enabled = srcOption;\n    return;\n  }\n\n  if (srcOption === null && !isObject(mergeTarget[option])) {\n    // If possible, explicit copy from globals\n    if (isPresent(globalOption)) {\n      mergeTarget[option] = Object.create(globalOption);\n    } else {\n      return; // Nothing to do\n    }\n  }\n\n  if (!isObject(srcOption)) {\n    return;\n  }\n\n  //\n  // Ensure that 'enabled' is properly set. It is required internally\n  // Note that the value from options will always overwrite the existing value\n  //\n  let enabled = true; // default value\n\n  if (srcOption.enabled !== undefined) {\n    enabled = srcOption.enabled;\n  } else {\n    // Take from globals, if present\n    if (globalEnabled !== undefined) {\n      enabled = globalOption.enabled;\n    }\n  }\n\n  doMerge(mergeTarget, options, option);\n  mergeTarget[option].enabled = enabled;\n}\n\nexport function binarySearchCustom<\n  O extends object,\n  K1 extends keyof O,\n  K2 extends keyof O[K1]\n>(\n  orderedItems: O[],\n  comparator: (v: O[K1][K2]) => -1 | 0 | 1,\n  field: K1,\n  field2: K2\n): number;\nexport function binarySearchCustom<O extends object, K1 extends keyof O>(\n  orderedItems: O[],\n  comparator: (v: O[K1]) => -1 | 0 | 1,\n  field: K1\n): number;\n/**\n * This function does a binary search for a visible item in a sorted list. If we find a visible item, the code that uses\n * this function will then iterate in both directions over this sorted list to find all visible items.\n *\n * @param orderedItems - Items ordered by start.\n * @param comparator - -1 is lower, 0 is equal, 1 is higher.\n * @param field - Property name on an item (That is item[field]).\n * @param field2 - Second property name on an item (That is item[field][field2]).\n * @returns Index of the found item or -1 if nothing was found.\n */\nexport function binarySearchCustom(\n  orderedItems: any[],\n  comparator: (v: unknown) => -1 | 0 | 1,\n  field: string,\n  field2?: string\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n\n  while (low <= high && iteration < maxIterations) {\n    const middle = Math.floor((low + high) / 2);\n\n    const item = orderedItems[middle];\n    const value = field2 === undefined ? item[field] : item[field][field2];\n\n    const searchResult = comparator(value);\n    if (searchResult == 0) {\n      // jihaa, found a visible item!\n      return middle;\n    } else if (searchResult == -1) {\n      // it is too small --> increase low\n      low = middle + 1;\n    } else {\n      // it is too big --> decrease high\n      high = middle - 1;\n    }\n\n    iteration++;\n  }\n\n  return -1;\n}\n\n/**\n * This function does a binary search for a specific value in a sorted array.\n * If it does not exist but is in between of two values, we return either the\n * one before or the one after, depending on user input If it is found, we\n * return the index, else -1.\n *\n * @param orderedItems - Sorted array.\n * @param target - The searched value.\n * @param field - Name of the property in items to be searched.\n * @param sidePreference - If the target is between two values, should the index of the before or the after be returned?\n * @param comparator - An optional comparator, returning -1, 0, 1 for \\<, ===, \\>.\n * @returns The index of found value or -1 if nothing was found.\n */\nexport function binarySearchValue<T extends string>(\n  orderedItems: { [K in T]: number }[],\n  target: number,\n  field: T,\n  sidePreference: \"before\" | \"after\",\n  comparator?: (a: number, b: number) => -1 | 0 | 1\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n  let prevValue;\n  let value;\n  let nextValue;\n  let middle;\n\n  comparator =\n    comparator != undefined\n      ? comparator\n      : function (a: number, b: number): -1 | 0 | 1 {\n          return a == b ? 0 : a < b ? -1 : 1;\n        };\n\n  while (low <= high && iteration < maxIterations) {\n    // get a new guess\n    middle = Math.floor(0.5 * (high + low));\n    prevValue = orderedItems[Math.max(0, middle - 1)][field];\n    value = orderedItems[middle][field];\n    nextValue =\n      orderedItems[Math.min(orderedItems.length - 1, middle + 1)][field];\n\n    if (comparator(value, target) == 0) {\n      // we found the target\n      return middle;\n    } else if (\n      comparator(prevValue, target) < 0 &&\n      comparator(value, target) > 0\n    ) {\n      // target is in between of the previous and the current\n      return sidePreference == \"before\" ? Math.max(0, middle - 1) : middle;\n    } else if (\n      comparator(value, target) < 0 &&\n      comparator(nextValue, target) > 0\n    ) {\n      // target is in between of the current and the next\n      return sidePreference == \"before\"\n        ? middle\n        : Math.min(orderedItems.length - 1, middle + 1);\n    } else {\n      // didnt find the target, we need to change our boundaries.\n      if (comparator(value, target) < 0) {\n        // it is too small --> increase low\n        low = middle + 1;\n      } else {\n        // it is too big --> decrease high\n        high = middle - 1;\n      }\n    }\n    iteration++;\n  }\n\n  // didnt find anything. Return -1.\n  return -1;\n}\n\n/*\n * Easing Functions.\n * Only considering the t value for the range [0, 1] => [0, 1].\n *\n * Inspiration: from http://gizma.com/easing/\n * https://gist.github.com/gre/1650294\n */\nexport const easingFunctions = {\n  /**\n   * Provides no easing and no acceleration.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  linear(t: number): number {\n    return t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuad(t: number): number {\n    return t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuad(t: number): number {\n    return t * (2 - t);\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuad(t: number): number {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInCubic(t: number): number {\n    return t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutCubic(t: number): number {\n    return --t * t * t + 1;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutCubic(t: number): number {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuart(t: number): number {\n    return t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuart(t: number): number {\n    return 1 - --t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuart(t: number): number {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuint(t: number): number {\n    return t * t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuint(t: number): number {\n    return 1 + --t * t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuint(t: number): number {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n  },\n};\n\n/**\n * Experimentaly compute the width of the scrollbar for this browser.\n *\n * @returns The width in pixels.\n */\nexport function getScrollBarWidth(): number {\n  const inner = document.createElement(\"p\");\n  inner.style.width = \"100%\";\n  inner.style.height = \"200px\";\n\n  const outer = document.createElement(\"div\");\n  outer.style.position = \"absolute\";\n  outer.style.top = \"0px\";\n  outer.style.left = \"0px\";\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"200px\";\n  outer.style.height = \"150px\";\n  outer.style.overflow = \"hidden\";\n  outer.appendChild(inner);\n\n  document.body.appendChild(outer);\n  const w1 = inner.offsetWidth;\n  outer.style.overflow = \"scroll\";\n  let w2 = inner.offsetWidth;\n  if (w1 == w2) {\n    w2 = outer.clientWidth;\n  }\n\n  document.body.removeChild(outer);\n\n  return w1 - w2;\n}\n\n// @TODO: This doesn't work properly.\n// It works only for single property objects,\n// otherwise it combines all of the types in a union.\n// export function topMost<K1 extends string, V1> (\n//   pile: Record<K1, undefined | V1>[],\n//   accessors: K1 | [K1]\n// ): undefined | V1\n// export function topMost<K1 extends string, K2 extends string, V1, V2> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2>>[],\n//   accessors: [K1, K2]\n// ): undefined | V1 | V2\n// export function topMost<K1 extends string, K2 extends string, K3 extends string, V1, V2, V3> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2 | Record<K3, undefined | V3>>>[],\n//   accessors: [K1, K2, K3]\n// ): undefined | V1 | V2 | V3\n/**\n * Get the top most property value from a pile of objects.\n *\n * @param pile - Array of objects, no required format.\n * @param accessors - Array of property names.\n * For example `object['foo']['bar']` → `['foo', 'bar']`.\n * @returns Value of the property with given accessors path from the first pile item where it's not undefined.\n */\nexport function topMost(pile: any, accessors: any): any {\n  let candidate;\n  if (!Array.isArray(accessors)) {\n    accessors = [accessors];\n  }\n  for (const member of pile) {\n    if (member) {\n      candidate = member[accessors[0]];\n      for (let i = 1; i < accessors.length; i++) {\n        if (candidate) {\n          candidate = candidate[accessors[i]];\n        }\n      }\n      if (typeof candidate !== \"undefined\") {\n        break;\n      }\n    }\n  }\n  return candidate;\n}\n", "import { Hammer } from \"./hammer\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio=1]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   *\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   *\n   * @param {string | object} color\n   * @param {boolean} [setInitial=true]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color)\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   *\n   * @param {boolean} [storePrevious=true]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   *\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   *\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   *\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   *\n   * @param {object} rgba\n   * @param {boolean} [setInitial=true]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   *\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   *\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   *\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   *\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   *\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   *\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util\";\n\nimport { ColorPicker } from \"./color-picker\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n *\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   *\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   *\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   *\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   *\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   *\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   *\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   *\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   *\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   *\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   *\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   *\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   *\n   * @param {object} obj\n   * @param {Array} [path=[]]    | where to look for the actual option\n   * @param {boolean} [checkOnly=false]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   *\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2))\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options\n      );\n    }\n    return options;\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". '\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"'\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\"\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive=false]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op)\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   * http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   * Copyright (c) 2011 Andrei Mackenzie\n   *\n   * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   *\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1\n            )\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", "import { Activator as ActivatorJS } from \"./activator\";\nimport { ColorPicker as ColorPickerJS } from \"./color-picker\";\nimport { Configurator as ConfiguratorJS } from \"./configurator\";\nimport { Hammer as HammerJS } from \"./hammer\";\nimport { Popup as PopupJS } from \"./popup\";\nimport { VALIDATOR_PRINT_STYLE as VALIDATOR_PRINT_STYLE_JS } from \"./validator\";\nimport { Validator as ValidatorJS } from \"./validator\";\n\nexport const Activator: any = ActivatorJS;\nexport const ColorPicker: any = ColorPickerJS;\nexport const Configurator: any = ConfiguratorJS;\nexport const Hammer: HammerStatic = HammerJS;\nexport const Popup: any = PopupJS;\nexport const VALIDATOR_PRINT_STYLE: string = VALIDATOR_PRINT_STYLE_JS;\nexport const Validator: any = ValidatorJS;\n\nexport * from \"./configurator-types\";\n", "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   *\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   *\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n", "/**\n * Seedable, fast and reasonably good (not crypto but more than okay for our\n * needs) random number generator.\n *\n * @remarks\n * Adapted from {@link https://web.archive.org/web/20110429100736/http://baagoe.com:80/en/RandomMusings/javascript}.\n * Original algorithm created by <PERSON> \\<baagoe\\@baagoe.com\\> in 2010.\n */\n\n/**\n * Random number generator.\n */\nexport interface RNG {\n  /** Returns \\<0, 1). Faster than [[fract53]]. */\n  (): number;\n  /** Returns \\<0, 1). Provides more precise data. */\n  fract53(): number;\n  /** Returns \\<0, 2^32). */\n  uint32(): number;\n\n  /** The algorithm gehind this instance. */\n  algorithm: string;\n  /** The seed used to seed this instance. */\n  seed: Mashable[];\n  /** The version of this instance. */\n  version: string;\n}\n\n/**\n * Create a seeded pseudo random generator based on <PERSON><PERSON> by <PERSON>.\n *\n * @param seed - All supplied arguments will be used as a seed. In case nothing\n * is supplied the current time will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nexport function Alea(...seed: Mashable[]): RNG {\n  return AleaImplementation(seed.length ? seed : [Date.now()]);\n}\n\n/**\n * An implementation of [[Alea]] without user input validation.\n *\n * @param seed - The data that will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nfunction AleaImplementation(seed: Mashable[]): RNG {\n  let [s0, s1, s2] = mashSeed(seed);\n  let c = 1;\n\n  const random: RNG = (): number => {\n    const t = 2091639 * s0 + c * 2.3283064365386963e-10; // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return (s2 = t - (c = t | 0));\n  };\n\n  random.uint32 = (): number => random() * 0x100000000; // 2^32\n\n  random.fract53 = (): number =>\n    random() + ((random() * 0x200000) | 0) * 1.1102230246251565e-16; // 2^-53\n\n  random.algorithm = \"Alea\";\n  random.seed = seed;\n  random.version = \"0.9\";\n\n  return random;\n}\n\n/**\n * Turn arbitrary data into values [[AleaImplementation]] can use to generate\n * random numbers.\n *\n * @param seed - Arbitrary data that will be used as the seed.\n * @returns Three numbers to use as initial values for [[AleaImplementation]].\n */\nfunction mashSeed(...seed: Mashable[]): [number, number, number] {\n  const mash = Mash();\n\n  let s0 = mash(\" \");\n  let s1 = mash(\" \");\n  let s2 = mash(\" \");\n\n  for (let i = 0; i < seed.length; i++) {\n    s0 -= mash(seed[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seed[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seed[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n\n  return [s0, s1, s2];\n}\n\n/**\n * Values of these types can be used as a seed.\n */\nexport type Mashable = number | string | boolean | object | bigint;\n\n/**\n * Create a new mash function.\n *\n * @returns A nonpure function that takes arbitrary [[Mashable]] data and turns\n * them into numbers.\n */\nfunction Mash(): (data: Mashable) => number {\n  let n = 0xefc8249d;\n\n  return function (data): number {\n    const string = data.toString();\n    for (let i = 0; i < string.length; i++) {\n      n += string.charCodeAt(i);\n      let h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n}\n"], "names": ["DELETE", "Symbol", "deepObjectAssign", "values", "merged", "deepObjectAssignNonentry", "stripDelete", "length", "slice", "a", "b", "Date", "setTime", "getTime", "prop", "Reflect", "ownKeys", "Object", "prototype", "propertyIsEnumerable", "call", "Array", "isArray", "clone", "map", "value", "keys", "Hammer", "window", "RealHammer", "noop", "on", "off", "destroy", "emit", "get", "set", "hammerMock", "Activator", "container", "this", "_cleanupQueue", "active", "_dom", "overlay", "document", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hammer", "_onTapOverlay", "bind", "for<PERSON>ach", "event", "srcEvent", "stopPropagation", "body", "_onClick", "element", "parent", "_hasParent", "target", "deactivate", "addEventListener", "removeEventListener", "_escListener", "key", "keyCode", "Emitter", "current", "callback", "splice", "reverse", "activate", "style", "display", "remove", "ASPDateRegex", "fullHexRE", "shortHexRE", "rgbRE", "rgbaRE", "isNumber", "Number", "isString", "String", "isObject", "copyOrDelete", "allowDeletion", "doDeletion", "undefined", "extend", "assign", "deepExtend", "protoExtend", "hasOwnProperty", "getPrototypeOf", "copyAndExtendArray", "arr", "newValue", "copyArray", "toArray", "option", "asBoolean", "defaultValue", "asNumber", "asString", "asSize", "asElement", "hexToRGB", "hex", "result", "exec", "r", "parseInt", "g", "RGBToHex", "red", "green", "blue", "toString", "RGBToHSV", "minRGB", "Math", "min", "maxRGB", "max", "h", "s", "v", "splitCSSText", "cssText", "tmpEllement", "styles", "i", "getPropertyValue", "HSVToRGB", "floor", "f", "p", "q", "t", "HSVToHex", "rgb", "hexToHSV", "TypeError", "isValidHex", "test", "isValidRGB", "isValidRGBA", "rgba", "bridgeObject", "referenceObject", "Element", "objectTo", "create", "easingFunctions", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "htmlColors", "black", "navy", "darkblue", "mediumblue", "darkgreen", "teal", "dark<PERSON>an", "deepskyblue", "darkturquoise", "mediumspringgreen", "lime", "springgreen", "aqua", "cyan", "midnightblue", "dodgerblue", "lightseagreen", "forestgreen", "seagreen", "darkslategray", "limegreen", "mediumseagreen", "turquoise", "royalblue", "steelblue", "darkslateblue", "mediumturquoise", "indigo", "darkolivegreen", "cadetblue", "cornflowerblue", "mediumaquamarine", "dimgray", "slateblue", "<PERSON><PERSON><PERSON>", "slategray", "lightslategray", "mediumslateblue", "lawngreen", "chartreuse", "aquamarine", "maroon", "purple", "olive", "gray", "skyblue", "lightskyblue", "blueviolet", "darkred", "darkmagenta", "saddlebrown", "darkseagreen", "lightgreen", "mediumpurple", "darkviolet", "palegreen", "darkorchid", "yellowgreen", "sienna", "brown", "darkgray", "lightblue", "greenyellow", "paleturquoise", "lightsteelblue", "powderblue", "firebrick", "darkgoldenrod", "mediumorchid", "rosybrown", "<PERSON><PERSON><PERSON>", "silver", "mediumvioletred", "indianred", "peru", "chocolate", "tan", "<PERSON><PERSON>rey", "palevioletred", "thistle", "orchid", "goldenrod", "crimson", "gainsboro", "plum", "burlywood", "lightcyan", "lavender", "<PERSON><PERSON><PERSON>", "violet", "palegoldenrod", "lightcoral", "khaki", "aliceblue", "honeydew", "azure", "sandybrown", "wheat", "beige", "whitesmoke", "mintcream", "ghostwhite", "salmon", "antiquewhite", "linen", "lightgoldenrodyellow", "oldlace", "fuchsia", "magenta", "deeppink", "orangered", "tomato", "hotpink", "coral", "darkorange", "<PERSON><PERSON><PERSON>", "orange", "lightpink", "pink", "gold", "peachpuff", "navajowhite", "moccasin", "bisque", "mistyrose", "blanche<PERSON><PERSON>", "papayawhip", "lavenderblush", "seashell", "cornsilk", "lemon<PERSON>ffon", "<PERSON><PERSON><PERSON><PERSON>", "snow", "yellow", "lightyellow", "ivory", "white", "ColorPicker$1", "constructor", "pixelRatio", "generated", "centerCoordinates", "x", "y", "color", "hueCircle", "initialColor", "previousColor", "applied", "updateCallback", "closeCallback", "_create", "insertTo", "frame", "_<PERSON><PERSON><PERSON><PERSON>", "_setSize", "setUpdateCallback", "Error", "setCloseCallback", "_isColorString", "setColor", "setInitial", "htmlColor", "rgbaArray", "substr", "split", "rgbObj", "alpha", "JSON", "stringify", "_setColor", "show", "_generateHueCircle", "_hide", "storePrevious", "setTimeout", "_save", "_apply", "_updatePicker", "_loadLast", "alert", "hsv", "angleConvert", "PI", "radius", "sin", "cos", "colorPickerSelector", "left", "clientWidth", "top", "clientHeight", "_setOpacity", "_setBrightness", "ctx", "colorPickerCanvas", "getContext", "pixelRation", "devicePixelRatio", "webkitBackingStorePixelRatio", "mozBackingStorePixelRatio", "msBackingStorePixelRatio", "oBackingStorePixelRatio", "backingStorePixelRatio", "setTransform", "w", "clearRect", "putImageData", "fillStyle", "circle", "fill", "brightnessRange", "opacityRange", "initialColorDiv", "backgroundColor", "newColorDiv", "width", "height", "className", "colorPickerDiv", "noCanvas", "fontWeight", "padding", "innerText", "opacityDiv", "brightnessDiv", "arrowDiv", "type", "err", "me", "onchange", "oninput", "brightnessLabel", "opacityLabel", "cancelButton", "onclick", "applyButton", "saveButton", "loadButton", "drag", "pinch", "enable", "<PERSON><PERSON><PERSON><PERSON>", "_moveSelector", "hue", "sat", "hfac", "sfac", "fillRect", "strokeStyle", "stroke", "getImageData", "rect", "getBoundingClientRect", "center", "centerY", "centerX", "angle", "atan2", "sqrt", "newTop", "newLeft", "wrapInTag", "rest", "createTextNode", "allOptions", "errorFound", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPicker", "ColorPickerJS", "Configurator", "parentModule", "defaultContainer", "configureOptions", "hideOption", "changedOptions", "allowCreation", "options", "initialized", "popup<PERSON><PERSON>nter", "defaultOptions", "enabled", "filter", "showButton", "moduleOptions", "dom<PERSON><PERSON>s", "popupDiv", "popupLimit", "popupHistory", "colorPicker", "wrapper", "setOptions", "_removePopup", "join", "_clean", "setModuleOptions", "counter", "_handleObject", "indexOf", "_makeItem", "_makeHeader", "_makeButton", "_push", "_showPopupIfNeeded", "_getValue", "path", "base", "item", "name", "div", "_make<PERSON><PERSON>l", "objectLabel", "<PERSON><PERSON><PERSON><PERSON>", "_makeDropdown", "select", "selected<PERSON><PERSON><PERSON>", "selected", "_update", "label", "_makeRange", "step", "range", "popupString", "popupValue", "factor", "ceil", "input", "itemIndex", "_setupPopup", "generateButton", "_printOptions", "on<PERSON><PERSON>ver", "onmouseout", "optionsContainer", "string", "index", "id", "html", "clearTimeout", "hideTimeout", "deleteTimeout", "opacity", "_makeCheckbox", "checkbox", "checked", "_makeTextInput", "_makeColorField", "defaultColor", "_showColorPicker", "colorString", "obj", "checkOnly", "visibleInSet", "subObj", "newPath", "_handleArray", "enabledPath", "enabledValue", "console", "error", "_constructOptions", "emitter", "optionsObj", "pointer", "getOptions", "HammerJS", "Popup", "overflowMethod", "hidden", "setPosition", "setText", "content", "doShow", "maxHeight", "max<PERSON><PERSON><PERSON>", "isLeft", "isTop", "visibility", "hide", "VALIDATOR_PRINT_STYLE_JS", "Validator", "static", "referenceOptions", "subObject", "usedOptions", "parse", "check", "__any__", "getSuggestion", "referenceOption", "is_object", "getType", "refOptionObj", "__type__", "checkFields", "log", "message", "printLocation", "optionType", "refOptionType", "print", "object", "Boolean", "nodeType", "_isAMomentObject", "localSearch", "findInOptions", "globalSearch", "msg", "indexMatch", "distance", "closestMatch", "recursive", "closestMatchPath", "lowerCaseOption", "toLowerCase", "op", "levenshteinDistance", "prefix", "str", "j", "replace", "matrix", "char<PERSON>t", "seed", "s0", "s1", "s2", "mash", "n", "data", "charCodeAt", "<PERSON><PERSON>", "mash<PERSON><PERSON>", "c", "random", "uint32", "fract53", "algorithm", "version", "AleaImplementation", "now", "elem", "classNames", "classes", "newClasses", "concat", "includes", "cssStyle", "entries", "setProperty", "orderedItems", "comparator", "field", "field2", "iteration", "low", "high", "middle", "searchResult", "sidePreference", "prevValue", "nextValue", "len", "fillIfDefined", "aProp", "bProp", "right", "inner", "outer", "position", "overflow", "w1", "offsetWidth", "w2", "srcElement", "compare", "k", "isNaN", "mergeTarget", "globalOptions", "isPresent", "srcOption", "globalOption", "isEmpty", "globalEnabled", "src", "dst", "doMerge", "inputColor", "colorStr", "lighterColorHSV", "darkerColorHSV", "darkerColorHex", "lighterColorHex", "background", "border", "highlight", "hover", "preventDefault", "returnValue", "updates", "recursiveDOMDelete", "DOMobject", "hasChildNodes", "child", "oldClasses", "removeProperty", "fields", "props", "others", "other", "propsToExclude", "fn", "scheduled", "requestAnimationFrame", "pile", "accessors", "candidate", "member"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;8VAGaA,EAASC,OAAO,UAgDb,SAAAC,KAAoBC,GAClC,MAAMC,EAASC,KAA4BF,GAE3C,OADAG,EAAYF,GACLA,CACT,CAUA,SAASC,KAA4BF,GACnC,GAAIA,EAAOI,OAAS,EAClB,OAAOJ,EAAO,GACT,GAAIA,EAAOI,OAAS,EACzB,OAAOF,EACLH,EAAiBC,EAAO,GAAIA,EAAO,OAChCA,EAAOK,MAAM,IAIpB,MAAMC,EAAIN,EAAO,GACXO,EAAIP,EAAO,GAEjB,GAAIM,aAAaE,MAAQD,aAAaC,KAEpC,OADAF,EAAEG,QAAQF,EAAEG,WACLJ,EAGT,IAAK,MAAMK,KAAQC,QAAQC,QAAQN,GAC5BO,OAAOC,UAAUC,qBAAqBC,KAAKV,EAAGI,KAExCJ,EAAEI,KAAUd,SACdS,EAAEK,GAEG,OAAZL,EAAEK,IACU,OAAZJ,EAAEI,IACiB,iBAAZL,EAAEK,IACU,iBAAZJ,EAAEI,IACRO,MAAMC,QAAQb,EAAEK,KAChBO,MAAMC,QAAQZ,EAAEI,IAIjBL,EAAEK,GAAQS,EAAMb,EAAEI,IAFlBL,EAAEK,GAAQT,EAAyBI,EAAEK,GAAOJ,EAAEI,KAMlD,OAAOL,CACT,CAQA,SAASc,EAAMd,GACb,OAAIY,MAAMC,QAAQb,GACTA,EAAEe,KAAKC,GAAoBF,EAAME,KAClB,iBAANhB,GAAwB,OAANA,EAC9BA,aAAaE,KACR,IAAIA,KAAKF,EAAEI,WAEbR,EAAyB,GAAII,GAE7BA,CAEX,CAOA,SAASH,EAAYG,GACnB,IAAK,MAAMK,KAAQG,OAAOS,KAAKjB,GACzBA,EAAEK,KAAUd,SACPS,EAAEK,GACmB,iBAAZL,EAAEK,IAAkC,OAAZL,EAAEK,IAC1CR,EAAYG,EAAEK,GAGpB,CC/GA,MAAMa,EACc,oBAAXC,OACHA,OAAOD,QAAUE,EACjB,WAEE,OAtBR,WACE,MAAMC,EAAO,OAEb,MAAO,CACLC,GAAID,EACJE,IAAKF,EACLG,QAASH,EACTI,KAAMJ,EAENK,IAAG,KACM,CACLC,IAAKN,IAIb,CAOeO,EACR,EClBA,SAASC,EAAUC,GACxBC,KAAKC,cAAgB,GAErBD,KAAKE,QAAS,EAEdF,KAAKG,KAAO,CACVJ,YACAK,QAASC,SAASC,cAAc,QAGlCN,KAAKG,KAAKC,QAAQG,UAAUC,IAAI,eAEhCR,KAAKG,KAAKJ,UAAUU,YAAYT,KAAKG,KAAKC,SAC1CJ,KAAKC,cAAcS,MAAK,KACtBV,KAAKG,KAAKC,QAAQO,WAAWC,YAAYZ,KAAKG,KAAKC,QAAQ,IAG7D,MAAMS,EAAS1B,EAAOa,KAAKG,KAAKC,SAChCS,EAAOtB,GAAG,MAAOS,KAAKc,cAAcC,KAAKf,OACzCA,KAAKC,cAAcS,MAAK,KACtBG,EAAOpB,SAAS,IAMH,CACb,MACA,YACA,QACA,QACA,MACA,WACA,UACA,UAEKuB,SAASC,IACdJ,EAAOtB,GAAG0B,GAAQA,IAChBA,EAAMC,SAASC,iBAAiB,GAChC,IAIAd,UAAYA,SAASe,OACvBpB,KAAKqB,SAAYJ,KAmGrB,SAAoBK,EAASC,GAC3B,KAAOD,GAAS,CACd,GAAIA,IAAYC,EACd,OAAO,EAETD,EAAUA,EAAQX,UACnB,CACD,OAAO,CACT,EA1GWa,CAAWP,EAAMQ,OAAQ1B,IAC5BC,KAAK0B,YACN,EAEHrB,SAASe,KAAKO,iBAAiB,QAAS3B,KAAKqB,UAC7CrB,KAAKC,cAAcS,MAAK,KACtBL,SAASe,KAAKQ,oBAAoB,QAAS5B,KAAKqB,SAAS,KAK7DrB,KAAK6B,aAAgBZ,KAEjB,QAASA,EACS,WAAdA,EAAMa,IACY,KAAlBb,EAAMc,UAEV/B,KAAK0B,YACN,CAEL,CAGAM,EAAQlC,EAAUpB,WAGlBoB,EAAUmC,QAAU,KAKpBnC,EAAUpB,UAAUe,QAAU,WAC5BO,KAAK0B,aAEL,IAAK,MAAMQ,KAAYlC,KAAKC,cAAckC,OAAO,GAAGC,UAClDF,GAEJ,EAMApC,EAAUpB,UAAU2D,SAAW,WAEzBvC,EAAUmC,SACZnC,EAAUmC,QAAQP,aAEpB5B,EAAUmC,QAAUjC,KAEpBA,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,OAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUC,IAAI,cAElCR,KAAKN,KAAK,UACVM,KAAKN,KAAK,YAIVW,SAASe,KAAKO,iBAAiB,UAAW3B,KAAK6B,aACjD,EAMA/B,EAAUpB,UAAUgD,WAAa,WAC/B1B,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,QAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUiC,OAAO,cACrCnC,SAASe,KAAKQ,oBAAoB,UAAW5B,KAAK6B,cAElD7B,KAAKN,KAAK,UACVM,KAAKN,KAAK,aACZ,EAQAI,EAAUpB,UAAUoC,cAAgB,SAAUG,GAE5CjB,KAAKqC,WACLpB,EAAMC,SAASC,iBACjB,EC5IA,MAAMsB,EAAe,qBAGfC,EAAY,4CACZC,EAAa,mCACbC,EACJ,+GACIC,EACJ,mIAkEI,SAAUC,EAAS7D,GACvB,OAAOA,aAAiB8D,QAA2B,iBAAV9D,CAC3C,CAyBM,SAAU+D,EAAS/D,GACvB,OAAOA,aAAiBgE,QAA2B,iBAAVhE,CAC3C,CAQM,SAAUiE,EAASjE,GACvB,MAAwB,iBAAVA,GAAgC,OAAVA,CACtC,CAmCA,SAASkE,EACPlF,EACAC,EACAI,EACA8E,GAEA,IAAIC,GAAa,GACK,IAAlBD,IACFC,EAAyB,OAAZnF,EAAEI,SAA8BgF,IAAZrF,EAAEK,IAGjC+E,SACKpF,EAAEK,GAETL,EAAEK,GAAQJ,EAAEI,EAEhB,CA2Ca,MAAAiF,EAAS9E,OAAO+E,OAmJb,SAAAC,EACdxF,EACAC,EACAwF,GAAc,EACdN,GAAgB,GAEhB,IAAK,MAAM9E,KAAQJ,GACbO,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,KAAyB,IAAhBoF,KAE9B,iBAAZxF,EAAEI,IACG,OAAZJ,EAAEI,IACFG,OAAOmF,eAAe1F,EAAEI,MAAWG,OAAOC,eAE1B4E,IAAZrF,EAAEK,GACJL,EAAEK,GAAQmF,EAAW,CAAA,EAAIvF,EAAEI,GAAOoF,GAEf,iBAAZzF,EAAEK,IACG,OAAZL,EAAEK,IACFG,OAAOmF,eAAe3F,EAAEK,MAAWG,OAAOC,UAE1C+E,EAAWxF,EAAEK,GAAOJ,EAAEI,GAAOoF,GAE7BP,EAAalF,EAAGC,EAAGI,EAAM8E,GAElBvE,MAAMC,QAAQZ,EAAEI,IACzBL,EAAEK,GAAQJ,EAAEI,GAAMN,QAElBmF,EAAalF,EAAGC,EAAGI,EAAM8E,IAI/B,OAAOnF,CACT,CAkFgB,SAAA4F,EACdC,EACAC,GAEA,MAAO,IAAID,EAAKC,EAClB,CAQM,SAAUC,EAAaF,GAC3B,OAAOA,EAAI9F,OACb,CAuGa,MAAAiG,EAAUxF,OAAOd,OA0HjB,MAAAuG,EAAS,CAQpBC,UAAS,CAAClF,EAAgBmF,KACJ,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACc,GAATA,EAGFmF,GAAgB,MAUzBC,SAAQ,CAACpF,EAAgBmF,KACH,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACK8D,OAAO9D,IAAUmF,GAAgB,KAGnCA,GAAgB,MAUzBE,SAAQ,CAACrF,EAAgBmF,KACH,mBAATnF,IACTA,EAAQA,KAGG,MAATA,EACKgE,OAAOhE,GAGTmF,GAAgB,MAUzBG,OAAM,CAACtF,EAAgBmF,KACD,mBAATnF,IACTA,EAAQA,KAGN+D,EAAS/D,GACJA,EACE6D,EAAS7D,GACXA,EAAQ,KAERmF,GAAgB,MAW3BI,UAAS,CACPvF,EACAmF,KAEoB,mBAATnF,IACTA,EAAQA,KAGHA,GAASmF,GAAgB,OAY9B,SAAUK,EAASC,GACvB,IAAIC,EACJ,OAAQD,EAAI3G,QACV,KAAK,EACL,KAAK,EAEH,OADA4G,EAAShC,EAAWiC,KAAKF,GAClBC,EACH,CACEE,EAAGC,SAASH,EAAO,GAAKA,EAAO,GAAI,IACnCI,EAAGD,SAASH,EAAO,GAAKA,EAAO,GAAI,IACnCzG,EAAG4G,SAASH,EAAO,GAAKA,EAAO,GAAI,KAErC,KACN,KAAK,EACL,KAAK,EAEH,OADAA,EAASjC,EAAUkC,KAAKF,GACjBC,EACH,CACEE,EAAGC,SAASH,EAAO,GAAI,IACvBI,EAAGD,SAASH,EAAO,GAAI,IACvBzG,EAAG4G,SAASH,EAAO,GAAI,KAEzB,KACN,QACE,OAAO,KAEb,UAoCgBK,EAASC,EAAaC,EAAeC,GACnD,MACE,MAAQ,GAAK,KAAOF,GAAO,KAAOC,GAAS,GAAKC,GAAMC,SAAS,IAAIpH,MAAM,EAE7E,UA0LgBqH,EAASJ,EAAaC,EAAeC,GACnDF,GAAY,IACZC,GAAgB,IAChBC,GAAc,IACd,MAAMG,EAASC,KAAKC,IAAIP,EAAKM,KAAKC,IAAIN,EAAOC,IACvCM,EAASF,KAAKG,IAAIT,EAAKM,KAAKG,IAAIR,EAAOC,IAG7C,GAAIG,IAAWG,EACb,MAAO,CAAEE,EAAG,EAAGC,EAAG,EAAGC,EAAGP,GAU1B,MAAO,CAAEK,EAHI,KADHV,IAAQK,EAAS,EAAIH,IAASG,EAAS,EAAI,IADnDL,IAAQK,EAASJ,EAAQC,EAAOA,IAASG,EAASL,EAAMC,EAAQC,EAAOF,IAE7CQ,EAASH,IAAY,IAGhCM,GAFGH,EAASH,GAAUG,EAEPI,EADlBJ,EAEhB,CAYA,SAASK,EAAaC,GACpB,MAAMC,EAAc3F,SAASC,cAAc,OAErC2F,EAAoB,CAAA,EAE1BD,EAAY1D,MAAMyD,QAAUA,EAE5B,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAY1D,MAAMvE,SAAUmI,EAC9CD,EAAOD,EAAY1D,MAAM4D,IAAMF,EAAY1D,MAAM6D,iBAC/CH,EAAY1D,MAAM4D,IAItB,OAAOD,CACT,UAsCgBG,EAAST,EAAWC,EAAWC,GAC7C,IAAIhB,EACAE,EACA7G,EAEJ,MAAMgI,EAAIX,KAAKc,MAAU,EAAJV,GACfW,EAAQ,EAAJX,EAAQO,EACZK,EAAIV,GAAK,EAAID,GACbY,EAAIX,GAAK,EAAIS,EAAIV,GACjBa,EAAIZ,GAAK,GAAK,EAAIS,GAAKV,GAE7B,OAAQM,EAAI,GACV,KAAK,EACFrB,EAAIgB,EAAKd,EAAI0B,EAAKvI,EAAIqI,EACvB,MACF,KAAK,EACF1B,EAAI2B,EAAKzB,EAAIc,EAAK3H,EAAIqI,EACvB,MACF,KAAK,EACF1B,EAAI0B,EAAKxB,EAAIc,EAAK3H,EAAIuI,EACvB,MACF,KAAK,EACF5B,EAAI0B,EAAKxB,EAAIyB,EAAKtI,EAAI2H,EACvB,MACF,KAAK,EACFhB,EAAI4B,EAAK1B,EAAIwB,EAAKrI,EAAI2H,EACvB,MACF,KAAK,EACFhB,EAAIgB,EAAKd,EAAIwB,EAAKrI,EAAIsI,EAI3B,MAAO,CACL3B,EAAGU,KAAKc,MAAsB,IAAfxB,GACfE,EAAGQ,KAAKc,MAAsB,IAAftB,GACf7G,EAAGqH,KAAKc,MAAsB,IAAfnI,GAEnB,UAUgBwI,EAASf,EAAWC,EAAWC,GAC7C,MAAMc,EAAMP,EAAST,EAAGC,EAAGC,GAC3B,OAAOb,EAAS2B,EAAI9B,EAAG8B,EAAI5B,EAAG4B,EAAIzI,EACpC,CAQM,SAAU0I,EAASlC,GACvB,MAAMiC,EAAMlC,EAASC,GACrB,IAAKiC,EACH,MAAM,IAAIE,UAAU,IAAInC,4BAE1B,OAAOW,EAASsB,EAAI9B,EAAG8B,EAAI5B,EAAG4B,EAAIzI,EACpC,CAQM,SAAU4I,EAAWpC,GAEzB,MADa,qCAAqCqC,KAAKrC,EAEzD,CAQM,SAAUsC,EAAWL,GACzB,OAAO/D,EAAMmE,KAAKJ,EACpB,CAQM,SAAUM,EAAYC,GAC1B,OAAOrE,EAAOkE,KAAKG,EACrB,CAuCM,SAAUC,EACdC,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EACrC,OAAO,KAGT,GAAIA,aAA2BC,QAE7B,OAAOD,EAGT,MAAME,EAAW7I,OAAO8I,OAAOH,GAC/B,IAAK,MAAMlB,KAAKkB,EACV3I,OAAOC,UAAUiF,eAAe/E,KAAKwI,EAAiBlB,IACd,iBAA9BkB,EAAwBlB,KAClCoB,EAASpB,GAAKiB,EAAcC,EAAwBlB,KAK1D,OAAOoB,CACT,CAqSa,MAAAE,EAAkB,CAO7BC,OAAOhB,GACEA,EASTiB,WAAWjB,GACFA,EAAIA,EASbkB,YAAYlB,GACHA,GAAK,EAAIA,GASlBmB,cAAcnB,GACLA,EAAI,GAAM,EAAIA,EAAIA,GAAU,EAAI,EAAIA,GAAKA,EAAlB,EAShCoB,YAAYpB,GACHA,EAAIA,EAAIA,EASjBqB,aAAarB,KACFA,EAAIA,EAAIA,EAAI,EASvBsB,eAAetB,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,GAAKA,EAAI,IAAM,EAAIA,EAAI,IAAM,EAAIA,EAAI,GAAK,EASzEuB,YAAYvB,GACHA,EAAIA,EAAIA,EAAIA,EASrBwB,aAAaxB,GACJ,KAAMA,EAAIA,EAAIA,EAAIA,EAS3ByB,eAAezB,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,IAAMA,EAAIA,EAAIA,EAAIA,EAS7D0B,YAAY1B,GACHA,EAAIA,EAAIA,EAAIA,EAAIA,EASzB2B,aAAa3B,GACJ,IAAMA,EAAIA,EAAIA,EAAIA,EAAIA,EAS/B4B,eAAe5B,GACNA,EAAI,GAAM,GAAKA,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAOA,EAAIA,EAAIA,EAAIA,EAAIA,GCzrDzE,MAAM6B,EAAa,CACjBC,MAAO,UACPC,KAAM,UACNC,SAAU,UACVC,WAAY,UACZvD,KAAM,UACNwD,UAAW,UACXzD,MAAO,UACP0D,KAAM,UACNC,SAAU,UACVC,YAAa,UACbC,cAAe,UACfC,kBAAmB,UACnBC,KAAM,UACNC,YAAa,UACbC,KAAM,UACNC,KAAM,UACNC,aAAc,UACdC,WAAY,UACZC,cAAe,UACfC,YAAa,UACbC,SAAU,UACVC,cAAe,UACfC,UAAW,UACXC,eAAgB,UAChBC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,cAAe,UACfC,gBAAiB,UACjBC,OAAQ,UACRC,eAAgB,UAChBC,UAAW,UACXC,eAAgB,UAChBC,iBAAkB,UAClBC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,eAAgB,UAChBC,gBAAiB,UACjBC,UAAW,UACXC,WAAY,UACZC,WAAY,UACZC,OAAQ,UACRC,OAAQ,UACRC,MAAO,UACPC,KAAM,UACNC,QAAS,UACTC,aAAc,UACdC,WAAY,UACZC,QAAS,UACTC,YAAa,UACbC,YAAa,UACbC,aAAc,UACdC,WAAY,UACZC,aAAc,UACdC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,MAAO,UACPC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,eAAgB,UAChBC,WAAY,UACZC,UAAW,UACXC,cAAe,UACfC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,OAAQ,UACRC,gBAAiB,UACjBC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,IAAK,UACLC,UAAW,UACXC,cAAe,UACfC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,QAAS,UACTC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,WAAY,UACZC,OAAQ,UACRC,cAAe,UACfC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,SAAU,UACVC,MAAO,UACPC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,aAAc,UACdC,MAAO,UACPC,qBAAsB,UACtBC,QAAS,UACThK,IAAK,UACLiK,QAAS,UACTC,QAAS,UACTC,SAAU,UACVC,UAAW,UACXC,OAAQ,UACRC,QAAS,UACTC,MAAO,UACPC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,UAAW,UACXC,YAAa,UACbC,SAAU,UACVC,OAAQ,UACRC,UAAW,UACXC,eAAgB,UAChBC,WAAY,UACZC,cAAe,UACfC,SAAU,UACVC,SAAU,UACVC,aAAc,UACdC,YAAa,UACbC,KAAM,UACNC,OAAQ,UACRC,YAAa,UACbC,MAAO,UACPC,MAAO,WAMF,IAAAC,EAAA,MAILC,YAAYC,EAAa,GACvBlR,KAAKkR,WAAaA,EAClBlR,KAAKmR,WAAY,EACjBnR,KAAKoR,kBAAoB,CAAEC,EAAG,MAASC,EAAG,OAC1CtR,KAAK6E,EAAI,IAAM,IACf7E,KAAKuR,MAAQ,CAAE1M,EAAG,IAAKE,EAAG,IAAK7G,EAAG,IAAKD,EAAG,GAC1C+B,KAAKwR,eAAYlO,EACjBtD,KAAKyR,aAAe,CAAE5M,EAAG,IAAKE,EAAG,IAAK7G,EAAG,IAAKD,EAAG,GACjD+B,KAAK0R,mBAAgBpO,EACrBtD,KAAK2R,SAAU,EAGf3R,KAAK4R,eAAiB,OACtB5R,KAAK6R,cAAgB,OAGrB7R,KAAK8R,SACN,CAODC,SAAShS,QACauD,IAAhBtD,KAAKa,SACPb,KAAKa,OAAOpB,UACZO,KAAKa,YAASyC,GAEhBtD,KAAKD,UAAYA,EACjBC,KAAKD,UAAUU,YAAYT,KAAKgS,OAChChS,KAAKiS,cAELjS,KAAKkS,UACN,CAODC,kBAAkBjQ,GAChB,GAAwB,mBAAbA,EAGT,MAAM,IAAIkQ,MACR,+EAHFpS,KAAK4R,eAAiB1P,CAMzB,CAODmQ,iBAAiBnQ,GACf,GAAwB,mBAAbA,EAGT,MAAM,IAAIkQ,MACR,gFAHFpS,KAAK6R,cAAgB3P,CAMxB,CAQDoQ,eAAef,GACb,GAAqB,iBAAVA,EACT,OAAOjJ,EAAWiJ,EAErB,CAeDgB,SAAShB,EAAOiB,GAAa,GAC3B,GAAc,SAAVjB,EACF,OAGF,IAAIrK,EAGJ,MAAMuL,EAAYzS,KAAKsS,eAAef,GAMtC,QALkBjO,IAAdmP,IACFlB,EAAQkB,IAIc,IAApBzP,EAASuO,IACX,IAA0B,IAAtBvK,EAAWuK,GAAiB,CAC9B,MAAMmB,EAAYnB,EACfoB,OAAO,GACPA,OAAO,EAAGpB,EAAMxT,OAAS,GACzB6U,MAAM,KACT1L,EAAO,CAAErC,EAAG6N,EAAU,GAAI3N,EAAG2N,EAAU,GAAIxU,EAAGwU,EAAU,GAAIzU,EAAG,EAChE,MAAM,IAA2B,IAAvBgJ,EAAYsK,GAAiB,CACtC,MAAMmB,EAAYnB,EACfoB,OAAO,GACPA,OAAO,EAAGpB,EAAMxT,OAAS,GACzB6U,MAAM,KACT1L,EAAO,CACLrC,EAAG6N,EAAU,GACb3N,EAAG2N,EAAU,GACbxU,EAAGwU,EAAU,GACbzU,EAAGyU,EAAU,GAEhB,MAAM,IAA0B,IAAtB5L,EAAWyK,GAAiB,CACrC,MAAMsB,EAASpO,EAAS8M,GACxBrK,EAAO,CAAErC,EAAGgO,EAAOhO,EAAGE,EAAG8N,EAAO9N,EAAG7G,EAAG2U,EAAO3U,EAAGD,EAAG,EACpD,OAED,GAAIsT,aAAiB9S,aAEL6E,IAAZiO,EAAM1M,QACMvB,IAAZiO,EAAMxM,QACMzB,IAAZiO,EAAMrT,EACN,CACA,MAAM4U,OAAoBxP,IAAZiO,EAAMtT,EAAkBsT,EAAMtT,EAAI,MAChDiJ,EAAO,CAAErC,EAAG0M,EAAM1M,EAAGE,EAAGwM,EAAMxM,EAAG7G,EAAGqT,EAAMrT,EAAGD,EAAG6U,EACjD,CAKL,QAAaxP,IAAT4D,EACF,MAAM,IAAIkL,MACR,gIACEW,KAAKC,UAAUzB,IAGnBvR,KAAKiT,UAAU/L,EAAMsL,EAExB,CAMDU,YAC6B5P,IAAvBtD,KAAK6R,gBACP7R,KAAK6R,gBACL7R,KAAK6R,mBAAgBvO,GAGvBtD,KAAK2R,SAAU,EACf3R,KAAKgS,MAAM1P,MAAMC,QAAU,QAC3BvC,KAAKmT,oBACN,CAWDC,MAAMC,GAAgB,IAEE,IAAlBA,IACFrT,KAAK0R,cAAgBjT,OAAO+E,OAAO,CAAA,EAAIxD,KAAKuR,SAGzB,IAAjBvR,KAAK2R,SACP3R,KAAK4R,eAAe5R,KAAKyR,cAG3BzR,KAAKgS,MAAM1P,MAAMC,QAAU,OAI3B+Q,YAAW,UACkBhQ,IAAvBtD,KAAK6R,gBACP7R,KAAK6R,gBACL7R,KAAK6R,mBAAgBvO,EACtB,GACA,EACJ,CAODiQ,QACEvT,KAAK4R,eAAe5R,KAAKuR,OACzBvR,KAAK2R,SAAU,EACf3R,KAAKoT,OACN,CAODI,SACExT,KAAK2R,SAAU,EACf3R,KAAK4R,eAAe5R,KAAKuR,OACzBvR,KAAKyT,cAAczT,KAAKuR,MACzB,CAODmC,iBAC6BpQ,IAAvBtD,KAAK0R,cACP1R,KAAKuS,SAASvS,KAAK0R,eAAe,GAElCiC,MAAM,oCAET,CASDV,UAAU/L,EAAMsL,GAAa,IAER,IAAfA,IACFxS,KAAKyR,aAAehT,OAAO+E,OAAO,CAAE,EAAE0D,IAGxClH,KAAKuR,MAAQrK,EACb,MAAM0M,EAAMvO,EAAS6B,EAAKrC,EAAGqC,EAAKnC,EAAGmC,EAAKhJ,GAEpC2V,EAAe,EAAItO,KAAKuO,GACxBC,EAAS/T,KAAK6E,EAAI+O,EAAIhO,EACtByL,EACJrR,KAAKoR,kBAAkBC,EAAI0C,EAASxO,KAAKyO,IAAIH,EAAeD,EAAIjO,GAC5D2L,EACJtR,KAAKoR,kBAAkBE,EAAIyC,EAASxO,KAAK0O,IAAIJ,EAAeD,EAAIjO,GAElE3F,KAAKkU,oBAAoB5R,MAAM6R,KAC7B9C,EAAI,GAAMrR,KAAKkU,oBAAoBE,YAAc,KACnDpU,KAAKkU,oBAAoB5R,MAAM+R,IAC7B/C,EAAI,GAAMtR,KAAKkU,oBAAoBI,aAAe,KAEpDtU,KAAKyT,cAAcvM,EACpB,CAQDqN,YAAYtV,GACVe,KAAKuR,MAAMtT,EAAIgB,EAAQ,IACvBe,KAAKyT,cAAczT,KAAKuR,MACzB,CAQDiD,eAAevV,GACb,MAAM2U,EAAMvO,EAASrF,KAAKuR,MAAM1M,EAAG7E,KAAKuR,MAAMxM,EAAG/E,KAAKuR,MAAMrT,GAC5D0V,EAAI/N,EAAI5G,EAAQ,IAChB,MAAMiI,EAAOd,EAASwN,EAAIjO,EAAGiO,EAAIhO,EAAGgO,EAAI/N,GACxCqB,EAAQ,EAAIlH,KAAKuR,MAAMtT,EACvB+B,KAAKuR,MAAQrK,EACblH,KAAKyT,eACN,CAQDA,cAAcvM,EAAOlH,KAAKuR,OACxB,MAAMqC,EAAMvO,EAAS6B,EAAKrC,EAAGqC,EAAKnC,EAAGmC,EAAKhJ,GACpCuW,EAAMzU,KAAK0U,kBAAkBC,WAAW,WACrBrR,IAArBtD,KAAK4U,cACP5U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,GAG5D,MAAMkE,EAAIpV,KAAK0U,kBAAkBN,YAC3BzO,EAAI3F,KAAK0U,kBAAkBJ,aACjCG,EAAIY,UAAU,EAAG,EAAGD,EAAGzP,GAEvB8O,EAAIa,aAAatV,KAAKwR,UAAW,EAAG,GACpCiD,EAAIc,UAAY,eAAiB,EAAI3B,EAAI/N,GAAK,IAC9C4O,EAAIe,OAAOxV,KAAKoR,kBAAkBC,EAAGrR,KAAKoR,kBAAkBE,EAAGtR,KAAK6E,GACpE4P,EAAIgB,OAEJzV,KAAK0V,gBAAgBzW,MAAQ,IAAM2U,EAAI/N,EACvC7F,KAAK2V,aAAa1W,MAAQ,IAAMiI,EAAKjJ,EAErC+B,KAAK4V,gBAAgBtT,MAAMuT,gBACzB,QACA7V,KAAKyR,aAAa5M,EAClB,IACA7E,KAAKyR,aAAa1M,EAClB,IACA/E,KAAKyR,aAAavT,EAClB,IACA8B,KAAKyR,aAAaxT,EAClB,IACF+B,KAAK8V,YAAYxT,MAAMuT,gBACrB,QACA7V,KAAKuR,MAAM1M,EACX,IACA7E,KAAKuR,MAAMxM,EACX,IACA/E,KAAKuR,MAAMrT,EACX,IACA8B,KAAKuR,MAAMtT,EACX,GACH,CAODiU,WACElS,KAAK0U,kBAAkBpS,MAAMyT,MAAQ,OACrC/V,KAAK0U,kBAAkBpS,MAAM0T,OAAS,OAEtChW,KAAK0U,kBAAkBqB,MAAQ,IAAM/V,KAAKkR,WAC1ClR,KAAK0U,kBAAkBsB,OAAS,IAAMhW,KAAKkR,UAC5C,CAQDY,UAYE,GAXA9R,KAAKgS,MAAQ3R,SAASC,cAAc,OACpCN,KAAKgS,MAAMiE,UAAY,mBAEvBjW,KAAKkW,eAAiB7V,SAASC,cAAc,OAC7CN,KAAKkU,oBAAsB7T,SAASC,cAAc,OAClDN,KAAKkU,oBAAoB+B,UAAY,eACrCjW,KAAKkW,eAAezV,YAAYT,KAAKkU,qBAErClU,KAAK0U,kBAAoBrU,SAASC,cAAc,UAChDN,KAAKkW,eAAezV,YAAYT,KAAK0U,mBAEhC1U,KAAK0U,kBAAkBC,WAOrB,CACL,MAAMF,EAAMzU,KAAK0U,kBAAkBC,WAAW,MAC9C3U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,GACJlV,KAAK0U,kBACFC,WAAW,MACXQ,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,EAC5D,KApBuC,CACtC,MAAMiF,EAAW9V,SAASC,cAAc,OACxC6V,EAAS7T,MAAMiP,MAAQ,MACvB4E,EAAS7T,MAAM8T,WAAa,OAC5BD,EAAS7T,MAAM+T,QAAU,OACzBF,EAASG,UAAY,mDACrBtW,KAAK0U,kBAAkBjU,YAAY0V,EACzC,CAeInW,KAAKkW,eAAeD,UAAY,YAEhCjW,KAAKuW,WAAalW,SAASC,cAAc,OACzCN,KAAKuW,WAAWN,UAAY,cAE5BjW,KAAKwW,cAAgBnW,SAASC,cAAc,OAC5CN,KAAKwW,cAAcP,UAAY,iBAE/BjW,KAAKyW,SAAWpW,SAASC,cAAc,OACvCN,KAAKyW,SAASR,UAAY,YAE1BjW,KAAK2V,aAAetV,SAASC,cAAc,SAC3C,IACEN,KAAK2V,aAAae,KAAO,QACzB1W,KAAK2V,aAAanQ,IAAM,IACxBxF,KAAK2V,aAAajQ,IAAM,KACzB,CAAC,MAAOiR,GAER,CACD3W,KAAK2V,aAAa1W,MAAQ,MAC1Be,KAAK2V,aAAaM,UAAY,YAE9BjW,KAAK0V,gBAAkBrV,SAASC,cAAc,SAC9C,IACEN,KAAK0V,gBAAgBgB,KAAO,QAC5B1W,KAAK0V,gBAAgBlQ,IAAM,IAC3BxF,KAAK0V,gBAAgBhQ,IAAM,KAC5B,CAAC,MAAOiR,GAER,CACD3W,KAAK0V,gBAAgBzW,MAAQ,MAC7Be,KAAK0V,gBAAgBO,UAAY,YAEjCjW,KAAKuW,WAAW9V,YAAYT,KAAK2V,cACjC3V,KAAKwW,cAAc/V,YAAYT,KAAK0V,iBAEpC,MAAMkB,EAAK5W,KACXA,KAAK2V,aAAakB,SAAW,WAC3BD,EAAGrC,YAAYvU,KAAKf,MAC1B,EACIe,KAAK2V,aAAamB,QAAU,WAC1BF,EAAGrC,YAAYvU,KAAKf,MAC1B,EACIe,KAAK0V,gBAAgBmB,SAAW,WAC9BD,EAAGpC,eAAexU,KAAKf,MAC7B,EACIe,KAAK0V,gBAAgBoB,QAAU,WAC7BF,EAAGpC,eAAexU,KAAKf,MAC7B,EAEIe,KAAK+W,gBAAkB1W,SAASC,cAAc,OAC9CN,KAAK+W,gBAAgBd,UAAY,2BACjCjW,KAAK+W,gBAAgBT,UAAY,cAEjCtW,KAAKgX,aAAe3W,SAASC,cAAc,OAC3CN,KAAKgX,aAAaf,UAAY,wBAC9BjW,KAAKgX,aAAaV,UAAY,WAE9BtW,KAAK8V,YAAczV,SAASC,cAAc,OAC1CN,KAAK8V,YAAYG,UAAY,gBAC7BjW,KAAK8V,YAAYQ,UAAY,MAE7BtW,KAAK4V,gBAAkBvV,SAASC,cAAc,OAC9CN,KAAK4V,gBAAgBK,UAAY,oBACjCjW,KAAK4V,gBAAgBU,UAAY,UAEjCtW,KAAKiX,aAAe5W,SAASC,cAAc,OAC3CN,KAAKiX,aAAahB,UAAY,wBAC9BjW,KAAKiX,aAAaX,UAAY,SAC9BtW,KAAKiX,aAAaC,QAAUlX,KAAKoT,MAAMrS,KAAKf,MAAM,GAElDA,KAAKmX,YAAc9W,SAASC,cAAc,OAC1CN,KAAKmX,YAAYlB,UAAY,uBAC7BjW,KAAKmX,YAAYb,UAAY,QAC7BtW,KAAKmX,YAAYD,QAAUlX,KAAKwT,OAAOzS,KAAKf,MAE5CA,KAAKoX,WAAa/W,SAASC,cAAc,OACzCN,KAAKoX,WAAWnB,UAAY,sBAC5BjW,KAAKoX,WAAWd,UAAY,OAC5BtW,KAAKoX,WAAWF,QAAUlX,KAAKuT,MAAMxS,KAAKf,MAE1CA,KAAKqX,WAAahX,SAASC,cAAc,OACzCN,KAAKqX,WAAWpB,UAAY,sBAC5BjW,KAAKqX,WAAWf,UAAY,YAC5BtW,KAAKqX,WAAWH,QAAUlX,KAAK0T,UAAU3S,KAAKf,MAE9CA,KAAKgS,MAAMvR,YAAYT,KAAKkW,gBAC5BlW,KAAKgS,MAAMvR,YAAYT,KAAKyW,UAC5BzW,KAAKgS,MAAMvR,YAAYT,KAAK+W,iBAC5B/W,KAAKgS,MAAMvR,YAAYT,KAAKwW,eAC5BxW,KAAKgS,MAAMvR,YAAYT,KAAKgX,cAC5BhX,KAAKgS,MAAMvR,YAAYT,KAAKuW,YAC5BvW,KAAKgS,MAAMvR,YAAYT,KAAK8V,aAC5B9V,KAAKgS,MAAMvR,YAAYT,KAAK4V,iBAE5B5V,KAAKgS,MAAMvR,YAAYT,KAAKiX,cAC5BjX,KAAKgS,MAAMvR,YAAYT,KAAKmX,aAC5BnX,KAAKgS,MAAMvR,YAAYT,KAAKoX,YAC5BpX,KAAKgS,MAAMvR,YAAYT,KAAKqX,WAC7B,CAODpF,cACEjS,KAAKsX,KAAO,GACZtX,KAAKuX,MAAQ,GACbvX,KAAKa,OAAS,IAAI1B,EAAOa,KAAK0U,mBAC9B1U,KAAKa,OAAOlB,IAAI,SAASC,IAAI,CAAE4X,QAAQ,IAEvCxX,KAAKa,OAAOtB,GAAG,gBAAiB0B,IAC1BA,EAAMwW,SACRzX,KAAK0X,cAAczW,EACpB,IAEHjB,KAAKa,OAAOtB,GAAG,OAAQ0B,IACrBjB,KAAK0X,cAAczW,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,YAAa0B,IAC1BjB,KAAK0X,cAAczW,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,WAAY0B,IACzBjB,KAAK0X,cAAczW,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,UAAW0B,IACxBjB,KAAK0X,cAAczW,EAAM,GAE5B,CAODkS,qBACE,IAAuB,IAAnBnT,KAAKmR,UAAqB,CAC5B,MAAMsD,EAAMzU,KAAK0U,kBAAkBC,WAAW,WACrBrR,IAArBtD,KAAK4U,cACP5U,KAAKkR,YACF9R,OAAOyV,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAanV,KAAKkR,WAAY,EAAG,EAAGlR,KAAKkR,WAAY,EAAG,GAG5D,MAAMkE,EAAIpV,KAAK0U,kBAAkBN,YAC3BzO,EAAI3F,KAAK0U,kBAAkBJ,aAIjC,IAAIjD,EAAGC,EAAGqG,EAAKC,EAHfnD,EAAIY,UAAU,EAAG,EAAGD,EAAGzP,GAIvB3F,KAAKoR,kBAAoB,CAAEC,EAAO,GAAJ+D,EAAS9D,EAAO,GAAJ3L,GAC1C3F,KAAK6E,EAAI,IAAOuQ,EAChB,MAAMvB,EAAgB,EAAItO,KAAKuO,GAAM,IAC/B+D,EAAO,EAAI,IACXC,EAAO,EAAI9X,KAAK6E,EACtB,IAAI8B,EACJ,IAAKgR,EAAM,EAAGA,EAAM,IAAKA,IACvB,IAAKC,EAAM,EAAGA,EAAM5X,KAAK6E,EAAG+S,IAC1BvG,EAAIrR,KAAKoR,kBAAkBC,EAAIuG,EAAMrS,KAAKyO,IAAIH,EAAe8D,GAC7DrG,EAAItR,KAAKoR,kBAAkBE,EAAIsG,EAAMrS,KAAK0O,IAAIJ,EAAe8D,GAC7DhR,EAAMP,EAASuR,EAAME,EAAMD,EAAME,EAAM,GACvCrD,EAAIc,UAAY,OAAS5O,EAAI9B,EAAI,IAAM8B,EAAI5B,EAAI,IAAM4B,EAAIzI,EAAI,IAC7DuW,EAAIsD,SAAS1G,EAAI,GAAKC,EAAI,GAAK,EAAG,GAGtCmD,EAAIuD,YAAc,gBAClBvD,EAAIe,OAAOxV,KAAKoR,kBAAkBC,EAAGrR,KAAKoR,kBAAkBE,EAAGtR,KAAK6E,GACpE4P,EAAIwD,SAEJjY,KAAKwR,UAAYiD,EAAIyD,aAAa,EAAG,EAAG9C,EAAGzP,EAC5C,CACD3F,KAAKmR,WAAY,CAClB,CAQDuG,cAAczW,GACZ,MAAMkX,EAAOnY,KAAKkW,eAAekC,wBAC3BjE,EAAOlT,EAAMoX,OAAOhH,EAAI8G,EAAKhE,KAC7BE,EAAMpT,EAAMoX,OAAO/G,EAAI6G,EAAK9D,IAE5BiE,EAAU,GAAMtY,KAAKkW,eAAe5B,aACpCiE,EAAU,GAAMvY,KAAKkW,eAAe9B,YAEpC/C,EAAI8C,EAAOoE,EACXjH,EAAI+C,EAAMiE,EAEVE,EAAQjT,KAAKkT,MAAMpH,EAAGC,GACtByC,EAAS,IAAOxO,KAAKC,IAAID,KAAKmT,KAAKrH,EAAIA,EAAIC,EAAIA,GAAIiH,GAEnDI,EAASpT,KAAK0O,IAAIuE,GAASzE,EAASuE,EACpCM,EAAUrT,KAAKyO,IAAIwE,GAASzE,EAASwE,EAE3CvY,KAAKkU,oBAAoB5R,MAAM+R,IAC7BsE,EAAS,GAAM3Y,KAAKkU,oBAAoBI,aAAe,KACzDtU,KAAKkU,oBAAoB5R,MAAM6R,KAC7ByE,EAAU,GAAM5Y,KAAKkU,oBAAoBE,YAAc,KAGzD,IAAIzO,EAAI6S,GAAS,EAAIjT,KAAKuO,IAC1BnO,EAAIA,EAAI,EAAIA,EAAI,EAAIA,EACpB,MAAMC,EAAImO,EAAS/T,KAAK6E,EAClB+O,EAAMvO,EAASrF,KAAKuR,MAAM1M,EAAG7E,KAAKuR,MAAMxM,EAAG/E,KAAKuR,MAAMrT,GAC5D0V,EAAIjO,EAAIA,EACRiO,EAAIhO,EAAIA,EACR,MAAMsB,EAAOd,EAASwN,EAAIjO,EAAGiO,EAAIhO,EAAGgO,EAAI/N,GACxCqB,EAAQ,EAAIlH,KAAKuR,MAAMtT,EACvB+B,KAAKuR,MAAQrK,EAGblH,KAAK4V,gBAAgBtT,MAAMuT,gBACzB,QACA7V,KAAKyR,aAAa5M,EAClB,IACA7E,KAAKyR,aAAa1M,EAClB,IACA/E,KAAKyR,aAAavT,EAClB,IACA8B,KAAKyR,aAAaxT,EAClB,IACF+B,KAAK8V,YAAYxT,MAAMuT,gBACrB,QACA7V,KAAKuR,MAAM1M,EACX,IACA7E,KAAKuR,MAAMxM,EACX,IACA/E,KAAKuR,MAAMrT,EACX,IACA8B,KAAKuR,MAAMtT,EACX,GACH,GCvxBH,SAAS4a,KAAaC,GACpB,GAAIA,EAAK/a,OAAS,EAChB,MAAM,IAAI8I,UAAU,sBACf,GAAoB,IAAhBiS,EAAK/a,OACd,OAAOsC,SAAS0Y,eAAeD,EAAK,IAC/B,CACL,MAAMxX,EAAUjB,SAASC,cAAcwY,EAAK,IAE5C,OADAxX,EAAQb,YAAYoY,KAAaC,EAAK9a,MAAM,KACrCsD,CACR,CACH,CAWO,IC5BH0X,EADAC,GAAa,EAGV,MAAMC,EAAwB,sCCGxB,MAAApZ,EAAiBqZ,EACjBC,EAAmBC,EACnBC,EFqBN,MAQLrI,YACEsI,EACAC,EACAC,EACAvI,EAAa,EACbwI,EAAa,MAAM,IAEnB1Z,KAAKuB,OAASgY,EACdvZ,KAAK2Z,eAAiB,GACtB3Z,KAAKD,UAAYyZ,EACjBxZ,KAAK4Z,eAAgB,EACrB5Z,KAAK0Z,WAAaA,EAElB1Z,KAAK6Z,QAAU,GACf7Z,KAAK8Z,aAAc,EACnB9Z,KAAK+Z,aAAe,EACpB/Z,KAAKga,eAAiB,CACpBC,SAAS,EACTC,QAAQ,EACRna,eAAWuD,EACX6W,YAAY,GAEd1b,OAAO+E,OAAOxD,KAAK6Z,QAAS7Z,KAAKga,gBAEjCha,KAAKyZ,iBAAmBA,EACxBzZ,KAAKoa,cAAgB,GACrBpa,KAAKqa,YAAc,GACnBra,KAAKsa,SAAW,GAChBta,KAAKua,WAAa,EAClBva,KAAKwa,aAAe,GACpBxa,KAAKya,YAAc,IAAIrB,EAAYlI,GACnClR,KAAK0a,aAAUpX,CAChB,CAQDqX,WAAWd,GACT,QAAgBvW,IAAZuW,EAAuB,CAEzB7Z,KAAKwa,aAAe,GACpBxa,KAAK4a,eAEL,IAAIX,GAAU,EACd,GAAuB,iBAAZJ,EACT7Z,KAAK6Z,QAAQK,OAASL,OACjB,GAAIhb,MAAMC,QAAQ+a,GACvB7Z,KAAK6Z,QAAQK,OAASL,EAAQgB,YACzB,GAAuB,iBAAZhB,EAAsB,CACtC,GAAe,MAAXA,EACF,MAAM,IAAIhT,UAAU,+BAEIvD,IAAtBuW,EAAQ9Z,YACVC,KAAK6Z,QAAQ9Z,UAAY8Z,EAAQ9Z,gBAEZuD,IAAnBuW,EAAQK,SACVla,KAAK6Z,QAAQK,OAASL,EAAQK,aAEL5W,IAAvBuW,EAAQM,aACVna,KAAK6Z,QAAQM,WAAaN,EAAQM,iBAEZ7W,IAApBuW,EAAQI,UACVA,EAAUJ,EAAQI,QAE5B,KAAoC,kBAAZJ,GAChB7Z,KAAK6Z,QAAQK,QAAS,EACtBD,EAAUJ,GACkB,mBAAZA,IAChB7Z,KAAK6Z,QAAQK,OAASL,EACtBI,GAAU,IAEgB,IAAxBja,KAAK6Z,QAAQK,SACfD,GAAU,GAGZja,KAAK6Z,QAAQI,QAAUA,CACxB,CACDja,KAAK8a,QACN,CAMDC,iBAAiBX,GACfpa,KAAKoa,cAAgBA,GACQ,IAAzBpa,KAAK6Z,QAAQI,UACfja,KAAK8a,cAC0BxX,IAA3BtD,KAAK6Z,QAAQ9Z,YACfC,KAAKD,UAAYC,KAAK6Z,QAAQ9Z,WAEhCC,KAAK8R,UAER,CAODA,UACE9R,KAAK8a,SACL9a,KAAK2Z,eAAiB,GAEtB,MAAMO,EAASla,KAAK6Z,QAAQK,OAC5B,IAAIc,EAAU,EACV9H,GAAO,EACX,IAAK,MAAMhP,KAAUlE,KAAKyZ,iBACpBhb,OAAOC,UAAUiF,eAAe/E,KAAKoB,KAAKyZ,iBAAkBvV,KAC9DlE,KAAK4Z,eAAgB,EACrB1G,GAAO,EACe,mBAAXgH,GACThH,EAAOgH,EAAOhW,EAAQ,IACtBgP,EACEA,GACAlT,KAAKib,cAAcjb,KAAKyZ,iBAAiBvV,GAAS,CAACA,IAAS,KAC1C,IAAXgW,IAA+C,IAA5BA,EAAOgB,QAAQhX,KAC3CgP,GAAO,IAGI,IAATA,IACFlT,KAAK4Z,eAAgB,EAGjBoB,EAAU,GACZhb,KAAKmb,UAAU,IAGjBnb,KAAKob,YAAYlX,GAGjBlE,KAAKib,cAAcjb,KAAKyZ,iBAAiBvV,GAAS,CAACA,KAErD8W,KAGJhb,KAAKqb,cACLrb,KAAKsb,OAEN,CAODA,QACEtb,KAAK0a,QAAUra,SAASC,cAAc,OACtCN,KAAK0a,QAAQzE,UAAY,4BACzBjW,KAAKD,UAAUU,YAAYT,KAAK0a,SAChC,IAAK,IAAIxU,EAAI,EAAGA,EAAIlG,KAAKqa,YAAYtc,OAAQmI,IAC3ClG,KAAK0a,QAAQja,YAAYT,KAAKqa,YAAYnU,IAG5ClG,KAAKub,oBACN,CAODT,SACE,IAAK,IAAI5U,EAAI,EAAGA,EAAIlG,KAAKqa,YAAYtc,OAAQmI,IAC3ClG,KAAK0a,QAAQ9Z,YAAYZ,KAAKqa,YAAYnU,SAGvB5C,IAAjBtD,KAAK0a,UACP1a,KAAKD,UAAUa,YAAYZ,KAAK0a,SAChC1a,KAAK0a,aAAUpX,GAEjBtD,KAAKqa,YAAc,GAEnBra,KAAK4a,cACN,CASDY,UAAUC,GACR,IAAIC,EAAO1b,KAAKoa,cAChB,IAAK,IAAIlU,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IAAK,CACpC,QAAsB5C,IAAlBoY,EAAKD,EAAKvV,IAEP,CACLwV,OAAOpY,EACP,KACD,CAJCoY,EAAOA,EAAKD,EAAKvV,GAKpB,CACD,OAAOwV,CACR,CAUDP,UAAUM,KAASpB,GACjB,IAA2B,IAAvBra,KAAK4Z,cAAwB,CAC/B,MAAM+B,EAAOtb,SAASC,cAAc,OAOpC,OANAqb,EAAK1F,UACH,iDAAmDwF,EAAK1d,OAC1Dsc,EAAYrZ,SAASM,IACnBqa,EAAKlb,YAAYa,EAAQ,IAE3BtB,KAAKqa,YAAY3Z,KAAKib,GACf3b,KAAKqa,YAAYtc,MACzB,CACD,OAAO,CACR,CAQDqd,YAAYQ,GACV,MAAMC,EAAMxb,SAASC,cAAc,OACnCub,EAAI5F,UAAY,sCAChB4F,EAAIvF,UAAYsF,EAChB5b,KAAKmb,UAAU,GAAIU,EACpB,CAWDC,WAAWF,EAAMH,EAAMM,GAAc,GACnC,MAAMF,EAAMxb,SAASC,cAAc,OAGnC,GAFAub,EAAI5F,UACF,kDAAoDwF,EAAK1d,QACvC,IAAhBge,EAAsB,CACxB,KAAOF,EAAIG,YACTH,EAAIjb,YAAYib,EAAIG,YAEtBH,EAAIpb,YAAYoY,EAAU,IAAK,IAAK+C,GAC1C,MACMC,EAAIvF,UAAYsF,EAAO,IAEzB,OAAOC,CACR,CAUDI,cAAcnY,EAAK7E,EAAOwc,GACxB,MAAMS,EAAS7b,SAASC,cAAc,UACtC4b,EAAOjG,UAAY,sCACnB,IAAIkG,EAAgB,OACN7Y,IAAVrE,IAC0B,IAAxB6E,EAAIoX,QAAQjc,KACdkd,EAAgBrY,EAAIoX,QAAQjc,IAIhC,IAAK,IAAIiH,EAAI,EAAGA,EAAIpC,EAAI/F,OAAQmI,IAAK,CACnC,MAAMhC,EAAS7D,SAASC,cAAc,UACtC4D,EAAOjF,MAAQ6E,EAAIoC,GACfA,IAAMiW,IACRjY,EAAOkY,SAAW,YAEpBlY,EAAOoS,UAAYxS,EAAIoC,GACvBgW,EAAOzb,YAAYyD,EACpB,CAED,MAAM0S,EAAK5W,KACXkc,EAAOrF,SAAW,WAChBD,EAAGyF,QAAQrc,KAAKf,MAAOwc,EAC7B,EAEI,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOJ,EAC7B,CAUDK,WAAWzY,EAAK7E,EAAOwc,GACrB,MAAMrX,EAAeN,EAAI,GACnB0B,EAAM1B,EAAI,GACV4B,EAAM5B,EAAI,GACV0Y,EAAO1Y,EAAI,GACX2Y,EAAQpc,SAASC,cAAc,SACrCmc,EAAMxG,UAAY,qCAClB,IACEwG,EAAM/F,KAAO,QACb+F,EAAMjX,IAAMA,EACZiX,EAAM/W,IAAMA,CACb,CAAC,MAAOiR,GAER,CACD8F,EAAMD,KAAOA,EAGb,IAAIE,EAAc,GACdC,EAAa,EAEjB,QAAcrZ,IAAVrE,EAAqB,CACvB,MAAM2d,EAAS,IACX3d,EAAQ,GAAKA,EAAQ2d,EAASpX,GAChCiX,EAAMjX,IAAMD,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAMjX,IACnBkX,EAAc,mBACLzd,EAAQ2d,EAASpX,IAC1BiX,EAAMjX,IAAMD,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAMjX,IACnBkX,EAAc,mBAEZzd,EAAQ2d,EAASlX,GAAe,IAARA,IAC1B+W,EAAM/W,IAAMH,KAAKsX,KAAK5d,EAAQ2d,GAC9BD,EAAaF,EAAM/W,IACnBgX,EAAc,mBAEhBD,EAAMxd,MAAQA,CACpB,MACMwd,EAAMxd,MAAQmF,EAGhB,MAAM0Y,EAAQzc,SAASC,cAAc,SACrCwc,EAAM7G,UAAY,0CAClB6G,EAAM7d,MAAQwd,EAAMxd,MAEpB,MAAM2X,EAAK5W,KACXyc,EAAM5F,SAAW,WACfiG,EAAM7d,MAAQe,KAAKf,MACnB2X,EAAGyF,QAAQtZ,OAAO/C,KAAKf,OAAQwc,EACrC,EACIgB,EAAM3F,QAAU,WACdgG,EAAM7d,MAAQe,KAAKf,KACzB,EAEI,MAAMqd,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GAC/CsB,EAAY/c,KAAKmb,UAAUM,EAAMa,EAAOG,EAAOK,GAGjC,KAAhBJ,GAAsB1c,KAAKwa,aAAauC,KAAeJ,IACzD3c,KAAKwa,aAAauC,GAAaJ,EAC/B3c,KAAKgd,YAAYN,EAAaK,GAEjC,CAOD1B,cACE,IAAgC,IAA5Brb,KAAK6Z,QAAQM,WAAqB,CACpC,MAAM8C,EAAiB5c,SAASC,cAAc,OAC9C2c,EAAehH,UAAY,sCAC3BgH,EAAe3G,UAAY,mBAC3B2G,EAAe/F,QAAU,KACvBlX,KAAKkd,eAAe,EAEtBD,EAAeE,YAAc,KAC3BF,EAAehH,UAAY,2CAA2C,EAExEgH,EAAeG,WAAa,KAC1BH,EAAehH,UAAY,qCAAqC,EAGlEjW,KAAKqd,iBAAmBhd,SAASC,cAAc,OAC/CN,KAAKqd,iBAAiBpH,UACpB,gDAEFjW,KAAKqa,YAAY3Z,KAAKV,KAAKqd,kBAC3Brd,KAAKqa,YAAY3Z,KAAKuc,EACvB,CACF,CASDD,YAAYM,EAAQC,GAClB,IACuB,IAArBvd,KAAK8Z,cACkB,IAAvB9Z,KAAK4Z,eACL5Z,KAAK+Z,aAAe/Z,KAAKua,WACzB,CACA,MAAMsB,EAAMxb,SAASC,cAAc,OACnCub,EAAI2B,GAAK,0BACT3B,EAAI5F,UAAY,0BAChB4F,EAAIvF,UAAYgH,EAChBzB,EAAI3E,QAAU,KACZlX,KAAK4a,cAAc,EAErB5a,KAAK+Z,cAAgB,EACrB/Z,KAAKsa,SAAW,CAAEmD,KAAM5B,EAAK0B,MAAOA,EACrC,CACF,CAOD3C,oBAC6BtX,IAAvBtD,KAAKsa,SAASmD,OAChBzd,KAAKsa,SAASmD,KAAK9c,WAAWC,YAAYZ,KAAKsa,SAASmD,MACxDC,aAAa1d,KAAKsa,SAASqD,aAC3BD,aAAa1d,KAAKsa,SAASsD,eAC3B5d,KAAKsa,SAAW,GAEnB,CAODiB,qBACE,QAA2BjY,IAAvBtD,KAAKsa,SAASmD,KAAoB,CACpC,MACMtF,EADuBnY,KAAKqa,YAAYra,KAAKsa,SAASiD,OAC1BnF,wBAClCpY,KAAKsa,SAASmD,KAAKnb,MAAM6R,KAAOgE,EAAKhE,KAAO,KAC5CnU,KAAKsa,SAASmD,KAAKnb,MAAM+R,IAAM8D,EAAK9D,IAAM,GAAK,KAC/ChU,SAASe,KAAKX,YAAYT,KAAKsa,SAASmD,MACxCzd,KAAKsa,SAASqD,YAAcrK,YAAW,KACrCtT,KAAKsa,SAASmD,KAAKnb,MAAMub,QAAU,CAAC,GACnC,MACH7d,KAAKsa,SAASsD,cAAgBtK,YAAW,KACvCtT,KAAK4a,cAAc,GAClB,KACJ,CACF,CAUDkD,cAAc1Z,EAAcnF,EAAOwc,GACjC,MAAMsC,EAAW1d,SAASC,cAAc,SACxCyd,EAASrH,KAAO,WAChBqH,EAAS9H,UAAY,wCACrB8H,EAASC,QAAU5Z,OACLd,IAAVrE,IACF8e,EAASC,QAAU/e,EACfA,IAAUmF,IACgB,iBAAjBA,EACLnF,IAAUmF,EAAa6V,SACzBja,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,IAGhDe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,MAKpD,MAAM2X,EAAK5W,KACX+d,EAASlH,SAAW,WAClBD,EAAGyF,QAAQrc,KAAKge,QAASvC,EAC/B,EAEI,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOyB,EAC7B,CAUDE,eAAe7Z,EAAcnF,EAAOwc,GAClC,MAAMsC,EAAW1d,SAASC,cAAc,SACxCyd,EAASrH,KAAO,OAChBqH,EAAS9H,UAAY,oCACrB8H,EAAS9e,MAAQA,EACbA,IAAUmF,GACZpE,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,IAGhD,MAAM2X,EAAK5W,KACX+d,EAASlH,SAAW,WAClBD,EAAGyF,QAAQrc,KAAKf,MAAOwc,EAC7B,EAEI,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOyB,EAC7B,CAUDG,gBAAgBpa,EAAK7E,EAAOwc,GAC1B,MAAM0C,EAAera,EAAI,GACnB+X,EAAMxb,SAASC,cAAc,OAGrB,UAFdrB,OAAkBqE,IAAVrE,EAAsBkf,EAAelf,IAG3C4c,EAAI5F,UAAY,0CAChB4F,EAAIvZ,MAAMuT,gBAAkB5W,GAE5B4c,EAAI5F,UAAY,+CAGlBhX,OAAkBqE,IAAVrE,EAAsBkf,EAAelf,EAC7C4c,EAAI3E,QAAU,KACZlX,KAAKoe,iBAAiBnf,EAAO4c,EAAKJ,EAAK,EAGzC,MAAMa,EAAQtc,KAAK8b,WAAWL,EAAKA,EAAK1d,OAAS,GAAI0d,GACrDzb,KAAKmb,UAAUM,EAAMa,EAAOT,EAC7B,CAUDuC,iBAAiBnf,EAAO4c,EAAKJ,GAE3BI,EAAI3E,QAAU,aAEdlX,KAAKya,YAAY1I,SAAS8J,GAC1B7b,KAAKya,YAAYvH,OAEjBlT,KAAKya,YAAYlI,SAAStT,GAC1Be,KAAKya,YAAYtI,mBAAmBZ,IAClC,MAAM8M,EACJ,QAAU9M,EAAM1M,EAAI,IAAM0M,EAAMxM,EAAI,IAAMwM,EAAMrT,EAAI,IAAMqT,EAAMtT,EAAI,IACtE4d,EAAIvZ,MAAMuT,gBAAkBwI,EAC5Bre,KAAKqc,QAAQgC,EAAa5C,EAAK,IAIjCzb,KAAKya,YAAYpI,kBAAiB,KAChCwJ,EAAI3E,QAAU,KACZlX,KAAKoe,iBAAiBnf,EAAO4c,EAAKJ,EAAK,CACxC,GAEJ,CAWDR,cAAcqD,EAAK7C,EAAO,GAAI8C,GAAY,GACxC,IAAIrL,GAAO,EACX,MAAMgH,EAASla,KAAK6Z,QAAQK,OAC5B,IAAIsE,GAAe,EACnB,IAAK,MAAMC,KAAUH,EACnB,GAAI7f,OAAOC,UAAUiF,eAAe/E,KAAK0f,EAAKG,GAAS,CACrDvL,GAAO,EACP,MAAMyI,EAAO2C,EAAIG,GACXC,EAAU7a,EAAmB4X,EAAMgD,GAmBzC,GAlBsB,mBAAXvE,IACThH,EAAOgH,EAAOuE,EAAQhD,IAGT,IAATvI,IAECrU,MAAMC,QAAQ6c,IACC,iBAATA,GACS,kBAATA,GACPA,aAAgBld,SAEhBuB,KAAK4Z,eAAgB,EACrB1G,EAAOlT,KAAKib,cAAcU,EAAM+C,GAAS,GACzC1e,KAAK4Z,eAA8B,IAAd2E,KAKd,IAATrL,EAAgB,CAClBsL,GAAe,EACf,MAAMvf,EAAQe,KAAKwb,UAAUkD,GAE7B,GAAI7f,MAAMC,QAAQ6c,GAChB3b,KAAK2e,aAAahD,EAAM1c,EAAOyf,QAC1B,GAAoB,iBAAT/C,EAChB3b,KAAKie,eAAetC,EAAM1c,EAAOyf,QAC5B,GAAoB,kBAAT/C,EAChB3b,KAAK8d,cAAcnC,EAAM1c,EAAOyf,QAC3B,GAAI/C,aAAgBld,QAEzB,IAAKuB,KAAK0Z,WAAW+B,EAAMgD,EAAQze,KAAKoa,eAEtC,QAAqB9W,IAAjBqY,EAAK1B,QAAuB,CAC9B,MAAM2E,EAAc/a,EAAmB6a,EAAS,WAC1CG,EAAe7e,KAAKwb,UAAUoD,GACpC,IAAqB,IAAjBC,EAAuB,CACzB,MAAMvC,EAAQtc,KAAK8b,WAAW2C,EAAQC,GAAS,GAC/C1e,KAAKmb,UAAUuD,EAASpC,GACxBkC,EACExe,KAAKib,cAAcU,EAAM+C,IAAYF,CACzD,MACkBxe,KAAK8d,cAAcnC,EAAMkD,EAAcH,EAEzD,KAAqB,CACL,MAAMpC,EAAQtc,KAAK8b,WAAW2C,EAAQC,GAAS,GAC/C1e,KAAKmb,UAAUuD,EAASpC,GACxBkC,EACExe,KAAKib,cAAcU,EAAM+C,IAAYF,CACxC,OAGHM,QAAQC,MAAM,0BAA2BpD,EAAM8C,EAAQC,EAE1D,CACF,CAEH,OAAOF,CACR,CAUDG,aAAa7a,EAAK7E,EAAOwc,GACD,iBAAX3X,EAAI,IAA8B,UAAXA,EAAI,IACpC9D,KAAKke,gBAAgBpa,EAAK7E,EAAOwc,GAC7B3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,KAErB,iBAAX6E,EAAI,IACpB9D,KAAKic,cAAcnY,EAAK7E,EAAOwc,GAC3B3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAOA,KAErB,iBAAX6E,EAAI,KACpB9D,KAAKuc,WAAWzY,EAAK7E,EAAOwc,GACxB3X,EAAI,KAAO7E,GACbe,KAAK2Z,eAAejZ,KAAK,CAAE+a,KAAMA,EAAMxc,MAAO8D,OAAO9D,KAG1D,CASDod,QAAQpd,EAAOwc,GACb,MAAM5B,EAAU7Z,KAAKgf,kBAAkB/f,EAAOwc,GAG5Czb,KAAKuB,OAAOH,MACZpB,KAAKuB,OAAOH,KAAK6d,SACjBjf,KAAKuB,OAAOH,KAAK6d,QAAQvf,MAEzBM,KAAKuB,OAAOH,KAAK6d,QAAQvf,KAAK,eAAgBma,GAEhD7Z,KAAK8Z,aAAc,EACnB9Z,KAAKuB,OAAOoZ,WAAWd,EACxB,CAUDmF,kBAAkB/f,EAAOwc,EAAMyD,EAAa,CAAA,GAC1C,IAAIC,EAAUD,EAIdjgB,EAAkB,WADlBA,EAAkB,SAAVA,GAA0BA,IACEA,EAEpC,IAAK,IAAIiH,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IACf,WAAZuV,EAAKvV,UACkB5C,IAArB6b,EAAQ1D,EAAKvV,MACfiZ,EAAQ1D,EAAKvV,IAAM,CAAA,GAEjBA,IAAMuV,EAAK1d,OAAS,EACtBohB,EAAUA,EAAQ1D,EAAKvV,IAEvBiZ,EAAQ1D,EAAKvV,IAAMjH,GAIzB,OAAOigB,CACR,CAKDhC,gBACE,MAAMrD,EAAU7Z,KAAKof,aAErB,KAAOpf,KAAKqd,iBAAiBrB,YAC3Bhc,KAAKqd,iBAAiBzc,YAAYZ,KAAKqd,iBAAiBrB,YAE1Dhc,KAAKqd,iBAAiB5c,YACpBoY,EAAU,MAAO,mBAAqB9F,KAAKC,UAAU6G,EAAS,KAAM,IAEvE,CAMDuF,aACE,MAAMvF,EAAU,CAAA,EAChB,IAAK,IAAI3T,EAAI,EAAGA,EAAIlG,KAAK2Z,eAAe5b,OAAQmI,IAC9ClG,KAAKgf,kBACHhf,KAAK2Z,eAAezT,GAAGjH,MACvBe,KAAK2Z,eAAezT,GAAGuV,KACvB5B,GAGJ,OAAOA,CACR,GEpxBU1a,EAAuBkgB,EACvBC,ECTN,MAKLrO,YAAYlR,EAAWwf,GACrBvf,KAAKD,UAAYA,EACjBC,KAAKuf,eAAiBA,GAAkB,MAExCvf,KAAKqR,EAAI,EACTrR,KAAKsR,EAAI,EACTtR,KAAKqW,QAAU,EACfrW,KAAKwf,QAAS,EAGdxf,KAAKgS,MAAQ3R,SAASC,cAAc,OACpCN,KAAKgS,MAAMiE,UAAY,cACvBjW,KAAKD,UAAUU,YAAYT,KAAKgS,MACjC,CAMDyN,YAAYpO,EAAGC,GACbtR,KAAKqR,EAAIvM,SAASuM,GAClBrR,KAAKsR,EAAIxM,SAASwM,EACnB,CAODoO,QAAQC,GACN,GAAIA,aAAmBtY,QAAS,CAC9B,KAAOrH,KAAKgS,MAAMgK,YAChBhc,KAAKgS,MAAMpR,YAAYZ,KAAKgS,MAAMgK,YAEpChc,KAAKgS,MAAMvR,YAAYkf,EAC7B,MAGM3f,KAAKgS,MAAMsE,UAAYqJ,CAE1B,CAODzM,KAAK0M,GAKH,QAJetc,IAAXsc,IACFA,GAAS,IAGI,IAAXA,EAAiB,CACnB,MAAM5J,EAAShW,KAAKgS,MAAMsC,aACpByB,EAAQ/V,KAAKgS,MAAMoC,YACnByL,EAAY7f,KAAKgS,MAAMrR,WAAW2T,aAClCwL,EAAW9f,KAAKgS,MAAMrR,WAAWyT,YAEvC,IAAID,EAAO,EACTE,EAAM,EAER,GAA2B,QAAvBrU,KAAKuf,eAA0B,CACjC,IAAIQ,GAAS,EACXC,GAAQ,EAENhgB,KAAKsR,EAAI0E,EAAShW,KAAKqW,UACzB2J,GAAQ,GAGNhgB,KAAKqR,EAAI0E,EAAQ+J,EAAW9f,KAAKqW,UACnC0J,GAAS,GAIT5L,EADE4L,EACK/f,KAAKqR,EAAI0E,EAET/V,KAAKqR,EAIZgD,EADE2L,EACIhgB,KAAKsR,EAAI0E,EAEThW,KAAKsR,CAErB,MACQ+C,EAAMrU,KAAKsR,EAAI0E,EACX3B,EAAM2B,EAAShW,KAAKqW,QAAUwJ,IAChCxL,EAAMwL,EAAY7J,EAAShW,KAAKqW,SAE9BhC,EAAMrU,KAAKqW,UACbhC,EAAMrU,KAAKqW,SAGblC,EAAOnU,KAAKqR,EACR8C,EAAO4B,EAAQ/V,KAAKqW,QAAUyJ,IAChC3L,EAAO2L,EAAW/J,EAAQ/V,KAAKqW,SAE7BlC,EAAOnU,KAAKqW,UACdlC,EAAOnU,KAAKqW,SAIhBrW,KAAKgS,MAAM1P,MAAM6R,KAAOA,EAAO,KAC/BnU,KAAKgS,MAAM1P,MAAM+R,IAAMA,EAAM,KAC7BrU,KAAKgS,MAAM1P,MAAM2d,WAAa,UAC9BjgB,KAAKwf,QAAS,CACpB,MACMxf,KAAKkgB,MAER,CAKDA,OACElgB,KAAKwf,QAAS,EACdxf,KAAKgS,MAAM1P,MAAM6R,KAAO,IACxBnU,KAAKgS,MAAM1P,MAAM+R,IAAM,IACvBrU,KAAKgS,MAAM1P,MAAM2d,WAAa,QAC/B,CAKDxgB,UACEO,KAAKgS,MAAMrR,WAAWC,YAAYZ,KAAKgS,MACxC,GDzHUkH,EAAgCiH,EAChCC,EDJN,MAAMA,EAUXC,gBAAgBxG,EAASyG,EAAkBC,GACzCtH,GAAa,EACbD,EAAasH,EACb,IAAIE,EAAcF,EAKlB,YAJkBhd,IAAdid,IACFC,EAAcF,EAAiBC,IAEjCH,EAAUK,MAAM5G,EAAS2G,EAAa,IAC/BvH,CACR,CAUDoH,aAAaxG,EAASyG,EAAkB7E,GACtC,IAAK,MAAMvX,KAAU2V,EACfpb,OAAOC,UAAUiF,eAAe/E,KAAKib,EAAS3V,IAChDkc,EAAUM,MAAMxc,EAAQ2V,EAASyG,EAAkB7E,EAGxD,CAWD4E,aAAanc,EAAQ2V,EAASyG,EAAkB7E,GAC9C,QAC+BnY,IAA7Bgd,EAAiBpc,SACYZ,IAA7Bgd,EAAiBK,QAGjB,YADAP,EAAUQ,cAAc1c,EAAQoc,EAAkB7E,GAIpD,IAAIoF,EAAkB3c,EAClB4c,GAAY,OAGexd,IAA7Bgd,EAAiBpc,SACYZ,IAA7Bgd,EAAiBK,UAOjBE,EAAkB,UAIlBC,EAAmD,WAAvCV,EAAUW,QAAQlH,EAAQ3V,KAOxC,IAAI8c,EAAeV,EAAiBO,GAChCC,QAAuCxd,IAA1B0d,EAAaC,WAC5BD,EAAeA,EAAaC,UAG9Bb,EAAUc,YACRhd,EACA2V,EACAyG,EACAO,EACAG,EACAvF,EAEH,CAYD4E,mBACEnc,EACA2V,EACAyG,EACAO,EACAG,EACAvF,GAEA,MAAM0F,EAAM,SAAUC,GACpBtC,QAAQC,MACN,KAAOqC,EAAUhB,EAAUiB,cAAc5F,EAAMvX,GAC/CgV,EAER,EAEUoI,EAAalB,EAAUW,QAAQlH,EAAQ3V,IACvCqd,EAAgBP,EAAaM,QAEbhe,IAAlBie,EAGqC,UAArCnB,EAAUW,QAAQQ,KAC0B,IAA5CA,EAAcrG,QAAQrB,EAAQ3V,KAE9Bid,EACE,+BACEjd,EADF,yBAIEkc,EAAUoB,MAAMD,GAChB,SACA1H,EAAQ3V,GACR,OAEJ+U,GAAa,GACW,WAAfqI,GAA+C,YAApBT,IACpCpF,EAAO5X,EAAmB4X,EAAMvX,GAChCkc,EAAUK,MACR5G,EAAQ3V,GACRoc,EAAiBO,GACjBpF,SAG6BnY,IAAxB0d,EAAkB,MAE3BG,EACE,8BACEjd,EACA,gBACAkc,EAAUoB,MAAM/iB,OAAOS,KAAK8hB,IAC5B,eACAM,EACA,MACAzH,EAAQ3V,GACR,KAEJ+U,GAAa,EAEhB,CAQDoH,eAAeoB,GACb,MAAM/K,SAAc+K,EAEpB,MAAa,WAAT/K,EACa,OAAX+K,EACK,OAELA,aAAkBC,QACb,UAELD,aAAkB1e,OACb,SAEL0e,aAAkBxe,OACb,SAELpE,MAAMC,QAAQ2iB,GACT,QAELA,aAAkBtjB,KACb,YAEemF,IAApBme,EAAOE,SACF,OAEuB,IAA5BF,EAAOG,iBACF,SAEF,SACW,WAATlL,EACF,SACW,YAATA,EACF,UACW,WAATA,EACF,cACWpT,IAAToT,EACF,YAEFA,CACR,CAQD2J,qBAAqBnc,EAAQ2V,EAAS4B,GACpC,MAAMoG,EAAczB,EAAU0B,cAAc5d,EAAQ2V,EAAS4B,GAAM,GAC7DsG,EAAe3B,EAAU0B,cAAc5d,EAAQ8U,EAAY,IAAI,GAKrE,IAAIgJ,EAEFA,OAD6B1e,IAA3Bue,EAAYI,WAEZ,OACA7B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,EAAQ,IAClD,6CACA2d,EAAYI,WACZ,SAEFF,EAAaG,UAXe,GAY5BL,EAAYK,SAAWH,EAAaG,SAGlC,OACA9B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,EAAQ,IAClD,uDACAkc,EAAUiB,cACRU,EAAatG,KACbsG,EAAaI,aACb,IAEKN,EAAYK,UAxBM,EA0BzB,mBACAL,EAAYM,aACZ,KACA/B,EAAUiB,cAAcQ,EAAYpG,KAAMvX,GAG1C,gCACAkc,EAAUoB,MAAM/iB,OAAOS,KAAK2a,IAC5BuG,EAAUiB,cAAc5F,EAAMvX,GAGlC4a,QAAQC,MACN,+BAAiC7a,EAAS,IAAM8d,EAChD9I,GAEFD,GAAa,CACd,CAYDoH,qBAAqBnc,EAAQ2V,EAAS4B,EAAM2G,GAAY,GACtD,IAAI5c,EAAM,IACN2c,EAAe,GACfE,EAAmB,GACvB,MAAMC,EAAkBpe,EAAOqe,cAC/B,IAAIN,EACJ,IAAK,MAAMO,KAAM3I,EAAS,CACxB,IAAIqI,EACJ,QAA6B5e,IAAzBuW,EAAQ2I,GAAIvB,WAAwC,IAAdmB,EAAoB,CAC5D,MAAMzd,EAASyb,EAAU0B,cACvB5d,EACA2V,EAAQ2I,GACR3e,EAAmB4X,EAAM+G,IAEvBhd,EAAMb,EAAOud,WACfC,EAAexd,EAAOwd,aACtBE,EAAmB1d,EAAO8W,KAC1BjW,EAAMb,EAAOud,SACbD,EAAatd,EAAOsd,WAE9B,MAC2D,IAA/CO,EAAGD,cAAcrH,QAAQoH,KAC3BL,EAAaO,GAEfN,EAAW9B,EAAUqC,oBAAoBve,EAAQse,GAC7Chd,EAAM0c,IACRC,EAAeK,EACfH,EAAmBre,EAAUyX,GAC7BjW,EAAM0c,EAGX,CACD,MAAO,CACLC,aAAcA,EACd1G,KAAM4G,EACNH,SAAU1c,EACVyc,WAAYA,EAEf,CASD5B,qBAAqB5E,EAAMvX,EAAQwe,EAAS,8BAC1C,IAAIC,EAAM,OAASD,EAAS,gBAC5B,IAAK,IAAIxc,EAAI,EAAGA,EAAIuV,EAAK1d,OAAQmI,IAAK,CACpC,IAAK,IAAI0c,EAAI,EAAGA,EAAI1c,EAAI,EAAG0c,IACzBD,GAAO,KAETA,GAAOlH,EAAKvV,GAAK,OAClB,CACD,IAAK,IAAI0c,EAAI,EAAGA,EAAInH,EAAK1d,OAAS,EAAG6kB,IACnCD,GAAO,KAETA,GAAOze,EAAS,KAChB,IAAK,IAAIgC,EAAI,EAAGA,EAAIuV,EAAK1d,OAAS,EAAGmI,IAAK,CACxC,IAAK,IAAI0c,EAAI,EAAGA,EAAInH,EAAK1d,OAASmI,EAAG0c,IACnCD,GAAO,KAETA,GAAO,KACR,CACD,OAAOA,EAAM,MACd,CAODtC,aAAaxG,GACX,OAAO9G,KAAKC,UAAU6G,GACnBgJ,QAAQ,+BAAgC,IACxCA,QAAQ,OAAQ,KACpB,CAmBDxC,2BAA2BpiB,EAAGC,GAC5B,GAAiB,IAAbD,EAAEF,OAAc,OAAOG,EAAEH,OAC7B,GAAiB,IAAbG,EAAEH,OAAc,OAAOE,EAAEF,OAE7B,MAAM+kB,EAAS,GAGf,IAAI5c,EAMA0c,EALJ,IAAK1c,EAAI,EAAGA,GAAKhI,EAAEH,OAAQmI,IACzB4c,EAAO5c,GAAK,CAACA,GAKf,IAAK0c,EAAI,EAAGA,GAAK3kB,EAAEF,OAAQ6kB,IACzBE,EAAO,GAAGF,GAAKA,EAIjB,IAAK1c,EAAI,EAAGA,GAAKhI,EAAEH,OAAQmI,IACzB,IAAK0c,EAAI,EAAGA,GAAK3kB,EAAEF,OAAQ6kB,IACrB1kB,EAAE6kB,OAAO7c,EAAI,IAAMjI,EAAE8kB,OAAOH,EAAI,GAClCE,EAAO5c,GAAG0c,GAAKE,EAAO5c,EAAI,GAAG0c,EAAI,GAEjCE,EAAO5c,GAAG0c,GAAKrd,KAAKC,IAClBsd,EAAO5c,EAAI,GAAG0c,EAAI,GAAK,EACvBrd,KAAKC,IACHsd,EAAO5c,GAAG0c,EAAI,GAAK,EACnBE,EAAO5c,EAAI,GAAG0c,GAAK,IAO7B,OAAOE,EAAO5kB,EAAEH,QAAQE,EAAEF,OAC3B,wBG7Xa,YAAQilB,GACtB,OASF,SAA4BA,GAC1B,IAAKC,EAAIC,EAAIC,GA6Bf,YAAqBH,GACnB,MAAMI,EAmCR,WACE,IAAIC,EAAI,WAER,OAAO,SAAUC,GACf,MAAMhG,EAASgG,EAAKle,WACpB,IAAK,IAAIc,EAAI,EAAGA,EAAIoX,EAAOvf,OAAQmI,IAAK,CACtCmd,GAAK/F,EAAOiG,WAAWrd,GACvB,IAAIP,EAAI,mBAAsB0d,EAC9BA,EAAI1d,IAAM,EACVA,GAAK0d,EACL1d,GAAK0d,EACLA,EAAI1d,IAAM,EACVA,GAAK0d,EACLA,GAAS,WAAJ1d,CACN,CACD,OAAmB,wBAAX0d,IAAM,EAChB,CACF,CApDeG,GAEb,IAAIP,EAAKG,EAAK,KACVF,EAAKE,EAAK,KACVD,EAAKC,EAAK,KAEd,IAAK,IAAIld,EAAI,EAAGA,EAAI8c,EAAKjlB,OAAQmI,IAC/B+c,GAAMG,EAAKJ,EAAK9c,IACZ+c,EAAK,IACPA,GAAM,GAERC,GAAME,EAAKJ,EAAK9c,IACZgd,EAAK,IACPA,GAAM,GAERC,GAAMC,EAAKJ,EAAK9c,IACZid,EAAK,IACPA,GAAM,GAIV,MAAO,CAACF,EAAIC,EAAIC,EAClB,CApDqBM,CAAST,GACxBU,EAAI,EAER,MAAMC,EAAc,KAClB,MAAMld,EAAI,QAAUwc,EAAS,uBAAJS,EAGzB,OAFAT,EAAKC,EACLA,EAAKC,EACGA,EAAK1c,GAAKid,EAAQ,EAAJjd,EAAQ,EAYhC,OATAkd,EAAOC,OAAS,IAAyB,WAAXD,IAE9BA,EAAOE,QAAU,IACfF,IAAyC,uBAAjB,QAAXA,IAAuB,GAEtCA,EAAOG,UAAY,OACnBH,EAAOX,KAAOA,EACdW,EAAOI,QAAU,MAEVJ,CACT,CA9BSK,CAAmBhB,EAAKjlB,OAASilB,EAAO,CAAC7kB,KAAK8lB,OACvD,8KNyegB,SAAaC,EAAeC,GAC1C,IAAIC,EAAUF,EAAKjO,UAAUrD,MAAM,KACnC,MAAMyR,EAAaF,EAAWvR,MAAM,KACpCwR,EAAUA,EAAQE,OAChBD,EAAWnK,QAAO,SAAUjE,GAC1B,OAAQmO,EAAQG,SAAStO,EAC1B,KAEHiO,EAAKjO,UAAYmO,EAAQvJ,KAAK,IAChC,eA8kBgB,SAAWvZ,EAAsByE,GAC/C,MAAMye,EAAW1e,EAAaC,GAC9B,IAAK,MAAOjE,EAAK7C,KAAUR,OAAOgmB,QAAQD,GACxCljB,EAAQgB,MAAMoiB,YAAY5iB,EAAK7C,EAEnC,uBAkWM,SACJ0lB,EACAC,EACAC,EACAC,GAGA,IAAIC,EAAY,EACZC,EAAM,EACNC,EAAON,EAAa5mB,OAAS,EAEjC,KAAOinB,GAAOC,GAAQF,EALA,KAK2B,CAC/C,MAAMG,EAAS3f,KAAKc,OAAO2e,EAAMC,GAAQ,GAEnCtJ,EAAOgJ,EAAaO,GAGpBC,EAAeP,OAFIthB,IAAXwhB,EAAuBnJ,EAAKkJ,GAASlJ,EAAKkJ,GAAOC,IAG/D,GAAoB,GAAhBK,EAEF,OAAOD,GACmB,GAAjBC,EAETH,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGlBH,GACD,CAED,OAAQ,CACV,sBAeM,SACJJ,EACAljB,EACAojB,EACAO,EACAR,GAGA,IAGIS,EACApmB,EACAqmB,EACAJ,EANAH,EAAY,EACZC,EAAM,EACNC,EAAON,EAAa5mB,OAAS,EAajC,IAPA6mB,EACgBthB,MAAdshB,EACIA,EACA,SAAU3mB,EAAWC,GACnB,OAAOD,GAAKC,EAAI,EAAID,EAAIC,GAAK,EAAI,CACnC,EAEC8mB,GAAOC,GAAQF,EAhBA,KAgB2B,CAQ/C,GANAG,EAAS3f,KAAKc,MAAM,IAAO4e,EAAOD,IAClCK,EAAYV,EAAapf,KAAKG,IAAI,EAAGwf,EAAS,IAAIL,GAClD5lB,EAAQ0lB,EAAaO,GAAQL,GAC7BS,EACEX,EAAapf,KAAKC,IAAImf,EAAa5mB,OAAS,EAAGmnB,EAAS,IAAIL,GAE7B,GAA7BD,EAAW3lB,EAAOwC,GAEpB,OAAOyjB,EACF,GACLN,EAAWS,EAAW5jB,GAAU,GAChCmjB,EAAW3lB,EAAOwC,GAAU,EAG5B,MAAyB,UAAlB2jB,EAA6B7f,KAAKG,IAAI,EAAGwf,EAAS,GAAKA,EACzD,GACLN,EAAW3lB,EAAOwC,GAAU,GAC5BmjB,EAAWU,EAAW7jB,GAAU,EAGhC,MAAyB,UAAlB2jB,EACHF,EACA3f,KAAKC,IAAImf,EAAa5mB,OAAS,EAAGmnB,EAAS,GAG3CN,EAAW3lB,EAAOwC,GAAU,EAE9BujB,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGpBH,GACD,CAGD,OAAQ,CACV,6HA3qCgB,SAAW9mB,EAAcC,GACvC,GAAID,EAAEF,SAAWG,EAAEH,OACjB,OAAO,EAGT,IAAK,IAAImI,EAAI,EAAGqf,EAAMtnB,EAAEF,OAAQmI,EAAIqf,EAAKrf,IACvC,GAAIjI,EAAEiI,IAAMhI,EAAEgI,GACZ,OAAO,EAIX,OAAO,CACT,6BAvOM,SAAUsf,EACdvnB,EACAC,EACAkF,GAAgB,GAIhB,IAAK,MAAM9E,KAAQL,EACjB,QAAgBqF,IAAZpF,EAAEI,GACJ,GAAgB,OAAZJ,EAAEI,IAAqC,iBAAZJ,EAAEI,GAE/B6E,EAAalF,EAAGC,EAAGI,EAAM8E,OACpB,CACL,MAAMqiB,EAAQxnB,EAAEK,GACVonB,EAAQxnB,EAAEI,GACZ4E,EAASuiB,IAAUviB,EAASwiB,IAC9BF,EAAcC,EAAOC,EAAOtiB,EAE/B,CAGP,YA8WgB,SAAQqe,EAAavf,GACnC,GAAIrD,MAAMC,QAAQ2iB,GAAS,CAEzB,MAAM8D,EAAM9D,EAAO1jB,OACnB,IAAK,IAAImI,EAAI,EAAGA,EAAIqf,EAAKrf,IACvBhE,EAASuf,EAAOvb,GAAIA,EAAGub,EAE1B,MAEC,IAAK,MAAM3f,KAAO2f,EACZhjB,OAAOC,UAAUiF,eAAe/E,KAAK6iB,EAAQ3f,IAC/CI,EAASuf,EAAO3f,GAAMA,EAAK2f,EAInC,oBAvFM,SAA0ByC,GAC9B,OAAOA,EAAK9L,wBAAwBjE,IACtC,qBAQM,SAA2B+P,GAC/B,OAAOA,EAAK9L,wBAAwBuN,KACtC,mBAQM,SAAyBzB,GAC7B,OAAOA,EAAK9L,wBAAwB/D,GACtC,iCAwsCE,MAAMuR,EAAQvlB,SAASC,cAAc,KACrCslB,EAAMtjB,MAAMyT,MAAQ,OACpB6P,EAAMtjB,MAAM0T,OAAS,QAErB,MAAM6P,EAAQxlB,SAASC,cAAc,OACrCulB,EAAMvjB,MAAMwjB,SAAW,WACvBD,EAAMvjB,MAAM+R,IAAM,MAClBwR,EAAMvjB,MAAM6R,KAAO,MACnB0R,EAAMvjB,MAAM2d,WAAa,SACzB4F,EAAMvjB,MAAMyT,MAAQ,QACpB8P,EAAMvjB,MAAM0T,OAAS,QACrB6P,EAAMvjB,MAAMyjB,SAAW,SACvBF,EAAMplB,YAAYmlB,GAElBvlB,SAASe,KAAKX,YAAYolB,GAC1B,MAAMG,EAAKJ,EAAMK,YACjBJ,EAAMvjB,MAAMyjB,SAAW,SACvB,IAAIG,EAAKN,EAAMK,YAOf,OANID,GAAME,IACRA,EAAKL,EAAMzR,aAGb/T,SAASe,KAAKR,YAAYilB,GAEnBG,EAAKE,CACd,uBAllCEjlB,EAA2B7B,OAAO6B,OAKlC,IAAIQ,EAA6B,KASjC,OARKR,IAEMA,EAAMQ,OACfA,EAASR,EAAMQ,OACNR,EAAMklB,aACf1kB,EAASR,EAAMklB,aAGX1kB,aAAkB4F,UAID,MAAnB5F,EAAOkgB,UAAuC,GAAnBlgB,EAAOkgB,WAEpClgB,EAASA,EAAOd,WACVc,aAAkB4F,UAKnB5F,EAXE,IAYX,YA5QM,SAAkBggB,GACtB,MAAM/K,SAAc+K,EAEpB,MAAa,WAAT/K,EACa,OAAX+K,EACK,OAELA,aAAkBC,QACb,UAELD,aAAkB1e,OACb,SAEL0e,aAAkBxe,OACb,SAELpE,MAAMC,QAAQ2iB,GACT,QAELA,aAAkBtjB,KACb,OAGF,SAEI,WAATuY,EACK,SAEI,YAATA,EACK,UAEI,WAATA,EACK,cAEIpT,IAAToT,EACK,YAGFA,CACT,cA8OgB,SAAUpV,EAAkBC,GAC1C,IAAI2iB,EAAa5iB,EAEjB,KAAO4iB,GAAM,CACX,GAAIA,IAAS3iB,EACX,OAAO,EACF,IAAI2iB,EAAKvjB,WAGd,OAAO,EAFPujB,EAAOA,EAAKvjB,UAIf,CAED,OAAO,CACT,yCAimBgB,SAAc1C,EAAQmoB,GACpC,IAAK,IAAIlgB,EAAI,EAAGA,EAAIjI,EAAEF,OAAQmI,IAAK,CACjC,MAAMmgB,EAAIpoB,EAAEiI,GACZ,IAAI0c,EACJ,IAAKA,EAAI1c,EAAG0c,EAAI,GAAKwD,EAAQC,EAAGpoB,EAAE2kB,EAAI,IAAM,EAAGA,IAC7C3kB,EAAE2kB,GAAK3kB,EAAE2kB,EAAI,GAEf3kB,EAAE2kB,GAAKyD,CACR,CACD,OAAOpoB,CACT,WAprCM,SAAiBgB,GACrB,GAAIA,aAAiBd,KACnB,OAAO,EACF,GAAI6E,EAAS/D,GAAQ,CAG1B,GADcwD,EAAamC,KAAK3F,GAE9B,OAAO,EACF,IAAKqnB,MAAMnoB,KAAKsiB,MAAMxhB,IAC3B,OAAO,CAEV,CAED,OAAO,CACT,sGAqrCM,SACJsnB,EACA1M,EACA3V,EACAsiB,EAAqB,CAAA,GAGrB,MAAMC,EAAY,SAAUnI,GAC1B,OAAOA,OACT,EAEMpb,EAAW,SAAUob,GACzB,OAAe,OAARA,GAA+B,iBAARA,CAChC,EAaA,IAAKpb,EAASqjB,GACZ,MAAM,IAAInU,MAAM,2CAGlB,IAAKlP,EAAS2W,GACZ,MAAM,IAAIzH,MAAM,uCAGlB,IAAKqU,EAAUviB,GACb,MAAM,IAAIkO,MAAM,sCAGlB,IAAKlP,EAASsjB,GACZ,MAAM,IAAIpU,MAAM,6CAOlB,MAeMsU,EAAY7M,EAAQ3V,GAEpByiB,EADezjB,EAASsjB,KA9Cd,SAAUlI,GACxB,IAAK,MAAMjN,KAAKiN,EACd,GAAI7f,OAAOC,UAAUiF,eAAe/E,KAAK0f,EAAKjN,GAC5C,OAAO,EAGX,OAAO,CACT,CAuCiDuV,CAAQJ,GACrBA,EAActiB,QAAUZ,EACtDujB,EAAgBF,EAAeA,EAAa1M,aAAU3W,EAK5D,QAAkBA,IAAdojB,EACF,OAGF,GAAyB,kBAAdA,EAMT,OALKxjB,EAASqjB,EAAYriB,MACxBqiB,EAAYriB,GAAU,SAGxBqiB,EAAYriB,GAAQ+V,QAAUyM,GAIhC,GAAkB,OAAdA,IAAuBxjB,EAASqjB,EAAYriB,IAAU,CAExD,IAAIuiB,EAAUE,GAGZ,OAFAJ,EAAYriB,GAAUzF,OAAO8I,OAAOof,EAIvC,CAED,IAAKzjB,EAASwjB,GACZ,OAOF,IAAIzM,GAAU,OAEY3W,IAAtBojB,EAAUzM,QACZA,EAAUyM,EAAUzM,aAGE3W,IAAlBujB,IACF5M,EAAU0M,EAAa1M,SA5DX,SAAUxY,EAAaoY,EAAc3V,GAC9ChB,EAASzB,EAAOyC,MACnBzC,EAAOyC,GAAU,IAGnB,MAAM4iB,EAAMjN,EAAQ3V,GACd6iB,EAAMtlB,EAAOyC,GACnB,IAAK,MAAM5F,KAAQwoB,EACbroB,OAAOC,UAAUiF,eAAe/E,KAAKkoB,EAAKxoB,KAC5CyoB,EAAIzoB,GAAQwoB,EAAIxoB,GAGtB,CAoDA0oB,CAAQT,EAAa1M,EAAS3V,GAC9BqiB,EAAYriB,GAAQ+V,QAAUA,CAChC,+BA3lBgB,SAAgB1I,EAAesM,GAC7C,GAAItM,EAAMgT,SAAS,QACjB,OAAOhT,EACF,GAAIA,EAAMgT,SAAS,OAAQ,CAChC,MAAM5d,EAAM4K,EACToB,OAAOpB,EAAM2J,QAAQ,KAAO,GAC5B2H,QAAQ,IAAK,IACbjQ,MAAM,KACT,MAAO,QAAUjM,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMkX,EAAU,GACzE,CAAM,CACL,MAAMlX,EAAMlC,EAAS8M,GACrB,OAAW,MAAP5K,EACK4K,EAEA,QAAU5K,EAAI9B,EAAI,IAAM8B,EAAI5B,EAAI,IAAM4B,EAAIzI,EAAI,IAAM2f,EAAU,GAExE,CACH,eA2DgB,SACdoJ,EACA9I,GAEA,GAAInb,EAASikB,GAAa,CACxB,IAAIC,EAAmBD,EACvB,GAAIjgB,EAAWkgB,GAAW,CACxB,MAAMvgB,EAAMugB,EACTvU,OAAO,GACPA,OAAO,EAAGuU,EAASnpB,OAAS,GAC5B6U,MAAM,KACN5T,KAAI,SAAUC,GACb,OAAO6F,SAAS7F,EAClB,IACFioB,EAAWliB,EAAS2B,EAAI,GAAIA,EAAI,GAAIA,EAAI,GACzC,CACD,IAA6B,IAAzBG,EAAWogB,GAAoB,CACjC,MAAMtT,EAAMhN,EAASsgB,GACfC,EAAkB,CACtBxhB,EAAGiO,EAAIjO,EACPC,EAAW,GAARgO,EAAIhO,EACPC,EAAGN,KAAKC,IAAI,EAAW,KAARoO,EAAI/N,IAEfuhB,EAAiB,CACrBzhB,EAAGiO,EAAIjO,EACPC,EAAGL,KAAKC,IAAI,EAAW,KAARoO,EAAIhO,GACnBC,EAAW,GAAR+N,EAAI/N,GAEHwhB,EAAiB3gB,EACrB0gB,EAAezhB,EACfyhB,EAAexhB,EACfwhB,EAAevhB,GAEXyhB,EAAkB5gB,EACtBygB,EAAgBxhB,EAChBwhB,EAAgBvhB,EAChBuhB,EAAgBthB,GAElB,MAAO,CACL0hB,WAAYL,EACZM,OAAQH,EACRI,UAAW,CACTF,WAAYD,EACZE,OAAQH,GAEVK,MAAO,CACLH,WAAYD,EACZE,OAAQH,GAGb,CACC,MAAO,CACLE,WAAYL,EACZM,OAAQN,EACRO,UAAW,CACTF,WAAYL,EACZM,OAAQN,GAEVQ,MAAO,CACLH,WAAYL,EACZM,OAAQN,GAIf,CACC,GAAI/I,EAAc,CA+BhB,MA9B+B,CAC7BoJ,WAAYN,EAAWM,YAAcpJ,EAAaoJ,WAClDC,OAAQP,EAAWO,QAAUrJ,EAAaqJ,OAC1CC,UAAWzkB,EAASikB,EAAWQ,WAC3B,CACED,OAAQP,EAAWQ,UACnBF,WAAYN,EAAWQ,WAEzB,CACEF,WACGN,EAAWQ,WAAaR,EAAWQ,UAAUF,YAC9CpJ,EAAasJ,UAAUF,WACzBC,OACGP,EAAWQ,WAAaR,EAAWQ,UAAUD,QAC9CrJ,EAAasJ,UAAUD,QAE/BE,MAAO1kB,EAASikB,EAAWS,OACvB,CACEF,OAAQP,EAAWS,MACnBH,WAAYN,EAAWS,OAEzB,CACEF,OACGP,EAAWS,OAAST,EAAWS,MAAMF,QACtCrJ,EAAauJ,MAAMF,OACrBD,WACGN,EAAWS,OAAST,EAAWS,MAAMH,YACtCpJ,EAAauJ,MAAMH,YAI9B,CA6BC,MA5B2B,CACzBA,WAAYN,EAAWM,iBAAcjkB,EACrCkkB,OAAQP,EAAWO,aAAUlkB,EAC7BmkB,UAAWzkB,EAASikB,EAAWQ,WAC3B,CACED,OAAQP,EAAWQ,UACnBF,WAAYN,EAAWQ,WAEzB,CACEF,WACGN,EAAWQ,WAAaR,EAAWQ,UAAUF,iBAC9CjkB,EACFkkB,OACGP,EAAWQ,WAAaR,EAAWQ,UAAUD,aAC9ClkB,GAERokB,MAAO1kB,EAASikB,EAAWS,OACvB,CACEF,OAAQP,EAAWS,MACnBH,WAAYN,EAAWS,OAEzB,CACEF,OACGP,EAAWS,OAAST,EAAWS,MAAMF,aAAWlkB,EACnDikB,WACGN,EAAWS,OAAST,EAAWS,MAAMH,iBAAejkB,GAMrE,mBApaM,SAAyBrC,GACxBA,IACHA,EAAQ7B,OAAO6B,OAGZA,IAEMA,EAAM0mB,eACf1mB,EAAM0mB,iBAGL1mB,EAAc2mB,aAAc,EAEjC,kCH7mBElM,KACGmM,GAEH,OAAOnqB,EAAiB,CAAS,EAAEge,KAASmM,EAC9C,uBGqDM,SAAUC,EAAmBC,GACjC,GAAIA,EACF,MAAqC,IAA9BA,EAAUC,iBAA0B,CACzC,MAAMC,EAAQF,EAAU/L,WACpBiM,IACFH,EAAmBG,GACnBF,EAAUnnB,YAAYqnB,GAEzB,CAEL,oBA6bgB,SAAgB/D,EAAeC,GAC7C,IAAIC,EAAUF,EAAKjO,UAAUrD,MAAM,KACnC,MAAMsV,EAAa/D,EAAWvR,MAAM,KACpCwR,EAAUA,EAAQlK,QAAO,SAAUjE,GACjC,OAAQiS,EAAW3D,SAAStO,EAC9B,IACAiO,EAAKjO,UAAYmO,EAAQvJ,KAAK,IAChC,kBA4kBgB,SAAcvZ,EAAsByE,GAClD,MAAMye,EAAW1e,EAAaC,GAC9B,IAAK,MAAMjE,KAAOrD,OAAOS,KAAKslB,GAC5BljB,EAAQgB,MAAM6lB,eAAermB,EAEjC,0BAqHgB,SACdsmB,EACAhhB,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EAA8B,CAEnE,MAAME,EAAW7I,OAAO8I,OAAOH,GAC/B,IAAK,IAAIlB,EAAI,EAAGA,EAAIkiB,EAAOrqB,OAAQmI,IAC7BzH,OAAOC,UAAUiF,eAAe/E,KAAKwI,EAAiBghB,EAAOliB,KACtB,iBAA9BkB,EAAgBghB,EAAOliB,MAChCoB,EAAS8gB,EAAOliB,IAAMiB,EAAaC,EAAgBghB,EAAOliB,MAIhE,OAAOoB,CACR,CACC,OAAO,IAEX,wBA5/BM,SACJ+gB,EACApqB,EACAC,EACAkF,GAAgB,GAGhB,GAAIvE,MAAMC,QAAQZ,GAChB,MAAM,IAAI2I,UAAU,0CAGtB,IAAK,IAAIN,EAAI,EAAGA,EAAI8hB,EAAMtqB,OAAQwI,IAAK,CACrC,MAAMjI,EAAO+pB,EAAM9hB,GACnB,GAAI9H,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,GAC1C,GAAIJ,EAAEI,IAASJ,EAAEI,GAAM2S,cAAgBxS,YACrB6E,IAAZrF,EAAEK,KACJL,EAAEK,GAAQ,IAERL,EAAEK,GAAM2S,cAAgBxS,OAC1BgF,EAAWxF,EAAEK,GAAOJ,EAAEI,IAAO,EAAO8E,GAEpCD,EAAalF,EAAGC,EAAGI,EAAM8E,OAEtB,IAAIvE,MAAMC,QAAQZ,EAAEI,IACzB,MAAM,IAAIuI,UAAU,0CAEpB1D,EAAalF,EAAGC,EAAGI,EAAM8E,EAC1B,CAEJ,CACD,OAAOnF,CACT,oBAjEM,SACJoqB,EACApqB,KACGqqB,GAEH,IAAKzpB,MAAMC,QAAQupB,GACjB,MAAM,IAAIjW,MAAM,wDAGlB,IAAK,MAAMmW,KAASD,EAClB,IAAK,IAAI/hB,EAAI,EAAGA,EAAI8hB,EAAMtqB,OAAQwI,IAAK,CACrC,MAAMjI,EAAO+pB,EAAM9hB,GACfgiB,GAAS9pB,OAAOC,UAAUiF,eAAe/E,KAAK2pB,EAAOjqB,KACvDL,EAAEK,GAAQiqB,EAAMjqB,GAEnB,CAEH,OAAOL,CACT,2BAgEM,SACJuqB,EACAvqB,EACAC,EACAkF,GAAgB,GAIhB,GAAIvE,MAAMC,QAAQZ,GAChB,MAAM,IAAI2I,UAAU,0CAGtB,IAAK,MAAMvI,KAAQJ,EACjB,GAAKO,OAAOC,UAAUiF,eAAe/E,KAAKV,EAAGI,KAGzCkqB,EAAejE,SAASjmB,GAI5B,GAAIJ,EAAEI,IAASJ,EAAEI,GAAM2S,cAAgBxS,YACrB6E,IAAZrF,EAAEK,KACJL,EAAEK,GAAQ,IAERL,EAAEK,GAAM2S,cAAgBxS,OAC1BgF,EAAWxF,EAAEK,GAAOJ,EAAEI,IAEtB6E,EAAalF,EAAGC,EAAGI,EAAM8E,QAEtB,GAAIvE,MAAMC,QAAQZ,EAAEI,IAAQ,CACjCL,EAAEK,GAAQ,GACV,IAAK,IAAI4H,EAAI,EAAGA,EAAIhI,EAAEI,GAAMP,OAAQmI,IAClCjI,EAAEK,GAAMoC,KAAKxC,EAAEI,GAAM4H,GAExB,MACC/C,EAAalF,EAAGC,EAAGI,EAAM8E,GAI7B,OAAOnF,CACT,aAkRM,SAAmBwqB,GACvB,IAAIC,GAAY,EAEhB,MAAO,KACAA,IACHA,GAAY,EACZC,uBAAsB,KACpBD,GAAY,EACZD,GAAI,IAEP,CAEL,wBAwoCgB,SAAQG,EAAWC,GACjC,IAAIC,EACCjqB,MAAMC,QAAQ+pB,KACjBA,EAAY,CAACA,IAEf,IAAK,MAAME,KAAUH,EACnB,GAAIG,EAAQ,CACVD,EAAYC,EAAOF,EAAU,IAC7B,IAAK,IAAI3iB,EAAI,EAAGA,EAAI2iB,EAAU9qB,OAAQmI,IAChC4iB,IACFA,EAAYA,EAAUD,EAAU3iB,KAGpC,QAAyB,IAAd4iB,EACT,KAEH,CAEH,OAAOA,CACT,4BAzrCErH,EACA3f,EACA7C,GAEA,OAAIwiB,EAAO3f,KAAS7C,IAClBwiB,EAAO3f,GAAO7C,GACP,EAIX"}