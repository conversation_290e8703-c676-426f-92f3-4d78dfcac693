/**
 * vis-util
 * https://github.com/visjs/vis-util
 *
 * utilitie collection for visjs
 *
 * @version 5.0.7
 * @date    2023-11-20T09:06:51.067Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n=function(t){try{return!!t()}catch(t){return!0}},r=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),i=r,o=Function.prototype,a=o.call,s=i&&o.bind.bind(a,a),c=i?s:function(t){return function(){return a.apply(t,arguments)}},u=Math.ceil,l=Math.floor,h=Math.trunc||function(t){var e=+t;return(e>0?l:u)(e)},f=function(t){var e=+t;return e!=e||0===e?0:h(e)},p=function(t){return t&&t.Math===Math&&t},v=p("object"==typeof globalThis&&globalThis)||p("object"==typeof window&&window)||p("object"==typeof self&&self)||p("object"==typeof t&&t)||function(){return this}()||t||Function("return this")(),d={exports:{}},g=v,y=Object.defineProperty,m=function(t,e){try{y(g,t,{value:e,configurable:!0,writable:!0})}catch(n){g[t]=e}return e},b="__core-js_shared__",w=v[b]||m(b,{}),k=w;(d.exports=function(t,e){return k[t]||(k[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.33.0",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.0/LICENSE",source:"https://github.com/zloirock/core-js"});var C,E,O=d.exports,F=function(t){return null==t},T=F,_=TypeError,S=function(t){if(T(t))throw new _("Can't call method on "+t);return t},A=S,P=Object,x=function(t){return P(A(t))},D=x,j=c({}.hasOwnProperty),I=Object.hasOwn||function(t,e){return j(D(t),e)},R=c,M=0,N=Math.random(),B=R(1..toString),L=function(t){return"Symbol("+(void 0===t?"":t)+")_"+B(++M+N,36)},z="undefined"!=typeof navigator&&String(navigator.userAgent)||"",H=v,W=z,q=H.process,Y=H.Deno,X=q&&q.versions||Y&&Y.version,V=X&&X.v8;V&&(E=(C=V.split("."))[0]>0&&C[0]<4?1:+(C[0]+C[1])),!E&&W&&(!(C=W.match(/Edge\/(\d+)/))||C[1]>=74)&&(C=W.match(/Chrome\/(\d+)/))&&(E=+C[1]);var U=E,G=U,Q=n,$=v.String,J=!!Object.getOwnPropertySymbols&&!Q((function(){var t=Symbol("symbol detection");return!$(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&G&&G<41})),K=J&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=O,tt=I,et=L,nt=J,rt=K,it=v.Symbol,ot=Z("wks"),at=rt?it.for||it:it&&it.withoutSetter||et,st=function(t){return tt(ot,t)||(ot[t]=nt&&tt(it,t)?it[t]:at("Symbol."+t)),ot[t]},ct={};ct[st("toStringTag")]="z";var ut="[object z]"===String(ct),lt="object"==typeof document&&document.all,ht={all:lt,IS_HTMLDDA:void 0===lt&&void 0!==lt},ft=ht.all,pt=ht.IS_HTMLDDA?function(t){return"function"==typeof t||t===ft}:function(t){return"function"==typeof t},vt=c,dt=vt({}.toString),gt=vt("".slice),yt=function(t){return gt(dt(t),8,-1)},mt=ut,bt=pt,wt=yt,kt=st("toStringTag"),Ct=Object,Et="Arguments"===wt(function(){return arguments}()),Ot=mt?wt:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ct(t),kt))?n:Et?wt(e):"Object"===(r=wt(e))&&bt(e.callee)?"Arguments":r},Ft=Ot,Tt=String,_t=function(t){if("Symbol"===Ft(t))throw new TypeError("Cannot convert a Symbol value to a string");return Tt(t)},St=c,At=f,Pt=_t,xt=S,Dt=St("".charAt),jt=St("".charCodeAt),It=St("".slice),Rt=function(t){return function(e,n){var r,i,o=Pt(xt(e)),a=At(n),s=o.length;return a<0||a>=s?t?"":void 0:(r=jt(o,a))<55296||r>56319||a+1===s||(i=jt(o,a+1))<56320||i>57343?t?Dt(o,a):r:t?It(o,a,a+2):i-56320+(r-55296<<10)+65536}},Mt={codeAt:Rt(!1),charAt:Rt(!0)},Nt=pt,Bt=v.WeakMap,Lt=Nt(Bt)&&/native code/.test(String(Bt)),zt=pt,Ht=ht.all,Wt=ht.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:zt(t)||t===Ht}:function(t){return"object"==typeof t?null!==t:zt(t)},qt=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),Yt={},Xt=Wt,Vt=v.document,Ut=Xt(Vt)&&Xt(Vt.createElement),Gt=function(t){return Ut?Vt.createElement(t):{}},Qt=Gt,$t=!qt&&!n((function(){return 7!==Object.defineProperty(Qt("div"),"a",{get:function(){return 7}}).a})),Jt=qt&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Kt=Wt,Zt=String,te=TypeError,ee=function(t){if(Kt(t))return t;throw new te(Zt(t)+" is not an object")},ne=r,re=Function.prototype.call,ie=ne?re.bind(re):function(){return re.apply(re,arguments)},oe={},ae=oe,se=v,ce=pt,ue=function(t){return ce(t)?t:void 0},le=function(t,e){return arguments.length<2?ue(ae[t])||ue(se[t]):ae[t]&&ae[t][e]||se[t]&&se[t][e]},he=c({}.isPrototypeOf),fe=le,pe=pt,ve=he,de=Object,ge=K?function(t){return"symbol"==typeof t}:function(t){var e=fe("Symbol");return pe(e)&&ve(e.prototype,de(t))},ye=String,me=function(t){try{return ye(t)}catch(t){return"Object"}},be=pt,we=me,ke=TypeError,Ce=function(t){if(be(t))return t;throw new ke(we(t)+" is not a function")},Ee=Ce,Oe=F,Fe=function(t,e){var n=t[e];return Oe(n)?void 0:Ee(n)},Te=ie,_e=pt,Se=Wt,Ae=TypeError,Pe=ie,xe=Wt,De=ge,je=Fe,Ie=function(t,e){var n,r;if("string"===e&&_e(n=t.toString)&&!Se(r=Te(n,t)))return r;if(_e(n=t.valueOf)&&!Se(r=Te(n,t)))return r;if("string"!==e&&_e(n=t.toString)&&!Se(r=Te(n,t)))return r;throw new Ae("Can't convert object to primitive value")},Re=TypeError,Me=st("toPrimitive"),Ne=function(t,e){if(!xe(t)||De(t))return t;var n,r=je(t,Me);if(r){if(void 0===e&&(e="default"),n=Pe(r,t,e),!xe(n)||De(n))return n;throw new Re("Can't convert object to primitive value")}return void 0===e&&(e="number"),Ie(t,e)},Be=ge,Le=function(t){var e=Ne(t,"string");return Be(e)?e:e+""},ze=qt,He=$t,We=Jt,qe=ee,Ye=Le,Xe=TypeError,Ve=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,Ge="enumerable",Qe="configurable",$e="writable";Yt.f=ze?We?function(t,e,n){if(qe(t),e=Ye(e),qe(n),"function"==typeof t&&"prototype"===e&&"value"in n&&$e in n&&!n[$e]){var r=Ue(t,e);r&&r[$e]&&(t[e]=n.value,n={configurable:Qe in n?n[Qe]:r[Qe],enumerable:Ge in n?n[Ge]:r[Ge],writable:!1})}return Ve(t,e,n)}:Ve:function(t,e,n){if(qe(t),e=Ye(e),qe(n),He)try{return Ve(t,e,n)}catch(t){}if("get"in n||"set"in n)throw new Xe("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var Je,Ke,Ze,tn=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},en=Yt,nn=tn,rn=qt?function(t,e,n){return en.f(t,e,nn(1,n))}:function(t,e,n){return t[e]=n,t},on=L,an=O("keys"),sn=function(t){return an[t]||(an[t]=on(t))},cn={},un=Lt,ln=v,hn=Wt,fn=rn,pn=I,vn=w,dn=sn,gn=cn,yn="Object already initialized",mn=ln.TypeError,bn=ln.WeakMap;if(un||vn.state){var wn=vn.state||(vn.state=new bn);wn.get=wn.get,wn.has=wn.has,wn.set=wn.set,Je=function(t,e){if(wn.has(t))throw new mn(yn);return e.facade=t,wn.set(t,e),e},Ke=function(t){return wn.get(t)||{}},Ze=function(t){return wn.has(t)}}else{var kn=dn("state");gn[kn]=!0,Je=function(t,e){if(pn(t,kn))throw new mn(yn);return e.facade=t,fn(t,kn,e),e},Ke=function(t){return pn(t,kn)?t[kn]:{}},Ze=function(t){return pn(t,kn)}}var Cn={set:Je,get:Ke,has:Ze,enforce:function(t){return Ze(t)?Ke(t):Je(t,{})},getterFor:function(t){return function(e){var n;if(!hn(e)||(n=Ke(e)).type!==t)throw new mn("Incompatible receiver, "+t+" required");return n}}},En=r,On=Function.prototype,Fn=On.apply,Tn=On.call,_n="object"==typeof Reflect&&Reflect.apply||(En?Tn.bind(Fn):function(){return Tn.apply(Fn,arguments)}),Sn=yt,An=c,Pn=function(t){if("Function"===Sn(t))return An(t)},xn={},Dn={},jn={}.propertyIsEnumerable,In=Object.getOwnPropertyDescriptor,Rn=In&&!jn.call({1:2},1);Dn.f=Rn?function(t){var e=In(this,t);return!!e&&e.enumerable}:jn;var Mn=n,Nn=yt,Bn=Object,Ln=c("".split),zn=Mn((function(){return!Bn("z").propertyIsEnumerable(0)}))?function(t){return"String"===Nn(t)?Ln(t,""):Bn(t)}:Bn,Hn=zn,Wn=S,qn=function(t){return Hn(Wn(t))},Yn=qt,Xn=ie,Vn=Dn,Un=tn,Gn=qn,Qn=Le,$n=I,Jn=$t,Kn=Object.getOwnPropertyDescriptor;xn.f=Yn?Kn:function(t,e){if(t=Gn(t),e=Qn(e),Jn)try{return Kn(t,e)}catch(t){}if($n(t,e))return Un(!Xn(Vn.f,t,e),t[e])};var Zn=n,tr=pt,er=/#|\.prototype\./,nr=function(t,e){var n=ir[rr(t)];return n===ar||n!==or&&(tr(e)?Zn(e):!!e)},rr=nr.normalize=function(t){return String(t).replace(er,".").toLowerCase()},ir=nr.data={},or=nr.NATIVE="N",ar=nr.POLYFILL="P",sr=nr,cr=Ce,ur=r,lr=Pn(Pn.bind),hr=function(t,e){return cr(t),void 0===e?t:ur?lr(t,e):function(){return t.apply(e,arguments)}},fr=v,pr=_n,vr=Pn,dr=pt,gr=xn.f,yr=sr,mr=oe,br=hr,wr=rn,kr=I,Cr=function(t){var e=function(n,r,i){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(n);case 2:return new t(n,r)}return new t(n,r,i)}return pr(t,this,arguments)};return e.prototype=t.prototype,e},Er=function(t,e){var n,r,i,o,a,s,c,u,l,h=t.target,f=t.global,p=t.stat,v=t.proto,d=f?fr:p?fr[h]:(fr[h]||{}).prototype,g=f?mr:mr[h]||wr(mr,h,{})[h],y=g.prototype;for(o in e)r=!(n=yr(f?o:h+(p?".":"#")+o,t.forced))&&d&&kr(d,o),s=g[o],r&&(c=t.dontCallGetSet?(l=gr(d,o))&&l.value:d[o]),a=r&&c?c:e[o],r&&typeof s==typeof a||(u=t.bind&&r?br(a,fr):t.wrap&&r?Cr(a):v&&dr(a)?vr(a):a,(t.sham||a&&a.sham||s&&s.sham)&&wr(u,"sham",!0),wr(g,o,u),v&&(kr(mr,i=h+"Prototype")||wr(mr,i,{}),wr(mr[i],o,a),t.real&&y&&(n||!y[o])&&wr(y,o,a)))},Or=qt,Fr=I,Tr=Function.prototype,_r=Or&&Object.getOwnPropertyDescriptor,Sr=Fr(Tr,"name"),Ar={EXISTS:Sr,PROPER:Sr&&"something"===function(){}.name,CONFIGURABLE:Sr&&(!Or||Or&&_r(Tr,"name").configurable)},Pr={},xr=f,Dr=Math.max,jr=Math.min,Ir=function(t,e){var n=xr(t);return n<0?Dr(n+e,0):jr(n,e)},Rr=f,Mr=Math.min,Nr=function(t){return t>0?Mr(Rr(t),9007199254740991):0},Br=function(t){return Nr(t.length)},Lr=qn,zr=Ir,Hr=Br,Wr=function(t){return function(e,n,r){var i,o=Lr(e),a=Hr(o),s=zr(r,a);if(t&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((t||s in o)&&o[s]===n)return t||s||0;return!t&&-1}},qr={includes:Wr(!0),indexOf:Wr(!1)},Yr=I,Xr=qn,Vr=qr.indexOf,Ur=cn,Gr=c([].push),Qr=function(t,e){var n,r=Xr(t),i=0,o=[];for(n in r)!Yr(Ur,n)&&Yr(r,n)&&Gr(o,n);for(;e.length>i;)Yr(r,n=e[i++])&&(~Vr(o,n)||Gr(o,n));return o},$r=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Jr=Qr,Kr=$r,Zr=Object.keys||function(t){return Jr(t,Kr)},ti=qt,ei=Jt,ni=Yt,ri=ee,ii=qn,oi=Zr;Pr.f=ti&&!ei?Object.defineProperties:function(t,e){ri(t);for(var n,r=ii(e),i=oi(e),o=i.length,a=0;o>a;)ni.f(t,n=i[a++],r[n]);return t};var ai,si=le("document","documentElement"),ci=ee,ui=Pr,li=$r,hi=cn,fi=si,pi=Gt,vi="prototype",di="script",gi=sn("IE_PROTO"),yi=function(){},mi=function(t){return"<"+di+">"+t+"</"+di+">"},bi=function(t){t.write(mi("")),t.close();var e=t.parentWindow.Object;return t=null,e},wi=function(){try{ai=new ActiveXObject("htmlfile")}catch(t){}var t,e,n;wi="undefined"!=typeof document?document.domain&&ai?bi(ai):(e=pi("iframe"),n="java"+di+":",e.style.display="none",fi.appendChild(e),e.src=String(n),(t=e.contentWindow.document).open(),t.write(mi("document.F=Object")),t.close(),t.F):bi(ai);for(var r=li.length;r--;)delete wi[vi][li[r]];return wi()};hi[gi]=!0;var ki,Ci,Ei,Oi=Object.create||function(t,e){var n;return null!==t?(yi[vi]=ci(t),n=new yi,yi[vi]=null,n[gi]=t):n=wi(),void 0===e?n:ui.f(n,e)},Fi=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Ti=I,_i=pt,Si=x,Ai=Fi,Pi=sn("IE_PROTO"),xi=Object,Di=xi.prototype,ji=Ai?xi.getPrototypeOf:function(t){var e=Si(t);if(Ti(e,Pi))return e[Pi];var n=e.constructor;return _i(n)&&e instanceof n?n.prototype:e instanceof xi?Di:null},Ii=rn,Ri=function(t,e,n,r){return r&&r.enumerable?t[e]=n:Ii(t,e,n),t},Mi=n,Ni=pt,Bi=Wt,Li=Oi,zi=ji,Hi=Ri,Wi=st("iterator"),qi=!1;[].keys&&("next"in(Ei=[].keys())?(Ci=zi(zi(Ei)))!==Object.prototype&&(ki=Ci):qi=!0);var Yi=!Bi(ki)||Mi((function(){var t={};return ki[Wi].call(t)!==t}));Ni((ki=Yi?{}:Li(ki))[Wi])||Hi(ki,Wi,(function(){return this}));var Xi={IteratorPrototype:ki,BUGGY_SAFARI_ITERATORS:qi},Vi=Ot,Ui=ut?{}.toString:function(){return"[object "+Vi(this)+"]"},Gi=ut,Qi=Yt.f,$i=rn,Ji=I,Ki=Ui,Zi=st("toStringTag"),to=function(t,e,n,r){if(t){var i=n?t:t.prototype;Ji(i,Zi)||Qi(i,Zi,{configurable:!0,value:e}),r&&!Gi&&$i(i,"toString",Ki)}},eo={},no=Xi.IteratorPrototype,ro=Oi,io=tn,oo=to,ao=eo,so=function(){return this},co=Er,uo=ie,lo=Ar,ho=function(t,e,n,r){var i=e+" Iterator";return t.prototype=ro(no,{next:io(+!r,n)}),oo(t,i,!1,!0),ao[i]=so,t},fo=ji,po=to,vo=Ri,go=eo,yo=Xi,mo=lo.PROPER,bo=yo.BUGGY_SAFARI_ITERATORS,wo=st("iterator"),ko="keys",Co="values",Eo="entries",Oo=function(){return this},Fo=function(t,e,n,r,i,o,a){ho(n,e,r);var s,c,u,l=function(t){if(t===i&&d)return d;if(!bo&&t&&t in p)return p[t];switch(t){case ko:case Co:case Eo:return function(){return new n(this,t)}}return function(){return new n(this)}},h=e+" Iterator",f=!1,p=t.prototype,v=p[wo]||p["@@iterator"]||i&&p[i],d=!bo&&v||l(i),g="Array"===e&&p.entries||v;if(g&&(s=fo(g.call(new t)))!==Object.prototype&&s.next&&(po(s,h,!0,!0),go[h]=Oo),mo&&i===Co&&v&&v.name!==Co&&(f=!0,d=function(){return uo(v,this)}),i)if(c={values:l(Co),keys:o?d:l(ko),entries:l(Eo)},a)for(u in c)(bo||f||!(u in p))&&vo(p,u,c[u]);else co({target:e,proto:!0,forced:bo||f},c);return a&&p[wo]!==d&&vo(p,wo,d,{name:i}),go[e]=d,c},To=function(t,e){return{value:t,done:e}},_o=Mt.charAt,So=_t,Ao=Cn,Po=Fo,xo=To,Do="String Iterator",jo=Ao.set,Io=Ao.getterFor(Do);Po(String,"String",(function(t){jo(this,{type:Do,string:So(t),index:0})}),(function(){var t,e=Io(this),n=e.string,r=e.index;return r>=n.length?xo(void 0,!0):(t=_o(n,r),e.index+=t.length,xo(t,!1))}));var Ro=ie,Mo=ee,No=Fe,Bo=ee,Lo=function(t,e,n){var r,i;Mo(t);try{if(!(r=No(t,"return"))){if("throw"===e)throw n;return n}r=Ro(r,t)}catch(t){i=!0,r=t}if("throw"===e)throw n;if(i)throw r;return Mo(r),n},zo=eo,Ho=st("iterator"),Wo=Array.prototype,qo=pt,Yo=w,Xo=c(Function.toString);qo(Yo.inspectSource)||(Yo.inspectSource=function(t){return Xo(t)});var Vo=Yo.inspectSource,Uo=c,Go=n,Qo=pt,$o=Ot,Jo=Vo,Ko=function(){},Zo=[],ta=le("Reflect","construct"),ea=/^\s*(?:class|function)\b/,na=Uo(ea.exec),ra=!ea.test(Ko),ia=function(t){if(!Qo(t))return!1;try{return ta(Ko,Zo,t),!0}catch(t){return!1}},oa=function(t){if(!Qo(t))return!1;switch($o(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ra||!!na(ea,Jo(t))}catch(t){return!0}};oa.sham=!0;var aa=!ta||Go((function(){var t;return ia(ia.call)||!ia(Object)||!ia((function(){t=!0}))||t}))?oa:ia,sa=Le,ca=Yt,ua=tn,la=function(t,e,n){var r=sa(e);r in t?ca.f(t,r,ua(0,n)):t[r]=n},ha=Ot,fa=Fe,pa=F,va=eo,da=st("iterator"),ga=function(t){if(!pa(t))return fa(t,da)||fa(t,"@@iterator")||va[ha(t)]},ya=ie,ma=Ce,ba=ee,wa=me,ka=ga,Ca=TypeError,Ea=hr,Oa=ie,Fa=x,Ta=function(t,e,n,r){try{return r?e(Bo(n)[0],n[1]):e(n)}catch(e){Lo(t,"throw",e)}},_a=function(t){return void 0!==t&&(zo.Array===t||Wo[Ho]===t)},Sa=aa,Aa=Br,Pa=la,xa=function(t,e){var n=arguments.length<2?ka(t):e;if(ma(n))return ba(ya(n,t));throw new Ca(wa(t)+" is not iterable")},Da=ga,ja=Array,Ia=st("iterator"),Ra=!1;try{var Ma=0,Na={next:function(){return{done:!!Ma++}},return:function(){Ra=!0}};Na[Ia]=function(){return this},Array.from(Na,(function(){throw 2}))}catch(t){}var Ba=function(t){var e=Fa(t),n=Sa(this),r=arguments.length,i=r>1?arguments[1]:void 0,o=void 0!==i;o&&(i=Ea(i,r>2?arguments[2]:void 0));var a,s,c,u,l,h,f=Da(e),p=0;if(!f||this===ja&&_a(f))for(a=Aa(e),s=n?new this(a):ja(a);a>p;p++)h=o?i(e[p],p):e[p],Pa(s,p,h);else for(l=(u=xa(e,f)).next,s=n?new this:[];!(c=Oa(l,u)).done;p++)h=o?Ta(u,i,[c.value,p],!0):c.value,Pa(s,p,h);return s.length=p,s},La=function(t,e){try{if(!e&&!Ra)return!1}catch(t){return!1}var n=!1;try{var r={};r[Ia]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n};Er({target:"Array",stat:!0,forced:!La((function(t){Array.from(t)}))},{from:Ba});var za=oe.Array.from,Ha=e(za),Wa=qn,qa=eo,Ya=Cn;Yt.f;var Xa=Fo,Va=To,Ua="Array Iterator",Ga=Ya.set,Qa=Ya.getterFor(Ua);Xa(Array,"Array",(function(t,e){Ga(this,{type:Ua,target:Wa(t),index:0,kind:e})}),(function(){var t=Qa(this),e=t.target,n=t.kind,r=t.index++;if(!e||r>=e.length)return t.target=void 0,Va(void 0,!0);switch(n){case"keys":return Va(r,!1);case"values":return Va(e[r],!1)}return Va([r,e[r]],!1)}),"values"),qa.Arguments=qa.Array;var $a=ga,Ja={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ka=v,Za=Ot,ts=rn,es=eo,ns=st("toStringTag");for(var rs in Ja){var is=Ka[rs],os=is&&is.prototype;os&&Za(os)!==ns&&ts(os,ns,rs),es[rs]=es.Array}var as=$a,ss=e(as),cs=e(as),us=yt,ls=Array.isArray||function(t){return"Array"===us(t)},hs=TypeError,fs=function(t){if(t>9007199254740991)throw hs("Maximum allowed index exceeded");return t},ps=ls,vs=aa,ds=Wt,gs=st("species"),ys=Array,ms=function(t){var e;return ps(t)&&(e=t.constructor,(vs(e)&&(e===ys||ps(e.prototype))||ds(e)&&null===(e=e[gs]))&&(e=void 0)),void 0===e?ys:e},bs=function(t,e){return new(ms(t))(0===e?0:e)},ws=n,ks=U,Cs=st("species"),Es=function(t){return ks>=51||!ws((function(){var e=[];return(e.constructor={})[Cs]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Os=Er,Fs=n,Ts=ls,_s=Wt,Ss=x,As=Br,Ps=fs,xs=la,Ds=bs,js=Es,Is=U,Rs=st("isConcatSpreadable"),Ms=Is>=51||!Fs((function(){var t=[];return t[Rs]=!1,t.concat()[0]!==t})),Ns=function(t){if(!_s(t))return!1;var e=t[Rs];return void 0!==e?!!e:Ts(t)};Os({target:"Array",proto:!0,arity:1,forced:!Ms||!js("concat")},{concat:function(t){var e,n,r,i,o,a=Ss(this),s=Ds(a,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(Ns(o=-1===e?a:arguments[e]))for(i=As(o),Ps(c+i),n=0;n<i;n++,c++)n in o&&xs(s,c,o[n]);else Ps(c+1),xs(s,c++,o);return s.length=c,s}});var Bs={},Ls=Qr,zs=$r.concat("length","prototype");Bs.f=Object.getOwnPropertyNames||function(t){return Ls(t,zs)};var Hs={},Ws=Ir,qs=Br,Ys=la,Xs=Array,Vs=Math.max,Us=yt,Gs=qn,Qs=Bs.f,$s=function(t,e,n){for(var r=qs(t),i=Ws(e,r),o=Ws(void 0===n?r:n,r),a=Xs(Vs(o-i,0)),s=0;i<o;i++,s++)Ys(a,s,t[i]);return a.length=s,a},Js="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Hs.f=function(t){return Js&&"Window"===Us(t)?function(t){try{return Qs(t)}catch(t){return $s(Js)}}(t):Qs(Gs(t))};var Ks={};Ks.f=Object.getOwnPropertySymbols;var Zs=Yt,tc={},ec=st;tc.f=ec;var nc=oe,rc=I,ic=tc,oc=Yt.f,ac=function(t){var e=nc.Symbol||(nc.Symbol={});rc(e,t)||oc(e,t,{value:ic.f(t)})},sc=ie,cc=le,uc=st,lc=Ri,hc=function(){var t=cc("Symbol"),e=t&&t.prototype,n=e&&e.valueOf,r=uc("toPrimitive");e&&!e[r]&&lc(e,r,(function(t){return sc(n,this)}),{arity:1})},fc=hr,pc=zn,vc=x,dc=Br,gc=bs,yc=c([].push),mc=function(t){var e=1===t,n=2===t,r=3===t,i=4===t,o=6===t,a=7===t,s=5===t||o;return function(c,u,l,h){for(var f,p,v=vc(c),d=pc(v),g=fc(u,l),y=dc(d),m=0,b=h||gc,w=e?b(c,y):n||a?b(c,0):void 0;y>m;m++)if((s||m in d)&&(p=g(f=d[m],m,v),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return f;case 6:return m;case 2:yc(w,f)}else switch(t){case 4:return!1;case 7:yc(w,f)}return o?-1:r||i?i:w}},bc={forEach:mc(0),map:mc(1),filter:mc(2),some:mc(3),every:mc(4),find:mc(5),findIndex:mc(6),filterReject:mc(7)},wc=Er,kc=v,Cc=ie,Ec=c,Oc=qt,Fc=J,Tc=n,_c=I,Sc=he,Ac=ee,Pc=qn,xc=Le,Dc=_t,jc=tn,Ic=Oi,Rc=Zr,Mc=Bs,Nc=Hs,Bc=Ks,Lc=xn,zc=Yt,Hc=Pr,Wc=Dn,qc=Ri,Yc=function(t,e,n){return Zs.f(t,e,n)},Xc=O,Vc=cn,Uc=L,Gc=st,Qc=tc,$c=ac,Jc=hc,Kc=to,Zc=Cn,tu=bc.forEach,eu=sn("hidden"),nu="Symbol",ru="prototype",iu=Zc.set,ou=Zc.getterFor(nu),au=Object[ru],su=kc.Symbol,cu=su&&su[ru],uu=kc.RangeError,lu=kc.TypeError,hu=kc.QObject,fu=Lc.f,pu=zc.f,vu=Nc.f,du=Wc.f,gu=Ec([].push),yu=Xc("symbols"),mu=Xc("op-symbols"),bu=Xc("wks"),wu=!hu||!hu[ru]||!hu[ru].findChild,ku=function(t,e,n){var r=fu(au,e);r&&delete au[e],pu(t,e,n),r&&t!==au&&pu(au,e,r)},Cu=Oc&&Tc((function(){return 7!==Ic(pu({},"a",{get:function(){return pu(this,"a",{value:7}).a}})).a}))?ku:pu,Eu=function(t,e){var n=yu[t]=Ic(cu);return iu(n,{type:nu,tag:t,description:e}),Oc||(n.description=e),n},Ou=function(t,e,n){t===au&&Ou(mu,e,n),Ac(t);var r=xc(e);return Ac(n),_c(yu,r)?(n.enumerable?(_c(t,eu)&&t[eu][r]&&(t[eu][r]=!1),n=Ic(n,{enumerable:jc(0,!1)})):(_c(t,eu)||pu(t,eu,jc(1,{})),t[eu][r]=!0),Cu(t,r,n)):pu(t,r,n)},Fu=function(t,e){Ac(t);var n=Pc(e),r=Rc(n).concat(Au(n));return tu(r,(function(e){Oc&&!Cc(Tu,n,e)||Ou(t,e,n[e])})),t},Tu=function(t){var e=xc(t),n=Cc(du,this,e);return!(this===au&&_c(yu,e)&&!_c(mu,e))&&(!(n||!_c(this,e)||!_c(yu,e)||_c(this,eu)&&this[eu][e])||n)},_u=function(t,e){var n=Pc(t),r=xc(e);if(n!==au||!_c(yu,r)||_c(mu,r)){var i=fu(n,r);return!i||!_c(yu,r)||_c(n,eu)&&n[eu][r]||(i.enumerable=!0),i}},Su=function(t){var e=vu(Pc(t)),n=[];return tu(e,(function(t){_c(yu,t)||_c(Vc,t)||gu(n,t)})),n},Au=function(t){var e=t===au,n=vu(e?mu:Pc(t)),r=[];return tu(n,(function(t){!_c(yu,t)||e&&!_c(au,t)||gu(r,yu[t])})),r};Fc||(qc(cu=(su=function(){if(Sc(cu,this))throw new lu("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?Dc(arguments[0]):void 0,e=Uc(t),n=function(t){this===au&&Cc(n,mu,t),_c(this,eu)&&_c(this[eu],e)&&(this[eu][e]=!1);var r=jc(1,t);try{Cu(this,e,r)}catch(t){if(!(t instanceof uu))throw t;ku(this,e,r)}};return Oc&&wu&&Cu(au,e,{configurable:!0,set:n}),Eu(e,t)})[ru],"toString",(function(){return ou(this).tag})),qc(su,"withoutSetter",(function(t){return Eu(Uc(t),t)})),Wc.f=Tu,zc.f=Ou,Hc.f=Fu,Lc.f=_u,Mc.f=Nc.f=Su,Bc.f=Au,Qc.f=function(t){return Eu(Gc(t),t)},Oc&&Yc(cu,"description",{configurable:!0,get:function(){return ou(this).description}})),wc({global:!0,constructor:!0,wrap:!0,forced:!Fc,sham:!Fc},{Symbol:su}),tu(Rc(bu),(function(t){$c(t)})),wc({target:nu,stat:!0,forced:!Fc},{useSetter:function(){wu=!0},useSimple:function(){wu=!1}}),wc({target:"Object",stat:!0,forced:!Fc,sham:!Oc},{create:function(t,e){return void 0===e?Ic(t):Fu(Ic(t),e)},defineProperty:Ou,defineProperties:Fu,getOwnPropertyDescriptor:_u}),wc({target:"Object",stat:!0,forced:!Fc},{getOwnPropertyNames:Su}),Jc(),Kc(su,nu),Vc[eu]=!0;var Pu=J&&!!Symbol.for&&!!Symbol.keyFor,xu=Er,Du=le,ju=I,Iu=_t,Ru=O,Mu=Pu,Nu=Ru("string-to-symbol-registry"),Bu=Ru("symbol-to-string-registry");xu({target:"Symbol",stat:!0,forced:!Mu},{for:function(t){var e=Iu(t);if(ju(Nu,e))return Nu[e];var n=Du("Symbol")(e);return Nu[e]=n,Bu[n]=e,n}});var Lu=Er,zu=I,Hu=ge,Wu=me,qu=Pu,Yu=O("symbol-to-string-registry");Lu({target:"Symbol",stat:!0,forced:!qu},{keyFor:function(t){if(!Hu(t))throw new TypeError(Wu(t)+" is not a symbol");if(zu(Yu,t))return Yu[t]}});var Xu=c([].slice),Vu=ls,Uu=pt,Gu=yt,Qu=_t,$u=c([].push),Ju=Er,Ku=le,Zu=_n,tl=ie,el=c,nl=n,rl=pt,il=ge,ol=Xu,al=function(t){if(Uu(t))return t;if(Vu(t)){for(var e=t.length,n=[],r=0;r<e;r++){var i=t[r];"string"==typeof i?$u(n,i):"number"!=typeof i&&"Number"!==Gu(i)&&"String"!==Gu(i)||$u(n,Qu(i))}var o=n.length,a=!0;return function(t,e){if(a)return a=!1,e;if(Vu(this))return e;for(var r=0;r<o;r++)if(n[r]===t)return e}}},sl=J,cl=String,ul=Ku("JSON","stringify"),ll=el(/./.exec),hl=el("".charAt),fl=el("".charCodeAt),pl=el("".replace),vl=el(1..toString),dl=/[\uD800-\uDFFF]/g,gl=/^[\uD800-\uDBFF]$/,yl=/^[\uDC00-\uDFFF]$/,ml=!sl||nl((function(){var t=Ku("Symbol")("stringify detection");return"[null]"!==ul([t])||"{}"!==ul({a:t})||"{}"!==ul(Object(t))})),bl=nl((function(){return'"\\udf06\\ud834"'!==ul("\udf06\ud834")||'"\\udead"'!==ul("\udead")})),wl=function(t,e){var n=ol(arguments),r=al(e);if(rl(r)||void 0!==t&&!il(t))return n[1]=function(t,e){if(rl(r)&&(e=tl(r,this,cl(t),e)),!il(e))return e},Zu(ul,null,n)},kl=function(t,e,n){var r=hl(n,e-1),i=hl(n,e+1);return ll(gl,t)&&!ll(yl,i)||ll(yl,t)&&!ll(gl,r)?"\\u"+vl(fl(t,0),16):t};ul&&Ju({target:"JSON",stat:!0,arity:3,forced:ml||bl},{stringify:function(t,e,n){var r=ol(arguments),i=Zu(ml?wl:ul,null,r);return bl&&"string"==typeof i?pl(i,dl,kl):i}});var Cl=Ks,El=x;Er({target:"Object",stat:!0,forced:!J||n((function(){Cl.f(1)}))},{getOwnPropertySymbols:function(t){var e=Cl.f;return e?e(El(t)):[]}}),ac("asyncIterator"),ac("hasInstance"),ac("isConcatSpreadable"),ac("iterator"),ac("match"),ac("matchAll"),ac("replace"),ac("search"),ac("species"),ac("split");var Ol=hc;ac("toPrimitive"),Ol();var Fl=le,Tl=to;ac("toStringTag"),Tl(Fl("Symbol"),"Symbol"),ac("unscopables"),to(v.JSON,"JSON",!0);var _l=oe.Symbol,Sl=st,Al=Yt.f,Pl=Sl("metadata"),xl=Function.prototype;void 0===xl[Pl]&&Al(xl,Pl,{value:null}),ac("asyncDispose"),ac("dispose"),ac("metadata");var Dl=_l,jl=c,Il=le("Symbol"),Rl=Il.keyFor,Ml=jl(Il.prototype.valueOf),Nl=Il.isRegisteredSymbol||function(t){try{return void 0!==Rl(Ml(t))}catch(t){return!1}};Er({target:"Symbol",stat:!0},{isRegisteredSymbol:Nl});for(var Bl=O,Ll=le,zl=c,Hl=ge,Wl=st,ql=Ll("Symbol"),Yl=ql.isWellKnownSymbol,Xl=Ll("Object","getOwnPropertyNames"),Vl=zl(ql.prototype.valueOf),Ul=Bl("wks"),Gl=0,Ql=Xl(ql),$l=Ql.length;Gl<$l;Gl++)try{var Jl=Ql[Gl];Hl(ql[Jl])&&Wl(Jl)}catch(t){}var Kl=function(t){if(Yl&&Yl(t))return!0;try{for(var e=Vl(t),n=0,r=Xl(Ul),i=r.length;n<i;n++)if(Ul[r[n]]==e)return!0}catch(t){}return!1};Er({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Kl}),ac("matcher"),ac("observable"),Er({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Nl}),Er({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Kl}),ac("metadataKey"),ac("patternMatch"),ac("replaceAll");var Zl=e(Dl),th=e(tc.f("iterator"));function eh(t){return eh="function"==typeof Zl&&"symbol"==typeof th?function(t){return typeof t}:function(t){return t&&"function"==typeof Zl&&t.constructor===Zl&&t!==Zl.prototype?"symbol":typeof t},eh(t)}Er({target:"Array",stat:!0},{isArray:ls});var nh=oe.Array.isArray,rh=e(nh);function ih(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var oh=e(za);var ah=Er,sh=ls,ch=aa,uh=Wt,lh=Ir,hh=Br,fh=qn,ph=la,vh=st,dh=Xu,gh=Es("slice"),yh=vh("species"),mh=Array,bh=Math.max;ah({target:"Array",proto:!0,forced:!gh},{slice:function(t,e){var n,r,i,o=fh(this),a=hh(o),s=lh(t,a),c=lh(void 0===e?a:e,a);if(sh(o)&&(n=o.constructor,(ch(n)&&(n===mh||sh(n.prototype))||uh(n)&&null===(n=n[yh]))&&(n=void 0),n===mh||void 0===n))return dh(o,s,c);for(r=new(void 0===n?mh:n)(bh(c-s,0)),i=0;s<c;s++,i++)s in o&&ph(r,i,o[s]);return r.length=i,r}});var wh=oe,kh=function(t){return wh[t+"Prototype"]},Ch=kh("Array").slice,Eh=he,Oh=Ch,Fh=Array.prototype,Th=function(t){var e=t.slice;return t===Fh||Eh(Fh,t)&&e===Fh.slice?Oh:e},_h=e(Th);function Sh(t,e){var n;if(t){if("string"==typeof t)return ih(t,e);var r=_h(n=Object.prototype.toString.call(t)).call(n,8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?oh(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ih(t,e):void 0}}function Ah(t){return function(t){if(rh(t))return ih(t)}(t)||function(t){if(void 0!==Zl&&null!=ss(t)||null!=t["@@iterator"])return oh(t)}(t)||Sh(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ph=e(_l),xh=kh("Array").concat,Dh=he,jh=xh,Ih=Array.prototype,Rh=e((function(t){var e=t.concat;return t===Ih||Dh(Ih,t)&&e===Ih.concat?jh:e})),Mh=e(Th),Nh=le,Bh=Bs,Lh=Ks,zh=ee,Hh=c([].concat),Wh=Nh("Reflect","ownKeys")||function(t){var e=Bh.f(zh(t)),n=Lh.f;return n?Hh(e,n(t)):e};Er({target:"Reflect",stat:!0},{ownKeys:Wh});var qh=e(oe.Reflect.ownKeys),Yh=e(nh),Xh=bc.map;Er({target:"Array",proto:!0,forced:!Es("map")},{map:function(t){return Xh(this,t,arguments.length>1?arguments[1]:void 0)}});var Vh=kh("Array").map,Uh=he,Gh=Vh,Qh=Array.prototype,$h=e((function(t){var e=t.map;return t===Qh||Uh(Qh,t)&&e===Qh.map?Gh:e})),Jh=x,Kh=Zr;Er({target:"Object",stat:!0,forced:n((function(){Kh(1)}))},{keys:function(t){return Kh(Jh(t))}});var Zh=e(oe.Object.keys);function tf(t,e){var n=void 0!==Ph&&cs(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return ef(t,e);var r=Mh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Ha(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ef(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function ef(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var nf=Ph("DELETE");function rf(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return of.apply(void 0,Rh(e=[{},t]).call(e,r))}function of(){var t=af.apply(void 0,arguments);return cf(t),t}function af(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<2)return e[0];var r;if(e.length>2)return af.apply(void 0,Rh(r=[of(e[0],e[1])]).call(r,Ah(Mh(e).call(e,2))));var i=e[0],o=e[1];if(i instanceof Date&&o instanceof Date)return i.setTime(o.getTime()),i;var a,s=tf(qh(o));try{for(s.s();!(a=s.n()).done;){var c=a.value;Object.prototype.propertyIsEnumerable.call(o,c)&&(o[c]===nf?delete i[c]:null===i[c]||null===o[c]||"object"!==eh(i[c])||"object"!==eh(o[c])||Yh(i[c])||Yh(o[c])?i[c]=sf(o[c]):i[c]=af(i[c],o[c]))}}catch(t){s.e(t)}finally{s.f()}return i}function sf(t){return Yh(t)?$h(t).call(t,(function(t){return sf(t)})):"object"===eh(t)&&null!==t?t instanceof Date?new Date(t.getTime()):af({},t):t}function cf(t){for(var e=0,n=Zh(t);e<n.length;e++){var r=n[e];t[r]===nf?delete t[r]:"object"===eh(t[r])&&null!==t[r]&&cf(t[r])}}var uf=qt,lf=ls,hf=TypeError,ff=Object.getOwnPropertyDescriptor,pf=uf&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,e){if(lf(t)&&!ff(t,"length").writable)throw new hf("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},vf=x,df=Br,gf=pf,yf=fs;Er({target:"Array",proto:!0,arity:1,forced:n((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=vf(this),n=df(e),r=arguments.length;yf(n+r);for(var i=0;i<r;i++)e[n]=arguments[i],n++;return gf(e,n),n}});var mf=kh("Array").push,bf=he,wf=mf,kf=Array.prototype,Cf=e((function(t){var e=t.push;return t===kf||bf(kf,t)&&e===kf.push?wf:e}));function Ef(t,e){return function(t){if(rh(t))return t}(t)||function(t,e){var n=null==t?null:void 0!==Zl&&ss(t)||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,u=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(Cf(s).call(s,r.value),s.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(t,e)||Sh(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Of=Er,Ff=Date,Tf=c(Ff.prototype.getTime);Of({target:"Date",stat:!0},{now:function(){return Tf(new Ff)}});var _f=e(oe.Date.now);function Sf(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){var e=function(){for(var t=function(){var t=4022871197;return function(e){for(var n=e.toString(),r=0;r<n.length;r++){var i=.02519603282416938*(t+=n.charCodeAt(r));i-=t=i>>>0,t=(i*=t)>>>0,t+=4294967296*(i-=t)}return 2.3283064365386963e-10*(t>>>0)}}(),e=t(" "),n=t(" "),r=t(" "),i=0;i<arguments.length;i++)(e-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(e+=1),(n-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(n+=1),(r-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(r+=1);return[e,n,r]}(t),n=Ef(e,3),r=n[0],i=n[1],o=n[2],a=1,s=function(){var t=2091639*r+2.3283064365386963e-10*a;return r=i,i=o,o=t-(a=0|t)};return s.uint32=function(){return 4294967296*s()},s.fract53=function(){return s()+11102230246251565e-32*(2097152*s()|0)},s.algorithm="Alea",s.seed=t,s.version="0.9",s}(e.length?e:[_f()])}var Af=c,Pf=Ce,xf=Wt,Df=I,jf=Xu,If=r,Rf=Function,Mf=Af([].concat),Nf=Af([].join),Bf={},Lf=If?Rf.bind:function(t){var e=Pf(this),n=e.prototype,r=jf(arguments,1),i=function(){var n=Mf(r,jf(arguments));return this instanceof i?function(t,e,n){if(!Df(Bf,e)){for(var r=[],i=0;i<e;i++)r[i]="a["+i+"]";Bf[e]=Rf("C,a","return new C("+Nf(r,",")+")")}return Bf[e](t,n)}(e,n.length,n):e.apply(t,n)};return xf(n)&&(i.prototype=n),i},zf=Lf;Er({target:"Function",proto:!0,forced:Function.bind!==zf},{bind:zf});var Hf=kh("Function").bind,Wf=he,qf=Hf,Yf=Function.prototype,Xf=e((function(t){var e=t.bind;return t===Yf||Wf(Yf,t)&&e===Yf.bind?qf:e})),Vf=n,Uf=function(t,e){var n=[][t];return!!n&&Vf((function(){n.call(null,e||function(){return 1},1)}))},Gf=bc.forEach,Qf=Uf("forEach")?[].forEach:function(t){return Gf(this,t,arguments.length>1?arguments[1]:void 0)};Er({target:"Array",proto:!0,forced:[].forEach!==Qf},{forEach:Qf});var $f=kh("Array").forEach,Jf=Ot,Kf=I,Zf=he,tp=$f,ep=Array.prototype,np={DOMTokenList:!0,NodeList:!0},rp=e((function(t){var e=t.forEach;return t===ep||Zf(ep,t)&&e===ep.forEach||Kf(np,Jf(t))?tp:e})),ip=Er,op=ls,ap=c([].reverse),sp=[1,2];ip({target:"Array",proto:!0,forced:String(sp)===String(sp.reverse())},{reverse:function(){return op(this)&&(this.length=this.length),ap(this)}});var cp=kh("Array").reverse,up=he,lp=cp,hp=Array.prototype,fp=e((function(t){var e=t.reverse;return t===hp||up(hp,t)&&e===hp.reverse?lp:e})),pp=me,vp=TypeError,dp=Er,gp=x,yp=Ir,mp=f,bp=Br,wp=pf,kp=fs,Cp=bs,Ep=la,Op=function(t,e){if(!delete t[e])throw new vp("Cannot delete property "+pp(e)+" of "+pp(t))},Fp=Es("splice"),Tp=Math.max,_p=Math.min;dp({target:"Array",proto:!0,forced:!Fp},{splice:function(t,e){var n,r,i,o,a,s,c=gp(this),u=bp(c),l=yp(t,u),h=arguments.length;for(0===h?n=r=0:1===h?(n=0,r=u-l):(n=h-2,r=_p(Tp(mp(e),0),u-l)),kp(u+n-r),i=Cp(c,r),o=0;o<r;o++)(a=l+o)in c&&Ep(i,o,c[a]);if(i.length=r,n<r){for(o=l;o<u-r;o++)s=o+n,(a=o+r)in c?c[s]=c[a]:Op(c,s);for(o=u;o>u-r+n;o--)Op(c,o-1)}else if(n>r)for(o=u-r;o>l;o--)s=o+n-1,(a=o+r-1)in c?c[s]=c[a]:Op(c,s);for(o=0;o<n;o++)c[o+l]=arguments[o+2];return wp(c,u-r+n),i}});var Sp=kh("Array").splice,Ap=he,Pp=Sp,xp=Array.prototype,Dp=e((function(t){var e=t.splice;return t===xp||Ap(xp,t)&&e===xp.splice?Pp:e})),jp={exports:{}};!function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const n=this._callbacks.get(t)??[];return n.push(e),this._callbacks.set(t,n),this},e.prototype.once=function(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return n.fn=e,this.on(t,n),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const n=this._callbacks.get(t);if(n){for(const[t,r]of n.entries())if(r===e||r.fn===e){n.splice(t,1);break}0===n.length?this._callbacks.delete(t):this._callbacks.set(t,n)}return this},e.prototype.emit=function(t,...e){const n=this._callbacks.get(t);if(n){const t=[...n];for(const n of t)n.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(jp);var Ip=e(jp.exports);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
 * http://naver.github.io/egjs
 *
 * Forked By Naver egjs
 * Copyright (c) hammerjs
 * Licensed under the MIT license */
function Rp(){return Rp=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Rp.apply(this,arguments)}function Mp(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function Np(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var Bp,Lp="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var i in r)r.hasOwnProperty(i)&&(e[i]=r[i])}return e}:Object.assign,zp=["","webkit","Moz","MS","ms","o"],Hp="undefined"==typeof document?{style:{}}:document.createElement("div"),Wp=Math.round,qp=Math.abs,Yp=Date.now;function Xp(t,e){for(var n,r,i=e[0].toUpperCase()+e.slice(1),o=0;o<zp.length;){if((r=(n=zp[o])?n+i:e)in t)return r;o++}}Bp="undefined"==typeof window?{}:window;var Vp=Xp(Hp.style,"touchAction"),Up=void 0!==Vp;var Gp="compute",Qp="auto",$p="manipulation",Jp="none",Kp="pan-x",Zp="pan-y",tv=function(){if(!Up)return!1;var t={},e=Bp.CSS&&Bp.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){return t[n]=!e||Bp.CSS.supports("touch-action",n)})),t}(),ev="ontouchstart"in Bp,nv=void 0!==Xp(Bp,"PointerEvent"),rv=ev&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),iv="touch",ov="mouse",av=25,sv=1,cv=4,uv=8,lv=1,hv=2,fv=4,pv=8,vv=16,dv=hv|fv,gv=pv|vv,yv=dv|gv,mv=["x","y"],bv=["clientX","clientY"];function wv(t,e,n){var r;if(t)if(t.forEach)t.forEach(e,n);else if(void 0!==t.length)for(r=0;r<t.length;)e.call(n,t[r],r,t),r++;else for(r in t)t.hasOwnProperty(r)&&e.call(n,t[r],r,t)}function kv(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function Cv(t,e){return t.indexOf(e)>-1}var Ev=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Gp&&(t=this.compute()),Up&&this.manager.element.style&&tv[t]&&(this.manager.element.style[Vp]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return wv(this.manager.recognizers,(function(e){kv(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(Cv(t,Jp))return Jp;var e=Cv(t,Kp),n=Cv(t,Zp);return e&&n?Jp:e||n?e?Kp:Zp:Cv(t,$p)?$p:Qp}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var r=this.actions,i=Cv(r,Jp)&&!tv[Jp],o=Cv(r,Zp)&&!tv[Zp],a=Cv(r,Kp)&&!tv[Kp];if(i){var s=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(s&&c&&u)return}if(!a||!o)return i||o&&n&dv||a&&n&gv?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Ov(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function Fv(t){var e=t.length;if(1===e)return{x:Wp(t[0].clientX),y:Wp(t[0].clientY)};for(var n=0,r=0,i=0;i<e;)n+=t[i].clientX,r+=t[i].clientY,i++;return{x:Wp(n/e),y:Wp(r/e)}}function Tv(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:Wp(t.pointers[n].clientX),clientY:Wp(t.pointers[n].clientY)},n++;return{timeStamp:Yp(),pointers:e,center:Fv(e),deltaX:t.deltaX,deltaY:t.deltaY}}function _v(t,e,n){n||(n=mv);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return Math.sqrt(r*r+i*i)}function Sv(t,e,n){n||(n=mv);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return 180*Math.atan2(i,r)/Math.PI}function Av(t,e){return t===e?lv:qp(t)>=qp(e)?t<0?hv:fv:e<0?pv:vv}function Pv(t,e,n){return{x:e/t||0,y:n/t||0}}function xv(t,e){var n=t.session,r=e.pointers,i=r.length;n.firstInput||(n.firstInput=Tv(e)),i>1&&!n.firstMultiple?n.firstMultiple=Tv(e):1===i&&(n.firstMultiple=!1);var o=n.firstInput,a=n.firstMultiple,s=a?a.center:o.center,c=e.center=Fv(r);e.timeStamp=Yp(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=Sv(s,c),e.distance=_v(s,c),function(t,e){var n=e.center,r=t.offsetDelta||{},i=t.prevDelta||{},o=t.prevInput||{};e.eventType!==sv&&o.eventType!==cv||(i=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},r=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=i.x+(n.x-r.x),e.deltaY=i.y+(n.y-r.y)}(n,e),e.offsetDirection=Av(e.deltaX,e.deltaY);var u,l,h=Pv(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=qp(h.x)>qp(h.y)?h.x:h.y,e.scale=a?(u=a.pointers,_v((l=r)[0],l[1],bv)/_v(u[0],u[1],bv)):1,e.rotation=a?function(t,e){return Sv(e[1],e[0],bv)+Sv(t[1],t[0],bv)}(a.pointers,r):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,r,i,o,a=t.lastInterval||e,s=e.timeStamp-a.timeStamp;if(e.eventType!==uv&&(s>av||void 0===a.velocity)){var c=e.deltaX-a.deltaX,u=e.deltaY-a.deltaY,l=Pv(s,c,u);r=l.x,i=l.y,n=qp(l.x)>qp(l.y)?l.x:l.y,o=Av(c,u),t.lastInterval=e}else n=a.velocity,r=a.velocityX,i=a.velocityY,o=a.direction;e.velocity=n,e.velocityX=r,e.velocityY=i,e.direction=o}(n,e);var f,p=t.element,v=e.srcEvent;Ov(f=v.composedPath?v.composedPath()[0]:v.path?v.path[0]:v.target,p)&&(p=f),e.target=p}function Dv(t,e,n){var r=n.pointers.length,i=n.changedPointers.length,o=e&sv&&r-i==0,a=e&(cv|uv)&&r-i==0;n.isFirst=!!o,n.isFinal=!!a,o&&(t.session={}),n.eventType=e,xv(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function jv(t){return t.trim().split(/\s+/g)}function Iv(t,e,n){wv(jv(e),(function(e){t.addEventListener(e,n,!1)}))}function Rv(t,e,n){wv(jv(e),(function(e){t.removeEventListener(e,n,!1)}))}function Mv(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var Nv=function(){function t(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){kv(t.options.enable,[t])&&n.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&Iv(this.element,this.evEl,this.domHandler),this.evTarget&&Iv(this.target,this.evTarget,this.domHandler),this.evWin&&Iv(Mv(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&Rv(this.element,this.evEl,this.domHandler),this.evTarget&&Rv(this.target,this.evTarget,this.domHandler),this.evWin&&Rv(Mv(this.element),this.evWin,this.domHandler)},t}();function Bv(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var r=0;r<t.length;){if(n&&t[r][n]==e||!n&&t[r]===e)return r;r++}return-1}var Lv={pointerdown:sv,pointermove:2,pointerup:cv,pointercancel:uv,pointerout:uv},zv={2:iv,3:"pen",4:ov,5:"kinect"},Hv="pointerdown",Wv="pointermove pointerup pointercancel";Bp.MSPointerEvent&&!Bp.PointerEvent&&(Hv="MSPointerDown",Wv="MSPointerMove MSPointerUp MSPointerCancel");var qv=function(t){function e(){var n,r=e.prototype;return r.evEl=Hv,r.evWin=Wv,(n=t.apply(this,arguments)||this).store=n.manager.session.pointerEvents=[],n}return Mp(e,t),e.prototype.handler=function(t){var e=this.store,n=!1,r=t.type.toLowerCase().replace("ms",""),i=Lv[r],o=zv[t.pointerType]||t.pointerType,a=o===iv,s=Bv(e,t.pointerId,"pointerId");i&sv&&(0===t.button||a)?s<0&&(e.push(t),s=e.length-1):i&(cv|uv)&&(n=!0),s<0||(e[s]=t,this.callback(this.manager,i,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(s,1))},e}(Nv);function Yv(t){return Array.prototype.slice.call(t,0)}function Xv(t,e,n){for(var r=[],i=[],o=0;o<t.length;){var a=e?t[o][e]:t[o];Bv(i,a)<0&&r.push(t[o]),i[o]=a,o++}return n&&(r=e?r.sort((function(t,n){return t[e]>n[e]})):r.sort()),r}var Vv={touchstart:sv,touchmove:2,touchend:cv,touchcancel:uv},Uv=function(t){function e(){var n;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).targetIds={},n}return Mp(e,t),e.prototype.handler=function(t){var e=Vv[t.type],n=Gv.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:iv,srcEvent:t})},e}(Nv);function Gv(t,e){var n,r,i=Yv(t.touches),o=this.targetIds;if(e&(2|sv)&&1===i.length)return o[i[0].identifier]=!0,[i,i];var a=Yv(t.changedTouches),s=[],c=this.target;if(r=i.filter((function(t){return Ov(t.target,c)})),e===sv)for(n=0;n<r.length;)o[r[n].identifier]=!0,n++;for(n=0;n<a.length;)o[a[n].identifier]&&s.push(a[n]),e&(cv|uv)&&delete o[a[n].identifier],n++;return s.length?[Xv(r.concat(s),"identifier",!0),s]:void 0}var Qv={mousedown:sv,mousemove:2,mouseup:cv},$v=function(t){function e(){var n,r=e.prototype;return r.evEl="mousedown",r.evWin="mousemove mouseup",(n=t.apply(this,arguments)||this).pressed=!1,n}return Mp(e,t),e.prototype.handler=function(t){var e=Qv[t.type];e&sv&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=cv),this.pressed&&(e&cv&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:ov,srcEvent:t}))},e}(Nv),Jv=2500;function Kv(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY},r=this.lastTouches;this.lastTouches.push(n);setTimeout((function(){var t=r.indexOf(n);t>-1&&r.splice(t,1)}),Jv)}}function Zv(t,e){t&sv?(this.primaryTouch=e.changedPointers[0].identifier,Kv.call(this,e)):t&(cv|uv)&&Kv.call(this,e)}function td(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,r=0;r<this.lastTouches.length;r++){var i=this.lastTouches[r],o=Math.abs(e-i.x),a=Math.abs(n-i.y);if(o<=25&&a<=25)return!0}return!1}var ed=function(){return function(t){function e(e,n){var r;return(r=t.call(this,e,n)||this).handler=function(t,e,n){var i=n.pointerType===iv,o=n.pointerType===ov;if(!(o&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)Zv.call(Np(Np(r)),e,n);else if(o&&td.call(Np(Np(r)),n))return;r.callback(t,e,n)}},r.touch=new Uv(r.manager,r.handler),r.mouse=new $v(r.manager,r.handler),r.primaryTouch=null,r.lastTouches=[],r}return Mp(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(Nv)}();function nd(t,e,n){return!!Array.isArray(t)&&(wv(t,n[e],n),!0)}var rd=32,id=1;function od(t,e){var n=e.manager;return n?n.get(t):t}function ad(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var sd=function(){function t(t){void 0===t&&(t={}),this.options=Rp({enable:!0},t),this.id=id++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Lp(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(nd(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=od(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return nd(t,"dropRecognizeWith",this)||(t=od(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(nd(t,"requireFailure",this))return this;var e=this.requireFail;return-1===Bv(e,t=od(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(nd(t,"dropRequireFailure",this))return this;t=od(t,this);var e=Bv(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,n=this.state;function r(n){e.manager.emit(n,t)}n<8&&r(e.options.event+ad(n)),r(e.options.event),t.additionalEvent&&r(t.additionalEvent),n>=8&&r(e.options.event+ad(n))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=rd},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Lp({},t);if(!kv(this.options.enable,[this,e]))return this.reset(),void(this.state=rd);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),cd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,n.pCenter=!1,n._timer=null,n._input=null,n.count=0,n}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){return[$p]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime<n.time;if(this.reset(),t.eventType&sv&&0===this.count)return this.failTimeout();if(i&&o&&r){if(t.eventType!==cv)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<n.interval,s=!this.pCenter||_v(this.pCenter,t.center)<n.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,s&&a?this.count+=1:this.count=1,this._input=t,0===this.count%n.taps)return this.hasRequireFailures()?(this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),n.interval),2):8}return rd},n.failTimeout=function(){var t=this;return this._timer=setTimeout((function(){t.state=rd}),this.options.interval),rd},n.reset=function(){clearTimeout(this._timer)},n.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(sd),ud=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({pointers:1},e))||this}Mp(e,t);var n=e.prototype;return n.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},n.process=function(t){var e=this.state,n=t.eventType,r=6&e,i=this.attrTest(t);return r&&(n&uv||!i)?16|e:r||i?n&cv?8|e:2&e?4|e:2:rd},e}(sd);function ld(t){return t===vv?"down":t===pv?"up":t===hv?"left":t===fv?"right":""}var hd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"pan",threshold:10,pointers:1,direction:yv},e))||this).pX=null,n.pY=null,n}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){var t=this.options.direction,e=[];return t&dv&&e.push(Zp),t&gv&&e.push(Kp),e},n.directionTest=function(t){var e=this.options,n=!0,r=t.distance,i=t.direction,o=t.deltaX,a=t.deltaY;return i&e.direction||(e.direction&dv?(i=0===o?lv:o<0?hv:fv,n=o!==this.pX,r=Math.abs(t.deltaX)):(i=0===a?lv:a<0?pv:vv,n=a!==this.pY,r=Math.abs(t.deltaY))),t.direction=i,n&&r>e.threshold&&i&e.direction},n.attrTest=function(t){return ud.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},n.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var n=ld(e.direction);n&&(e.additionalEvent=this.options.event+n),t.prototype.emit.call(this,e)},e}(ud),fd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"swipe",threshold:10,velocity:.3,direction:dv|gv,pointers:1},e))||this}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){return hd.prototype.getTouchAction.call(this)},n.attrTest=function(e){var n,r=this.options.direction;return r&(dv|gv)?n=e.overallVelocity:r&dv?n=e.overallVelocityX:r&gv&&(n=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&r&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&qp(n)>this.options.velocity&&e.eventType&cv},n.emit=function(t){var e=ld(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(ud),pd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"pinch",threshold:0,pointers:2},e))||this}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Jp]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},n.emit=function(e){if(1!==e.scale){var n=e.scale<1?"in":"out";e.additionalEvent=this.options.event+n}t.prototype.emit.call(this,e)},e}(ud),vd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"rotate",threshold:0,pointers:2},e))||this}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Jp]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(ud),dd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,n._input=null,n}Mp(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Qp]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime>n.time;if(this._input=t,!i||!r||t.eventType&(cv|uv)&&!o)this.reset();else if(t.eventType&sv)this.reset(),this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),n.time);else if(t.eventType&cv)return 8;return rd},n.reset=function(){clearTimeout(this._timer)},n.emit=function(t){8===this.state&&(t&&t.eventType&cv?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Yp(),this.manager.emit(this.options.event,this._input)))},e}(sd),gd={domEvents:!1,touchAction:Gp,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},yd=[[vd,{enable:!1}],[pd,{enable:!1},["rotate"]],[fd,{direction:dv}],[hd,{direction:dv},["swipe"]],[cd],[cd,{event:"doubletap",taps:2},["tap"]],[dd]];function md(t,e){var n,r=t.element;r.style&&(wv(t.options.cssProps,(function(i,o){n=Xp(r.style,o),e?(t.oldCssProps[n]=r.style[n],r.style[n]=i):r.style[n]=t.oldCssProps[n]||""})),e||(t.oldCssProps={}))}var bd=function(){function t(t,e){var n,r=this;this.options=Lp({},gd,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(nv?qv:rv?Uv:ev?ed:$v))(n,Dv),this.touchAction=new Ev(this,this.options.touchAction),md(this,!0),wv(this.options.recognizers,(function(t){var e=r.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}var e=t.prototype;return e.set=function(t){return Lp(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var r=this.recognizers,i=e.curRecognizer;(!i||i&&8&i.state)&&(e.curRecognizer=null,i=null);for(var o=0;o<r.length;)n=r[o],2===e.stopped||i&&n!==i&&!n.canRecognizeWith(i)?n.reset():n.recognize(t),!i&&14&n.state&&(e.curRecognizer=n,i=n),o++}},e.get=function(t){if(t instanceof sd)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event===t)return e[n];return null},e.add=function(t){if(nd(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(nd(t,"remove",this))return this;var e=this.get(t);if(t){var n=this.recognizers,r=Bv(n,e);-1!==r&&(n.splice(r,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var n=this.handlers;return wv(jv(t),(function(t){n[t]=n[t]||[],n[t].push(e)})),this},e.off=function(t,e){if(void 0===t)return this;var n=this.handlers;return wv(jv(t),(function(t){e?n[t]&&n[t].splice(Bv(n[t],e),1):delete n[t]})),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var n=document.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<n.length;)n[r](e),r++}},e.destroy=function(){this.element&&md(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),wd={touchstart:sv,touchmove:2,touchend:cv,touchcancel:uv},kd=function(t){function e(){var n,r=e.prototype;return r.evTarget="touchstart",r.evWin="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).started=!1,n}return Mp(e,t),e.prototype.handler=function(t){var e=wd[t.type];if(e===sv&&(this.started=!0),this.started){var n=Cd.call(this,t,e);e&(cv|uv)&&n[0].length-n[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:iv,srcEvent:t})}},e}(Nv);function Cd(t,e){var n=Yv(t.touches),r=Yv(t.changedTouches);return e&(cv|uv)&&(n=Xv(n.concat(r),"identifier",!0)),[n,r]}function Ed(t,e,n){var r="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=window.console&&(window.console.warn||window.console.log);return i&&i.call(window.console,r,n),t.apply(this,arguments)}}var Od=Ed((function(t,e,n){for(var r=Object.keys(e),i=0;i<r.length;)(!n||n&&void 0===t[r[i]])&&(t[r[i]]=e[r[i]]),i++;return t}),"extend","Use `assign`."),Fd=Ed((function(t,e){return Od(t,e,!0)}),"merge","Use `assign`.");function Td(t,e,n){var r,i=e.prototype;(r=t.prototype=Object.create(i)).constructor=t,r._super=i,n&&Lp(r,n)}function _d(t,e){return function(){return t.apply(e,arguments)}}var Sd=function(){var t=function(t,e){return void 0===e&&(e={}),new bd(t,Rp({recognizers:yd.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=yv,t.DIRECTION_DOWN=vv,t.DIRECTION_LEFT=hv,t.DIRECTION_RIGHT=fv,t.DIRECTION_UP=pv,t.DIRECTION_HORIZONTAL=dv,t.DIRECTION_VERTICAL=gv,t.DIRECTION_NONE=lv,t.DIRECTION_DOWN=vv,t.INPUT_START=sv,t.INPUT_MOVE=2,t.INPUT_END=cv,t.INPUT_CANCEL=uv,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=rd,t.Manager=bd,t.Input=Nv,t.TouchAction=Ev,t.TouchInput=Uv,t.MouseInput=$v,t.PointerEventInput=qv,t.TouchMouseInput=ed,t.SingleTouchInput=kd,t.Recognizer=sd,t.AttrRecognizer=ud,t.Tap=cd,t.Pan=hd,t.Swipe=fd,t.Pinch=pd,t.Rotate=vd,t.Press=dd,t.on=Iv,t.off=Rv,t.each=wv,t.merge=Fd,t.extend=Od,t.bindFn=_d,t.assign=Lp,t.inherit=Td,t.bindFn=_d,t.prefixed=Xp,t.toArray=Yv,t.inArray=Bv,t.uniqueArray=Xv,t.splitStr=jv,t.boolOrFn=kv,t.hasParent=Ov,t.addEventListeners=Iv,t.removeEventListeners=Rv,t.defaults=Lp({},gd,{preset:yd}),t}();Sd.defaults;var Ad=Sd;var Pd="undefined"!=typeof window?window.Hammer||Ad:function(){return function(){var t=function(){};return{on:t,off:t,destroy:t,emit:t,get:function(){return{set:t}}}}()};function xd(t,e){var n=void 0!==Ph&&cs(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return Dd(t,e);var r=Mh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Ha(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dd(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Dd(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function jd(t){var e,n=this;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push((function(){n._dom.overlay.parentNode.removeChild(n._dom.overlay)}));var r=Pd(this._dom.overlay);r.on("tap",Xf(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push((function(){r.destroy()}));var i=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];rp(i).call(i,(function(t){r.on(t,(function(t){t.srcEvent.stopPropagation()}))})),document&&document.body&&(this._onClick=function(e){(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||n.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push((function(){document.body.removeEventListener("click",n._onClick)}))),this._escListener=function(t){("key"in t?"Escape"===t.key:27===t.keyCode)&&n.deactivate()}}function Id(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Ip(jd.prototype),jd.current=null,jd.prototype.destroy=function(){var t,e;this.deactivate();var n,r=xd(fp(t=Dp(e=this._cleanupQueue).call(e,0)).call(t));try{for(r.s();!(n=r.n()).done;){(0,n.value)()}}catch(t){r.e(t)}finally{r.f()}},jd.prototype.activate=function(){jd.current&&jd.current.deactivate(),jd.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},jd.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},jd.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var Rd={exports:{}},Md=Er,Nd=qt,Bd=Yt.f;Md({target:"Object",stat:!0,forced:Object.defineProperty!==Bd,sham:!Nd},{defineProperty:Bd});var Ld=oe.Object,zd=Rd.exports=function(t,e,n){return Ld.defineProperty(t,e,n)};Ld.defineProperty.sham&&(zd.sham=!0);var Hd=e(Rd.exports),Wd=e(tc.f("toPrimitive"));function qd(t){var e=function(t,e){if("object"!==eh(t)||null===t)return t;var n=t[Wd];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==eh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===eh(e)?e:String(e)}function Yd(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Hd(t,qd(r.key),r)}}function Xd(t,e,n){return e&&Yd(t.prototype,e),n&&Yd(t,n),Hd(t,"prototype",{writable:!1}),t}var Vd=oe,Ud=_n;Vd.JSON||(Vd.JSON={stringify:JSON.stringify});var Gd=e((function(t,e,n){return Ud(Vd.JSON.stringify,null,arguments)})),Qd=qt,$d=c,Jd=ie,Kd=n,Zd=Zr,tg=Ks,eg=Dn,ng=x,rg=zn,ig=Object.assign,og=Object.defineProperty,ag=$d([].concat),sg=!ig||Kd((function(){if(Qd&&1!==ig({b:1},ig(og({},"a",{enumerable:!0,get:function(){og(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol("assign detection"),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!==ig({},t)[n]||Zd(ig({},e)).join("")!==r}))?function(t,e){for(var n=ng(t),r=arguments.length,i=1,o=tg.f,a=eg.f;r>i;)for(var s,c=rg(arguments[i++]),u=o?ag(Zd(c),o(c)):Zd(c),l=u.length,h=0;l>h;)s=u[h++],Qd&&!Jd(a,c,s)||(n[s]=c[s]);return n}:ig,cg=sg;Er({target:"Object",stat:!0,arity:2,forced:Object.assign!==cg},{assign:cg});var ug=e(oe.Object.assign),lg="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,hg=TypeError,fg=v,pg=_n,vg=pt,dg=lg,gg=z,yg=Xu,mg=function(t,e){if(t<e)throw new hg("Not enough arguments");return t},bg=fg.Function,wg=/MSIE .\./.test(gg)||dg&&function(){var t=fg.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),kg=function(t,e){var n=e?2:1;return wg?function(r,i){var o=mg(arguments.length,1)>n,a=vg(r)?r:bg(r),s=o?yg(arguments,n):[],c=o?function(){pg(a,this,s)}:a;return e?t(c,i):t(c)}:t},Cg=Er,Eg=v,Og=kg(Eg.setInterval,!0);Cg({global:!0,bind:!0,forced:Eg.setInterval!==Og},{setInterval:Og});var Fg=Er,Tg=v,_g=kg(Tg.setTimeout,!0);Fg({global:!0,bind:!0,forced:Tg.setTimeout!==_g},{setTimeout:_g});var Sg=e(oe.setTimeout),Ag=x,Pg=Ir,xg=Br;Er({target:"Array",proto:!0},{fill:function(t){for(var e=Ag(this),n=xg(e),r=arguments.length,i=Pg(r>1?arguments[1]:void 0,n),o=r>2?arguments[2]:void 0,a=void 0===o?n:Pg(o,n);a>i;)e[i++]=t;return e}});var Dg=kh("Array").fill,jg=he,Ig=Dg,Rg=Array.prototype,Mg=e((function(t){var e=t.fill;return t===Rg||jg(Rg,t)&&e===Rg.fill?Ig:e})),Ng=qr.includes;Er({target:"Array",proto:!0,forced:n((function(){return!Array(1).includes()}))},{includes:function(t){return Ng(this,t,arguments.length>1?arguments[1]:void 0)}});var Bg=kh("Array").includes,Lg=Wt,zg=yt,Hg=st("match"),Wg=function(t){var e;return Lg(t)&&(void 0!==(e=t[Hg])?!!e:"RegExp"===zg(t))},qg=TypeError,Yg=st("match"),Xg=Er,Vg=function(t){if(Wg(t))throw new qg("The method doesn't accept regular expressions");return t},Ug=S,Gg=_t,Qg=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[Yg]=!1,"/./"[t](e)}catch(t){}}return!1},$g=c("".indexOf);Xg({target:"String",proto:!0,forced:!Qg("includes")},{includes:function(t){return!!~$g(Gg(Ug(this)),Gg(Vg(t)),arguments.length>1?arguments[1]:void 0)}});var Jg=kh("String").includes,Kg=he,Zg=Bg,ty=Jg,ey=Array.prototype,ny=String.prototype,ry=e((function(t){var e=t.includes;return t===ey||Kg(ey,t)&&e===ey.includes?Zg:"string"==typeof t||t===ny||Kg(ny,t)&&e===ny.includes?ty:e})),iy=x,oy=ji,ay=Fi;Er({target:"Object",stat:!0,forced:n((function(){oy(1)})),sham:!ay},{getPrototypeOf:function(t){return oy(iy(t))}});var sy=e(oe.Object.getPrototypeOf),cy=bc.filter;Er({target:"Array",proto:!0,forced:!Es("filter")},{filter:function(t){return cy(this,t,arguments.length>1?arguments[1]:void 0)}});var uy=kh("Array").filter,ly=he,hy=uy,fy=Array.prototype,py=e((function(t){var e=t.filter;return t===fy||ly(fy,t)&&e===fy.filter?hy:e})),vy=qt,dy=n,gy=c,yy=ji,my=Zr,by=qn,wy=gy(Dn.f),ky=gy([].push),Cy=vy&&dy((function(){var t=Object.create(null);return t[2]=2,!wy(t,2)})),Ey=function(t){return function(e){for(var n,r=by(e),i=my(r),o=Cy&&null===yy(r),a=i.length,s=0,c=[];a>s;)n=i[s++],vy&&!(o?n in r:wy(r,n))||ky(c,t?[n,r[n]]:r[n]);return c}},Oy={entries:Ey(!0),values:Ey(!1)},Fy=Oy.values;Er({target:"Object",stat:!0},{values:function(t){return Fy(t)}});var Ty=e(oe.Object.values),_y="\t\n\v\f\r                　\u2028\u2029\ufeff",Sy=S,Ay=_t,Py=_y,xy=c("".replace),Dy=RegExp("^["+Py+"]+"),jy=RegExp("(^|[^"+Py+"])["+Py+"]+$"),Iy=function(t){return function(e){var n=Ay(Sy(e));return 1&t&&(n=xy(n,Dy,"")),2&t&&(n=xy(n,jy,"$1")),n}},Ry={start:Iy(1),end:Iy(2),trim:Iy(3)},My=v,Ny=n,By=c,Ly=_t,zy=Ry.trim,Hy=_y,Wy=My.parseInt,qy=My.Symbol,Yy=qy&&qy.iterator,Xy=/^[+-]?0x/i,Vy=By(Xy.exec),Uy=8!==Wy(Hy+"08")||22!==Wy(Hy+"0x16")||Yy&&!Ny((function(){Wy(Object(Yy))}))?function(t,e){var n=zy(Ly(t));return Wy(n,e>>>0||(Vy(Xy,n)?16:10))}:Wy;Er({global:!0,forced:parseInt!==Uy},{parseInt:Uy});var Gy=e(oe.parseInt),Qy=Er,$y=qr.indexOf,Jy=Uf,Ky=Pn([].indexOf),Zy=!!Ky&&1/Ky([1],1,-0)<0;Qy({target:"Array",proto:!0,forced:Zy||!Jy("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Zy?Ky(this,t,e)||0:$y(this,t,e)}});var tm=kh("Array").indexOf,em=he,nm=tm,rm=Array.prototype,im=e((function(t){var e=t.indexOf;return t===rm||em(rm,t)&&e===rm.indexOf?nm:e})),om=Oy.entries;Er({target:"Object",stat:!0},{entries:function(t){return om(t)}});var am=e(oe.Object.entries);Er({target:"Object",stat:!0,sham:!qt},{create:Oi});var sm=oe.Object,cm=e((function(t,e){return sm.create(t,e)}));function um(t,e){var n=void 0!==Ph&&cs(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return lm(t,e);var r=Mh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Ha(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lm(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function lm(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var hm=/^\/?Date\((-?\d+)/i,fm=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,pm=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,vm=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,dm=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function gm(t){return t instanceof Number||"number"==typeof t}function ym(t){if(t)for(;!0===t.hasChildNodes();){var e=t.firstChild;e&&(ym(e),t.removeChild(e))}}function mm(t){return t instanceof String||"string"==typeof t}function bm(t){return"object"===eh(t)&&null!==t}function wm(t){if(t instanceof Date)return!0;if(mm(t)){if(hm.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1}function km(t,e,n,r){var i=!1;!0===r&&(i=null===e[n]&&void 0!==t[n]),i?delete t[n]:t[n]=e[n]}function Cm(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var r in t)if(void 0!==e[r])if(null===e[r]||"object"!==eh(e[r]))km(t,e,r,n);else{var i=t[r],o=e[r];bm(i)&&bm(o)&&Cm(i,o,n)}}var Em=ug;function Om(t,e){if(!Yh(t))throw new Error("Array with property names expected as first argument");for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];for(var o=0,a=r;o<a.length;o++)for(var s=a[o],c=0;c<t.length;c++){var u=t[c];s&&Object.prototype.hasOwnProperty.call(s,u)&&(e[u]=s[u])}return e}function Fm(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Yh(n))throw new TypeError("Arrays are not supported by deepExtend");for(var i=0;i<t.length;i++){var o=t[i];if(Object.prototype.hasOwnProperty.call(n,o))if(n[o]&&n[o].constructor===Object)void 0===e[o]&&(e[o]={}),e[o].constructor===Object?_m(e[o],n[o],!1,r):km(e,n,o,r);else{if(Yh(n[o]))throw new TypeError("Arrays are not supported by deepExtend");km(e,n,o,r)}}return e}function Tm(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Yh(n))throw new TypeError("Arrays are not supported by deepExtend");for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&!ry(t).call(t,i))if(n[i]&&n[i].constructor===Object)void 0===e[i]&&(e[i]={}),e[i].constructor===Object?_m(e[i],n[i]):km(e,n,i,r);else if(Yh(n[i])){e[i]=[];for(var o=0;o<n[i].length;o++)e[i].push(n[i][o])}else km(e,n,i,r);return e}function _m(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)||!0===n)if("object"===eh(e[i])&&null!==e[i]&&sy(e[i])===Object.prototype)void 0===t[i]?t[i]=_m({},e[i],n):"object"===eh(t[i])&&null!==t[i]&&sy(t[i])===Object.prototype?_m(t[i],e[i],n):km(t,e,i,r);else if(Yh(e[i])){var o;t[i]=Mh(o=e[i]).call(o)}else km(t,e,i,r);return t}function Sm(t,e){if(t.length!==e.length)return!1;for(var n=0,r=t.length;n<r;n++)if(t[n]!=e[n])return!1;return!0}function Am(t){var e=eh(t);return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Yh(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e}function Pm(t,e){var n;return Rh(n=[]).call(n,Ah(t),[e])}function xm(t){return Mh(t).call(t)}function Dm(t){return t.getBoundingClientRect().left}function jm(t){return t.getBoundingClientRect().right}function Im(t){return t.getBoundingClientRect().top}function Rm(t,e){var n=t.className.split(" "),r=e.split(" ");n=Rh(n).call(n,py(r).call(r,(function(t){return!ry(n).call(n,t)}))),t.className=n.join(" ")}function Mm(t,e){var n=t.className.split(" "),r=e.split(" ");n=py(n).call(n,(function(t){return!ry(r).call(r,t)})),t.className=n.join(" ")}function Nm(t,e){if(Yh(t))for(var n=t.length,r=0;r<n;r++)e(t[r],r,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)}var Bm=Ty;function Lm(t,e,n){return t[e]!==n&&(t[e]=n,!0)}function zm(t){var e=!1;return function(){e||(e=!0,requestAnimationFrame((function(){e=!1,t()})))}}function Hm(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)}function Wm(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event,e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode)instanceof Element)?e:null}function qm(t,e){for(var n=t;n;){if(n===e)return!0;if(!n.parentNode)return!1;n=n.parentNode}return!1}var Ym={asBoolean:function(t,e){return"function"==typeof t&&(t=t()),null!=t?0!=t:e||null},asNumber:function(t,e){return"function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null},asString:function(t,e){return"function"==typeof t&&(t=t()),null!=t?String(t):e||null},asSize:function(t,e){return"function"==typeof t&&(t=t()),mm(t)?t:gm(t)?t+"px":e||null},asElement:function(t,e){return"function"==typeof t&&(t=t()),t||e||null}};function Xm(t){var e;switch(t.length){case 3:case 4:return(e=pm.exec(t))?{r:Gy(e[1]+e[1],16),g:Gy(e[2]+e[2],16),b:Gy(e[3]+e[3],16)}:null;case 6:case 7:return(e=fm.exec(t))?{r:Gy(e[1],16),g:Gy(e[2],16),b:Gy(e[3],16)}:null;default:return null}}function Vm(t,e){if(ry(t).call(t,"rgba"))return t;if(ry(t).call(t,"rgb")){var n=t.substr(im(t).call(t,"(")+1).replace(")","").split(",");return"rgba("+n[0]+","+n[1]+","+n[2]+","+e+")"}var r=Xm(t);return null==r?t:"rgba("+r.r+","+r.g+","+r.b+","+e+")"}function Um(t,e,n){var r;return"#"+Mh(r=((1<<24)+(t<<16)+(e<<8)+n).toString(16)).call(r,1)}function Gm(t,e){if(mm(t)){var n=t;if(rb(n)){var r,i=$h(r=n.substr(4).substr(0,n.length-5).split(",")).call(r,(function(t){return Gy(t)}));n=Um(i[0],i[1],i[2])}if(!0===nb(n)){var o=eb(n),a={h:o.h,s:.8*o.s,v:Math.min(1,1.02*o.v)},s={h:o.h,s:Math.min(1,1.25*o.s),v:.8*o.v},c=tb(s.h,s.s,s.v),u=tb(a.h,a.s,a.v);return{background:n,border:c,highlight:{background:u,border:c},hover:{background:u,border:c}}}return{background:n,border:n,highlight:{background:n,border:n},hover:{background:n,border:n}}}return e?{background:t.background||e.background,border:t.border||e.border,highlight:mm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:mm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}:{background:t.background||void 0,border:t.border||void 0,highlight:mm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:mm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function Qm(t,e,n){t/=255,e/=255,n/=255;var r=Math.min(t,Math.min(e,n)),i=Math.max(t,Math.max(e,n));return r===i?{h:0,s:0,v:r}:{h:60*((t===r?3:n===r?1:5)-(t===r?e-n:n===r?t-e:n-t)/(i-r))/360,s:(i-r)/i,v:i}}function $m(t){var e=document.createElement("div"),n={};e.style.cssText=t;for(var r=0;r<e.style.length;++r)n[e.style[r]]=e.style.getPropertyValue(e.style[r]);return n}function Jm(t,e){for(var n=$m(e),r=0,i=am(n);r<i.length;r++){var o=Ef(i[r],2),a=o[0],s=o[1];t.style.setProperty(a,s)}}function Km(t,e){for(var n=$m(e),r=0,i=Zh(n);r<i.length;r++){var o=i[r];t.style.removeProperty(o)}}function Zm(t,e,n){var r,i,o,a=Math.floor(6*t),s=6*t-a,c=n*(1-e),u=n*(1-s*e),l=n*(1-(1-s)*e);switch(a%6){case 0:r=n,i=l,o=c;break;case 1:r=u,i=n,o=c;break;case 2:r=c,i=n,o=l;break;case 3:r=c,i=u,o=n;break;case 4:r=l,i=c,o=n;break;case 5:r=n,i=c,o=u}return{r:Math.floor(255*r),g:Math.floor(255*i),b:Math.floor(255*o)}}function tb(t,e,n){var r=Zm(t,e,n);return Um(r.r,r.g,r.b)}function eb(t){var e=Xm(t);if(!e)throw new TypeError("'".concat(t,"' is not a valid color."));return Qm(e.r,e.g,e.b)}function nb(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function rb(t){return vm.test(t)}function ib(t){return dm.test(t)}function ob(t,e){if(null!==e&&"object"===eh(e)){for(var n=cm(e),r=0;r<t.length;r++)Object.prototype.hasOwnProperty.call(e,t[r])&&"object"==eh(e[t[r]])&&(n[t[r]]=ab(e[t[r]]));return n}return null}function ab(t){if(null===t||"object"!==eh(t))return null;if(t instanceof Element)return t;var e=cm(t);for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&"object"==eh(t[n])&&(e[n]=ab(t[n]));return e}function sb(t,e){for(var n=0;n<t.length;n++){var r=t[n],i=void 0;for(i=n;i>0&&e(r,t[i-1])<0;i--)t[i]=t[i-1];t[i]=r}return t}function cb(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=function(t){return null!=t},o=function(t){return null!==t&&"object"===eh(t)};if(!o(t))throw new Error("Parameter mergeTarget must be an object");if(!o(e))throw new Error("Parameter options must be an object");if(!i(n))throw new Error("Parameter option must have a value");if(!o(r))throw new Error("Parameter globalOptions must be an object");var a=e[n],s=o(r)&&!function(t){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(r)?r[n]:void 0,c=s?s.enabled:void 0;if(void 0!==a){if("boolean"==typeof a)return o(t[n])||(t[n]={}),void(t[n].enabled=a);if(null===a&&!o(t[n])){if(!i(s))return;t[n]=cm(s)}if(o(a)){var u=!0;void 0!==a.enabled?u=a.enabled:void 0!==c&&(u=s.enabled),function(t,e,n){o(t[n])||(t[n]={});var r=e[n],i=t[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(i[a]=r[a])}(t,e,n),t[n].enabled=u}}}function ub(t,e,n,r){for(var i=0,o=0,a=t.length-1;o<=a&&i<1e4;){var s=Math.floor((o+a)/2),c=t[s],u=e(void 0===r?c[n]:c[n][r]);if(0==u)return s;-1==u?o=s+1:a=s-1,i++}return-1}function lb(t,e,n,r,i){var o,a,s,c,u=0,l=0,h=t.length-1;for(i=null!=i?i:function(t,e){return t==e?0:t<e?-1:1};l<=h&&u<1e4;){if(c=Math.floor(.5*(h+l)),o=t[Math.max(0,c-1)][n],a=t[c][n],s=t[Math.min(t.length-1,c+1)][n],0==i(a,e))return c;if(i(o,e)<0&&i(a,e)>0)return"before"==r?Math.max(0,c-1):c;if(i(a,e)<0&&i(s,e)>0)return"before"==r?c:Math.min(t.length-1,c+1);i(a,e)<0?l=c+1:h=c-1,u++}return-1}var hb={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return t*(2-t)},easeInOutQuad:function(t){return t<.5?2*t*t:(4-2*t)*t-1},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return--t*t*t+1},easeInOutCubic:function(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1- --t*t*t*t},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-8*--t*t*t*t},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1+--t*t*t*t*t},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t}};function fb(){var t=document.createElement("p");t.style.width="100%",t.style.height="200px";var e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);var n=t.offsetWidth;e.style.overflow="scroll";var r=t.offsetWidth;return n==r&&(r=e.clientWidth),document.body.removeChild(e),n-r}function pb(t,e){var n;Yh(e)||(e=[e]);var r,i=um(t);try{for(i.s();!(r=i.n()).done;){var o=r.value;if(o){n=o[e[0]];for(var a=1;a<e.length;a++)n&&(n=n[e[a]]);if(void 0!==n)break}}}catch(t){i.e(t)}finally{i.f()}return n}var vb={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"},db=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;Id(this,t),this.pixelRatio=e,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=function(){},this.closeCallback=function(){},this._create()}return Xd(t,[{key:"insertTo",value:function(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}},{key:"setUpdateCallback",value:function(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}},{key:"setCloseCallback",value:function(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}},{key:"_isColorString",value:function(t){if("string"==typeof t)return vb[t]}},{key:"setColor",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("none"!==t){var n,r=this._isColorString(t);if(void 0!==r&&(t=r),!0===mm(t)){if(!0===rb(t)){var i=t.substr(4).substr(0,t.length-5).split(",");n={r:i[0],g:i[1],b:i[2],a:1}}else if(!0===ib(t)){var o=t.substr(5).substr(0,t.length-6).split(",");n={r:o[0],g:o[1],b:o[2],a:o[3]}}else if(!0===nb(t)){var a=Xm(t);n={r:a.r,g:a.g,b:a.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){var s=void 0!==t.a?t.a:"1.0";n={r:t.r,g:t.g,b:t.b,a:s}}if(void 0===n)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+Gd(t));this._setColor(n,e)}}},{key:"show",value:function(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}},{key:"_hide",value:function(){var t=this;!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.previousColor=ug({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",Sg((function(){void 0!==t.closeCallback&&(t.closeCallback(),t.closeCallback=void 0)}),0)}},{key:"_save",value:function(){this.updateCallback(this.color),this.applied=!1,this._hide()}},{key:"_apply",value:function(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}},{key:"_loadLast",value:function(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}},{key:"_setColor",value:function(t){!0===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(this.initialColor=ug({},t)),this.color=t;var e=Qm(t.r,t.g,t.b),n=2*Math.PI,r=this.r*e.s,i=this.centerCoordinates.x+r*Math.sin(n*e.h),o=this.centerCoordinates.y+r*Math.cos(n*e.h);this.colorPickerSelector.style.left=i-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=o-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}},{key:"_setOpacity",value:function(t){this.color.a=t/100,this._updatePicker(this.color)}},{key:"_setBrightness",value:function(t){var e=Qm(this.color.r,this.color.g,this.color.b);e.v=t/100;var n=Zm(e.h,e.s,e.v);n.a=this.color.a,this.color=n,this._updatePicker()}},{key:"_updatePicker",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color,e=Qm(t.r,t.g,t.b),n=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(n.webkitBackingStorePixelRatio||n.mozBackingStorePixelRatio||n.msBackingStorePixelRatio||n.oBackingStorePixelRatio||n.backingStorePixelRatio||1)),n.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var r=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;n.clearRect(0,0,r,i),n.putImageData(this.hueCircle,0,0),n.fillStyle="rgba(0,0,0,"+(1-e.v)+")",n.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),Mg(n).call(n),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}},{key:"_setSize",value:function(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}},{key:"_create",value:function(){var t,e,n,r;if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){var i=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{var o=document.createElement("DIV");o.style.color="red",o.style.fontWeight="bold",o.style.padding="10px",o.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(o)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);var a=this;this.opacityRange.onchange=function(){a._setOpacity(this.value)},this.opacityRange.oninput=function(){a._setOpacity(this.value)},this.brightnessRange.onchange=function(){a._setBrightness(this.value)},this.brightnessRange.oninput=function(){a._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=Xf(t=this._hide).call(t,this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=Xf(e=this._apply).call(e,this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=Xf(n=this._save).call(n,this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=Xf(r=this._loadLast).call(r,this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}},{key:"_bindHammer",value:function(){var t=this;this.drag={},this.pinch={},this.hammer=new Pd(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",(function(e){e.isFirst&&t._moveSelector(e)})),this.hammer.on("tap",(function(e){t._moveSelector(e)})),this.hammer.on("panstart",(function(e){t._moveSelector(e)})),this.hammer.on("panmove",(function(e){t._moveSelector(e)})),this.hammer.on("panend",(function(e){t._moveSelector(e)}))}},{key:"_generateHueCircle",value:function(){if(!1===this.generated){var t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var e,n,r,i,o=this.colorPickerCanvas.clientWidth,a=this.colorPickerCanvas.clientHeight;t.clearRect(0,0,o,a),this.centerCoordinates={x:.5*o,y:.5*a},this.r=.49*o;var s,c=2*Math.PI/360,u=1/this.r;for(r=0;r<360;r++)for(i=0;i<this.r;i++)e=this.centerCoordinates.x+i*Math.sin(c*r),n=this.centerCoordinates.y+i*Math.cos(c*r),s=Zm(.002777777777777778*r,i*u,1),t.fillStyle="rgb("+s.r+","+s.g+","+s.b+")",t.fillRect(e-.5,n-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,o,a)}this.generated=!0}},{key:"_moveSelector",value:function(t){var e=this.colorPickerDiv.getBoundingClientRect(),n=t.center.x-e.left,r=t.center.y-e.top,i=.5*this.colorPickerDiv.clientHeight,o=.5*this.colorPickerDiv.clientWidth,a=n-o,s=r-i,c=Math.atan2(a,s),u=.98*Math.min(Math.sqrt(a*a+s*s),o),l=Math.cos(c)*u+i,h=Math.sin(c)*u+o;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=h-.5*this.colorPickerSelector.clientWidth+"px";var f=c/(2*Math.PI);f=f<0?f+1:f;var p=u/this.r,v=Qm(this.color.r,this.color.g,this.color.b);v.h=f,v.s=p;var d=Zm(v.h,v.s,v.v);d.a=this.color.a,this.color=d,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}}]),t}();function gb(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<1)throw new TypeError("Invalid arguments.");if(1===e.length)return document.createTextNode(e[0]);var r=document.createElement(e[0]);return r.appendChild(gb.apply(void 0,Ah(Mh(e).call(e,1)))),r}var yb,mb=function(){function t(e,n,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(){return!1};Id(this,t),this.parent=e,this.changedOptions=[],this.container=n,this.allowCreation=!1,this.hideOption=o,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},ug(this.options,this.defaultOptions),this.configureOptions=r,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new db(i),this.wrapper=void 0}return Xd(t,[{key:"setOptions",value:function(t){if(void 0!==t){this.popupHistory={},this._removePopup();var e=!0;if("string"==typeof t)this.options.filter=t;else if(Yh(t))this.options.filter=t.join();else if("object"===eh(t)){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==py(t)&&(this.options.filter=py(t)),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===py(this.options)&&(e=!1),this.options.enabled=e}this._clean()}},{key:"setModuleOptions",value:function(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}},{key:"_create",value:function(){this._clean(),this.changedOptions=[];var t=py(this.options),e=0,n=!1;for(var r in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,r)&&(this.allowCreation=!1,n=!1,"function"==typeof t?n=(n=t(r,[]))||this._handleObject(this.configureOptions[r],[r],!0):!0!==t&&-1===im(t).call(t,r)||(n=!0),!1!==n&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(r),this._handleObject(this.configureOptions[r],[r])),e++);this._makeButton(),this._push()}},{key:"_push",value:function(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(var t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}},{key:"_clean",value:function(){for(var t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}},{key:"_getValue",value:function(t){for(var e=this.moduleOptions,n=0;n<t.length;n++){if(void 0===e[t[n]]){e=void 0;break}e=e[t[n]]}return e}},{key:"_makeItem",value:function(t){if(!0===this.allowCreation){var e=document.createElement("div");e.className="vis-configuration vis-config-item vis-config-s"+t.length;for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return rp(r).call(r,(function(t){e.appendChild(t)})),this.domElements.push(e),this.domElements.length}return 0}},{key:"_makeHeader",value:function(t){var e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}},{key:"_makeLabel",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=document.createElement("div");if(r.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===n){for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(gb("i","b",t))}else r.innerText=t+":";return r}},{key:"_makeDropdown",value:function(t,e,n){var r=document.createElement("select");r.className="vis-configuration vis-config-select";var i=0;void 0!==e&&-1!==im(t).call(t,e)&&(i=im(t).call(t,e));for(var o=0;o<t.length;o++){var a=document.createElement("option");a.value=t[o],o===i&&(a.selected="selected"),a.innerText=t[o],r.appendChild(a)}var s=this;r.onchange=function(){s._update(this.value,n)};var c=this._makeLabel(n[n.length-1],n);this._makeItem(n,c,r)}},{key:"_makeRange",value:function(t,e,n){var r=t[0],i=t[1],o=t[2],a=t[3],s=document.createElement("input");s.className="vis-configuration vis-config-range";try{s.type="range",s.min=i,s.max=o}catch(t){}s.step=a;var c="",u=0;if(void 0!==e){var l=1.2;e<0&&e*l<i?(s.min=Math.ceil(e*l),u=s.min,c="range increased"):e/l<i&&(s.min=Math.ceil(e/l),u=s.min,c="range increased"),e*l>o&&1!==o&&(s.max=Math.ceil(e*l),u=s.max,c="range increased"),s.value=e}else s.value=r;var h=document.createElement("input");h.className="vis-configuration vis-config-rangeinput",h.value=s.value;var f=this;s.onchange=function(){h.value=this.value,f._update(Number(this.value),n)},s.oninput=function(){h.value=this.value};var p=this._makeLabel(n[n.length-1],n),v=this._makeItem(n,p,s,h);""!==c&&this.popupHistory[v]!==u&&(this.popupHistory[v]=u,this._setupPopup(c,v))}},{key:"_makeButton",value:function(){var t=this;if(!0===this.options.showButton){var e=document.createElement("div");e.className="vis-configuration vis-config-button",e.innerText="generate options",e.onclick=function(){t._printOptions()},e.onmouseover=function(){e.className="vis-configuration vis-config-button hover"},e.onmouseout=function(){e.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(e)}}},{key:"_setupPopup",value:function(t,e){var n=this;if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){var r=document.createElement("div");r.id="vis-configuration-popup",r.className="vis-configuration-popup",r.innerText=t,r.onclick=function(){n._removePopup()},this.popupCounter+=1,this.popupDiv={html:r,index:e}}}},{key:"_removePopup",value:function(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}},{key:"_showPopupIfNeeded",value:function(){var t=this;if(void 0!==this.popupDiv.html){var e=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=e.left+"px",this.popupDiv.html.style.top=e.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=Sg((function(){t.popupDiv.html.style.opacity=0}),1500),this.popupDiv.deleteTimeout=Sg((function(){t._removePopup()}),1800)}}},{key:"_makeCheckbox",value:function(t,e,n){var r=document.createElement("input");r.type="checkbox",r.className="vis-configuration vis-config-checkbox",r.checked=t,void 0!==e&&(r.checked=e,e!==t&&("object"===eh(t)?e!==t.enabled&&this.changedOptions.push({path:n,value:e}):this.changedOptions.push({path:n,value:e})));var i=this;r.onchange=function(){i._update(this.checked,n)};var o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}},{key:"_makeTextInput",value:function(t,e,n){var r=document.createElement("input");r.type="text",r.className="vis-configuration vis-config-text",r.value=e,e!==t&&this.changedOptions.push({path:n,value:e});var i=this;r.onchange=function(){i._update(this.value,n)};var o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}},{key:"_makeColorField",value:function(t,e,n){var r=this,i=t[1],o=document.createElement("div");"none"!==(e=void 0===e?i:e)?(o.className="vis-configuration vis-config-colorBlock",o.style.backgroundColor=e):o.className="vis-configuration vis-config-colorBlock none",e=void 0===e?i:e,o.onclick=function(){r._showColorPicker(e,o,n)};var a=this._makeLabel(n[n.length-1],n);this._makeItem(n,a,o)}},{key:"_showColorPicker",value:function(t,e,n){var r=this;e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback((function(t){var i="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=i,r._update(i,n)})),this.colorPicker.setCloseCallback((function(){e.onclick=function(){r._showColorPicker(t,e,n)}}))}},{key:"_handleObject",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!1,i=py(this.options),o=!1;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){r=!0;var s=t[a],c=Pm(e,a);if("function"==typeof i&&!1===(r=i(a,e))&&!Yh(s)&&"string"!=typeof s&&"boolean"!=typeof s&&s instanceof Object&&(this.allowCreation=!1,r=this._handleObject(s,c,!0),this.allowCreation=!1===n),!1!==r){o=!0;var u=this._getValue(c);if(Yh(s))this._handleArray(s,u,c);else if("string"==typeof s)this._makeTextInput(s,u,c);else if("boolean"==typeof s)this._makeCheckbox(s,u,c);else if(s instanceof Object){if(!this.hideOption(e,a,this.moduleOptions))if(void 0!==s.enabled){var l=Pm(c,"enabled"),h=this._getValue(l);if(!0===h){var f=this._makeLabel(a,c,!0);this._makeItem(c,f),o=this._handleObject(s,c)||o}else this._makeCheckbox(s,h,c)}else{var p=this._makeLabel(a,c,!0);this._makeItem(c,p),o=this._handleObject(s,c)||o}}else console.error("dont know how to handle",s,a,c)}}return o}},{key:"_handleArray",value:function(t,e,n){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,n),t[1]!==e&&this.changedOptions.push({path:n,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:Number(e)}))}},{key:"_update",value:function(t,e){var n=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",n),this.initialized=!0,this.parent.setOptions(n)}},{key:"_constructOptions",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n;t="false"!==(t="true"===t||t)&&t;for(var i=0;i<e.length;i++)"global"!==e[i]&&(void 0===r[e[i]]&&(r[e[i]]={}),i!==e.length-1?r=r[e[i]]:r[e[i]]=t);return n}},{key:"_printOptions",value:function(){for(var t=this.getOptions();this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(gb("pre","const options = "+Gd(t,null,2)))}},{key:"getOptions",value:function(){for(var t={},e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}}]),t}(),bb=function(){function t(e,n){Id(this,t),this.container=e,this.overflowMethod=n||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}return Xd(t,[{key:"setPosition",value:function(t,e){this.x=Gy(t),this.y=Gy(e)}},{key:"setText",value:function(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}},{key:"show",value:function(t){if(void 0===t&&(t=!0),!0===t){var e=this.frame.clientHeight,n=this.frame.clientWidth,r=this.frame.parentNode.clientHeight,i=this.frame.parentNode.clientWidth,o=0,a=0;if("flip"==this.overflowMethod){var s=!1,c=!0;this.y-e<this.padding&&(c=!1),this.x+n>i-this.padding&&(s=!0),o=s?this.x-n:this.x,a=c?this.y-e:this.y}else(a=this.y-e)+e+this.padding>r&&(a=r-e-this.padding),a<this.padding&&(a=this.padding),(o=this.x)+n+this.padding>i&&(o=i-n-this.padding),o<this.padding&&(o=this.padding);this.frame.style.left=o+"px",this.frame.style.top=a+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}},{key:"hide",value:function(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}},{key:"destroy",value:function(){this.frame.parentNode.removeChild(this.frame)}}]),t}(),wb=!1,kb="background: #FFeeee; color: #dd0000",Cb=function(){function t(){Id(this,t)}return Xd(t,null,[{key:"validate",value:function(e,n,r){wb=!1,yb=n;var i=n;return void 0!==r&&(i=n[r]),t.parse(e,i,[]),wb}},{key:"parse",value:function(e,n,r){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.check(i,e,n,r)}},{key:"check",value:function(e,n,r,i){if(void 0!==r[e]||void 0!==r.__any__){var o=e,a=!0;void 0===r[e]&&void 0!==r.__any__&&(o="__any__",a="object"===t.getType(n[e]));var s=r[o];a&&void 0!==s.__type__&&(s=s.__type__),t.checkFields(e,n,r,o,s,i)}else t.getSuggestion(e,r,i)}},{key:"checkFields",value:function(e,n,r,i,o,a){var s=function(n){console.error("%c"+n+t.printLocation(a,e),kb)},c=t.getType(n[e]),u=o[c];void 0!==u?"array"===t.getType(u)&&-1===im(u).call(u,n[e])?(s('Invalid option detected in "'+e+'". Allowed values are:'+t.print(u)+' not "'+n[e]+'". '),wb=!0):"object"===c&&"__any__"!==i&&(a=Pm(a,e),t.parse(n[e],r[i],a)):void 0===o.any&&(s('Invalid type received for "'+e+'". Expected: '+t.print(Zh(o))+". Received ["+c+'] "'+n[e]+'"'),wb=!0)}},{key:"getType",value:function(t){var e=eh(t);return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Yh(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}},{key:"getSuggestion",value:function(e,n,r){var i,o=t.findInOptions(e,n,r,!1),a=t.findInOptions(e,yb,[],!0);i=void 0!==o.indexMatch?" in "+t.printLocation(o.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+o.indexMatch+'"?\n\n':a.distance<=4&&o.distance>a.distance?" in "+t.printLocation(o.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(a.path,a.closestMatch,""):o.distance<=8?'. Did you mean "'+o.closestMatch+'"?'+t.printLocation(o.path,e):". Did you mean one of these: "+t.print(Zh(n))+t.printLocation(r,e),console.error('%cUnknown option detected: "'+e+'"'+i,kb),wb=!0}},{key:"findInOptions",value:function(e,n,r){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=1e9,a="",s=[],c=e.toLowerCase(),u=void 0;for(var l in n){var h=void 0;if(void 0!==n[l].__type__&&!0===i){var f=t.findInOptions(e,n[l],Pm(r,l));o>f.distance&&(a=f.closestMatch,s=f.path,o=f.distance,u=f.indexMatch)}else{var p;-1!==im(p=l.toLowerCase()).call(p,c)&&(u=l),o>(h=t.levenshteinDistance(e,l))&&(a=l,s=xm(r),o=h)}}return{closestMatch:a,path:s,distance:o,indexMatch:u}}},{key:"printLocation",value:function(t,e){for(var n="\n\n"+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Problem value found at: \n")+"options = {\n",r=0;r<t.length;r++){for(var i=0;i<r+1;i++)n+="  ";n+=t[r]+": {\n"}for(var o=0;o<t.length+1;o++)n+="  ";n+=e+"\n";for(var a=0;a<t.length+1;a++){for(var s=0;s<t.length-a;s++)n+="  ";n+="}\n"}return n+"\n\n"}},{key:"print",value:function(t){return Gd(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}},{key:"levenshteinDistance",value:function(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;var n,r,i=[];for(n=0;n<=e.length;n++)i[n]=[n];for(r=0;r<=t.length;r++)i[0][r]=r;for(n=1;n<=e.length;n++)for(r=1;r<=t.length;r++)e.charAt(n-1)==t.charAt(r-1)?i[n][r]=i[n-1][r-1]:i[n][r]=Math.min(i[n-1][r-1]+1,Math.min(i[n][r-1]+1,i[n-1][r]+1));return i[e.length][t.length]}}]),t}(),Eb=jd,Ob=db,Fb=mb,Tb=Pd,_b=bb,Sb=kb,Ab=Cb;export{Eb as Activator,Sf as Alea,Ob as ColorPicker,Fb as Configurator,nf as DELETE,tb as HSVToHex,Zm as HSVToRGB,Tb as Hammer,_b as Popup,Qm as RGBToHSV,Um as RGBToHex,Sb as VALIDATOR_PRINT_STYLE,Ab as Validator,Rm as addClassName,Jm as addCssText,ub as binarySearchCustom,lb as binarySearchValue,ab as bridgeObject,Pm as copyAndExtendArray,xm as copyArray,_m as deepExtend,of as deepObjectAssign,hb as easingFunctions,Sm as equalArray,Em as extend,Cm as fillIfDefined,Nm as forEach,Dm as getAbsoluteLeft,jm as getAbsoluteRight,Im as getAbsoluteTop,fb as getScrollBarWidth,Wm as getTarget,Am as getType,qm as hasParent,eb as hexToHSV,Xm as hexToRGB,sb as insertSort,wm as isDate,gm as isNumber,bm as isObject,mm as isString,nb as isValidHex,rb as isValidRGB,ib as isValidRGBA,cb as mergeOptions,Ym as option,Vm as overrideOpacity,Gm as parseColor,Hm as preventDefault,rf as pureDeepObjectAssign,ym as recursiveDOMDelete,Mm as removeClassName,Km as removeCssText,ob as selectiveBridgeObject,Fm as selectiveDeepExtend,Om as selectiveExtend,Tm as selectiveNotDeepExtend,zm as throttle,Bm as toArray,pb as topMost,Lm as updateProperty};
//# sourceMappingURL=vis-util.min.js.map
