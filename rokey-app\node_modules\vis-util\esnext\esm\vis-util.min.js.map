{"version": 3, "file": "vis-util.min.js", "sources": ["../../src/deep-object-assign.ts", "../../src/random/alea.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/validator.js", "../../src/shared/index.ts", "../../src/shared/popup.js"], "sourcesContent": ["/**\n * Use this symbol to delete properies in deepObjectAssign.\n */\nexport const DELETE = Symbol(\"DELETE\");\n\n/**\n * Turns `undefined` into `undefined | typeof DELETE` and makes everything\n * partial. Intended to be used with `deepObjectAssign`.\n */\nexport type Assignable<T> = T extends undefined\n  ?\n      | (T extends Function\n          ? T\n          : T extends object\n          ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n          : T)\n      | typeof DELETE\n  : T extends Function\n  ? T | undefined\n  : T extends object\n  ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n  : T | undefined;\n\n/**\n * Pure version of deepObjectAssign, it doesn't modify any of it's arguments.\n *\n * @param base - The base object that fullfils the whole interface T.\n * @param updates - Updates that may change or delete props.\n * @returns A brand new instance with all the supplied objects deeply merged.\n */\nexport function pureDeepObjectAssign<T>(\n  base: T,\n  ...updates: Assignable<T>[]\n): T {\n  return deepObjectAssign({} as any, base, ...updates);\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param target - The object that will be augmented using the sources.\n * @param sources - Objects to be deeply merged into the target.\n * @returns The target (same instance).\n */\nexport function deepObjectAssign<T>(target: T, ...sources: Assignable<T>[]): T;\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nexport function deepObjectAssign(...values: readonly any[]): any {\n  const merged = deepObjectAssignNonentry(...values);\n  stripDelete(merged);\n  return merged;\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @remarks\n * This doesn't strip the DELETE symbols so they may end up in the final object.\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nfunction deepObjectAssignNonentry(...values: readonly any[]): any {\n  if (values.length < 2) {\n    return values[0];\n  } else if (values.length > 2) {\n    return deepObjectAssignNonentry(\n      deepObjectAssign(values[0], values[1]),\n      ...values.slice(2)\n    );\n  }\n\n  const a = values[0];\n  const b = values[1];\n\n  if (a instanceof Date && b instanceof Date) {\n    a.setTime(b.getTime());\n    return a;\n  }\n\n  for (const prop of Reflect.ownKeys(b)) {\n    if (!Object.prototype.propertyIsEnumerable.call(b, prop)) {\n      // Ignore nonenumerable props, Object.assign() would do the same.\n    } else if (b[prop] === DELETE) {\n      delete a[prop];\n    } else if (\n      a[prop] !== null &&\n      b[prop] !== null &&\n      typeof a[prop] === \"object\" &&\n      typeof b[prop] === \"object\" &&\n      !Array.isArray(a[prop]) &&\n      !Array.isArray(b[prop])\n    ) {\n      a[prop] = deepObjectAssignNonentry(a[prop], b[prop]);\n    } else {\n      a[prop] = clone(b[prop]);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep clone given object or array. In case of primitive simply return.\n *\n * @param a - Anything.\n * @returns Deep cloned object/array or unchanged a.\n */\nfunction clone(a: any): any {\n  if (Array.isArray(a)) {\n    return a.map((value: any): any => clone(value));\n  } else if (typeof a === \"object\" && a !== null) {\n    if (a instanceof Date) {\n      return new Date(a.getTime());\n    }\n    return deepObjectAssignNonentry({}, a);\n  } else {\n    return a;\n  }\n}\n\n/**\n * Strip DELETE from given object.\n *\n * @param a - Object which may contain DELETE but won't after this is executed.\n */\nfunction stripDelete(a: any): void {\n  for (const prop of Object.keys(a)) {\n    if (a[prop] === DELETE) {\n      delete a[prop];\n    } else if (typeof a[prop] === \"object\" && a[prop] !== null) {\n      stripDelete(a[prop]);\n    }\n  }\n}\n", "/**\n * Seedable, fast and reasonably good (not crypto but more than okay for our\n * needs) random number generator.\n *\n * @remarks\n * Adapted from {@link https://web.archive.org/web/20110429100736/http://baagoe.com:80/en/RandomMusings/javascript}.\n * Original algorithm created by <PERSON> \\<baagoe\\@baagoe.com\\> in 2010.\n */\n\n/**\n * Random number generator.\n */\nexport interface RNG {\n  /** Returns \\<0, 1). Faster than [[fract53]]. */\n  (): number;\n  /** Returns \\<0, 1). Provides more precise data. */\n  fract53(): number;\n  /** Returns \\<0, 2^32). */\n  uint32(): number;\n\n  /** The algorithm gehind this instance. */\n  algorithm: string;\n  /** The seed used to seed this instance. */\n  seed: Mashable[];\n  /** The version of this instance. */\n  version: string;\n}\n\n/**\n * Create a seeded pseudo random generator based on <PERSON><PERSON> by <PERSON>.\n *\n * @param seed - All supplied arguments will be used as a seed. In case nothing\n * is supplied the current time will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nexport function Alea(...seed: Mashable[]): RNG {\n  return AleaImplementation(seed.length ? seed : [Date.now()]);\n}\n\n/**\n * An implementation of [[Alea]] without user input validation.\n *\n * @param seed - The data that will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nfunction AleaImplementation(seed: Mashable[]): RNG {\n  let [s0, s1, s2] = mashSeed(seed);\n  let c = 1;\n\n  const random: RNG = (): number => {\n    const t = 2091639 * s0 + c * 2.3283064365386963e-10; // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return (s2 = t - (c = t | 0));\n  };\n\n  random.uint32 = (): number => random() * 0x100000000; // 2^32\n\n  random.fract53 = (): number =>\n    random() + ((random() * 0x200000) | 0) * 1.1102230246251565e-16; // 2^-53\n\n  random.algorithm = \"Alea\";\n  random.seed = seed;\n  random.version = \"0.9\";\n\n  return random;\n}\n\n/**\n * Turn arbitrary data into values [[AleaImplementation]] can use to generate\n * random numbers.\n *\n * @param seed - Arbitrary data that will be used as the seed.\n * @returns Three numbers to use as initial values for [[AleaImplementation]].\n */\nfunction mashSeed(...seed: Mashable[]): [number, number, number] {\n  const mash = Mash();\n\n  let s0 = mash(\" \");\n  let s1 = mash(\" \");\n  let s2 = mash(\" \");\n\n  for (let i = 0; i < seed.length; i++) {\n    s0 -= mash(seed[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seed[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seed[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n\n  return [s0, s1, s2];\n}\n\n/**\n * Values of these types can be used as a seed.\n */\nexport type Mashable = number | string | boolean | object | bigint;\n\n/**\n * Create a new mash function.\n *\n * @returns A nonpure function that takes arbitrary [[Mashable]] data and turns\n * them into numbers.\n */\nfunction Mash(): (data: Mashable) => number {\n  let n = 0xefc8249d;\n\n  return function (data): number {\n    const string = data.toString();\n    for (let i = 0; i < string.length; i++) {\n      n += string.charCodeAt(i);\n      let h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n}\n", "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n *\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n *\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n *\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n *\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", "// utility functions\n\n// parse ASP.Net Date pattern,\n// for example '/Date(1198908717056)/' or '/Date(1198908717056-0700)/'\n// code from http://momentjs.com/\nconst ASPDateRegex = /^\\/?Date\\((-?\\d+)/i;\n\n// Color REs\nconst fullHexRE = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\nconst shortHexRE = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\nconst rgbRE =\n  /^rgb\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *\\)$/i;\nconst rgbaRE =\n  /^rgba\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *([01]|0?\\.\\d+) *\\)$/i;\n\n/**\n * Hue, Saturation, Value.\n */\nexport interface HSV {\n  /**\n   * Hue \\<0, 1\\>.\n   */\n  h: number;\n  /**\n   * Saturation \\<0, 1\\>.\n   */\n  s: number;\n  /**\n   * Value \\<0, 1\\>.\n   */\n  v: number;\n}\n\n/**\n * Red, Green, Blue.\n */\nexport interface RGB {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n}\n\n/**\n * Red, Green, Blue, Alpha.\n */\nexport interface RGBA {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n  /**\n   * Alpha \\<0, 1\\>.\n   */\n  a: number;\n}\n\n/**\n * Test whether given object is a number.\n *\n * @param value - Input value of unknown type.\n * @returns True if number, false otherwise.\n */\nexport function isNumber(value: unknown): value is number {\n  return value instanceof Number || typeof value === \"number\";\n}\n\n/**\n * Remove everything in the DOM object.\n *\n * @param DOMobject - Node whose child nodes will be recursively deleted.\n */\nexport function recursiveDOMDelete(DOMobject: Node | null | undefined): void {\n  if (DOMobject) {\n    while (DOMobject.hasChildNodes() === true) {\n      const child = DOMobject.firstChild;\n      if (child) {\n        recursiveDOMDelete(child);\n        DOMobject.removeChild(child);\n      }\n    }\n  }\n}\n\n/**\n * Test whether given object is a string.\n *\n * @param value - Input value of unknown type.\n * @returns True if string, false otherwise.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === \"string\";\n}\n\n/**\n * Test whether given object is a object (not primitive or null).\n *\n * @param value - Input value of unknown type.\n * @returns True if not null object, false otherwise.\n */\nexport function isObject(value: unknown): value is object {\n  return typeof value === \"object\" && value !== null;\n}\n\n/**\n * Test whether given object is a Date, or a String containing a Date.\n *\n * @param value - Input value of unknown type.\n * @returns True if Date instance or string date representation, false otherwise.\n */\nexport function isDate(value: unknown): value is Date | string {\n  if (value instanceof Date) {\n    return true;\n  } else if (isString(value)) {\n    // test whether this string contains a date\n    const match = ASPDateRegex.exec(value);\n    if (match) {\n      return true;\n    } else if (!isNaN(Date.parse(value))) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Copy property from b to a if property present in a.\n * If property in b explicitly set to null, delete it if `allowDeletion` set.\n *\n * Internal helper routine, should not be exported. Not added to `exports` for that reason.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param prop - Name of property to copy from b to a.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n */\nfunction copyOrDelete(\n  a: any,\n  b: any,\n  prop: string,\n  allowDeletion: boolean\n): void {\n  let doDeletion = false;\n  if (allowDeletion === true) {\n    doDeletion = b[prop] === null && a[prop] !== undefined;\n  }\n\n  if (doDeletion) {\n    delete a[prop];\n  } else {\n    a[prop] = b[prop]; // Remember, this is a reference copy!\n  }\n}\n\n/**\n * Fill an object with a possibly partially defined other object.\n *\n * Only copies values for the properties already present in a.\n * That means an object is not created on a property if only the b object has it.\n *\n * @param a - The object that will have it's properties updated.\n * @param b - The object with property updates.\n * @param allowDeletion - If true, delete properties in a that are explicitly set to null in b.\n */\nexport function fillIfDefined<T extends object>(\n  a: T,\n  b: Partial<T>,\n  allowDeletion = false\n): void {\n  // NOTE: iteration of properties of a\n  // NOTE: prototype properties iterated over as well\n  for (const prop in a) {\n    if (b[prop] !== undefined) {\n      if (b[prop] === null || typeof b[prop] !== \"object\") {\n        // Note: typeof null === 'object'\n        copyOrDelete(a, b, prop, allowDeletion);\n      } else {\n        const aProp = a[prop];\n        const bProp = b[prop];\n        if (isObject(aProp) && isObject(bProp)) {\n          fillIfDefined(aProp, bProp, allowDeletion);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Copy the values of all of the enumerable own properties from one or more source objects to a\n * target object. Returns the target object.\n *\n * @param target - The target object to copy to.\n * @param source - The source object from which to copy properties.\n * @returns The target object.\n */\nexport const extend = Object.assign;\n\n/**\n * Extend object a with selected properties of object b or a series of objects.\n *\n * @remarks\n * Only properties with defined values are copied.\n * @param props - Properties to be copied to a.\n * @param a - The target.\n * @param others - The sources.\n * @returns Argument a.\n */\nexport function selectiveExtend(\n  props: string[],\n  a: any,\n  ...others: any[]\n): any {\n  if (!Array.isArray(props)) {\n    throw new Error(\"Array with property names expected as first argument\");\n  }\n\n  for (const other of others) {\n    for (let p = 0; p < props.length; p++) {\n      const prop = props[p];\n      if (other && Object.prototype.hasOwnProperty.call(other, prop)) {\n        a[prop] = other[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object a with selected properties of object b.\n * Only properties with defined values are copied.\n *\n * @remarks\n * Previous version of this routine implied that multiple source objects could\n * be used; however, the implementation was **wrong**. Since multiple (\\>1)\n * sources weren't used anywhere in the `vis.js` code, this has been removed\n * @param props - Names of first-level properties to copy over.\n * @param a - Target object.\n * @param b - Source object.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n * @returns Argument a.\n */\nexport function selectiveDeepExtend(\n  props: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (let p = 0; p < props.length; p++) {\n    const prop = props[p];\n    if (Object.prototype.hasOwnProperty.call(b, prop)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop], false, allowDeletion);\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError(\"Arrays are not supported by deepExtend\");\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object `a` with properties of object `b`, ignoring properties which\n * are explicitly specified to be excluded.\n *\n * @remarks\n * The properties of `b` are considered for copying. Properties which are\n * themselves objects are are also extended. Only properties with defined\n * values are copied.\n * @param propsToExclude - Names of properties which should *not* be copied.\n * @param a - Object to extend.\n * @param b - Object to take properties from for extension.\n * @param allowDeletion - If true, delete properties in a that are explicitly\n * set to null in b.\n * @returns Argument a.\n */\nexport function selectiveNotDeepExtend(\n  propsToExclude: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  // NOTE: array properties have an else-below; apparently, there is a problem here.\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (const prop in b) {\n    if (!Object.prototype.hasOwnProperty.call(b, prop)) {\n      continue;\n    } // Handle local properties only\n    if (propsToExclude.includes(prop)) {\n      continue;\n    } // In exclusion list, skip\n\n    if (b[prop] && b[prop].constructor === Object) {\n      if (a[prop] === undefined) {\n        a[prop] = {};\n      }\n      if (a[prop].constructor === Object) {\n        deepExtend(a[prop], b[prop]); // NOTE: allowDeletion not propagated!\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    } else if (Array.isArray(b[prop])) {\n      a[prop] = [];\n      for (let i = 0; i < b[prop].length; i++) {\n        a[prop].push(b[prop][i]);\n      }\n    } else {\n      copyOrDelete(a, b, prop, allowDeletion);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param protoExtend - If true, the prototype values will also be extended.\n * (That is the options objects that inherit from others will also get the\n * inherited options).\n * @param allowDeletion - If true, the values of fields that are null will be deleted.\n * @returns Argument a.\n */\nexport function deepExtend(\n  a: any,\n  b: any,\n  protoExtend = false,\n  allowDeletion = false\n): any {\n  for (const prop in b) {\n    if (Object.prototype.hasOwnProperty.call(b, prop) || protoExtend === true) {\n      if (\n        typeof b[prop] === \"object\" &&\n        b[prop] !== null &&\n        Object.getPrototypeOf(b[prop]) === Object.prototype\n      ) {\n        if (a[prop] === undefined) {\n          a[prop] = deepExtend({}, b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else if (\n          typeof a[prop] === \"object\" &&\n          a[prop] !== null &&\n          Object.getPrototypeOf(a[prop]) === Object.prototype\n        ) {\n          deepExtend(a[prop], b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        a[prop] = b[prop].slice();\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Test whether all elements in two arrays are equal.\n *\n * @param a - First array.\n * @param b - Second array.\n * @returns True if both arrays have the same length and same elements (1 = '1').\n */\nexport function equalArray(a: unknown[], b: unknown[]): boolean {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0, len = a.length; i < len; i++) {\n    if (a[i] != b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Get the type of an object, for example exports.getType([]) returns 'Array'.\n *\n * @param object - Input value of unknown type.\n * @returns Detected type.\n */\nexport function getType(object: unknown): string {\n  const type = typeof object;\n\n  if (type === \"object\") {\n    if (object === null) {\n      return \"null\";\n    }\n    if (object instanceof Boolean) {\n      return \"Boolean\";\n    }\n    if (object instanceof Number) {\n      return \"Number\";\n    }\n    if (object instanceof String) {\n      return \"String\";\n    }\n    if (Array.isArray(object)) {\n      return \"Array\";\n    }\n    if (object instanceof Date) {\n      return \"Date\";\n    }\n\n    return \"Object\";\n  }\n  if (type === \"number\") {\n    return \"Number\";\n  }\n  if (type === \"boolean\") {\n    return \"Boolean\";\n  }\n  if (type === \"string\") {\n    return \"String\";\n  }\n  if (type === undefined) {\n    return \"undefined\";\n  }\n\n  return type;\n}\n\nexport function copyAndExtendArray<T>(arr: ReadonlyArray<T>, newValue: T): T[];\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[];\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - First part.\n * @param newValue - The value to be aadded into the array.\n * @returns A new array with all items from arr and newValue (which is last).\n */\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[] {\n  return [...arr, newValue];\n}\n\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - The array to be copied.\n * @returns Shallow copy of arr.\n */\nexport function copyArray<T>(arr: ReadonlyArray<T>): T[] {\n  return arr.slice();\n}\n\n/**\n * Retrieve the absolute left value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute left position of this element in the browser page.\n */\nexport function getAbsoluteLeft(elem: Element): number {\n  return elem.getBoundingClientRect().left;\n}\n\n/**\n * Retrieve the absolute right value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute right position of this element in the browser page.\n */\nexport function getAbsoluteRight(elem: Element): number {\n  return elem.getBoundingClientRect().right;\n}\n\n/**\n * Retrieve the absolute top value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute top position of this element in the browser page.\n */\nexport function getAbsoluteTop(elem: Element): number {\n  return elem.getBoundingClientRect().top;\n}\n\n/**\n * Add a className to the given elements style.\n *\n * @param elem - The element to which the classes will be added.\n * @param classNames - Space separated list of classes.\n */\nexport function addClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const newClasses = classNames.split(\" \");\n  classes = classes.concat(\n    newClasses.filter(function (className): boolean {\n      return !classes.includes(className);\n    })\n  );\n  elem.className = classes.join(\" \");\n}\n\n/**\n * Remove a className from the given elements style.\n *\n * @param elem - The element from which the classes will be removed.\n * @param classNames - Space separated list of classes.\n */\nexport function removeClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const oldClasses = classNames.split(\" \");\n  classes = classes.filter(function (className): boolean {\n    return !oldClasses.includes(className);\n  });\n  elem.className = classes.join(\" \");\n}\n\nexport function forEach<V>(\n  array: undefined | null | V[],\n  callback: (value: V, index: number, object: V[]) => void\n): void;\nexport function forEach<O extends object>(\n  object: undefined | null | O,\n  callback: <Key extends keyof O>(value: O[Key], key: Key, object: O) => void\n): void;\n/**\n * For each method for both arrays and objects.\n * In case of an array, the built-in Array.forEach() is applied (**No, it's not!**).\n * In case of an Object, the method loops over all properties of the object.\n *\n * @param object - An Object or Array to be iterated over.\n * @param callback - Array.forEach-like callback.\n */\nexport function forEach(object: any, callback: any): void {\n  if (Array.isArray(object)) {\n    // array\n    const len = object.length;\n    for (let i = 0; i < len; i++) {\n      callback(object[i], i, object);\n    }\n  } else {\n    // object\n    for (const key in object) {\n      if (Object.prototype.hasOwnProperty.call(object, key)) {\n        callback(object[key], key, object);\n      }\n    }\n  }\n}\n\n/**\n * Convert an object into an array: all objects properties are put into the array. The resulting array is unordered.\n *\n * @param o - Object that contains the properties and methods.\n * @returns An array of unordered values.\n */\nexport const toArray = Object.values;\n\n/**\n * Update a property in an object.\n *\n * @param object - The object whose property will be updated.\n * @param key - Name of the property to be updated.\n * @param value - The new value to be assigned.\n * @returns Whether the value was updated (true) or already strictly the same in the original object (false).\n */\nexport function updateProperty<K extends string, V>(\n  object: Record<K, V>,\n  key: K,\n  value: V\n): boolean {\n  if (object[key] !== value) {\n    object[key] = value;\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Throttle the given function to be only executed once per animation frame.\n *\n * @param fn - The original function.\n * @returns The throttled function.\n */\nexport function throttle(fn: () => void): () => void {\n  let scheduled = false;\n\n  return (): void => {\n    if (!scheduled) {\n      scheduled = true;\n      requestAnimationFrame((): void => {\n        scheduled = false;\n        fn();\n      });\n    }\n  };\n}\n\n/**\n * Cancels the event's default action if it is cancelable, without stopping further propagation of the event.\n *\n * @param event - The event whose default action should be prevented.\n */\nexport function preventDefault(event: Event | undefined): void {\n  if (!event) {\n    event = window.event;\n  }\n\n  if (!event) {\n    // No event, no work.\n  } else if (event.preventDefault) {\n    event.preventDefault(); // non-IE browsers\n  } else {\n    // @TODO: IE types? Does anyone care?\n    (event as any).returnValue = false; // IE browsers\n  }\n}\n\n/**\n * Get HTML element which is the target of the event.\n *\n * @param event - The event.\n * @returns The element or null if not obtainable.\n */\nexport function getTarget(\n  event: Event | undefined = window.event\n): Element | null {\n  // code from http://www.quirksmode.org/js/events_properties.html\n  // @TODO: EventTarget can be almost anything, is it okay to return only Elements?\n\n  let target: null | EventTarget = null;\n  if (!event) {\n    // No event, no target.\n  } else if (event.target) {\n    target = event.target;\n  } else if (event.srcElement) {\n    target = event.srcElement;\n  }\n\n  if (!(target instanceof Element)) {\n    return null;\n  }\n\n  if (target.nodeType != null && target.nodeType == 3) {\n    // defeat Safari bug\n    target = target.parentNode;\n    if (!(target instanceof Element)) {\n      return null;\n    }\n  }\n\n  return target;\n}\n\n/**\n * Check if given element contains given parent somewhere in the DOM tree.\n *\n * @param element - The element to be tested.\n * @param parent - The ancestor (not necessarily parent) of the element.\n * @returns True if parent is an ancestor of the element, false otherwise.\n */\nexport function hasParent(element: Element, parent: Element): boolean {\n  let elem: Node = element;\n\n  while (elem) {\n    if (elem === parent) {\n      return true;\n    } else if (elem.parentNode) {\n      elem = elem.parentNode;\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n\nexport const option = {\n  /**\n   * Convert a value into a boolean.\n   *\n   * @param value - Value to be converted intoboolean, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding boolean value, if none then the default value, if none then null.\n   */\n  asBoolean(value: unknown, defaultValue?: boolean): boolean | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return value != false;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a number.\n   *\n   * @param value - Value to be converted intonumber, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** number value, if none then the default value, if none then null.\n   */\n  asNumber(value: unknown, defaultValue?: number): number | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return Number(value) || defaultValue || null;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a string.\n   *\n   * @param value - Value to be converted intostring, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** string value, if none then the default value, if none then null.\n   */\n  asString(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return String(value);\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a size.\n   *\n   * @param value - Value to be converted intosize, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding string value (number + 'px'), if none then the default value, if none then null.\n   */\n  asSize(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (isString(value)) {\n      return value;\n    } else if (isNumber(value)) {\n      return value + \"px\";\n    } else {\n      return defaultValue || null;\n    }\n  },\n\n  /**\n   * Convert a value into a DOM Element.\n   *\n   * @param value - Value to be converted into DOM Element, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns The DOM Element, if none then the default value, if none then null.\n   */\n  asElement<T extends Node>(\n    value: T | (() => T | undefined) | undefined,\n    defaultValue: T\n  ): T | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    return value || defaultValue || null;\n  },\n};\n\n/**\n * Convert hex color string into RGB color object.\n *\n * @remarks\n * {@link http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb}\n * @param hex - Hex color string (3 or 6 digits, with or without #).\n * @returns RGB color object.\n */\nexport function hexToRGB(hex: string): RGB | null {\n  let result;\n  switch (hex.length) {\n    case 3:\n    case 4:\n      result = shortHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1] + result[1], 16),\n            g: parseInt(result[2] + result[2], 16),\n            b: parseInt(result[3] + result[3], 16),\n          }\n        : null;\n    case 6:\n    case 7:\n      result = fullHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16),\n          }\n        : null;\n    default:\n      return null;\n  }\n}\n\n/**\n * This function takes string color in hex or RGB format and adds the opacity, RGBA is passed through unchanged.\n *\n * @param color - The color string (hex, RGB, RGBA).\n * @param opacity - The new opacity.\n * @returns RGBA string, for example 'rgba(255, 0, 127, 0.3)'.\n */\nexport function overrideOpacity(color: string, opacity: number): string {\n  if (color.includes(\"rgba\")) {\n    return color;\n  } else if (color.includes(\"rgb\")) {\n    const rgb = color\n      .substr(color.indexOf(\"(\") + 1)\n      .replace(\")\", \"\")\n      .split(\",\");\n    return \"rgba(\" + rgb[0] + \",\" + rgb[1] + \",\" + rgb[2] + \",\" + opacity + \")\";\n  } else {\n    const rgb = hexToRGB(color);\n    if (rgb == null) {\n      return color;\n    } else {\n      return \"rgba(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \",\" + opacity + \")\";\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into hex color string.\n *\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns Hex color string (for example: '#0acdc0').\n */\nexport function RGBToHex(red: number, green: number, blue: number): string {\n  return (\n    \"#\" + ((1 << 24) + (red << 16) + (green << 8) + blue).toString(16).slice(1)\n  );\n}\n\nexport interface ColorObject {\n  background?: string;\n  border?: string;\n  hover?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n  highlight?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n}\nexport interface FullColorObject {\n  background: string;\n  border: string;\n  hover: {\n    border: string;\n    background: string;\n  };\n  highlight: {\n    border: string;\n    background: string;\n  };\n}\n\nexport function parseColor(inputColor: string): FullColorObject;\nexport function parseColor(inputColor: FullColorObject): FullColorObject;\nexport function parseColor(inputColor: ColorObject): ColorObject;\nexport function parseColor(\n  inputColor: ColorObject,\n  defaultColor: FullColorObject\n): FullColorObject;\n/**\n * Parse a color property into an object with border, background, and highlight colors.\n *\n * @param inputColor - Shorthand color string or input color object.\n * @param defaultColor - Full color object to fill in missing values in inputColor.\n * @returns Color object.\n */\nexport function parseColor(\n  inputColor: ColorObject | string,\n  defaultColor?: FullColorObject\n): ColorObject | FullColorObject {\n  if (isString(inputColor)) {\n    let colorStr: string = inputColor;\n    if (isValidRGB(colorStr)) {\n      const rgb = colorStr\n        .substr(4)\n        .substr(0, colorStr.length - 5)\n        .split(\",\")\n        .map(function (value): number {\n          return parseInt(value);\n        });\n      colorStr = RGBToHex(rgb[0], rgb[1], rgb[2]);\n    }\n    if (isValidHex(colorStr) === true) {\n      const hsv = hexToHSV(colorStr);\n      const lighterColorHSV = {\n        h: hsv.h,\n        s: hsv.s * 0.8,\n        v: Math.min(1, hsv.v * 1.02),\n      };\n      const darkerColorHSV = {\n        h: hsv.h,\n        s: Math.min(1, hsv.s * 1.25),\n        v: hsv.v * 0.8,\n      };\n      const darkerColorHex = HSVToHex(\n        darkerColorHSV.h,\n        darkerColorHSV.s,\n        darkerColorHSV.v\n      );\n      const lighterColorHex = HSVToHex(\n        lighterColorHSV.h,\n        lighterColorHSV.s,\n        lighterColorHSV.v\n      );\n      return {\n        background: colorStr,\n        border: darkerColorHex,\n        highlight: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n        hover: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n      };\n    } else {\n      return {\n        background: colorStr,\n        border: colorStr,\n        highlight: {\n          background: colorStr,\n          border: colorStr,\n        },\n        hover: {\n          background: colorStr,\n          border: colorStr,\n        },\n      };\n    }\n  } else {\n    if (defaultColor) {\n      const color: FullColorObject = {\n        background: inputColor.background || defaultColor.background,\n        border: inputColor.border || defaultColor.border,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                defaultColor.highlight.background,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                defaultColor.highlight.border,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) ||\n                defaultColor.hover.border,\n              background:\n                (inputColor.hover && inputColor.hover.background) ||\n                defaultColor.hover.background,\n            },\n      };\n      return color;\n    } else {\n      const color: ColorObject = {\n        background: inputColor.background || undefined,\n        border: inputColor.border || undefined,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                undefined,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                undefined,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) || undefined,\n              background:\n                (inputColor.hover && inputColor.hover.background) || undefined,\n            },\n      };\n      return color;\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into HSV object.\n *\n * @remarks\n * {@link http://www.javascripter.net/faq/rgb2hsv.htm}\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns HSV color object.\n */\nexport function RGBToHSV(red: number, green: number, blue: number): HSV {\n  red = red / 255;\n  green = green / 255;\n  blue = blue / 255;\n  const minRGB = Math.min(red, Math.min(green, blue));\n  const maxRGB = Math.max(red, Math.max(green, blue));\n\n  // Black-gray-white\n  if (minRGB === maxRGB) {\n    return { h: 0, s: 0, v: minRGB };\n  }\n\n  // Colors other than black-gray-white:\n  const d =\n    red === minRGB ? green - blue : blue === minRGB ? red - green : blue - red;\n  const h = red === minRGB ? 3 : blue === minRGB ? 1 : 5;\n  const hue = (60 * (h - d / (maxRGB - minRGB))) / 360;\n  const saturation = (maxRGB - minRGB) / maxRGB;\n  const value = maxRGB;\n  return { h: hue, s: saturation, v: value };\n}\n\ninterface CSSStyles {\n  [key: string]: string;\n}\n\n/**\n * Split a string with css styles into an object with key/values.\n *\n * @param cssText - CSS source code to split into key/value object.\n * @returns Key/value object corresponding to {@link cssText}.\n */\nfunction splitCSSText(cssText: string): CSSStyles {\n  const tmpEllement = document.createElement(\"div\");\n\n  const styles: CSSStyles = {};\n\n  tmpEllement.style.cssText = cssText;\n\n  for (let i = 0; i < tmpEllement.style.length; ++i) {\n    styles[tmpEllement.style[i]] = tmpEllement.style.getPropertyValue(\n      tmpEllement.style[i]\n    );\n  }\n\n  return styles;\n}\n\n/**\n * Append a string with css styles to an element.\n *\n * @param element - The element that will receive new styles.\n * @param cssText - The styles to be appended.\n */\nexport function addCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const [key, value] of Object.entries(cssStyle)) {\n    element.style.setProperty(key, value);\n  }\n}\n\n/**\n * Remove a string with css styles from an element.\n *\n * @param element - The element from which styles should be removed.\n * @param cssText - The styles to be removed.\n */\nexport function removeCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const key of Object.keys(cssStyle)) {\n    element.style.removeProperty(key);\n  }\n}\n\n/**\n * Convert HSV \\<0, 1\\> into RGB color object.\n *\n * @remarks\n * {@link https://gist.github.com/mjijackson/5311256}\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns RGB color object.\n */\nexport function HSVToRGB(h: number, s: number, v: number): RGB {\n  let r: undefined | number;\n  let g: undefined | number;\n  let b: undefined | number;\n\n  const i = Math.floor(h * 6);\n  const f = h * 6 - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n\n  switch (i % 6) {\n    case 0:\n      (r = v), (g = t), (b = p);\n      break;\n    case 1:\n      (r = q), (g = v), (b = p);\n      break;\n    case 2:\n      (r = p), (g = v), (b = t);\n      break;\n    case 3:\n      (r = p), (g = q), (b = v);\n      break;\n    case 4:\n      (r = t), (g = p), (b = v);\n      break;\n    case 5:\n      (r = v), (g = p), (b = q);\n      break;\n  }\n\n  return {\n    r: Math.floor((r as number) * 255),\n    g: Math.floor((g as number) * 255),\n    b: Math.floor((b as number) * 255),\n  };\n}\n\n/**\n * Convert HSV \\<0, 1\\> into hex color string.\n *\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns Hex color string.\n */\nexport function HSVToHex(h: number, s: number, v: number): string {\n  const rgb = HSVToRGB(h, s, v);\n  return RGBToHex(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Convert hex color string into HSV \\<0, 1\\>.\n *\n * @param hex - Hex color string.\n * @returns HSV color object.\n */\nexport function hexToHSV(hex: string): HSV {\n  const rgb = hexToRGB(hex);\n  if (!rgb) {\n    throw new TypeError(`'${hex}' is not a valid color.`);\n  }\n  return RGBToHSV(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Validate hex color string.\n *\n * @param hex - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidHex(hex: string): boolean {\n  const isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(hex);\n  return isOk;\n}\n\n/**\n * Validate RGB color string.\n *\n * @param rgb - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGB(rgb: string): boolean {\n  return rgbRE.test(rgb);\n}\n\n/**\n * Validate RGBA color string.\n *\n * @param rgba - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGBA(rgba: string): boolean {\n  return rgbaRE.test(rgba);\n}\n\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param fields - Names of properties to be bridged.\n * @param referenceObject - The original object.\n * @returns A new object inheriting from the referenceObject.\n */\nexport function selectiveBridgeObject<F extends string, V>(\n  fields: F[],\n  referenceObject: Record<F, V>\n): Record<F, V> | null {\n  if (referenceObject !== null && typeof referenceObject === \"object\") {\n    // !!! typeof null === 'object'\n    const objectTo = Object.create(referenceObject);\n    for (let i = 0; i < fields.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(referenceObject, fields[i])) {\n        if (typeof referenceObject[fields[i]] == \"object\") {\n          objectTo[fields[i]] = bridgeObject(referenceObject[fields[i]]);\n        }\n      }\n    }\n    return objectTo;\n  } else {\n    return null;\n  }\n}\n\nexport function bridgeObject<T extends object>(referenceObject: T): T;\nexport function bridgeObject<T>(referenceObject: T): null;\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param referenceObject - The original object.\n * @returns The Element if the referenceObject is an Element, or a new object inheriting from the referenceObject.\n */\nexport function bridgeObject<T extends object | null>(\n  referenceObject: T\n): T | null {\n  if (referenceObject === null || typeof referenceObject !== \"object\") {\n    return null;\n  }\n\n  if (referenceObject instanceof Element) {\n    // Avoid bridging DOM objects\n    return referenceObject;\n  }\n\n  const objectTo = Object.create(referenceObject);\n  for (const i in referenceObject) {\n    if (Object.prototype.hasOwnProperty.call(referenceObject, i)) {\n      if (typeof (referenceObject as any)[i] == \"object\") {\n        objectTo[i] = bridgeObject((referenceObject as any)[i]);\n      }\n    }\n  }\n\n  return objectTo;\n}\n\n/**\n * This method provides a stable sort implementation, very fast for presorted data.\n *\n * @param a - The array to be sorted (in-place).\n * @param compare - An order comparator.\n * @returns The argument a.\n */\nexport function insertSort<T>(a: T[], compare: (a: T, b: T) => number): T[] {\n  for (let i = 0; i < a.length; i++) {\n    const k = a[i];\n    let j;\n    for (j = i; j > 0 && compare(k, a[j - 1]) < 0; j--) {\n      a[j] = a[j - 1];\n    }\n    a[j] = k;\n  }\n  return a;\n}\n\n/**\n * This is used to set the options of subobjects in the options object.\n *\n * A requirement of these subobjects is that they have an 'enabled' element\n * which is optional for the user but mandatory for the program.\n *\n * The added value here of the merge is that option 'enabled' is set as required.\n *\n * @param mergeTarget - Either this.options or the options used for the groups.\n * @param options - Options.\n * @param option - Option key in the options argument.\n * @param globalOptions - Global options, passed in to determine value of option 'enabled'.\n */\nexport function mergeOptions(\n  mergeTarget: any,\n  options: any,\n  option: string,\n  globalOptions: any = {}\n): void {\n  // Local helpers\n  const isPresent = function (obj: any): boolean {\n    return obj !== null && obj !== undefined;\n  };\n\n  const isObject = function (obj: unknown): boolean {\n    return obj !== null && typeof obj === \"object\";\n  };\n\n  // https://stackoverflow.com/a/34491287/1223531\n  const isEmpty = function (obj: object): obj is {} {\n    for (const x in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, x)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  // Guards\n  if (!isObject(mergeTarget)) {\n    throw new Error(\"Parameter mergeTarget must be an object\");\n  }\n\n  if (!isObject(options)) {\n    throw new Error(\"Parameter options must be an object\");\n  }\n\n  if (!isPresent(option)) {\n    throw new Error(\"Parameter option must have a value\");\n  }\n\n  if (!isObject(globalOptions)) {\n    throw new Error(\"Parameter globalOptions must be an object\");\n  }\n\n  //\n  // Actual merge routine, separated from main logic\n  // Only a single level of options is merged. Deeper levels are ref'd. This may actually be an issue.\n  //\n  const doMerge = function (target: any, options: any, option: string): void {\n    if (!isObject(target[option])) {\n      target[option] = {};\n    }\n\n    const src = options[option];\n    const dst = target[option];\n    for (const prop in src) {\n      if (Object.prototype.hasOwnProperty.call(src, prop)) {\n        dst[prop] = src[prop];\n      }\n    }\n  };\n\n  // Local initialization\n  const srcOption = options[option];\n  const globalPassed = isObject(globalOptions) && !isEmpty(globalOptions);\n  const globalOption = globalPassed ? globalOptions[option] : undefined;\n  const globalEnabled = globalOption ? globalOption.enabled : undefined;\n\n  /////////////////////////////////////////\n  // Main routine\n  /////////////////////////////////////////\n  if (srcOption === undefined) {\n    return; // Nothing to do\n  }\n\n  if (typeof srcOption === \"boolean\") {\n    if (!isObject(mergeTarget[option])) {\n      mergeTarget[option] = {};\n    }\n\n    mergeTarget[option].enabled = srcOption;\n    return;\n  }\n\n  if (srcOption === null && !isObject(mergeTarget[option])) {\n    // If possible, explicit copy from globals\n    if (isPresent(globalOption)) {\n      mergeTarget[option] = Object.create(globalOption);\n    } else {\n      return; // Nothing to do\n    }\n  }\n\n  if (!isObject(srcOption)) {\n    return;\n  }\n\n  //\n  // Ensure that 'enabled' is properly set. It is required internally\n  // Note that the value from options will always overwrite the existing value\n  //\n  let enabled = true; // default value\n\n  if (srcOption.enabled !== undefined) {\n    enabled = srcOption.enabled;\n  } else {\n    // Take from globals, if present\n    if (globalEnabled !== undefined) {\n      enabled = globalOption.enabled;\n    }\n  }\n\n  doMerge(mergeTarget, options, option);\n  mergeTarget[option].enabled = enabled;\n}\n\nexport function binarySearchCustom<\n  O extends object,\n  K1 extends keyof O,\n  K2 extends keyof O[K1]\n>(\n  orderedItems: O[],\n  comparator: (v: O[K1][K2]) => -1 | 0 | 1,\n  field: K1,\n  field2: K2\n): number;\nexport function binarySearchCustom<O extends object, K1 extends keyof O>(\n  orderedItems: O[],\n  comparator: (v: O[K1]) => -1 | 0 | 1,\n  field: K1\n): number;\n/**\n * This function does a binary search for a visible item in a sorted list. If we find a visible item, the code that uses\n * this function will then iterate in both directions over this sorted list to find all visible items.\n *\n * @param orderedItems - Items ordered by start.\n * @param comparator - -1 is lower, 0 is equal, 1 is higher.\n * @param field - Property name on an item (That is item[field]).\n * @param field2 - Second property name on an item (That is item[field][field2]).\n * @returns Index of the found item or -1 if nothing was found.\n */\nexport function binarySearchCustom(\n  orderedItems: any[],\n  comparator: (v: unknown) => -1 | 0 | 1,\n  field: string,\n  field2?: string\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n\n  while (low <= high && iteration < maxIterations) {\n    const middle = Math.floor((low + high) / 2);\n\n    const item = orderedItems[middle];\n    const value = field2 === undefined ? item[field] : item[field][field2];\n\n    const searchResult = comparator(value);\n    if (searchResult == 0) {\n      // jihaa, found a visible item!\n      return middle;\n    } else if (searchResult == -1) {\n      // it is too small --> increase low\n      low = middle + 1;\n    } else {\n      // it is too big --> decrease high\n      high = middle - 1;\n    }\n\n    iteration++;\n  }\n\n  return -1;\n}\n\n/**\n * This function does a binary search for a specific value in a sorted array.\n * If it does not exist but is in between of two values, we return either the\n * one before or the one after, depending on user input If it is found, we\n * return the index, else -1.\n *\n * @param orderedItems - Sorted array.\n * @param target - The searched value.\n * @param field - Name of the property in items to be searched.\n * @param sidePreference - If the target is between two values, should the index of the before or the after be returned?\n * @param comparator - An optional comparator, returning -1, 0, 1 for \\<, ===, \\>.\n * @returns The index of found value or -1 if nothing was found.\n */\nexport function binarySearchValue<T extends string>(\n  orderedItems: { [K in T]: number }[],\n  target: number,\n  field: T,\n  sidePreference: \"before\" | \"after\",\n  comparator?: (a: number, b: number) => -1 | 0 | 1\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n  let prevValue;\n  let value;\n  let nextValue;\n  let middle;\n\n  comparator =\n    comparator != undefined\n      ? comparator\n      : function (a: number, b: number): -1 | 0 | 1 {\n          return a == b ? 0 : a < b ? -1 : 1;\n        };\n\n  while (low <= high && iteration < maxIterations) {\n    // get a new guess\n    middle = Math.floor(0.5 * (high + low));\n    prevValue = orderedItems[Math.max(0, middle - 1)][field];\n    value = orderedItems[middle][field];\n    nextValue =\n      orderedItems[Math.min(orderedItems.length - 1, middle + 1)][field];\n\n    if (comparator(value, target) == 0) {\n      // we found the target\n      return middle;\n    } else if (\n      comparator(prevValue, target) < 0 &&\n      comparator(value, target) > 0\n    ) {\n      // target is in between of the previous and the current\n      return sidePreference == \"before\" ? Math.max(0, middle - 1) : middle;\n    } else if (\n      comparator(value, target) < 0 &&\n      comparator(nextValue, target) > 0\n    ) {\n      // target is in between of the current and the next\n      return sidePreference == \"before\"\n        ? middle\n        : Math.min(orderedItems.length - 1, middle + 1);\n    } else {\n      // didnt find the target, we need to change our boundaries.\n      if (comparator(value, target) < 0) {\n        // it is too small --> increase low\n        low = middle + 1;\n      } else {\n        // it is too big --> decrease high\n        high = middle - 1;\n      }\n    }\n    iteration++;\n  }\n\n  // didnt find anything. Return -1.\n  return -1;\n}\n\n/*\n * Easing Functions.\n * Only considering the t value for the range [0, 1] => [0, 1].\n *\n * Inspiration: from http://gizma.com/easing/\n * https://gist.github.com/gre/1650294\n */\nexport const easingFunctions = {\n  /**\n   * Provides no easing and no acceleration.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  linear(t: number): number {\n    return t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuad(t: number): number {\n    return t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuad(t: number): number {\n    return t * (2 - t);\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuad(t: number): number {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInCubic(t: number): number {\n    return t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutCubic(t: number): number {\n    return --t * t * t + 1;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutCubic(t: number): number {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuart(t: number): number {\n    return t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuart(t: number): number {\n    return 1 - --t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuart(t: number): number {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuint(t: number): number {\n    return t * t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuint(t: number): number {\n    return 1 + --t * t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuint(t: number): number {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n  },\n};\n\n/**\n * Experimentaly compute the width of the scrollbar for this browser.\n *\n * @returns The width in pixels.\n */\nexport function getScrollBarWidth(): number {\n  const inner = document.createElement(\"p\");\n  inner.style.width = \"100%\";\n  inner.style.height = \"200px\";\n\n  const outer = document.createElement(\"div\");\n  outer.style.position = \"absolute\";\n  outer.style.top = \"0px\";\n  outer.style.left = \"0px\";\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"200px\";\n  outer.style.height = \"150px\";\n  outer.style.overflow = \"hidden\";\n  outer.appendChild(inner);\n\n  document.body.appendChild(outer);\n  const w1 = inner.offsetWidth;\n  outer.style.overflow = \"scroll\";\n  let w2 = inner.offsetWidth;\n  if (w1 == w2) {\n    w2 = outer.clientWidth;\n  }\n\n  document.body.removeChild(outer);\n\n  return w1 - w2;\n}\n\n// @TODO: This doesn't work properly.\n// It works only for single property objects,\n// otherwise it combines all of the types in a union.\n// export function topMost<K1 extends string, V1> (\n//   pile: Record<K1, undefined | V1>[],\n//   accessors: K1 | [K1]\n// ): undefined | V1\n// export function topMost<K1 extends string, K2 extends string, V1, V2> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2>>[],\n//   accessors: [K1, K2]\n// ): undefined | V1 | V2\n// export function topMost<K1 extends string, K2 extends string, K3 extends string, V1, V2, V3> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2 | Record<K3, undefined | V3>>>[],\n//   accessors: [K1, K2, K3]\n// ): undefined | V1 | V2 | V3\n/**\n * Get the top most property value from a pile of objects.\n *\n * @param pile - Array of objects, no required format.\n * @param accessors - Array of property names.\n * For example `object['foo']['bar']` → `['foo', 'bar']`.\n * @returns Value of the property with given accessors path from the first pile item where it's not undefined.\n */\nexport function topMost(pile: any, accessors: any): any {\n  let candidate;\n  if (!Array.isArray(accessors)) {\n    accessors = [accessors];\n  }\n  for (const member of pile) {\n    if (member) {\n      candidate = member[accessors[0]];\n      for (let i = 1; i < accessors.length; i++) {\n        if (candidate) {\n          candidate = candidate[accessors[i]];\n        }\n      }\n      if (typeof candidate !== \"undefined\") {\n        break;\n      }\n    }\n  }\n  return candidate;\n}\n", "import { Hammer } from \"./hammer\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio=1]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   *\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   *\n   * @param {string | object} color\n   * @param {boolean} [setInitial=true]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color)\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   *\n   * @param {boolean} [storePrevious=true]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   *\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   *\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   *\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   *\n   * @param {object} rgba\n   * @param {boolean} [setInitial=true]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   *\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   *\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   *\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   *\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   *\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   *\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util\";\n\nimport { ColorPicker } from \"./color-picker\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n *\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   *\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   *\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   *\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   *\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   *\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   *\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   *\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   *\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   *\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   *\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   *\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   *\n   * @param {object} obj\n   * @param {Array} [path=[]]    | where to look for the actual option\n   * @param {boolean} [checkOnly=false]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   *\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2))\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options\n      );\n    }\n    return options;\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". '\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"'\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\"\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive=false]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op)\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   * http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   * Copyright (c) 2011 Andrei Mackenzie\n   *\n   * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   *\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1\n            )\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", "import { Activator as ActivatorJS } from \"./activator\";\nimport { ColorPicker as ColorPickerJS } from \"./color-picker\";\nimport { Configurator as ConfiguratorJS } from \"./configurator\";\nimport { Hammer as HammerJS } from \"./hammer\";\nimport { Popup as PopupJS } from \"./popup\";\nimport { VALIDATOR_PRINT_STYLE as VALIDATOR_PRINT_STYLE_JS } from \"./validator\";\nimport { Validator as ValidatorJS } from \"./validator\";\n\nexport const Activator: any = ActivatorJS;\nexport const ColorPicker: any = ColorPickerJS;\nexport const Configurator: any = ConfiguratorJS;\nexport const Hammer: HammerStatic = HammerJS;\nexport const Popup: any = PopupJS;\nexport const VALIDATOR_PRINT_STYLE: string = VALIDATOR_PRINT_STYLE_JS;\nexport const Validator: any = ValidatorJS;\n\nexport * from \"./configurator-types\";\n", "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   *\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   *\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n"], "names": ["DELETE", "Symbol", "pureDeepObjectAssign", "base", "updates", "deepObjectAssign", "values", "merged", "deepObjectAssignNonentry", "stripDelete", "length", "slice", "a", "b", "Date", "setTime", "getTime", "prop", "Reflect", "ownKeys", "Object", "prototype", "propertyIsEnumerable", "call", "Array", "isArray", "clone", "map", "value", "keys", "Alea", "seed", "s0", "s1", "s2", "mash", "n", "data", "string", "toString", "i", "charCodeAt", "h", "<PERSON><PERSON>", "mash<PERSON><PERSON>", "c", "random", "t", "uint32", "fract53", "algorithm", "version", "AleaImplementation", "now", "Hammer", "window", "RealHammer", "noop", "on", "off", "destroy", "emit", "get", "set", "hammerMock", "Activator", "container", "this", "_cleanupQueue", "active", "_dom", "overlay", "document", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hammer", "_onTapOverlay", "bind", "for<PERSON>ach", "event", "srcEvent", "stopPropagation", "body", "_onClick", "element", "parent", "_hasParent", "target", "deactivate", "addEventListener", "removeEventListener", "_escListener", "key", "keyCode", "Emitter", "current", "callback", "splice", "reverse", "activate", "style", "display", "remove", "ASPDateRegex", "fullHexRE", "shortHexRE", "rgbRE", "rgbaRE", "isNumber", "Number", "recursiveDOMDelete", "DOMobject", "hasChildNodes", "child", "<PERSON><PERSON><PERSON><PERSON>", "isString", "String", "isObject", "isDate", "exec", "isNaN", "parse", "copyOrDelete", "allowDeletion", "doDeletion", "undefined", "fillIfDefined", "aProp", "bProp", "extend", "assign", "selectiveExtend", "props", "others", "Error", "other", "p", "hasOwnProperty", "selectiveDeepExtend", "TypeError", "constructor", "deepExtend", "selectiveNotDeepExtend", "propsToExclude", "includes", "protoExtend", "getPrototypeOf", "equalArray", "len", "getType", "object", "type", "Boolean", "copyAndExtendArray", "arr", "newValue", "copyArray", "getAbsoluteLeft", "elem", "getBoundingClientRect", "left", "getAbsoluteRight", "right", "getAbsoluteTop", "top", "addClassName", "classNames", "classes", "className", "split", "newClasses", "concat", "filter", "join", "removeClassName", "oldClasses", "toArray", "updateProperty", "throttle", "fn", "scheduled", "requestAnimationFrame", "preventDefault", "returnValue", "get<PERSON><PERSON><PERSON>", "srcElement", "Element", "nodeType", "hasParent", "option", "asBoolean", "defaultValue", "asNumber", "asString", "asSize", "asElement", "hexToRGB", "hex", "result", "r", "parseInt", "g", "overrideOpacity", "color", "opacity", "rgb", "substr", "indexOf", "replace", "RGBToHex", "red", "green", "blue", "parseColor", "inputColor", "defaultColor", "colorStr", "isValidRGB", "isValidHex", "hsv", "hexToHSV", "lighterColorHSV", "s", "v", "Math", "min", "darkerColorHSV", "darkerColorHex", "HSVToHex", "lighterColorHex", "background", "border", "highlight", "hover", "RGBToHSV", "minRGB", "maxRGB", "max", "splitCSSText", "cssText", "tmpEllement", "styles", "getPropertyValue", "addCssText", "cssStyle", "entries", "setProperty", "removeCssText", "removeProperty", "HSVToRGB", "floor", "f", "q", "test", "isValidRGBA", "rgba", "selectiveBridgeObject", "fields", "referenceObject", "objectTo", "create", "bridgeObject", "insertSort", "compare", "k", "j", "mergeOptions", "mergeTarget", "options", "globalOptions", "isPresent", "obj", "srcOption", "globalOption", "x", "isEmpty", "globalEnabled", "enabled", "src", "dst", "doMerge", "binarySearchCustom", "orderedItems", "comparator", "field", "field2", "iteration", "low", "high", "middle", "item", "searchResult", "binarySearchValue", "sidePreference", "prevValue", "nextValue", "easingFunctions", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "getScrollBarWidth", "inner", "width", "height", "outer", "position", "visibility", "overflow", "w1", "offsetWidth", "w2", "clientWidth", "topMost", "pile", "accessors", "candidate", "member", "htmlColors", "black", "navy", "darkblue", "mediumblue", "darkgreen", "teal", "dark<PERSON>an", "deepskyblue", "darkturquoise", "mediumspringgreen", "lime", "springgreen", "aqua", "cyan", "midnightblue", "dodgerblue", "lightseagreen", "forestgreen", "seagreen", "darkslategray", "limegreen", "mediumseagreen", "turquoise", "royalblue", "steelblue", "darkslateblue", "mediumturquoise", "indigo", "darkolivegreen", "cadetblue", "cornflowerblue", "mediumaquamarine", "dimgray", "slateblue", "<PERSON><PERSON><PERSON>", "slategray", "lightslategray", "mediumslateblue", "lawngreen", "chartreuse", "aquamarine", "maroon", "purple", "olive", "gray", "skyblue", "lightskyblue", "blueviolet", "darkred", "darkmagenta", "saddlebrown", "darkseagreen", "lightgreen", "mediumpurple", "darkviolet", "palegreen", "darkorchid", "yellowgreen", "sienna", "brown", "darkgray", "lightblue", "greenyellow", "paleturquoise", "lightsteelblue", "powderblue", "firebrick", "darkgoldenrod", "mediumorchid", "rosybrown", "<PERSON><PERSON><PERSON>", "silver", "mediumvioletred", "indianred", "peru", "chocolate", "tan", "<PERSON><PERSON>rey", "palevioletred", "thistle", "orchid", "goldenrod", "crimson", "gainsboro", "plum", "burlywood", "lightcyan", "lavender", "<PERSON><PERSON><PERSON>", "violet", "palegoldenrod", "lightcoral", "khaki", "aliceblue", "honeydew", "azure", "sandybrown", "wheat", "beige", "whitesmoke", "mintcream", "ghostwhite", "salmon", "antiquewhite", "linen", "lightgoldenrodyellow", "oldlace", "fuchsia", "magenta", "deeppink", "orangered", "tomato", "hotpink", "coral", "darkorange", "<PERSON><PERSON><PERSON>", "orange", "lightpink", "pink", "gold", "peachpuff", "navajowhite", "moccasin", "bisque", "mistyrose", "blanche<PERSON><PERSON>", "papayawhip", "lavenderblush", "seashell", "cornsilk", "lemon<PERSON>ffon", "<PERSON><PERSON><PERSON><PERSON>", "snow", "yellow", "lightyellow", "ivory", "white", "ColorPicker$1", "pixelRatio", "generated", "centerCoordinates", "y", "hueCircle", "initialColor", "previousColor", "applied", "updateCallback", "closeCallback", "_create", "insertTo", "frame", "_<PERSON><PERSON><PERSON><PERSON>", "_setSize", "setUpdateCallback", "setCloseCallback", "_isColorString", "setColor", "setInitial", "htmlColor", "rgbaArray", "rgbObj", "alpha", "JSON", "stringify", "_setColor", "show", "_generateHueCircle", "_hide", "storePrevious", "setTimeout", "_save", "_apply", "_updatePicker", "_loadLast", "alert", "angleConvert", "PI", "radius", "sin", "cos", "colorPickerSelector", "clientHeight", "_setOpacity", "_setBrightness", "ctx", "colorPickerCanvas", "getContext", "pixelRation", "devicePixelRatio", "webkitBackingStorePixelRatio", "mozBackingStorePixelRatio", "msBackingStorePixelRatio", "oBackingStorePixelRatio", "backingStorePixelRatio", "setTransform", "w", "clearRect", "putImageData", "fillStyle", "circle", "fill", "brightnessRange", "opacityRange", "initialColorDiv", "backgroundColor", "newColorDiv", "colorPickerDiv", "noCanvas", "fontWeight", "padding", "innerText", "opacityDiv", "brightnessDiv", "arrowDiv", "err", "me", "onchange", "oninput", "brightnessLabel", "opacityLabel", "cancelButton", "onclick", "applyButton", "saveButton", "loadButton", "drag", "pinch", "enable", "<PERSON><PERSON><PERSON><PERSON>", "_moveSelector", "hue", "sat", "hfac", "sfac", "fillRect", "strokeStyle", "stroke", "getImageData", "rect", "center", "centerY", "centerX", "angle", "atan2", "sqrt", "newTop", "newLeft", "wrapInTag", "rest", "createTextNode", "allOptions", "errorFound", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPicker", "ColorPickerJS", "Configurator", "parentModule", "defaultContainer", "configureOptions", "hideOption", "changedOptions", "allowCreation", "initialized", "popup<PERSON><PERSON>nter", "defaultOptions", "showButton", "moduleOptions", "dom<PERSON><PERSON>s", "popupDiv", "popupLimit", "popupHistory", "colorPicker", "wrapper", "setOptions", "_removePopup", "_clean", "setModuleOptions", "counter", "_handleObject", "_makeItem", "_makeHeader", "_makeButton", "_push", "_showPopupIfNeeded", "_getValue", "path", "name", "div", "_make<PERSON><PERSON>l", "objectLabel", "_makeDropdown", "select", "selected<PERSON><PERSON><PERSON>", "selected", "_update", "label", "_makeRange", "step", "range", "popupString", "popupValue", "factor", "ceil", "input", "itemIndex", "_setupPopup", "generateButton", "_printOptions", "on<PERSON><PERSON>ver", "onmouseout", "optionsContainer", "index", "id", "html", "clearTimeout", "hideTimeout", "deleteTimeout", "_makeCheckbox", "checkbox", "checked", "_makeTextInput", "_makeColorField", "_showColorPicker", "colorString", "checkOnly", "visibleInSet", "subObj", "newPath", "_handleArray", "enabledPath", "enabledValue", "console", "error", "_constructOptions", "emitter", "optionsObj", "pointer", "getOptions", "HammerJS", "Popup", "overflowMethod", "hidden", "setPosition", "setText", "content", "doShow", "maxHeight", "max<PERSON><PERSON><PERSON>", "isLeft", "isTop", "hide", "VALIDATOR_PRINT_STYLE_JS", "Validator", "static", "referenceOptions", "subObject", "usedOptions", "check", "__any__", "getSuggestion", "referenceOption", "is_object", "refOptionObj", "__type__", "checkFields", "log", "message", "printLocation", "optionType", "refOptionType", "print", "_isAMomentObject", "localSearch", "findInOptions", "globalSearch", "msg", "indexMatch", "distance", "closestMatch", "recursive", "closestMatchPath", "lowerCaseOption", "toLowerCase", "op", "levenshteinDistance", "prefix", "str", "matrix", "char<PERSON>t"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;qEAGaA,EAASC,OAAO,mBA2BbC,EACdC,KACGC,GAEH,OAAOC,EAAiB,CAAS,EAAEF,KAASC,EAC9C,CAgBgB,SAAAC,KAAoBC,GAClC,MAAMC,EAASC,KAA4BF,GAE3C,OADAG,EAAYF,GACLA,CACT,CAUA,SAASC,KAA4BF,GACnC,GAAIA,EAAOI,OAAS,EAClB,OAAOJ,EAAO,GACT,GAAIA,EAAOI,OAAS,EACzB,OAAOF,EACLH,EAAiBC,EAAO,GAAIA,EAAO,OAChCA,EAAOK,MAAM,IAIpB,MAAMC,EAAIN,EAAO,GACXO,EAAIP,EAAO,GAEjB,GAAIM,aAAaE,MAAQD,aAAaC,KAEpC,OADAF,EAAEG,QAAQF,EAAEG,WACLJ,EAGT,IAAK,MAAMK,KAAQC,QAAQC,QAAQN,GAC5BO,OAAOC,UAAUC,qBAAqBC,KAAKV,EAAGI,KAExCJ,EAAEI,KAAUjB,SACdY,EAAEK,GAEG,OAAZL,EAAEK,IACU,OAAZJ,EAAEI,IACiB,iBAAZL,EAAEK,IACU,iBAAZJ,EAAEI,IACRO,MAAMC,QAAQb,EAAEK,KAChBO,MAAMC,QAAQZ,EAAEI,IAIjBL,EAAEK,GAAQS,EAAMb,EAAEI,IAFlBL,EAAEK,GAAQT,EAAyBI,EAAEK,GAAOJ,EAAEI,KAMlD,OAAOL,CACT,CAQA,SAASc,EAAMd,GACb,OAAIY,MAAMC,QAAQb,GACTA,EAAEe,KAAKC,GAAoBF,EAAME,KAClB,iBAANhB,GAAwB,OAANA,EAC9BA,aAAaE,KACR,IAAIA,KAAKF,EAAEI,WAEbR,EAAyB,GAAII,GAE7BA,CAEX,CAOA,SAASH,EAAYG,GACnB,IAAK,MAAMK,KAAQG,OAAOS,KAAKjB,GACzBA,EAAEK,KAAUjB,SACPY,EAAEK,GACmB,iBAAZL,EAAEK,IAAkC,OAAZL,EAAEK,IAC1CR,EAAYG,EAAEK,GAGpB,CCtGgB,SAAAa,KAAQC,GACtB,OASF,SAA4BA,GAC1B,IAAKC,EAAIC,EAAIC,GA6Bf,YAAqBH,GACnB,MAAMI,EAmCR,WACE,IAAIC,EAAI,WAER,OAAO,SAAUC,GACf,MAAMC,EAASD,EAAKE,WACpB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAO5B,OAAQ8B,IAAK,CACtCJ,GAAKE,EAAOG,WAAWD,GACvB,IAAIE,EAAI,mBAAsBN,EAC9BA,EAAIM,IAAM,EACVA,GAAKN,EACLM,GAAKN,EACLA,EAAIM,IAAM,EACVA,GAAKN,EACLA,GAAS,WAAJM,CACN,CACD,OAAmB,wBAAXN,IAAM,EAChB,CACF,CApDeO,GAEb,IAAIX,EAAKG,EAAK,KACVF,EAAKE,EAAK,KACVD,EAAKC,EAAK,KAEd,IAAK,IAAIK,EAAI,EAAGA,EAAIT,EAAKrB,OAAQ8B,IAC/BR,GAAMG,EAAKJ,EAAKS,IACZR,EAAK,IACPA,GAAM,GAERC,GAAME,EAAKJ,EAAKS,IACZP,EAAK,IACPA,GAAM,GAERC,GAAMC,EAAKJ,EAAKS,IACZN,EAAK,IACPA,GAAM,GAIV,MAAO,CAACF,EAAIC,EAAIC,EAClB,CApDqBU,CAASb,GACxBc,EAAI,EAER,MAAMC,EAAc,KAClB,MAAMC,EAAI,QAAUf,EAAS,uBAAJa,EAGzB,OAFAb,EAAKC,EACLA,EAAKC,EACGA,EAAKa,GAAKF,EAAQ,EAAJE,EAAQ,EAYhC,OATAD,EAAOE,OAAS,IAAyB,WAAXF,IAE9BA,EAAOG,QAAU,IACfH,IAAyC,uBAAjB,QAAXA,IAAuB,GAEtCA,EAAOI,UAAY,OACnBJ,EAAOf,KAAOA,EACde,EAAOK,QAAU,MAEVL,CACT,CA9BSM,CAAmBrB,EAAKrB,OAASqB,EAAO,CAACjB,KAAKuC,OACvD,CCXA,MAAMC,EACc,oBAAXC,OACHA,OAAOD,QAAUE,EACjB,WAEE,OAtBR,WACE,MAAMC,EAAO,OAEb,MAAO,CACLC,GAAID,EACJE,IAAKF,EACLG,QAASH,EACTI,KAAMJ,EAENK,IAAG,KACM,CACLC,IAAKN,IAIb,CAOeO,EACR,EClBA,SAASC,EAAUC,GACxBC,KAAKC,cAAgB,GAErBD,KAAKE,QAAS,EAEdF,KAAKG,KAAO,CACVJ,YACAK,QAASC,SAASC,cAAc,QAGlCN,KAAKG,KAAKC,QAAQG,UAAUC,IAAI,eAEhCR,KAAKG,KAAKJ,UAAUU,YAAYT,KAAKG,KAAKC,SAC1CJ,KAAKC,cAAcS,MAAK,KACtBV,KAAKG,KAAKC,QAAQO,WAAWC,YAAYZ,KAAKG,KAAKC,QAAQ,IAG7D,MAAMS,EAAS1B,EAAOa,KAAKG,KAAKC,SAChCS,EAAOtB,GAAG,MAAOS,KAAKc,cAAcC,KAAKf,OACzCA,KAAKC,cAAcS,MAAK,KACtBG,EAAOpB,SAAS,IAMH,CACb,MACA,YACA,QACA,QACA,MACA,WACA,UACA,UAEKuB,SAASC,IACdJ,EAAOtB,GAAG0B,GAAQA,IAChBA,EAAMC,SAASC,iBAAiB,GAChC,IAIAd,UAAYA,SAASe,OACvBpB,KAAKqB,SAAYJ,KAmGrB,SAAoBK,EAASC,GAC3B,KAAOD,GAAS,CACd,GAAIA,IAAYC,EACd,OAAO,EAETD,EAAUA,EAAQX,UACnB,CACD,OAAO,CACT,EA1GWa,CAAWP,EAAMQ,OAAQ1B,IAC5BC,KAAK0B,YACN,EAEHrB,SAASe,KAAKO,iBAAiB,QAAS3B,KAAKqB,UAC7CrB,KAAKC,cAAcS,MAAK,KACtBL,SAASe,KAAKQ,oBAAoB,QAAS5B,KAAKqB,SAAS,KAK7DrB,KAAK6B,aAAgBZ,KAEjB,QAASA,EACS,WAAdA,EAAMa,IACY,KAAlBb,EAAMc,UAEV/B,KAAK0B,YACN,CAEL,CAGAM,EAAQlC,EAAU5C,WAGlB4C,EAAUmC,QAAU,KAKpBnC,EAAU5C,UAAUuC,QAAU,WAC5BO,KAAK0B,aAEL,IAAK,MAAMQ,KAAYlC,KAAKC,cAAckC,OAAO,GAAGC,UAClDF,GAEJ,EAMApC,EAAU5C,UAAUmF,SAAW,WAEzBvC,EAAUmC,SACZnC,EAAUmC,QAAQP,aAEpB5B,EAAUmC,QAAUjC,KAEpBA,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,OAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUC,IAAI,cAElCR,KAAKN,KAAK,UACVM,KAAKN,KAAK,YAIVW,SAASe,KAAKO,iBAAiB,UAAW3B,KAAK6B,aACjD,EAMA/B,EAAU5C,UAAUwE,WAAa,WAC/B1B,KAAKE,QAAS,EACdF,KAAKG,KAAKC,QAAQkC,MAAMC,QAAU,QAClCvC,KAAKG,KAAKJ,UAAUQ,UAAUiC,OAAO,cACrCnC,SAASe,KAAKQ,oBAAoB,UAAW5B,KAAK6B,cAElD7B,KAAKN,KAAK,UACVM,KAAKN,KAAK,aACZ,EAQAI,EAAU5C,UAAU4D,cAAgB,SAAUG,GAE5CjB,KAAKqC,WACLpB,EAAMC,SAASC,iBACjB,EC5IA,MAAMsB,EAAe,qBAGfC,EAAY,4CACZC,EAAa,mCACbC,EACJ,+GACIC,EACJ,mIAkEI,SAAUC,EAASrF,GACvB,OAAOA,aAAiBsF,QAA2B,iBAAVtF,CAC3C,CAOM,SAAUuF,EAAmBC,GACjC,GAAIA,EACF,MAAqC,IAA9BA,EAAUC,iBAA0B,CACzC,MAAMC,EAAQF,EAAUG,WACpBD,IACFH,EAAmBG,GACnBF,EAAUrC,YAAYuC,GAEzB,CAEL,CAQM,SAAUE,EAAS5F,GACvB,OAAOA,aAAiB6F,QAA2B,iBAAV7F,CAC3C,CAQM,SAAU8F,EAAS9F,GACvB,MAAwB,iBAAVA,GAAgC,OAAVA,CACtC,CAQM,SAAU+F,EAAO/F,GACrB,GAAIA,aAAiBd,KACnB,OAAO,EACF,GAAI0G,EAAS5F,GAAQ,CAG1B,GADcgF,EAAagB,KAAKhG,GAE9B,OAAO,EACF,IAAKiG,MAAM/G,KAAKgH,MAAMlG,IAC3B,OAAO,CAEV,CAED,OAAO,CACT,CAaA,SAASmG,EACPnH,EACAC,EACAI,EACA+G,GAEA,IAAIC,GAAa,GACK,IAAlBD,IACFC,EAAyB,OAAZpH,EAAEI,SAA8BiH,IAAZtH,EAAEK,IAGjCgH,SACKrH,EAAEK,GAETL,EAAEK,GAAQJ,EAAEI,EAEhB,CAYM,SAAUkH,EACdvH,EACAC,EACAmH,GAAgB,GAIhB,IAAK,MAAM/G,KAAQL,EACjB,QAAgBsH,IAAZrH,EAAEI,GACJ,GAAgB,OAAZJ,EAAEI,IAAqC,iBAAZJ,EAAEI,GAE/B8G,EAAanH,EAAGC,EAAGI,EAAM+G,OACpB,CACL,MAAMI,EAAQxH,EAAEK,GACVoH,EAAQxH,EAAEI,GACZyG,EAASU,IAAUV,EAASW,IAC9BF,EAAcC,EAAOC,EAAOL,EAE/B,CAGP,CAUa,MAAAM,EAASlH,OAAOmH,OAYvB,SAAUC,EACdC,EACA7H,KACG8H,GAEH,IAAKlH,MAAMC,QAAQgH,GACjB,MAAM,IAAIE,MAAM,wDAGlB,IAAK,MAAMC,KAASF,EAClB,IAAK,IAAIG,EAAI,EAAGA,EAAIJ,EAAM/H,OAAQmI,IAAK,CACrC,MAAM5H,EAAOwH,EAAMI,GACfD,GAASxH,OAAOC,UAAUyH,eAAevH,KAAKqH,EAAO3H,KACvDL,EAAEK,GAAQ2H,EAAM3H,GAEnB,CAEH,OAAOL,CACT,CAgBM,SAAUmI,EACdN,EACA7H,EACAC,EACAmH,GAAgB,GAGhB,GAAIxG,MAAMC,QAAQZ,GAChB,MAAM,IAAImI,UAAU,0CAGtB,IAAK,IAAIH,EAAI,EAAGA,EAAIJ,EAAM/H,OAAQmI,IAAK,CACrC,MAAM5H,EAAOwH,EAAMI,GACnB,GAAIzH,OAAOC,UAAUyH,eAAevH,KAAKV,EAAGI,GAC1C,GAAIJ,EAAEI,IAASJ,EAAEI,GAAMgI,cAAgB7H,YACrB8G,IAAZtH,EAAEK,KACJL,EAAEK,GAAQ,IAERL,EAAEK,GAAMgI,cAAgB7H,OAC1B8H,EAAWtI,EAAEK,GAAOJ,EAAEI,IAAO,EAAO+G,GAEpCD,EAAanH,EAAGC,EAAGI,EAAM+G,OAEtB,IAAIxG,MAAMC,QAAQZ,EAAEI,IACzB,MAAM,IAAI+H,UAAU,0CAEpBjB,EAAanH,EAAGC,EAAGI,EAAM+G,EAC1B,CAEJ,CACD,OAAOpH,CACT,CAiBM,SAAUuI,EACdC,EACAxI,EACAC,EACAmH,GAAgB,GAIhB,GAAIxG,MAAMC,QAAQZ,GAChB,MAAM,IAAImI,UAAU,0CAGtB,IAAK,MAAM/H,KAAQJ,EACjB,GAAKO,OAAOC,UAAUyH,eAAevH,KAAKV,EAAGI,KAGzCmI,EAAeC,SAASpI,GAI5B,GAAIJ,EAAEI,IAASJ,EAAEI,GAAMgI,cAAgB7H,YACrB8G,IAAZtH,EAAEK,KACJL,EAAEK,GAAQ,IAERL,EAAEK,GAAMgI,cAAgB7H,OAC1B8H,EAAWtI,EAAEK,GAAOJ,EAAEI,IAEtB8G,EAAanH,EAAGC,EAAGI,EAAM+G,QAEtB,GAAIxG,MAAMC,QAAQZ,EAAEI,IAAQ,CACjCL,EAAEK,GAAQ,GACV,IAAK,IAAIuB,EAAI,EAAGA,EAAI3B,EAAEI,GAAMP,OAAQ8B,IAClC5B,EAAEK,GAAM4D,KAAKhE,EAAEI,GAAMuB,GAExB,MACCuF,EAAanH,EAAGC,EAAGI,EAAM+G,GAI7B,OAAOpH,CACT,CAagB,SAAAsI,EACdtI,EACAC,EACAyI,GAAc,EACdtB,GAAgB,GAEhB,IAAK,MAAM/G,KAAQJ,GACbO,OAAOC,UAAUyH,eAAevH,KAAKV,EAAGI,KAAyB,IAAhBqI,KAE9B,iBAAZzI,EAAEI,IACG,OAAZJ,EAAEI,IACFG,OAAOmI,eAAe1I,EAAEI,MAAWG,OAAOC,eAE1B6G,IAAZtH,EAAEK,GACJL,EAAEK,GAAQiI,EAAW,CAAA,EAAIrI,EAAEI,GAAOqI,GAEf,iBAAZ1I,EAAEK,IACG,OAAZL,EAAEK,IACFG,OAAOmI,eAAe3I,EAAEK,MAAWG,OAAOC,UAE1C6H,EAAWtI,EAAEK,GAAOJ,EAAEI,GAAOqI,GAE7BvB,EAAanH,EAAGC,EAAGI,EAAM+G,GAElBxG,MAAMC,QAAQZ,EAAEI,IACzBL,EAAEK,GAAQJ,EAAEI,GAAMN,QAElBoH,EAAanH,EAAGC,EAAGI,EAAM+G,IAI/B,OAAOpH,CACT,CASgB,SAAA4I,EAAW5I,EAAcC,GACvC,GAAID,EAAEF,SAAWG,EAAEH,OACjB,OAAO,EAGT,IAAK,IAAI8B,EAAI,EAAGiH,EAAM7I,EAAEF,OAAQ8B,EAAIiH,EAAKjH,IACvC,GAAI5B,EAAE4B,IAAM3B,EAAE2B,GACZ,OAAO,EAIX,OAAO,CACT,CAQM,SAAUkH,EAAQC,GACtB,MAAMC,SAAcD,EAEpB,MAAa,WAATC,EACa,OAAXD,EACK,OAELA,aAAkBE,QACb,UAELF,aAAkBzC,OACb,SAELyC,aAAkBlC,OACb,SAELjG,MAAMC,QAAQkI,GACT,QAELA,aAAkB7I,KACb,OAGF,SAEI,WAAT8I,EACK,SAEI,YAATA,EACK,UAEI,WAATA,EACK,cAEI1B,IAAT0B,EACK,YAGFA,CACT,CAcgB,SAAAE,EACdC,EACAC,GAEA,MAAO,IAAID,EAAKC,EAClB,CAQM,SAAUC,EAAaF,GAC3B,OAAOA,EAAIpJ,OACb,CAQM,SAAUuJ,EAAgBC,GAC9B,OAAOA,EAAKC,wBAAwBC,IACtC,CAQM,SAAUC,EAAiBH,GAC/B,OAAOA,EAAKC,wBAAwBG,KACtC,CAQM,SAAUC,EAAeL,GAC7B,OAAOA,EAAKC,wBAAwBK,GACtC,CAQgB,SAAAC,EAAaP,EAAeQ,GAC1C,IAAIC,EAAUT,EAAKU,UAAUC,MAAM,KACnC,MAAMC,EAAaJ,EAAWG,MAAM,KACpCF,EAAUA,EAAQI,OAChBD,EAAWE,QAAO,SAAUJ,GAC1B,OAAQD,EAAQvB,SAASwB,EAC1B,KAEHV,EAAKU,UAAYD,EAAQM,KAAK,IAChC,CAQgB,SAAAC,EAAgBhB,EAAeQ,GAC7C,IAAIC,EAAUT,EAAKU,UAAUC,MAAM,KACnC,MAAMM,EAAaT,EAAWG,MAAM,KACpCF,EAAUA,EAAQK,QAAO,SAAUJ,GACjC,OAAQO,EAAW/B,SAASwB,EAC9B,IACAV,EAAKU,UAAYD,EAAQM,KAAK,IAChC,CAkBgB,SAAA/F,EAAQwE,EAAatD,GACnC,GAAI7E,MAAMC,QAAQkI,GAAS,CAEzB,MAAMF,EAAME,EAAOjJ,OACnB,IAAK,IAAI8B,EAAI,EAAGA,EAAIiH,EAAKjH,IACvB6D,EAASsD,EAAOnH,GAAIA,EAAGmH,EAE1B,MAEC,IAAK,MAAM1D,KAAO0D,EACZvI,OAAOC,UAAUyH,eAAevH,KAAKoI,EAAQ1D,IAC/CI,EAASsD,EAAO1D,GAAMA,EAAK0D,EAInC,CAQa,MAAA0B,EAAUjK,OAAOd,gBAUdgL,EACd3B,EACA1D,EACArE,GAEA,OAAI+H,EAAO1D,KAASrE,IAClB+H,EAAO1D,GAAOrE,GACP,EAIX,CAQM,SAAU2J,EAASC,GACvB,IAAIC,GAAY,EAEhB,MAAO,KACAA,IACHA,GAAY,EACZC,uBAAsB,KACpBD,GAAY,EACZD,GAAI,IAEP,CAEL,CAOM,SAAUG,EAAevG,GACxBA,IACHA,EAAQ7B,OAAO6B,OAGZA,IAEMA,EAAMuG,eACfvG,EAAMuG,iBAGLvG,EAAcwG,aAAc,EAEjC,UAQgBC,EACdzG,EAA2B7B,OAAO6B,OAKlC,IAAIQ,EAA6B,KASjC,OARKR,IAEMA,EAAMQ,OACfA,EAASR,EAAMQ,OACNR,EAAM0G,aACflG,EAASR,EAAM0G,aAGXlG,aAAkBmG,UAID,MAAnBnG,EAAOoG,UAAuC,GAAnBpG,EAAOoG,WAEpCpG,EAASA,EAAOd,WACVc,aAAkBmG,UAKnBnG,EAXE,IAYX,CASgB,SAAAqG,EAAUxG,EAAkBC,GAC1C,IAAIyE,EAAa1E,EAEjB,KAAO0E,GAAM,CACX,GAAIA,IAASzE,EACX,OAAO,EACF,IAAIyE,EAAKrF,WAGd,OAAO,EAFPqF,EAAOA,EAAKrF,UAIf,CAED,OAAO,CACT,CAEa,MAAAoH,EAAS,CAQpBC,UAAS,CAACvK,EAAgBwK,KACJ,mBAATxK,IACTA,EAAQA,KAGG,MAATA,EACc,GAATA,EAGFwK,GAAgB,MAUzBC,SAAQ,CAACzK,EAAgBwK,KACH,mBAATxK,IACTA,EAAQA,KAGG,MAATA,EACKsF,OAAOtF,IAAUwK,GAAgB,KAGnCA,GAAgB,MAUzBE,SAAQ,CAAC1K,EAAgBwK,KACH,mBAATxK,IACTA,EAAQA,KAGG,MAATA,EACK6F,OAAO7F,GAGTwK,GAAgB,MAUzBG,OAAM,CAAC3K,EAAgBwK,KACD,mBAATxK,IACTA,EAAQA,KAGN4F,EAAS5F,GACJA,EACEqF,EAASrF,GACXA,EAAQ,KAERwK,GAAgB,MAW3BI,UAAS,CACP5K,EACAwK,KAEoB,mBAATxK,IACTA,EAAQA,KAGHA,GAASwK,GAAgB,OAY9B,SAAUK,EAASC,GACvB,IAAIC,EACJ,OAAQD,EAAIhM,QACV,KAAK,EACL,KAAK,EAEH,OADAiM,EAAS7F,EAAWc,KAAK8E,GAClBC,EACH,CACEC,EAAGC,SAASF,EAAO,GAAKA,EAAO,GAAI,IACnCG,EAAGD,SAASF,EAAO,GAAKA,EAAO,GAAI,IACnC9L,EAAGgM,SAASF,EAAO,GAAKA,EAAO,GAAI,KAErC,KACN,KAAK,EACL,KAAK,EAEH,OADAA,EAAS9F,EAAUe,KAAK8E,GACjBC,EACH,CACEC,EAAGC,SAASF,EAAO,GAAI,IACvBG,EAAGD,SAASF,EAAO,GAAI,IACvB9L,EAAGgM,SAASF,EAAO,GAAI,KAEzB,KACN,QACE,OAAO,KAEb,CASgB,SAAAI,EAAgBC,EAAeC,GAC7C,GAAID,EAAM3D,SAAS,QACjB,OAAO2D,EACF,GAAIA,EAAM3D,SAAS,OAAQ,CAChC,MAAM6D,EAAMF,EACTG,OAAOH,EAAMI,QAAQ,KAAO,GAC5BC,QAAQ,IAAK,IACbvC,MAAM,KACT,MAAO,QAAUoC,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMA,EAAI,GAAK,IAAMD,EAAU,GACzE,CAAM,CACL,MAAMC,EAAMT,EAASO,GACrB,OAAW,MAAPE,EACKF,EAEA,QAAUE,EAAIN,EAAI,IAAMM,EAAIJ,EAAI,IAAMI,EAAIrM,EAAI,IAAMoM,EAAU,GAExE,CACH,UAUgBK,EAASC,EAAaC,EAAeC,GACnD,MACE,MAAQ,GAAK,KAAOF,GAAO,KAAOC,GAAS,GAAKC,GAAMlL,SAAS,IAAI5B,MAAM,EAE7E,CA6CgB,SAAA+M,EACdC,EACAC,GAEA,GAAIpG,EAASmG,GAAa,CACxB,IAAIE,EAAmBF,EACvB,GAAIG,GAAWD,GAAW,CACxB,MAAMX,EAAMW,EACTV,OAAO,GACPA,OAAO,EAAGU,EAASnN,OAAS,GAC5BoK,MAAM,KACNnJ,KAAI,SAAUC,GACb,OAAOiL,SAASjL,EAClB,IACFiM,EAAWP,EAASJ,EAAI,GAAIA,EAAI,GAAIA,EAAI,GACzC,CACD,IAA6B,IAAzBa,GAAWF,GAAoB,CACjC,MAAMG,EAAMC,GAASJ,GACfK,EAAkB,CACtBxL,EAAGsL,EAAItL,EACPyL,EAAW,GAARH,EAAIG,EACPC,EAAGC,KAAKC,IAAI,EAAW,KAARN,EAAII,IAEfG,EAAiB,CACrB7L,EAAGsL,EAAItL,EACPyL,EAAGE,KAAKC,IAAI,EAAW,KAARN,EAAIG,GACnBC,EAAW,GAARJ,EAAII,GAEHI,EAAiBC,GACrBF,EAAe7L,EACf6L,EAAeJ,EACfI,EAAeH,GAEXM,EAAkBD,GACtBP,EAAgBxL,EAChBwL,EAAgBC,EAChBD,EAAgBE,GAElB,MAAO,CACLO,WAAYd,EACZe,OAAQJ,EACRK,UAAW,CACTF,WAAYD,EACZE,OAAQJ,GAEVM,MAAO,CACLH,WAAYD,EACZE,OAAQJ,GAGb,CACC,MAAO,CACLG,WAAYd,EACZe,OAAQf,EACRgB,UAAW,CACTF,WAAYd,EACZe,OAAQf,GAEViB,MAAO,CACLH,WAAYd,EACZe,OAAQf,GAIf,CACC,GAAID,EAAc,CA+BhB,MA9B+B,CAC7Be,WAAYhB,EAAWgB,YAAcf,EAAae,WAClDC,OAAQjB,EAAWiB,QAAUhB,EAAagB,OAC1CC,UAAWrH,EAASmG,EAAWkB,WAC3B,CACED,OAAQjB,EAAWkB,UACnBF,WAAYhB,EAAWkB,WAEzB,CACEF,WACGhB,EAAWkB,WAAalB,EAAWkB,UAAUF,YAC9Cf,EAAaiB,UAAUF,WACzBC,OACGjB,EAAWkB,WAAalB,EAAWkB,UAAUD,QAC9ChB,EAAaiB,UAAUD,QAE/BE,MAAOtH,EAASmG,EAAWmB,OACvB,CACEF,OAAQjB,EAAWmB,MACnBH,WAAYhB,EAAWmB,OAEzB,CACEF,OACGjB,EAAWmB,OAASnB,EAAWmB,MAAMF,QACtChB,EAAakB,MAAMF,OACrBD,WACGhB,EAAWmB,OAASnB,EAAWmB,MAAMH,YACtCf,EAAakB,MAAMH,YAI9B,CA6BC,MA5B2B,CACzBA,WAAYhB,EAAWgB,iBAAczG,EACrC0G,OAAQjB,EAAWiB,aAAU1G,EAC7B2G,UAAWrH,EAASmG,EAAWkB,WAC3B,CACED,OAAQjB,EAAWkB,UACnBF,WAAYhB,EAAWkB,WAEzB,CACEF,WACGhB,EAAWkB,WAAalB,EAAWkB,UAAUF,iBAC9CzG,EACF0G,OACGjB,EAAWkB,WAAalB,EAAWkB,UAAUD,aAC9C1G,GAER4G,MAAOtH,EAASmG,EAAWmB,OACvB,CACEF,OAAQjB,EAAWmB,MACnBH,WAAYhB,EAAWmB,OAEzB,CACEF,OACGjB,EAAWmB,OAASnB,EAAWmB,MAAMF,aAAW1G,EACnDyG,WACGhB,EAAWmB,OAASnB,EAAWmB,MAAMH,iBAAezG,GAMrE,UAYgB6G,EAASxB,EAAaC,EAAeC,GACnDF,GAAY,IACZC,GAAgB,IAChBC,GAAc,IACd,MAAMuB,EAASX,KAAKC,IAAIf,EAAKc,KAAKC,IAAId,EAAOC,IACvCwB,EAASZ,KAAKa,IAAI3B,EAAKc,KAAKa,IAAI1B,EAAOC,IAG7C,GAAIuB,IAAWC,EACb,MAAO,CAAEvM,EAAG,EAAGyL,EAAG,EAAGC,EAAGY,GAU1B,MAAO,CAAEtM,EAHI,KADH6K,IAAQyB,EAAS,EAAIvB,IAASuB,EAAS,EAAI,IADnDzB,IAAQyB,EAASxB,EAAQC,EAAOA,IAASuB,EAASzB,EAAMC,EAAQC,EAAOF,IAE7C0B,EAASD,IAAY,IAGhCb,GAFGc,EAASD,GAAUC,EAEPb,EADlBa,EAEhB,CAYA,SAASE,EAAaC,GACpB,MAAMC,EAAc7K,SAASC,cAAc,OAErC6K,EAAoB,CAAA,EAE1BD,EAAY5I,MAAM2I,QAAUA,EAE5B,IAAK,IAAI5M,EAAI,EAAGA,EAAI6M,EAAY5I,MAAM/F,SAAU8B,EAC9C8M,EAAOD,EAAY5I,MAAMjE,IAAM6M,EAAY5I,MAAM8I,iBAC/CF,EAAY5I,MAAMjE,IAItB,OAAO8M,CACT,CAQgB,SAAAE,EAAW/J,EAAsB2J,GAC/C,MAAMK,EAAWN,EAAaC,GAC9B,IAAK,MAAOnJ,EAAKrE,KAAUR,OAAOsO,QAAQD,GACxChK,EAAQgB,MAAMkJ,YAAY1J,EAAKrE,EAEnC,CAQgB,SAAAgO,EAAcnK,EAAsB2J,GAClD,MAAMK,EAAWN,EAAaC,GAC9B,IAAK,MAAMnJ,KAAO7E,OAAOS,KAAK4N,GAC5BhK,EAAQgB,MAAMoJ,eAAe5J,EAEjC,UAYgB6J,EAASpN,EAAWyL,EAAWC,GAC7C,IAAIxB,EACAE,EACAjM,EAEJ,MAAM2B,EAAI6L,KAAK0B,MAAU,EAAJrN,GACfsN,EAAQ,EAAJtN,EAAQF,EACZqG,EAAIuF,GAAK,EAAID,GACb8B,EAAI7B,GAAK,EAAI4B,EAAI7B,GACjBpL,EAAIqL,GAAK,GAAK,EAAI4B,GAAK7B,GAE7B,OAAQ3L,EAAI,GACV,KAAK,EACFoK,EAAIwB,EAAKtB,EAAI/J,EAAKlC,EAAIgI,EACvB,MACF,KAAK,EACF+D,EAAIqD,EAAKnD,EAAIsB,EAAKvN,EAAIgI,EACvB,MACF,KAAK,EACF+D,EAAI/D,EAAKiE,EAAIsB,EAAKvN,EAAIkC,EACvB,MACF,KAAK,EACF6J,EAAI/D,EAAKiE,EAAImD,EAAKpP,EAAIuN,EACvB,MACF,KAAK,EACFxB,EAAI7J,EAAK+J,EAAIjE,EAAKhI,EAAIuN,EACvB,MACF,KAAK,EACFxB,EAAIwB,EAAKtB,EAAIjE,EAAKhI,EAAIoP,EAI3B,MAAO,CACLrD,EAAGyB,KAAK0B,MAAsB,IAAfnD,GACfE,EAAGuB,KAAK0B,MAAsB,IAAfjD,GACfjM,EAAGwN,KAAK0B,MAAsB,IAAflP,GAEnB,UAUgB4N,GAAS/L,EAAWyL,EAAWC,GAC7C,MAAMlB,EAAM4C,EAASpN,EAAGyL,EAAGC,GAC3B,OAAOd,EAASJ,EAAIN,EAAGM,EAAIJ,EAAGI,EAAIrM,EACpC,CAQM,SAAUoN,GAASvB,GACvB,MAAMQ,EAAMT,EAASC,GACrB,IAAKQ,EACH,MAAM,IAAIlE,UAAU,IAAI0D,4BAE1B,OAAOqC,EAAS7B,EAAIN,EAAGM,EAAIJ,EAAGI,EAAIrM,EACpC,CAQM,SAAUkN,GAAWrB,GAEzB,MADa,qCAAqCwD,KAAKxD,EAEzD,CAQM,SAAUoB,GAAWZ,GACzB,OAAOnG,EAAMmJ,KAAKhD,EACpB,CAQM,SAAUiD,GAAYC,GAC1B,OAAOpJ,EAAOkJ,KAAKE,EACrB,CAUgB,SAAAC,GACdC,EACAC,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EAA8B,CAEnE,MAAMC,EAAWpP,OAAOqP,OAAOF,GAC/B,IAAK,IAAI/N,EAAI,EAAGA,EAAI8N,EAAO5P,OAAQ8B,IAC7BpB,OAAOC,UAAUyH,eAAevH,KAAKgP,EAAiBD,EAAO9N,KACtB,iBAA9B+N,EAAgBD,EAAO9N,MAChCgO,EAASF,EAAO9N,IAAMkO,GAAaH,EAAgBD,EAAO9N,MAIhE,OAAOgO,CACR,CACC,OAAO,IAEX,CAWM,SAAUE,GACdH,GAEA,GAAwB,OAApBA,GAAuD,iBAApBA,EACrC,OAAO,KAGT,GAAIA,aAA2BxE,QAE7B,OAAOwE,EAGT,MAAMC,EAAWpP,OAAOqP,OAAOF,GAC/B,IAAK,MAAM/N,KAAK+N,EACVnP,OAAOC,UAAUyH,eAAevH,KAAKgP,EAAiB/N,IACd,iBAA9B+N,EAAwB/N,KAClCgO,EAAShO,GAAKkO,GAAcH,EAAwB/N,KAK1D,OAAOgO,CACT,CASgB,SAAAG,GAAc/P,EAAQgQ,GACpC,IAAK,IAAIpO,EAAI,EAAGA,EAAI5B,EAAEF,OAAQ8B,IAAK,CACjC,MAAMqO,EAAIjQ,EAAE4B,GACZ,IAAIsO,EACJ,IAAKA,EAAItO,EAAGsO,EAAI,GAAKF,EAAQC,EAAGjQ,EAAEkQ,EAAI,IAAM,EAAGA,IAC7ClQ,EAAEkQ,GAAKlQ,EAAEkQ,EAAI,GAEflQ,EAAEkQ,GAAKD,CACR,CACD,OAAOjQ,CACT,CAeM,SAAUmQ,GACdC,EACAC,EACA/E,EACAgF,EAAqB,CAAA,GAGrB,MAAMC,EAAY,SAAUC,GAC1B,OAAOA,OACT,EAEM1J,EAAW,SAAU0J,GACzB,OAAe,OAARA,GAA+B,iBAARA,CAChC,EAaA,IAAK1J,EAASsJ,GACZ,MAAM,IAAIrI,MAAM,2CAGlB,IAAKjB,EAASuJ,GACZ,MAAM,IAAItI,MAAM,uCAGlB,IAAKwI,EAAUjF,GACb,MAAM,IAAIvD,MAAM,sCAGlB,IAAKjB,EAASwJ,GACZ,MAAM,IAAIvI,MAAM,6CAOlB,MAeM0I,EAAYJ,EAAQ/E,GAEpBoF,EADe5J,EAASwJ,KA9Cd,SAAUE,GACxB,IAAK,MAAMG,KAAKH,EACd,GAAIhQ,OAAOC,UAAUyH,eAAevH,KAAK6P,EAAKG,GAC5C,OAAO,EAGX,OAAO,CACT,CAuCiDC,CAAQN,GACrBA,EAAchF,QAAUhE,EACtDuJ,EAAgBH,EAAeA,EAAaI,aAAUxJ,EAK5D,QAAkBA,IAAdmJ,EACF,OAGF,GAAyB,kBAAdA,EAMT,OALK3J,EAASsJ,EAAY9E,MACxB8E,EAAY9E,GAAU,SAGxB8E,EAAY9E,GAAQwF,QAAUL,GAIhC,GAAkB,OAAdA,IAAuB3J,EAASsJ,EAAY9E,IAAU,CAExD,IAAIiF,EAAUG,GAGZ,OAFAN,EAAY9E,GAAU9K,OAAOqP,OAAOa,EAIvC,CAED,IAAK5J,EAAS2J,GACZ,OAOF,IAAIK,GAAU,OAEYxJ,IAAtBmJ,EAAUK,QACZA,EAAUL,EAAUK,aAGExJ,IAAlBuJ,IACFC,EAAUJ,EAAaI,SA5DX,SAAU9L,EAAaqL,EAAc/E,GAC9CxE,EAAS9B,EAAOsG,MACnBtG,EAAOsG,GAAU,IAGnB,MAAMyF,EAAMV,EAAQ/E,GACd0F,EAAMhM,EAAOsG,GACnB,IAAK,MAAMjL,KAAQ0Q,EACbvQ,OAAOC,UAAUyH,eAAevH,KAAKoQ,EAAK1Q,KAC5C2Q,EAAI3Q,GAAQ0Q,EAAI1Q,GAGtB,CAoDA4Q,CAAQb,EAAaC,EAAS/E,GAC9B8E,EAAY9E,GAAQwF,QAAUA,CAChC,CA2BM,SAAUI,GACdC,EACAC,EACAC,EACAC,GAGA,IAAIC,EAAY,EACZC,EAAM,EACNC,EAAON,EAAarR,OAAS,EAEjC,KAAO0R,GAAOC,GAAQF,EALA,KAK2B,CAC/C,MAAMG,EAASjE,KAAK0B,OAAOqC,EAAMC,GAAQ,GAEnCE,EAAOR,EAAaO,GAGpBE,EAAeR,OAFI9J,IAAXgK,EAAuBK,EAAKN,GAASM,EAAKN,GAAOC,IAG/D,GAAoB,GAAhBM,EAEF,OAAOF,GACmB,GAAjBE,EAETJ,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGlBH,GACD,CAED,OAAQ,CACV,CAeM,SAAUM,GACdV,EACAnM,EACAqM,EACAS,EACAV,GAGA,IAGIW,EACA/Q,EACAgR,EACAN,EANAH,EAAY,EACZC,EAAM,EACNC,EAAON,EAAarR,OAAS,EAajC,IAPAsR,EACgB9J,MAAd8J,EACIA,EACA,SAAUpR,EAAWC,GACnB,OAAOD,GAAKC,EAAI,EAAID,EAAIC,GAAK,EAAI,CACnC,EAECuR,GAAOC,GAAQF,EAhBA,KAgB2B,CAQ/C,GANAG,EAASjE,KAAK0B,MAAM,IAAOsC,EAAOD,IAClCO,EAAYZ,EAAa1D,KAAKa,IAAI,EAAGoD,EAAS,IAAIL,GAClDrQ,EAAQmQ,EAAaO,GAAQL,GAC7BW,EACEb,EAAa1D,KAAKC,IAAIyD,EAAarR,OAAS,EAAG4R,EAAS,IAAIL,GAE7B,GAA7BD,EAAWpQ,EAAOgE,GAEpB,OAAO0M,EACF,GACLN,EAAWW,EAAW/M,GAAU,GAChCoM,EAAWpQ,EAAOgE,GAAU,EAG5B,MAAyB,UAAlB8M,EAA6BrE,KAAKa,IAAI,EAAGoD,EAAS,GAAKA,EACzD,GACLN,EAAWpQ,EAAOgE,GAAU,GAC5BoM,EAAWY,EAAWhN,GAAU,EAGhC,MAAyB,UAAlB8M,EACHJ,EACAjE,KAAKC,IAAIyD,EAAarR,OAAS,EAAG4R,EAAS,GAG3CN,EAAWpQ,EAAOgE,GAAU,EAE9BwM,EAAME,EAAS,EAGfD,EAAOC,EAAS,EAGpBH,GACD,CAGD,OAAQ,CACV,CASa,MAAAU,GAAkB,CAO7BC,OAAO/P,GACEA,EASTgQ,WAAWhQ,GACFA,EAAIA,EASbiQ,YAAYjQ,GACHA,GAAK,EAAIA,GASlBkQ,cAAclQ,GACLA,EAAI,GAAM,EAAIA,EAAIA,GAAU,EAAI,EAAIA,GAAKA,EAAlB,EAShCmQ,YAAYnQ,GACHA,EAAIA,EAAIA,EASjBoQ,aAAapQ,KACFA,EAAIA,EAAIA,EAAI,EASvBqQ,eAAerQ,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,GAAKA,EAAI,IAAM,EAAIA,EAAI,IAAM,EAAIA,EAAI,GAAK,EASzEsQ,YAAYtQ,GACHA,EAAIA,EAAIA,EAAIA,EASrBuQ,aAAavQ,GACJ,KAAMA,EAAIA,EAAIA,EAAIA,EAS3BwQ,eAAexQ,GACNA,EAAI,GAAM,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,IAAMA,EAAIA,EAAIA,EAAIA,EAS7DyQ,YAAYzQ,GACHA,EAAIA,EAAIA,EAAIA,EAAIA,EASzB0Q,aAAa1Q,GACJ,IAAMA,EAAIA,EAAIA,EAAIA,EAAIA,EAS/B2Q,eAAe3Q,GACNA,EAAI,GAAM,GAAKA,EAAIA,EAAIA,EAAIA,EAAIA,EAAI,EAAI,KAAOA,EAAIA,EAAIA,EAAIA,EAAIA,YASzD4Q,KACd,MAAMC,EAAQpP,SAASC,cAAc,KACrCmP,EAAMnN,MAAMoN,MAAQ,OACpBD,EAAMnN,MAAMqN,OAAS,QAErB,MAAMC,EAAQvP,SAASC,cAAc,OACrCsP,EAAMtN,MAAMuN,SAAW,WACvBD,EAAMtN,MAAMgE,IAAM,MAClBsJ,EAAMtN,MAAM4D,KAAO,MACnB0J,EAAMtN,MAAMwN,WAAa,SACzBF,EAAMtN,MAAMoN,MAAQ,QACpBE,EAAMtN,MAAMqN,OAAS,QACrBC,EAAMtN,MAAMyN,SAAW,SACvBH,EAAMnP,YAAYgP,GAElBpP,SAASe,KAAKX,YAAYmP,GAC1B,MAAMI,EAAKP,EAAMQ,YACjBL,EAAMtN,MAAMyN,SAAW,SACvB,IAAIG,EAAKT,EAAMQ,YAOf,OANID,GAAME,IACRA,EAAKN,EAAMO,aAGb9P,SAASe,KAAKR,YAAYgP,GAEnBI,EAAKE,CACd,CAyBgB,SAAAE,GAAQC,EAAWC,GACjC,IAAIC,EACClT,MAAMC,QAAQgT,KACjBA,EAAY,CAACA,IAEf,IAAK,MAAME,KAAUH,EACnB,GAAIG,EAAQ,CACVD,EAAYC,EAAOF,EAAU,IAC7B,IAAK,IAAIjS,EAAI,EAAGA,EAAIiS,EAAU/T,OAAQ8B,IAChCkS,IACFA,EAAYA,EAAUD,EAAUjS,KAGpC,QAAyB,IAAdkS,EACT,KAEH,CAEH,OAAOA,CACT,CCxwDA,MAAME,GAAa,CACjBC,MAAO,UACPC,KAAM,UACNC,SAAU,UACVC,WAAY,UACZvH,KAAM,UACNwH,UAAW,UACXzH,MAAO,UACP0H,KAAM,UACNC,SAAU,UACVC,YAAa,UACbC,cAAe,UACfC,kBAAmB,UACnBC,KAAM,UACNC,YAAa,UACbC,KAAM,UACNC,KAAM,UACNC,aAAc,UACdC,WAAY,UACZC,cAAe,UACfC,YAAa,UACbC,SAAU,UACVC,cAAe,UACfC,UAAW,UACXC,eAAgB,UAChBC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,cAAe,UACfC,gBAAiB,UACjBC,OAAQ,UACRC,eAAgB,UAChBC,UAAW,UACXC,eAAgB,UAChBC,iBAAkB,UAClBC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,eAAgB,UAChBC,gBAAiB,UACjBC,UAAW,UACXC,WAAY,UACZC,WAAY,UACZC,OAAQ,UACRC,OAAQ,UACRC,MAAO,UACPC,KAAM,UACNC,QAAS,UACTC,aAAc,UACdC,WAAY,UACZC,QAAS,UACTC,YAAa,UACbC,YAAa,UACbC,aAAc,UACdC,WAAY,UACZC,aAAc,UACdC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,MAAO,UACPC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,eAAgB,UAChBC,WAAY,UACZC,UAAW,UACXC,cAAe,UACfC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,OAAQ,UACRC,gBAAiB,UACjBC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,IAAK,UACLC,UAAW,UACXC,cAAe,UACfC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,QAAS,UACTC,UAAW,UACXC,KAAM,UACNC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,WAAY,UACZC,OAAQ,UACRC,cAAe,UACfC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,SAAU,UACVC,MAAO,UACPC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,WAAY,UACZC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,aAAc,UACdC,MAAO,UACPC,qBAAsB,UACtBC,QAAS,UACThO,IAAK,UACLiO,QAAS,UACTC,QAAS,UACTC,SAAU,UACVC,UAAW,UACXC,OAAQ,UACRC,QAAS,UACTC,MAAO,UACPC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,UAAW,UACXC,YAAa,UACbC,SAAU,UACVC,OAAQ,UACRC,UAAW,UACXC,eAAgB,UAChBC,WAAY,UACZC,cAAe,UACfC,SAAU,UACVC,SAAU,UACVC,aAAc,UACdC,YAAa,UACbC,KAAM,UACNC,OAAQ,UACRC,YAAa,UACbC,MAAO,UACPC,MAAO,WAMF,IAAAC,GAAA,MAILrU,YAAYsU,EAAa,GACvBpZ,KAAKoZ,WAAaA,EAClBpZ,KAAKqZ,WAAY,EACjBrZ,KAAKsZ,kBAAoB,CAAElM,EAAG,MAASmM,EAAG,OAC1CvZ,KAAKyI,EAAI,IAAM,IACfzI,KAAK6I,MAAQ,CAAEJ,EAAG,IAAKE,EAAG,IAAKjM,EAAG,IAAKD,EAAG,GAC1CuD,KAAKwZ,eAAYzV,EACjB/D,KAAKyZ,aAAe,CAAEhR,EAAG,IAAKE,EAAG,IAAKjM,EAAG,IAAKD,EAAG,GACjDuD,KAAK0Z,mBAAgB3V,EACrB/D,KAAK2Z,SAAU,EAGf3Z,KAAK4Z,eAAiB,OACtB5Z,KAAK6Z,cAAgB,OAGrB7Z,KAAK8Z,SACN,CAODC,SAASha,QACagE,IAAhB/D,KAAKa,SACPb,KAAKa,OAAOpB,UACZO,KAAKa,YAASkD,GAEhB/D,KAAKD,UAAYA,EACjBC,KAAKD,UAAUU,YAAYT,KAAKga,OAChCha,KAAKia,cAELja,KAAKka,UACN,CAODC,kBAAkBjY,GAChB,GAAwB,mBAAbA,EAGT,MAAM,IAAIsC,MACR,+EAHFxE,KAAK4Z,eAAiB1X,CAMzB,CAODkY,iBAAiBlY,GACf,GAAwB,mBAAbA,EAGT,MAAM,IAAIsC,MACR,gFAHFxE,KAAK6Z,cAAgB3X,CAMxB,CAQDmY,eAAexR,GACb,GAAqB,iBAAVA,EACT,OAAO4H,GAAW5H,EAErB,CAeDyR,SAASzR,EAAO0R,GAAa,GAC3B,GAAc,SAAV1R,EACF,OAGF,IAAIoD,EAGJ,MAAMuO,EAAYxa,KAAKqa,eAAexR,GAMtC,QALkB9E,IAAdyW,IACF3R,EAAQ2R,IAIc,IAApBnX,EAASwF,IACX,IAA0B,IAAtBc,GAAWd,GAAiB,CAC9B,MAAM4R,EAAY5R,EACfG,OAAO,GACPA,OAAO,EAAGH,EAAMtM,OAAS,GACzBoK,MAAM,KACTsF,EAAO,CAAExD,EAAGgS,EAAU,GAAI9R,EAAG8R,EAAU,GAAI/d,EAAG+d,EAAU,GAAIhe,EAAG,EAChE,MAAM,IAA2B,IAAvBuP,GAAYnD,GAAiB,CACtC,MAAM4R,EAAY5R,EACfG,OAAO,GACPA,OAAO,EAAGH,EAAMtM,OAAS,GACzBoK,MAAM,KACTsF,EAAO,CACLxD,EAAGgS,EAAU,GACb9R,EAAG8R,EAAU,GACb/d,EAAG+d,EAAU,GACbhe,EAAGge,EAAU,GAEhB,MAAM,IAA0B,IAAtB7Q,GAAWf,GAAiB,CACrC,MAAM6R,EAASpS,EAASO,GACxBoD,EAAO,CAAExD,EAAGiS,EAAOjS,EAAGE,EAAG+R,EAAO/R,EAAGjM,EAAGge,EAAOhe,EAAGD,EAAG,EACpD,OAED,GAAIoM,aAAiB5L,aAEL8G,IAAZ8E,EAAMJ,QACM1E,IAAZ8E,EAAMF,QACM5E,IAAZ8E,EAAMnM,EACN,CACA,MAAMie,OAAoB5W,IAAZ8E,EAAMpM,EAAkBoM,EAAMpM,EAAI,MAChDwP,EAAO,CAAExD,EAAGI,EAAMJ,EAAGE,EAAGE,EAAMF,EAAGjM,EAAGmM,EAAMnM,EAAGD,EAAGke,EACjD,CAKL,QAAa5W,IAATkI,EACF,MAAM,IAAIzH,MACR,gIACEoW,KAAKC,UAAUhS,IAGnB7I,KAAK8a,UAAU7O,EAAMsO,EAExB,CAMDQ,YAC6BhX,IAAvB/D,KAAK6Z,gBACP7Z,KAAK6Z,gBACL7Z,KAAK6Z,mBAAgB9V,GAGvB/D,KAAK2Z,SAAU,EACf3Z,KAAKga,MAAM1X,MAAMC,QAAU,QAC3BvC,KAAKgb,oBACN,CAWDC,MAAMC,GAAgB,IAEE,IAAlBA,IACFlb,KAAK0Z,cAAgBzc,OAAOmH,OAAO,CAAA,EAAIpE,KAAK6I,SAGzB,IAAjB7I,KAAK2Z,SACP3Z,KAAK4Z,eAAe5Z,KAAKyZ,cAG3BzZ,KAAKga,MAAM1X,MAAMC,QAAU,OAI3B4Y,YAAW,UACkBpX,IAAvB/D,KAAK6Z,gBACP7Z,KAAK6Z,gBACL7Z,KAAK6Z,mBAAgB9V,EACtB,GACA,EACJ,CAODqX,QACEpb,KAAK4Z,eAAe5Z,KAAK6I,OACzB7I,KAAK2Z,SAAU,EACf3Z,KAAKib,OACN,CAODI,SACErb,KAAK2Z,SAAU,EACf3Z,KAAK4Z,eAAe5Z,KAAK6I,OACzB7I,KAAKsb,cAActb,KAAK6I,MACzB,CAOD0S,iBAC6BxX,IAAvB/D,KAAK0Z,cACP1Z,KAAKsa,SAASta,KAAK0Z,eAAe,GAElC8B,MAAM,oCAET,CASDV,UAAU7O,EAAMsO,GAAa,IAER,IAAfA,IACFva,KAAKyZ,aAAexc,OAAOmH,OAAO,CAAE,EAAE6H,IAGxCjM,KAAK6I,MAAQoD,EACb,MAAMpC,EAAMe,EAASqB,EAAKxD,EAAGwD,EAAKtD,EAAGsD,EAAKvP,GAEpC+e,EAAe,EAAIvR,KAAKwR,GACxBC,EAAS3b,KAAKyI,EAAIoB,EAAIG,EACtBoD,EACJpN,KAAKsZ,kBAAkBlM,EAAIuO,EAASzR,KAAK0R,IAAIH,EAAe5R,EAAItL,GAC5Dgb,EACJvZ,KAAKsZ,kBAAkBC,EAAIoC,EAASzR,KAAK2R,IAAIJ,EAAe5R,EAAItL,GAElEyB,KAAK8b,oBAAoBxZ,MAAM4D,KAC7BkH,EAAI,GAAMpN,KAAK8b,oBAAoB3L,YAAc,KACnDnQ,KAAK8b,oBAAoBxZ,MAAMgE,IAC7BiT,EAAI,GAAMvZ,KAAK8b,oBAAoBC,aAAe,KAEpD/b,KAAKsb,cAAcrP,EACpB,CAQD+P,YAAYve,GACVuC,KAAK6I,MAAMpM,EAAIgB,EAAQ,IACvBuC,KAAKsb,cAActb,KAAK6I,MACzB,CAQDoT,eAAexe,GACb,MAAMoM,EAAMe,EAAS5K,KAAK6I,MAAMJ,EAAGzI,KAAK6I,MAAMF,EAAG3I,KAAK6I,MAAMnM,GAC5DmN,EAAII,EAAIxM,EAAQ,IAChB,MAAMwO,EAAON,EAAS9B,EAAItL,EAAGsL,EAAIG,EAAGH,EAAII,GACxCgC,EAAQ,EAAIjM,KAAK6I,MAAMpM,EACvBuD,KAAK6I,MAAQoD,EACbjM,KAAKsb,eACN,CAQDA,cAAcrP,EAAOjM,KAAK6I,OACxB,MAAMgB,EAAMe,EAASqB,EAAKxD,EAAGwD,EAAKtD,EAAGsD,EAAKvP,GACpCwf,EAAMlc,KAAKmc,kBAAkBC,WAAW,WACrBrY,IAArB/D,KAAKqc,cACPrc,KAAKoZ,YACFha,OAAOkd,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAa5c,KAAKoZ,WAAY,EAAG,EAAGpZ,KAAKoZ,WAAY,EAAG,GAG5D,MAAMyD,EAAI7c,KAAKmc,kBAAkBhM,YAC3B5R,EAAIyB,KAAKmc,kBAAkBJ,aACjCG,EAAIY,UAAU,EAAG,EAAGD,EAAGte,GAEvB2d,EAAIa,aAAa/c,KAAKwZ,UAAW,EAAG,GACpC0C,EAAIc,UAAY,eAAiB,EAAInT,EAAII,GAAK,IAC9CiS,EAAIe,OAAOjd,KAAKsZ,kBAAkBlM,EAAGpN,KAAKsZ,kBAAkBC,EAAGvZ,KAAKyI,GACpEyT,EAAIgB,OAEJld,KAAKmd,gBAAgB1f,MAAQ,IAAMoM,EAAII,EACvCjK,KAAKod,aAAa3f,MAAQ,IAAMwO,EAAKxP,EAErCuD,KAAKqd,gBAAgB/a,MAAMgb,gBACzB,QACAtd,KAAKyZ,aAAahR,EAClB,IACAzI,KAAKyZ,aAAa9Q,EAClB,IACA3I,KAAKyZ,aAAa/c,EAClB,IACAsD,KAAKyZ,aAAahd,EAClB,IACFuD,KAAKud,YAAYjb,MAAMgb,gBACrB,QACAtd,KAAK6I,MAAMJ,EACX,IACAzI,KAAK6I,MAAMF,EACX,IACA3I,KAAK6I,MAAMnM,EACX,IACAsD,KAAK6I,MAAMpM,EACX,GACH,CAODyd,WACEla,KAAKmc,kBAAkB7Z,MAAMoN,MAAQ,OACrC1P,KAAKmc,kBAAkB7Z,MAAMqN,OAAS,OAEtC3P,KAAKmc,kBAAkBzM,MAAQ,IAAM1P,KAAKoZ,WAC1CpZ,KAAKmc,kBAAkBxM,OAAS,IAAM3P,KAAKoZ,UAC5C,CAQDU,UAYE,GAXA9Z,KAAKga,MAAQ3Z,SAASC,cAAc,OACpCN,KAAKga,MAAMtT,UAAY,mBAEvB1G,KAAKwd,eAAiBnd,SAASC,cAAc,OAC7CN,KAAK8b,oBAAsBzb,SAASC,cAAc,OAClDN,KAAK8b,oBAAoBpV,UAAY,eACrC1G,KAAKwd,eAAe/c,YAAYT,KAAK8b,qBAErC9b,KAAKmc,kBAAoB9b,SAASC,cAAc,UAChDN,KAAKwd,eAAe/c,YAAYT,KAAKmc,mBAEhCnc,KAAKmc,kBAAkBC,WAOrB,CACL,MAAMF,EAAMlc,KAAKmc,kBAAkBC,WAAW,MAC9Cpc,KAAKoZ,YACFha,OAAOkd,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,GACJ3c,KAAKmc,kBACFC,WAAW,MACXQ,aAAa5c,KAAKoZ,WAAY,EAAG,EAAGpZ,KAAKoZ,WAAY,EAAG,EAC5D,KApBuC,CACtC,MAAMqE,EAAWpd,SAASC,cAAc,OACxCmd,EAASnb,MAAMuG,MAAQ,MACvB4U,EAASnb,MAAMob,WAAa,OAC5BD,EAASnb,MAAMqb,QAAU,OACzBF,EAASG,UAAY,mDACrB5d,KAAKmc,kBAAkB1b,YAAYgd,EACzC,CAeIzd,KAAKwd,eAAe9W,UAAY,YAEhC1G,KAAK6d,WAAaxd,SAASC,cAAc,OACzCN,KAAK6d,WAAWnX,UAAY,cAE5B1G,KAAK8d,cAAgBzd,SAASC,cAAc,OAC5CN,KAAK8d,cAAcpX,UAAY,iBAE/B1G,KAAK+d,SAAW1d,SAASC,cAAc,OACvCN,KAAK+d,SAASrX,UAAY,YAE1B1G,KAAKod,aAAe/c,SAASC,cAAc,SAC3C,IACEN,KAAKod,aAAa3X,KAAO,QACzBzF,KAAKod,aAAajT,IAAM,IACxBnK,KAAKod,aAAarS,IAAM,KACzB,CAAC,MAAOiT,GAER,CACDhe,KAAKod,aAAa3f,MAAQ,MAC1BuC,KAAKod,aAAa1W,UAAY,YAE9B1G,KAAKmd,gBAAkB9c,SAASC,cAAc,SAC9C,IACEN,KAAKmd,gBAAgB1X,KAAO,QAC5BzF,KAAKmd,gBAAgBhT,IAAM,IAC3BnK,KAAKmd,gBAAgBpS,IAAM,KAC5B,CAAC,MAAOiT,GAER,CACDhe,KAAKmd,gBAAgB1f,MAAQ,MAC7BuC,KAAKmd,gBAAgBzW,UAAY,YAEjC1G,KAAK6d,WAAWpd,YAAYT,KAAKod,cACjCpd,KAAK8d,cAAcrd,YAAYT,KAAKmd,iBAEpC,MAAMc,EAAKje,KACXA,KAAKod,aAAac,SAAW,WAC3BD,EAAGjC,YAAYhc,KAAKvC,MAC1B,EACIuC,KAAKod,aAAae,QAAU,WAC1BF,EAAGjC,YAAYhc,KAAKvC,MAC1B,EACIuC,KAAKmd,gBAAgBe,SAAW,WAC9BD,EAAGhC,eAAejc,KAAKvC,MAC7B,EACIuC,KAAKmd,gBAAgBgB,QAAU,WAC7BF,EAAGhC,eAAejc,KAAKvC,MAC7B,EAEIuC,KAAKoe,gBAAkB/d,SAASC,cAAc,OAC9CN,KAAKoe,gBAAgB1X,UAAY,2BACjC1G,KAAKoe,gBAAgBR,UAAY,cAEjC5d,KAAKqe,aAAehe,SAASC,cAAc,OAC3CN,KAAKqe,aAAa3X,UAAY,wBAC9B1G,KAAKqe,aAAaT,UAAY,WAE9B5d,KAAKud,YAAcld,SAASC,cAAc,OAC1CN,KAAKud,YAAY7W,UAAY,gBAC7B1G,KAAKud,YAAYK,UAAY,MAE7B5d,KAAKqd,gBAAkBhd,SAASC,cAAc,OAC9CN,KAAKqd,gBAAgB3W,UAAY,oBACjC1G,KAAKqd,gBAAgBO,UAAY,UAEjC5d,KAAKse,aAAeje,SAASC,cAAc,OAC3CN,KAAKse,aAAa5X,UAAY,wBAC9B1G,KAAKse,aAAaV,UAAY,SAC9B5d,KAAKse,aAAaC,QAAUve,KAAKib,MAAMla,KAAKf,MAAM,GAElDA,KAAKwe,YAAcne,SAASC,cAAc,OAC1CN,KAAKwe,YAAY9X,UAAY,uBAC7B1G,KAAKwe,YAAYZ,UAAY,QAC7B5d,KAAKwe,YAAYD,QAAUve,KAAKqb,OAAOta,KAAKf,MAE5CA,KAAKye,WAAape,SAASC,cAAc,OACzCN,KAAKye,WAAW/X,UAAY,sBAC5B1G,KAAKye,WAAWb,UAAY,OAC5B5d,KAAKye,WAAWF,QAAUve,KAAKob,MAAMra,KAAKf,MAE1CA,KAAK0e,WAAare,SAASC,cAAc,OACzCN,KAAK0e,WAAWhY,UAAY,sBAC5B1G,KAAK0e,WAAWd,UAAY,YAC5B5d,KAAK0e,WAAWH,QAAUve,KAAKub,UAAUxa,KAAKf,MAE9CA,KAAKga,MAAMvZ,YAAYT,KAAKwd,gBAC5Bxd,KAAKga,MAAMvZ,YAAYT,KAAK+d,UAC5B/d,KAAKga,MAAMvZ,YAAYT,KAAKoe,iBAC5Bpe,KAAKga,MAAMvZ,YAAYT,KAAK8d,eAC5B9d,KAAKga,MAAMvZ,YAAYT,KAAKqe,cAC5Bre,KAAKga,MAAMvZ,YAAYT,KAAK6d,YAC5B7d,KAAKga,MAAMvZ,YAAYT,KAAKud,aAC5Bvd,KAAKga,MAAMvZ,YAAYT,KAAKqd,iBAE5Brd,KAAKga,MAAMvZ,YAAYT,KAAKse,cAC5Bte,KAAKga,MAAMvZ,YAAYT,KAAKwe,aAC5Bxe,KAAKga,MAAMvZ,YAAYT,KAAKye,YAC5Bze,KAAKga,MAAMvZ,YAAYT,KAAK0e,WAC7B,CAODzE,cACEja,KAAK2e,KAAO,GACZ3e,KAAK4e,MAAQ,GACb5e,KAAKa,OAAS,IAAI1B,EAAOa,KAAKmc,mBAC9Bnc,KAAKa,OAAOlB,IAAI,SAASC,IAAI,CAAEif,QAAQ,IAEvC7e,KAAKa,OAAOtB,GAAG,gBAAiB0B,IAC1BA,EAAM6d,SACR9e,KAAK+e,cAAc9d,EACpB,IAEHjB,KAAKa,OAAOtB,GAAG,OAAQ0B,IACrBjB,KAAK+e,cAAc9d,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,YAAa0B,IAC1BjB,KAAK+e,cAAc9d,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,WAAY0B,IACzBjB,KAAK+e,cAAc9d,EAAM,IAE3BjB,KAAKa,OAAOtB,GAAG,UAAW0B,IACxBjB,KAAK+e,cAAc9d,EAAM,GAE5B,CAOD+Z,qBACE,IAAuB,IAAnBhb,KAAKqZ,UAAqB,CAC5B,MAAM6C,EAAMlc,KAAKmc,kBAAkBC,WAAW,WACrBrY,IAArB/D,KAAKqc,cACPrc,KAAKoZ,YACFha,OAAOkd,kBAAoB,IAC3BJ,EAAIK,8BACHL,EAAIM,2BACJN,EAAIO,0BACJP,EAAIQ,yBACJR,EAAIS,wBACJ,IAENT,EAAIU,aAAa5c,KAAKoZ,WAAY,EAAG,EAAGpZ,KAAKoZ,WAAY,EAAG,GAG5D,MAAMyD,EAAI7c,KAAKmc,kBAAkBhM,YAC3B5R,EAAIyB,KAAKmc,kBAAkBJ,aAIjC,IAAI3O,EAAGmM,EAAGyF,EAAKC,EAHf/C,EAAIY,UAAU,EAAG,EAAGD,EAAGte,GAIvByB,KAAKsZ,kBAAoB,CAAElM,EAAO,GAAJyP,EAAStD,EAAO,GAAJhb,GAC1CyB,KAAKyI,EAAI,IAAOoU,EAChB,MAAMpB,EAAgB,EAAIvR,KAAKwR,GAAM,IAC/BwD,EAAO,EAAI,IACXC,EAAO,EAAInf,KAAKyI,EACtB,IAAIM,EACJ,IAAKiW,EAAM,EAAGA,EAAM,IAAKA,IACvB,IAAKC,EAAM,EAAGA,EAAMjf,KAAKyI,EAAGwW,IAC1B7R,EAAIpN,KAAKsZ,kBAAkBlM,EAAI6R,EAAM/U,KAAK0R,IAAIH,EAAeuD,GAC7DzF,EAAIvZ,KAAKsZ,kBAAkBC,EAAI0F,EAAM/U,KAAK2R,IAAIJ,EAAeuD,GAC7DjW,EAAM4C,EAASqT,EAAME,EAAMD,EAAME,EAAM,GACvCjD,EAAIc,UAAY,OAASjU,EAAIN,EAAI,IAAMM,EAAIJ,EAAI,IAAMI,EAAIrM,EAAI,IAC7Dwf,EAAIkD,SAAShS,EAAI,GAAKmM,EAAI,GAAK,EAAG,GAGtC2C,EAAImD,YAAc,gBAClBnD,EAAIe,OAAOjd,KAAKsZ,kBAAkBlM,EAAGpN,KAAKsZ,kBAAkBC,EAAGvZ,KAAKyI,GACpEyT,EAAIoD,SAEJtf,KAAKwZ,UAAY0C,EAAIqD,aAAa,EAAG,EAAG1C,EAAGte,EAC5C,CACDyB,KAAKqZ,WAAY,CAClB,CAQD0F,cAAc9d,GACZ,MAAMue,EAAOxf,KAAKwd,eAAevX,wBAC3BC,EAAOjF,EAAMwe,OAAOrS,EAAIoS,EAAKtZ,KAC7BI,EAAMrF,EAAMwe,OAAOlG,EAAIiG,EAAKlZ,IAE5BoZ,EAAU,GAAM1f,KAAKwd,eAAezB,aACpC4D,EAAU,GAAM3f,KAAKwd,eAAerN,YAEpC/C,EAAIlH,EAAOyZ,EACXpG,EAAIjT,EAAMoZ,EAEVE,EAAQ1V,KAAK2V,MAAMzS,EAAGmM,GACtBoC,EAAS,IAAOzR,KAAKC,IAAID,KAAK4V,KAAK1S,EAAIA,EAAImM,EAAIA,GAAIoG,GAEnDI,EAAS7V,KAAK2R,IAAI+D,GAASjE,EAAS+D,EACpCM,EAAU9V,KAAK0R,IAAIgE,GAASjE,EAASgE,EAE3C3f,KAAK8b,oBAAoBxZ,MAAMgE,IAC7ByZ,EAAS,GAAM/f,KAAK8b,oBAAoBC,aAAe,KACzD/b,KAAK8b,oBAAoBxZ,MAAM4D,KAC7B8Z,EAAU,GAAMhgB,KAAK8b,oBAAoB3L,YAAc,KAGzD,IAAI5R,EAAIqhB,GAAS,EAAI1V,KAAKwR,IAC1Bnd,EAAIA,EAAI,EAAIA,EAAI,EAAIA,EACpB,MAAMyL,EAAI2R,EAAS3b,KAAKyI,EAClBoB,EAAMe,EAAS5K,KAAK6I,MAAMJ,EAAGzI,KAAK6I,MAAMF,EAAG3I,KAAK6I,MAAMnM,GAC5DmN,EAAItL,EAAIA,EACRsL,EAAIG,EAAIA,EACR,MAAMiC,EAAON,EAAS9B,EAAItL,EAAGsL,EAAIG,EAAGH,EAAII,GACxCgC,EAAQ,EAAIjM,KAAK6I,MAAMpM,EACvBuD,KAAK6I,MAAQoD,EAGbjM,KAAKqd,gBAAgB/a,MAAMgb,gBACzB,QACAtd,KAAKyZ,aAAahR,EAClB,IACAzI,KAAKyZ,aAAa9Q,EAClB,IACA3I,KAAKyZ,aAAa/c,EAClB,IACAsD,KAAKyZ,aAAahd,EAClB,IACFuD,KAAKud,YAAYjb,MAAMgb,gBACrB,QACAtd,KAAK6I,MAAMJ,EACX,IACAzI,KAAK6I,MAAMF,EACX,IACA3I,KAAK6I,MAAMnM,EACX,IACAsD,KAAK6I,MAAMpM,EACX,GACH,GCvxBH,SAASwjB,MAAaC,GACpB,GAAIA,EAAK3jB,OAAS,EAChB,MAAM,IAAIsI,UAAU,sBACf,GAAoB,IAAhBqb,EAAK3jB,OACd,OAAO8D,SAAS8f,eAAeD,EAAK,IAC/B,CACL,MAAM5e,EAAUjB,SAASC,cAAc4f,EAAK,IAE5C,OADA5e,EAAQb,YAAYwf,MAAaC,EAAK1jB,MAAM,KACrC8E,CACR,CACH,CAWO,IC5BH8e,GADAC,IAAa,EAGV,MAAMC,GAAwB,sCCG9B,MAAMxgB,GAAiBygB,EACjBC,GAAmBC,GACnBC,GFqBN,MAQL5b,YACE6b,EACAC,EACAC,EACAzH,EAAa,EACb0H,EAAa,MAAM,IAEnB9gB,KAAKuB,OAASof,EACd3gB,KAAK+gB,eAAiB,GACtB/gB,KAAKD,UAAY6gB,EACjB5gB,KAAKghB,eAAgB,EACrBhhB,KAAK8gB,WAAaA,EAElB9gB,KAAK8M,QAAU,GACf9M,KAAKihB,aAAc,EACnBjhB,KAAKkhB,aAAe,EACpBlhB,KAAKmhB,eAAiB,CACpB5T,SAAS,EACTzG,QAAQ,EACR/G,eAAWgE,EACXqd,YAAY,GAEdnkB,OAAOmH,OAAOpE,KAAK8M,QAAS9M,KAAKmhB,gBAEjCnhB,KAAK6gB,iBAAmBA,EACxB7gB,KAAKqhB,cAAgB,GACrBrhB,KAAKshB,YAAc,GACnBthB,KAAKuhB,SAAW,GAChBvhB,KAAKwhB,WAAa,EAClBxhB,KAAKyhB,aAAe,GACpBzhB,KAAK0hB,YAAc,IAAIlB,GAAYpH,GACnCpZ,KAAK2hB,aAAU5d,CAChB,CAQD6d,WAAW9U,GACT,QAAgB/I,IAAZ+I,EAAuB,CAEzB9M,KAAKyhB,aAAe,GACpBzhB,KAAK6hB,eAEL,IAAItU,GAAU,EACd,GAAuB,iBAAZT,EACT9M,KAAK8M,QAAQhG,OAASgG,OACjB,GAAIzP,MAAMC,QAAQwP,GACvB9M,KAAK8M,QAAQhG,OAASgG,EAAQ/F,YACzB,GAAuB,iBAAZ+F,EAAsB,CACtC,GAAe,MAAXA,EACF,MAAM,IAAIjI,UAAU,+BAEId,IAAtB+I,EAAQ/M,YACVC,KAAK8M,QAAQ/M,UAAY+M,EAAQ/M,gBAEZgE,IAAnB+I,EAAQhG,SACV9G,KAAK8M,QAAQhG,OAASgG,EAAQhG,aAEL/C,IAAvB+I,EAAQsU,aACVphB,KAAK8M,QAAQsU,WAAatU,EAAQsU,iBAEZrd,IAApB+I,EAAQS,UACVA,EAAUT,EAAQS,QAE5B,KAAoC,kBAAZT,GAChB9M,KAAK8M,QAAQhG,QAAS,EACtByG,EAAUT,GACkB,mBAAZA,IAChB9M,KAAK8M,QAAQhG,OAASgG,EACtBS,GAAU,IAEgB,IAAxBvN,KAAK8M,QAAQhG,SACfyG,GAAU,GAGZvN,KAAK8M,QAAQS,QAAUA,CACxB,CACDvN,KAAK8hB,QACN,CAMDC,iBAAiBV,GACfrhB,KAAKqhB,cAAgBA,GACQ,IAAzBrhB,KAAK8M,QAAQS,UACfvN,KAAK8hB,cAC0B/d,IAA3B/D,KAAK8M,QAAQ/M,YACfC,KAAKD,UAAYC,KAAK8M,QAAQ/M,WAEhCC,KAAK8Z,UAER,CAODA,UACE9Z,KAAK8hB,SACL9hB,KAAK+gB,eAAiB,GAEtB,MAAMja,EAAS9G,KAAK8M,QAAQhG,OAC5B,IAAIkb,EAAU,EACVjH,GAAO,EACX,IAAK,MAAMhT,KAAU/H,KAAK6gB,iBACpB5jB,OAAOC,UAAUyH,eAAevH,KAAK4C,KAAK6gB,iBAAkB9Y,KAC9D/H,KAAKghB,eAAgB,EACrBjG,GAAO,EACe,mBAAXjU,GACTiU,EAAOjU,EAAOiB,EAAQ,IACtBgT,EACEA,GACA/a,KAAKiiB,cAAcjiB,KAAK6gB,iBAAiB9Y,GAAS,CAACA,IAAS,KAC1C,IAAXjB,IAA+C,IAA5BA,EAAOmC,QAAQlB,KAC3CgT,GAAO,IAGI,IAATA,IACF/a,KAAKghB,eAAgB,EAGjBgB,EAAU,GACZhiB,KAAKkiB,UAAU,IAGjBliB,KAAKmiB,YAAYpa,GAGjB/H,KAAKiiB,cAAcjiB,KAAK6gB,iBAAiB9Y,GAAS,CAACA,KAErDia,KAGJhiB,KAAKoiB,cACLpiB,KAAKqiB,OAEN,CAODA,QACEriB,KAAK2hB,QAAUthB,SAASC,cAAc,OACtCN,KAAK2hB,QAAQjb,UAAY,4BACzB1G,KAAKD,UAAUU,YAAYT,KAAK2hB,SAChC,IAAK,IAAItjB,EAAI,EAAGA,EAAI2B,KAAKshB,YAAY/kB,OAAQ8B,IAC3C2B,KAAK2hB,QAAQlhB,YAAYT,KAAKshB,YAAYjjB,IAG5C2B,KAAKsiB,oBACN,CAODR,SACE,IAAK,IAAIzjB,EAAI,EAAGA,EAAI2B,KAAKshB,YAAY/kB,OAAQ8B,IAC3C2B,KAAK2hB,QAAQ/gB,YAAYZ,KAAKshB,YAAYjjB,SAGvB0F,IAAjB/D,KAAK2hB,UACP3hB,KAAKD,UAAUa,YAAYZ,KAAK2hB,SAChC3hB,KAAK2hB,aAAU5d,GAEjB/D,KAAKshB,YAAc,GAEnBthB,KAAK6hB,cACN,CASDU,UAAUC,GACR,IAAIxmB,EAAOgE,KAAKqhB,cAChB,IAAK,IAAIhjB,EAAI,EAAGA,EAAImkB,EAAKjmB,OAAQ8B,IAAK,CACpC,QAAsB0F,IAAlB/H,EAAKwmB,EAAKnkB,IAEP,CACLrC,OAAO+H,EACP,KACD,CAJC/H,EAAOA,EAAKwmB,EAAKnkB,GAKpB,CACD,OAAOrC,CACR,CAUDkmB,UAAUM,KAASlB,GACjB,IAA2B,IAAvBthB,KAAKghB,cAAwB,CAC/B,MAAM5S,EAAO/N,SAASC,cAAc,OAOpC,OANA8N,EAAK1H,UACH,iDAAmD8b,EAAKjmB,OAC1D+kB,EAAYtgB,SAASM,IACnB8M,EAAK3N,YAAYa,EAAQ,IAE3BtB,KAAKshB,YAAY5gB,KAAK0N,GACfpO,KAAKshB,YAAY/kB,MACzB,CACD,OAAO,CACR,CAQD4lB,YAAYM,GACV,MAAMC,EAAMriB,SAASC,cAAc,OACnCoiB,EAAIhc,UAAY,sCAChBgc,EAAI9E,UAAY6E,EAChBziB,KAAKkiB,UAAU,GAAIQ,EACpB,CAWDC,WAAWF,EAAMD,EAAMI,GAAc,GACnC,MAAMF,EAAMriB,SAASC,cAAc,OAGnC,GAFAoiB,EAAIhc,UACF,kDAAoD8b,EAAKjmB,QACvC,IAAhBqmB,EAAsB,CACxB,KAAOF,EAAItf,YACTsf,EAAI9hB,YAAY8hB,EAAItf,YAEtBsf,EAAIjiB,YAAYwf,GAAU,IAAK,IAAKwC,GAC1C,MACMC,EAAI9E,UAAY6E,EAAO,IAEzB,OAAOC,CACR,CAUDG,cAAcjd,EAAKnI,EAAO+kB,GACxB,MAAMM,EAASziB,SAASC,cAAc,UACtCwiB,EAAOpc,UAAY,sCACnB,IAAIqc,EAAgB,OACNhf,IAAVtG,IAC0B,IAAxBmI,EAAIqD,QAAQxL,KACdslB,EAAgBnd,EAAIqD,QAAQxL,IAIhC,IAAK,IAAIY,EAAI,EAAGA,EAAIuH,EAAIrJ,OAAQ8B,IAAK,CACnC,MAAM0J,EAAS1H,SAASC,cAAc,UACtCyH,EAAOtK,MAAQmI,EAAIvH,GACfA,IAAM0kB,IACRhb,EAAOib,SAAW,YAEpBjb,EAAO6V,UAAYhY,EAAIvH,GACvBykB,EAAOriB,YAAYsH,EACpB,CAED,MAAMkW,EAAKje,KACX8iB,EAAO5E,SAAW,WAChBD,EAAGgF,QAAQjjB,KAAKvC,MAAO+kB,EAC7B,EAEI,MAAMU,EAAQljB,KAAK2iB,WAAWH,EAAKA,EAAKjmB,OAAS,GAAIimB,GACrDxiB,KAAKkiB,UAAUM,EAAMU,EAAOJ,EAC7B,CAUDK,WAAWvd,EAAKnI,EAAO+kB,GACrB,MAAMva,EAAerC,EAAI,GACnBuE,EAAMvE,EAAI,GACVmF,EAAMnF,EAAI,GACVwd,EAAOxd,EAAI,GACXyd,EAAQhjB,SAASC,cAAc,SACrC+iB,EAAM3c,UAAY,qCAClB,IACE2c,EAAM5d,KAAO,QACb4d,EAAMlZ,IAAMA,EACZkZ,EAAMtY,IAAMA,CACb,CAAC,MAAOiT,GAER,CACDqF,EAAMD,KAAOA,EAGb,IAAIE,EAAc,GACdC,EAAa,EAEjB,QAAcxf,IAAVtG,EAAqB,CACvB,MAAM+lB,EAAS,IACX/lB,EAAQ,GAAKA,EAAQ+lB,EAASrZ,GAChCkZ,EAAMlZ,IAAMD,KAAKuZ,KAAKhmB,EAAQ+lB,GAC9BD,EAAaF,EAAMlZ,IACnBmZ,EAAc,mBACL7lB,EAAQ+lB,EAASrZ,IAC1BkZ,EAAMlZ,IAAMD,KAAKuZ,KAAKhmB,EAAQ+lB,GAC9BD,EAAaF,EAAMlZ,IACnBmZ,EAAc,mBAEZ7lB,EAAQ+lB,EAASzY,GAAe,IAARA,IAC1BsY,EAAMtY,IAAMb,KAAKuZ,KAAKhmB,EAAQ+lB,GAC9BD,EAAaF,EAAMtY,IACnBuY,EAAc,mBAEhBD,EAAM5lB,MAAQA,CACpB,MACM4lB,EAAM5lB,MAAQwK,EAGhB,MAAMyb,EAAQrjB,SAASC,cAAc,SACrCojB,EAAMhd,UAAY,0CAClBgd,EAAMjmB,MAAQ4lB,EAAM5lB,MAEpB,MAAMwgB,EAAKje,KACXqjB,EAAMnF,SAAW,WACfwF,EAAMjmB,MAAQuC,KAAKvC,MACnBwgB,EAAGgF,QAAQlgB,OAAO/C,KAAKvC,OAAQ+kB,EACrC,EACIa,EAAMlF,QAAU,WACduF,EAAMjmB,MAAQuC,KAAKvC,KACzB,EAEI,MAAMylB,EAAQljB,KAAK2iB,WAAWH,EAAKA,EAAKjmB,OAAS,GAAIimB,GAC/CmB,EAAY3jB,KAAKkiB,UAAUM,EAAMU,EAAOG,EAAOK,GAGjC,KAAhBJ,GAAsBtjB,KAAKyhB,aAAakC,KAAeJ,IACzDvjB,KAAKyhB,aAAakC,GAAaJ,EAC/BvjB,KAAK4jB,YAAYN,EAAaK,GAEjC,CAODvB,cACE,IAAgC,IAA5BpiB,KAAK8M,QAAQsU,WAAqB,CACpC,MAAMyC,EAAiBxjB,SAASC,cAAc,OAC9CujB,EAAend,UAAY,sCAC3Bmd,EAAejG,UAAY,mBAC3BiG,EAAetF,QAAU,KACvBve,KAAK8jB,eAAe,EAEtBD,EAAeE,YAAc,KAC3BF,EAAend,UAAY,2CAA2C,EAExEmd,EAAeG,WAAa,KAC1BH,EAAend,UAAY,qCAAqC,EAGlE1G,KAAKikB,iBAAmB5jB,SAASC,cAAc,OAC/CN,KAAKikB,iBAAiBvd,UACpB,gDAEF1G,KAAKshB,YAAY5gB,KAAKV,KAAKikB,kBAC3BjkB,KAAKshB,YAAY5gB,KAAKmjB,EACvB,CACF,CASDD,YAAYzlB,EAAQ+lB,GAClB,IACuB,IAArBlkB,KAAKihB,cACkB,IAAvBjhB,KAAKghB,eACLhhB,KAAKkhB,aAAelhB,KAAKwhB,WACzB,CACA,MAAMkB,EAAMriB,SAASC,cAAc,OACnCoiB,EAAIyB,GAAK,0BACTzB,EAAIhc,UAAY,0BAChBgc,EAAI9E,UAAYzf,EAChBukB,EAAInE,QAAU,KACZve,KAAK6hB,cAAc,EAErB7hB,KAAKkhB,cAAgB,EACrBlhB,KAAKuhB,SAAW,CAAE6C,KAAM1B,EAAKwB,MAAOA,EACrC,CACF,CAODrC,oBAC6B9d,IAAvB/D,KAAKuhB,SAAS6C,OAChBpkB,KAAKuhB,SAAS6C,KAAKzjB,WAAWC,YAAYZ,KAAKuhB,SAAS6C,MACxDC,aAAarkB,KAAKuhB,SAAS+C,aAC3BD,aAAarkB,KAAKuhB,SAASgD,eAC3BvkB,KAAKuhB,SAAW,GAEnB,CAODe,qBACE,QAA2Bve,IAAvB/D,KAAKuhB,SAAS6C,KAAoB,CACpC,MACM5E,EADuBxf,KAAKshB,YAAYthB,KAAKuhB,SAAS2C,OAC1Bje,wBAClCjG,KAAKuhB,SAAS6C,KAAK9hB,MAAM4D,KAAOsZ,EAAKtZ,KAAO,KAC5ClG,KAAKuhB,SAAS6C,KAAK9hB,MAAMgE,IAAMkZ,EAAKlZ,IAAM,GAAK,KAC/CjG,SAASe,KAAKX,YAAYT,KAAKuhB,SAAS6C,MACxCpkB,KAAKuhB,SAAS+C,YAAcnJ,YAAW,KACrCnb,KAAKuhB,SAAS6C,KAAK9hB,MAAMwG,QAAU,CAAC,GACnC,MACH9I,KAAKuhB,SAASgD,cAAgBpJ,YAAW,KACvCnb,KAAK6hB,cAAc,GAClB,KACJ,CACF,CAUD2C,cAAcvc,EAAcxK,EAAO+kB,GACjC,MAAMiC,EAAWpkB,SAASC,cAAc,SACxCmkB,EAAShf,KAAO,WAChBgf,EAAS/d,UAAY,wCACrB+d,EAASC,QAAUzc,OACLlE,IAAVtG,IACFgnB,EAASC,QAAUjnB,EACfA,IAAUwK,IACgB,iBAAjBA,EACLxK,IAAUwK,EAAasF,SACzBvN,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOA,IAGhDuC,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOA,MAKpD,MAAMwgB,EAAKje,KACXykB,EAASvG,SAAW,WAClBD,EAAGgF,QAAQjjB,KAAK0kB,QAASlC,EAC/B,EAEI,MAAMU,EAAQljB,KAAK2iB,WAAWH,EAAKA,EAAKjmB,OAAS,GAAIimB,GACrDxiB,KAAKkiB,UAAUM,EAAMU,EAAOuB,EAC7B,CAUDE,eAAe1c,EAAcxK,EAAO+kB,GAClC,MAAMiC,EAAWpkB,SAASC,cAAc,SACxCmkB,EAAShf,KAAO,OAChBgf,EAAS/d,UAAY,oCACrB+d,EAAShnB,MAAQA,EACbA,IAAUwK,GACZjI,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOA,IAGhD,MAAMwgB,EAAKje,KACXykB,EAASvG,SAAW,WAClBD,EAAGgF,QAAQjjB,KAAKvC,MAAO+kB,EAC7B,EAEI,MAAMU,EAAQljB,KAAK2iB,WAAWH,EAAKA,EAAKjmB,OAAS,GAAIimB,GACrDxiB,KAAKkiB,UAAUM,EAAMU,EAAOuB,EAC7B,CAUDG,gBAAgBhf,EAAKnI,EAAO+kB,GAC1B,MAAM/Y,EAAe7D,EAAI,GACnB8c,EAAMriB,SAASC,cAAc,OAGrB,UAFd7C,OAAkBsG,IAAVtG,EAAsBgM,EAAehM,IAG3CilB,EAAIhc,UAAY,0CAChBgc,EAAIpgB,MAAMgb,gBAAkB7f,GAE5BilB,EAAIhc,UAAY,+CAGlBjJ,OAAkBsG,IAAVtG,EAAsBgM,EAAehM,EAC7CilB,EAAInE,QAAU,KACZve,KAAK6kB,iBAAiBpnB,EAAOilB,EAAKF,EAAK,EAGzC,MAAMU,EAAQljB,KAAK2iB,WAAWH,EAAKA,EAAKjmB,OAAS,GAAIimB,GACrDxiB,KAAKkiB,UAAUM,EAAMU,EAAOR,EAC7B,CAUDmC,iBAAiBpnB,EAAOilB,EAAKF,GAE3BE,EAAInE,QAAU,aAEdve,KAAK0hB,YAAY3H,SAAS2I,GAC1B1iB,KAAK0hB,YAAY3G,OAEjB/a,KAAK0hB,YAAYpH,SAAS7c,GAC1BuC,KAAK0hB,YAAYvH,mBAAmBtR,IAClC,MAAMic,EACJ,QAAUjc,EAAMJ,EAAI,IAAMI,EAAMF,EAAI,IAAME,EAAMnM,EAAI,IAAMmM,EAAMpM,EAAI,IACtEimB,EAAIpgB,MAAMgb,gBAAkBwH,EAC5B9kB,KAAKijB,QAAQ6B,EAAatC,EAAK,IAIjCxiB,KAAK0hB,YAAYtH,kBAAiB,KAChCsI,EAAInE,QAAU,KACZve,KAAK6kB,iBAAiBpnB,EAAOilB,EAAKF,EAAK,CACxC,GAEJ,CAWDP,cAAchV,EAAKuV,EAAO,GAAIuC,GAAY,GACxC,IAAIhK,GAAO,EACX,MAAMjU,EAAS9G,KAAK8M,QAAQhG,OAC5B,IAAIke,GAAe,EACnB,IAAK,MAAMC,KAAUhY,EACnB,GAAIhQ,OAAOC,UAAUyH,eAAevH,KAAK6P,EAAKgY,GAAS,CACrDlK,GAAO,EACP,MAAM3M,EAAOnB,EAAIgY,GACXC,EAAUvf,EAAmB6c,EAAMyC,GAmBzC,GAlBsB,mBAAXne,IACTiU,EAAOjU,EAAOme,EAAQzC,IAGT,IAATzH,IAEC1d,MAAMC,QAAQ8Q,IACC,iBAATA,GACS,kBAATA,GACPA,aAAgBnR,SAEhB+C,KAAKghB,eAAgB,EACrBjG,EAAO/a,KAAKiiB,cAAc7T,EAAM8W,GAAS,GACzCllB,KAAKghB,eAA8B,IAAd+D,KAKd,IAAThK,EAAgB,CAClBiK,GAAe,EACf,MAAMvnB,EAAQuC,KAAKuiB,UAAU2C,GAE7B,GAAI7nB,MAAMC,QAAQ8Q,GAChBpO,KAAKmlB,aAAa/W,EAAM3Q,EAAOynB,QAC1B,GAAoB,iBAAT9W,EAChBpO,KAAK2kB,eAAevW,EAAM3Q,EAAOynB,QAC5B,GAAoB,kBAAT9W,EAChBpO,KAAKwkB,cAAcpW,EAAM3Q,EAAOynB,QAC3B,GAAI9W,aAAgBnR,QAEzB,IAAK+C,KAAK8gB,WAAW0B,EAAMyC,EAAQjlB,KAAKqhB,eAEtC,QAAqBtd,IAAjBqK,EAAKb,QAAuB,CAC9B,MAAM6X,EAAczf,EAAmBuf,EAAS,WAC1CG,EAAerlB,KAAKuiB,UAAU6C,GACpC,IAAqB,IAAjBC,EAAuB,CACzB,MAAMnC,EAAQljB,KAAK2iB,WAAWsC,EAAQC,GAAS,GAC/CllB,KAAKkiB,UAAUgD,EAAShC,GACxB8B,EACEhlB,KAAKiiB,cAAc7T,EAAM8W,IAAYF,CACzD,MACkBhlB,KAAKwkB,cAAcpW,EAAMiX,EAAcH,EAEzD,KAAqB,CACL,MAAMhC,EAAQljB,KAAK2iB,WAAWsC,EAAQC,GAAS,GAC/CllB,KAAKkiB,UAAUgD,EAAShC,GACxB8B,EACEhlB,KAAKiiB,cAAc7T,EAAM8W,IAAYF,CACxC,OAGHM,QAAQC,MAAM,0BAA2BnX,EAAM6W,EAAQC,EAE1D,CACF,CAEH,OAAOF,CACR,CAUDG,aAAavf,EAAKnI,EAAO+kB,GACD,iBAAX5c,EAAI,IAA8B,UAAXA,EAAI,IACpC5F,KAAK4kB,gBAAgBhf,EAAKnI,EAAO+kB,GAC7B5c,EAAI,KAAOnI,GACbuC,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOA,KAErB,iBAAXmI,EAAI,IACpB5F,KAAK6iB,cAAcjd,EAAKnI,EAAO+kB,GAC3B5c,EAAI,KAAOnI,GACbuC,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOA,KAErB,iBAAXmI,EAAI,KACpB5F,KAAKmjB,WAAWvd,EAAKnI,EAAO+kB,GACxB5c,EAAI,KAAOnI,GACbuC,KAAK+gB,eAAergB,KAAK,CAAE8hB,KAAMA,EAAM/kB,MAAOsF,OAAOtF,KAG1D,CASDwlB,QAAQxlB,EAAO+kB,GACb,MAAM1V,EAAU9M,KAAKwlB,kBAAkB/nB,EAAO+kB,GAG5CxiB,KAAKuB,OAAOH,MACZpB,KAAKuB,OAAOH,KAAKqkB,SACjBzlB,KAAKuB,OAAOH,KAAKqkB,QAAQ/lB,MAEzBM,KAAKuB,OAAOH,KAAKqkB,QAAQ/lB,KAAK,eAAgBoN,GAEhD9M,KAAKihB,aAAc,EACnBjhB,KAAKuB,OAAOqgB,WAAW9U,EACxB,CAUD0Y,kBAAkB/nB,EAAO+kB,EAAMkD,EAAa,CAAA,GAC1C,IAAIC,EAAUD,EAIdjoB,EAAkB,WADlBA,EAAkB,SAAVA,GAA0BA,IACEA,EAEpC,IAAK,IAAIY,EAAI,EAAGA,EAAImkB,EAAKjmB,OAAQ8B,IACf,WAAZmkB,EAAKnkB,UACkB0F,IAArB4hB,EAAQnD,EAAKnkB,MACfsnB,EAAQnD,EAAKnkB,IAAM,CAAA,GAEjBA,IAAMmkB,EAAKjmB,OAAS,EACtBopB,EAAUA,EAAQnD,EAAKnkB,IAEvBsnB,EAAQnD,EAAKnkB,IAAMZ,GAIzB,OAAOioB,CACR,CAKD5B,gBACE,MAAMhX,EAAU9M,KAAK4lB,aAErB,KAAO5lB,KAAKikB,iBAAiB7gB,YAC3BpD,KAAKikB,iBAAiBrjB,YAAYZ,KAAKikB,iBAAiB7gB,YAE1DpD,KAAKikB,iBAAiBxjB,YACpBwf,GAAU,MAAO,mBAAqBrF,KAAKC,UAAU/N,EAAS,KAAM,IAEvE,CAMD8Y,aACE,MAAM9Y,EAAU,CAAA,EAChB,IAAK,IAAIzO,EAAI,EAAGA,EAAI2B,KAAK+gB,eAAexkB,OAAQ8B,IAC9C2B,KAAKwlB,kBACHxlB,KAAK+gB,eAAe1iB,GAAGZ,MACvBuC,KAAK+gB,eAAe1iB,GAAGmkB,KACvB1V,GAGJ,OAAOA,CACR,GEpxBU3N,GAAuB0mB,EACvBC,GCTN,MAKLhhB,YAAY/E,EAAWgmB,GACrB/lB,KAAKD,UAAYA,EACjBC,KAAK+lB,eAAiBA,GAAkB,MAExC/lB,KAAKoN,EAAI,EACTpN,KAAKuZ,EAAI,EACTvZ,KAAK2d,QAAU,EACf3d,KAAKgmB,QAAS,EAGdhmB,KAAKga,MAAQ3Z,SAASC,cAAc,OACpCN,KAAKga,MAAMtT,UAAY,cACvB1G,KAAKD,UAAUU,YAAYT,KAAKga,MACjC,CAMDiM,YAAY7Y,EAAGmM,GACbvZ,KAAKoN,EAAI1E,SAAS0E,GAClBpN,KAAKuZ,EAAI7Q,SAAS6Q,EACnB,CAOD2M,QAAQC,GACN,GAAIA,aAAmBve,QAAS,CAC9B,KAAO5H,KAAKga,MAAM5W,YAChBpD,KAAKga,MAAMpZ,YAAYZ,KAAKga,MAAM5W,YAEpCpD,KAAKga,MAAMvZ,YAAY0lB,EAC7B,MAGMnmB,KAAKga,MAAM4D,UAAYuI,CAE1B,CAODpL,KAAKqL,GAKH,QAJeriB,IAAXqiB,IACFA,GAAS,IAGI,IAAXA,EAAiB,CACnB,MAAMzW,EAAS3P,KAAKga,MAAM+B,aACpBrM,EAAQ1P,KAAKga,MAAM7J,YACnBkW,EAAYrmB,KAAKga,MAAMrZ,WAAWob,aAClCuK,EAAWtmB,KAAKga,MAAMrZ,WAAWwP,YAEvC,IAAIjK,EAAO,EACTI,EAAM,EAER,GAA2B,QAAvBtG,KAAK+lB,eAA0B,CACjC,IAAIQ,GAAS,EACXC,GAAQ,EAENxmB,KAAKuZ,EAAI5J,EAAS3P,KAAK2d,UACzB6I,GAAQ,GAGNxmB,KAAKoN,EAAIsC,EAAQ4W,EAAWtmB,KAAK2d,UACnC4I,GAAS,GAITrgB,EADEqgB,EACKvmB,KAAKoN,EAAIsC,EAET1P,KAAKoN,EAIZ9G,EADEkgB,EACIxmB,KAAKuZ,EAAI5J,EAET3P,KAAKuZ,CAErB,MACQjT,EAAMtG,KAAKuZ,EAAI5J,EACXrJ,EAAMqJ,EAAS3P,KAAK2d,QAAU0I,IAChC/f,EAAM+f,EAAY1W,EAAS3P,KAAK2d,SAE9BrX,EAAMtG,KAAK2d,UACbrX,EAAMtG,KAAK2d,SAGbzX,EAAOlG,KAAKoN,EACRlH,EAAOwJ,EAAQ1P,KAAK2d,QAAU2I,IAChCpgB,EAAOogB,EAAW5W,EAAQ1P,KAAK2d,SAE7BzX,EAAOlG,KAAK2d,UACdzX,EAAOlG,KAAK2d,SAIhB3d,KAAKga,MAAM1X,MAAM4D,KAAOA,EAAO,KAC/BlG,KAAKga,MAAM1X,MAAMgE,IAAMA,EAAM,KAC7BtG,KAAKga,MAAM1X,MAAMwN,WAAa,UAC9B9P,KAAKgmB,QAAS,CACpB,MACMhmB,KAAKymB,MAER,CAKDA,OACEzmB,KAAKgmB,QAAS,EACdhmB,KAAKga,MAAM1X,MAAM4D,KAAO,IACxBlG,KAAKga,MAAM1X,MAAMgE,IAAM,IACvBtG,KAAKga,MAAM1X,MAAMwN,WAAa,QAC/B,CAKDrQ,UACEO,KAAKga,MAAMrZ,WAAWC,YAAYZ,KAAKga,MACxC,GDzHUsG,GAAgCoG,GAChCC,GDJN,MAAMA,EAUXC,gBAAgB9Z,EAAS+Z,EAAkBC,GACzCzG,IAAa,EACbD,GAAayG,EACb,IAAIE,EAAcF,EAKlB,YAJkB9iB,IAAd+iB,IACFC,EAAcF,EAAiBC,IAEjCH,EAAUhjB,MAAMmJ,EAASia,EAAa,IAC/B1G,EACR,CAUDuG,aAAa9Z,EAAS+Z,EAAkBrE,GACtC,IAAK,MAAMza,KAAU+E,EACf7P,OAAOC,UAAUyH,eAAevH,KAAK0P,EAAS/E,IAChD4e,EAAUK,MAAMjf,EAAQ+E,EAAS+Z,EAAkBrE,EAGxD,CAWDoE,aAAa7e,EAAQ+E,EAAS+Z,EAAkBrE,GAC9C,QAC+Bze,IAA7B8iB,EAAiB9e,SACYhE,IAA7B8iB,EAAiBI,QAGjB,YADAN,EAAUO,cAAcnf,EAAQ8e,EAAkBrE,GAIpD,IAAI2E,EAAkBpf,EAClBqf,GAAY,OAGerjB,IAA7B8iB,EAAiB9e,SACYhE,IAA7B8iB,EAAiBI,UAOjBE,EAAkB,UAIlBC,EAAmD,WAAvCT,EAAUphB,QAAQuH,EAAQ/E,KAOxC,IAAIsf,EAAeR,EAAiBM,GAChCC,QAAuCrjB,IAA1BsjB,EAAaC,WAC5BD,EAAeA,EAAaC,UAG9BX,EAAUY,YACRxf,EACA+E,EACA+Z,EACAM,EACAE,EACA7E,EAEH,CAYDoE,mBACE7e,EACA+E,EACA+Z,EACAM,EACAE,EACA7E,GAEA,MAAMgF,EAAM,SAAUC,GACpBnC,QAAQC,MACN,KAAOkC,EAAUd,EAAUe,cAAclF,EAAMza,GAC/CuY,GAER,EAEUqH,EAAahB,EAAUphB,QAAQuH,EAAQ/E,IACvC6f,EAAgBP,EAAaM,QAEb5jB,IAAlB6jB,EAGqC,UAArCjB,EAAUphB,QAAQqiB,KAC0B,IAA5CA,EAAc3e,QAAQ6D,EAAQ/E,KAE9Byf,EACE,+BACEzf,EADF,yBAIE4e,EAAUkB,MAAMD,GAChB,SACA9a,EAAQ/E,GACR,OAEJsY,IAAa,GACW,WAAfsH,GAA+C,YAApBR,IACpC3E,EAAO7c,EAAmB6c,EAAMza,GAChC4e,EAAUhjB,MACRmJ,EAAQ/E,GACR8e,EAAiBM,GACjB3E,SAG6Bze,IAAxBsjB,EAAkB,MAE3BG,EACE,8BACEzf,EACA,gBACA4e,EAAUkB,MAAM5qB,OAAOS,KAAK2pB,IAC5B,eACAM,EACA,MACA7a,EAAQ/E,GACR,KAEJsY,IAAa,EAEhB,CAQDuG,eAAephB,GACb,MAAMC,SAAcD,EAEpB,MAAa,WAATC,EACa,OAAXD,EACK,OAELA,aAAkBE,QACb,UAELF,aAAkBzC,OACb,SAELyC,aAAkBlC,OACb,SAELjG,MAAMC,QAAQkI,GACT,QAELA,aAAkB7I,KACb,YAEeoH,IAApByB,EAAOqC,SACF,OAEuB,IAA5BrC,EAAOsiB,iBACF,SAEF,SACW,WAATriB,EACF,SACW,YAATA,EACF,UACW,WAATA,EACF,cACW1B,IAAT0B,EACF,YAEFA,CACR,CAQDmhB,qBAAqB7e,EAAQ+E,EAAS0V,GACpC,MAAMuF,EAAcpB,EAAUqB,cAAcjgB,EAAQ+E,EAAS0V,GAAM,GAC7DyF,EAAetB,EAAUqB,cAAcjgB,EAAQqY,GAAY,IAAI,GAKrE,IAAI8H,EAEFA,OAD6BnkB,IAA3BgkB,EAAYI,WAEZ,OACAxB,EAAUe,cAAcK,EAAYvF,KAAMza,EAAQ,IAClD,6CACAggB,EAAYI,WACZ,SAEFF,EAAaG,UAXe,GAY5BL,EAAYK,SAAWH,EAAaG,SAGlC,OACAzB,EAAUe,cAAcK,EAAYvF,KAAMza,EAAQ,IAClD,uDACA4e,EAAUe,cACRO,EAAazF,KACbyF,EAAaI,aACb,IAEKN,EAAYK,UAxBM,EA0BzB,mBACAL,EAAYM,aACZ,KACA1B,EAAUe,cAAcK,EAAYvF,KAAMza,GAG1C,gCACA4e,EAAUkB,MAAM5qB,OAAOS,KAAKoP,IAC5B6Z,EAAUe,cAAclF,EAAMza,GAGlCud,QAAQC,MACN,+BAAiCxd,EAAS,IAAMmgB,EAChD5H,IAEFD,IAAa,CACd,CAYDuG,qBAAqB7e,EAAQ+E,EAAS0V,EAAM8F,GAAY,GACtD,IAAIne,EAAM,IACNke,EAAe,GACfE,EAAmB,GACvB,MAAMC,EAAkBzgB,EAAO0gB,cAC/B,IAAIN,EACJ,IAAK,MAAMO,KAAM5b,EAAS,CACxB,IAAIsb,EACJ,QAA6BrkB,IAAzB+I,EAAQ4b,GAAIpB,WAAwC,IAAdgB,EAAoB,CAC5D,MAAM9f,EAASme,EAAUqB,cACvBjgB,EACA+E,EAAQ4b,GACR/iB,EAAmB6c,EAAMkG,IAEvBve,EAAM3B,EAAO4f,WACfC,EAAe7f,EAAO6f,aACtBE,EAAmB/f,EAAOga,KAC1BrY,EAAM3B,EAAO4f,SACbD,EAAa3f,EAAO2f,WAE9B,MAC2D,IAA/CO,EAAGD,cAAcxf,QAAQuf,KAC3BL,EAAaO,GAEfN,EAAWzB,EAAUgC,oBAAoB5gB,EAAQ2gB,GAC7Cve,EAAMie,IACRC,EAAeK,EACfH,EAAmBziB,EAAU0c,GAC7BrY,EAAMie,EAGX,CACD,MAAO,CACLC,aAAcA,EACd7F,KAAM+F,EACNH,SAAUje,EACVge,WAAYA,EAEf,CASDvB,qBAAqBpE,EAAMza,EAAQ6gB,EAAS,8BAC1C,IAAIC,EAAM,OAASD,EAAS,gBAC5B,IAAK,IAAIvqB,EAAI,EAAGA,EAAImkB,EAAKjmB,OAAQ8B,IAAK,CACpC,IAAK,IAAIsO,EAAI,EAAGA,EAAItO,EAAI,EAAGsO,IACzBkc,GAAO,KAETA,GAAOrG,EAAKnkB,GAAK,OAClB,CACD,IAAK,IAAIsO,EAAI,EAAGA,EAAI6V,EAAKjmB,OAAS,EAAGoQ,IACnCkc,GAAO,KAETA,GAAO9gB,EAAS,KAChB,IAAK,IAAI1J,EAAI,EAAGA,EAAImkB,EAAKjmB,OAAS,EAAG8B,IAAK,CACxC,IAAK,IAAIsO,EAAI,EAAGA,EAAI6V,EAAKjmB,OAAS8B,EAAGsO,IACnCkc,GAAO,KAETA,GAAO,KACR,CACD,OAAOA,EAAM,MACd,CAODjC,aAAa9Z,GACX,OAAO8N,KAAKC,UAAU/N,GACnB5D,QAAQ,+BAAgC,IACxCA,QAAQ,OAAQ,KACpB,CAmBD0d,2BAA2BnqB,EAAGC,GAC5B,GAAiB,IAAbD,EAAEF,OAAc,OAAOG,EAAEH,OAC7B,GAAiB,IAAbG,EAAEH,OAAc,OAAOE,EAAEF,OAE7B,MAAMusB,EAAS,GAGf,IAAIzqB,EAMAsO,EALJ,IAAKtO,EAAI,EAAGA,GAAK3B,EAAEH,OAAQ8B,IACzByqB,EAAOzqB,GAAK,CAACA,GAKf,IAAKsO,EAAI,EAAGA,GAAKlQ,EAAEF,OAAQoQ,IACzBmc,EAAO,GAAGnc,GAAKA,EAIjB,IAAKtO,EAAI,EAAGA,GAAK3B,EAAEH,OAAQ8B,IACzB,IAAKsO,EAAI,EAAGA,GAAKlQ,EAAEF,OAAQoQ,IACrBjQ,EAAEqsB,OAAO1qB,EAAI,IAAM5B,EAAEssB,OAAOpc,EAAI,GAClCmc,EAAOzqB,GAAGsO,GAAKmc,EAAOzqB,EAAI,GAAGsO,EAAI,GAEjCmc,EAAOzqB,GAAGsO,GAAKzC,KAAKC,IAClB2e,EAAOzqB,EAAI,GAAGsO,EAAI,GAAK,EACvBzC,KAAKC,IACH2e,EAAOzqB,GAAGsO,EAAI,GAAK,EACnBmc,EAAOzqB,EAAI,GAAGsO,GAAK,IAO7B,OAAOmc,EAAOpsB,EAAEH,QAAQE,EAAEF,OAC3B"}