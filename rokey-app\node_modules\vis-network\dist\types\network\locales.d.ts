export interface Locale {
    addDescription: string;
    addEdge: string;
    addNode: string;
    back: string;
    close: string;
    createEdgeError: string;
    del: string;
    deleteClusterError: string;
    edgeDescription: string;
    edit: string;
    editClusterError: string;
    editEdge: string;
    editEdgeDescription: string;
    editNode: string;
}
export type Locales = Record<string, Locale>;
export declare const en: Locale;
export declare const de: Locale;
export declare const es: Locale;
export declare const it: Locale;
export declare const nl: Locale;
export declare const pt: Locale;
export declare const ru: Locale;
export declare const cn: Locale;
export declare const uk: Locale;
export declare const fr: Locale;
export declare const cs: Locale;
//# sourceMappingURL=locales.d.ts.map