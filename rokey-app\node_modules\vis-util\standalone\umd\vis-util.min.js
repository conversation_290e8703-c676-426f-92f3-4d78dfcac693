/**
 * vis-util
 * https://github.com/visjs/vis-util
 *
 * utilitie collection for visjs
 *
 * @version 5.0.7
 * @date    2023-11-20T09:06:51.067Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{})}(this,(function(t){var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var r=function(t){try{return!!t()}catch(t){return!0}},i=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),o=i,a=Function.prototype,s=a.call,c=o&&a.bind.bind(s,s),u=o?c:function(t){return function(){return s.apply(t,arguments)}},l=Math.ceil,h=Math.floor,f=Math.trunc||function(t){var e=+t;return(e>0?h:l)(e)},p=function(t){var e=+t;return e!=e||0===e?0:f(e)},v=function(t){return t&&t.Math===Math&&t},d=v("object"==typeof globalThis&&globalThis)||v("object"==typeof window&&window)||v("object"==typeof self&&self)||v("object"==typeof e&&e)||function(){return this}()||e||Function("return this")(),g={exports:{}},y=d,m=Object.defineProperty,b=function(t,e){try{m(y,t,{value:e,configurable:!0,writable:!0})}catch(n){y[t]=e}return e},w="__core-js_shared__",k=d[w]||b(w,{}),C=k;(g.exports=function(t,e){return C[t]||(C[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.33.0",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.0/LICENSE",source:"https://github.com/zloirock/core-js"});var E,O,T=g.exports,F=function(t){return null==t},S=F,_=TypeError,A=function(t){if(S(t))throw new _("Can't call method on "+t);return t},x=A,P=Object,D=function(t){return P(x(t))},j=D,I=u({}.hasOwnProperty),R=Object.hasOwn||function(t,e){return I(j(t),e)},N=u,B=0,M=Math.random(),L=N(1..toString),H=function(t){return"Symbol("+(void 0===t?"":t)+")_"+L(++B+M,36)},z="undefined"!=typeof navigator&&String(navigator.userAgent)||"",W=d,V=z,q=W.process,Y=W.Deno,X=q&&q.versions||Y&&Y.version,G=X&&X.v8;G&&(O=(E=G.split("."))[0]>0&&E[0]<4?1:+(E[0]+E[1])),!O&&V&&(!(E=V.match(/Edge\/(\d+)/))||E[1]>=74)&&(E=V.match(/Chrome\/(\d+)/))&&(O=+E[1]);var U=O,Q=U,$=r,J=d.String,K=!!Object.getOwnPropertySymbols&&!$((function(){var t=Symbol("symbol detection");return!J(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),Z=K&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,tt=T,et=R,nt=H,rt=K,it=Z,ot=d.Symbol,at=tt("wks"),st=it?ot.for||ot:ot&&ot.withoutSetter||nt,ct=function(t){return et(at,t)||(at[t]=rt&&et(ot,t)?ot[t]:st("Symbol."+t)),at[t]},ut={};ut[ct("toStringTag")]="z";var lt="[object z]"===String(ut),ht="object"==typeof document&&document.all,ft={all:ht,IS_HTMLDDA:void 0===ht&&void 0!==ht},pt=ft.all,vt=ft.IS_HTMLDDA?function(t){return"function"==typeof t||t===pt}:function(t){return"function"==typeof t},dt=u,gt=dt({}.toString),yt=dt("".slice),mt=function(t){return yt(gt(t),8,-1)},bt=lt,wt=vt,kt=mt,Ct=ct("toStringTag"),Et=Object,Ot="Arguments"===kt(function(){return arguments}()),Tt=bt?kt:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Et(t),Ct))?n:Ot?kt(e):"Object"===(r=kt(e))&&wt(e.callee)?"Arguments":r},Ft=Tt,St=String,_t=function(t){if("Symbol"===Ft(t))throw new TypeError("Cannot convert a Symbol value to a string");return St(t)},At=u,xt=p,Pt=_t,Dt=A,jt=At("".charAt),It=At("".charCodeAt),Rt=At("".slice),Nt=function(t){return function(e,n){var r,i,o=Pt(Dt(e)),a=xt(n),s=o.length;return a<0||a>=s?t?"":void 0:(r=It(o,a))<55296||r>56319||a+1===s||(i=It(o,a+1))<56320||i>57343?t?jt(o,a):r:t?Rt(o,a,a+2):i-56320+(r-55296<<10)+65536}},Bt={codeAt:Nt(!1),charAt:Nt(!0)},Mt=vt,Lt=d.WeakMap,Ht=Mt(Lt)&&/native code/.test(String(Lt)),zt=vt,Wt=ft.all,Vt=ft.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:zt(t)||t===Wt}:function(t){return"object"==typeof t?null!==t:zt(t)},qt=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),Yt={},Xt=Vt,Gt=d.document,Ut=Xt(Gt)&&Xt(Gt.createElement),Qt=function(t){return Ut?Gt.createElement(t):{}},$t=Qt,Jt=!qt&&!r((function(){return 7!==Object.defineProperty($t("div"),"a",{get:function(){return 7}}).a})),Kt=qt&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Zt=Vt,te=String,ee=TypeError,ne=function(t){if(Zt(t))return t;throw new ee(te(t)+" is not an object")},re=i,ie=Function.prototype.call,oe=re?ie.bind(ie):function(){return ie.apply(ie,arguments)},ae={},se=ae,ce=d,ue=vt,le=function(t){return ue(t)?t:void 0},he=function(t,e){return arguments.length<2?le(se[t])||le(ce[t]):se[t]&&se[t][e]||ce[t]&&ce[t][e]},fe=u({}.isPrototypeOf),pe=he,ve=vt,de=fe,ge=Object,ye=Z?function(t){return"symbol"==typeof t}:function(t){var e=pe("Symbol");return ve(e)&&de(e.prototype,ge(t))},me=String,be=function(t){try{return me(t)}catch(t){return"Object"}},we=vt,ke=be,Ce=TypeError,Ee=function(t){if(we(t))return t;throw new Ce(ke(t)+" is not a function")},Oe=Ee,Te=F,Fe=function(t,e){var n=t[e];return Te(n)?void 0:Oe(n)},Se=oe,_e=vt,Ae=Vt,xe=TypeError,Pe=oe,De=Vt,je=ye,Ie=Fe,Re=function(t,e){var n,r;if("string"===e&&_e(n=t.toString)&&!Ae(r=Se(n,t)))return r;if(_e(n=t.valueOf)&&!Ae(r=Se(n,t)))return r;if("string"!==e&&_e(n=t.toString)&&!Ae(r=Se(n,t)))return r;throw new xe("Can't convert object to primitive value")},Ne=TypeError,Be=ct("toPrimitive"),Me=function(t,e){if(!De(t)||je(t))return t;var n,r=Ie(t,Be);if(r){if(void 0===e&&(e="default"),n=Pe(r,t,e),!De(n)||je(n))return n;throw new Ne("Can't convert object to primitive value")}return void 0===e&&(e="number"),Re(t,e)},Le=ye,He=function(t){var e=Me(t,"string");return Le(e)?e:e+""},ze=qt,We=Jt,Ve=Kt,qe=ne,Ye=He,Xe=TypeError,Ge=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,Qe="enumerable",$e="configurable",Je="writable";Yt.f=ze?Ve?function(t,e,n){if(qe(t),e=Ye(e),qe(n),"function"==typeof t&&"prototype"===e&&"value"in n&&Je in n&&!n[Je]){var r=Ue(t,e);r&&r[Je]&&(t[e]=n.value,n={configurable:$e in n?n[$e]:r[$e],enumerable:Qe in n?n[Qe]:r[Qe],writable:!1})}return Ge(t,e,n)}:Ge:function(t,e,n){if(qe(t),e=Ye(e),qe(n),We)try{return Ge(t,e,n)}catch(t){}if("get"in n||"set"in n)throw new Xe("Accessors not supported");return"value"in n&&(t[e]=n.value),t};var Ke,Ze,tn,en=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},nn=Yt,rn=en,on=qt?function(t,e,n){return nn.f(t,e,rn(1,n))}:function(t,e,n){return t[e]=n,t},an=H,sn=T("keys"),cn=function(t){return sn[t]||(sn[t]=an(t))},un={},ln=Ht,hn=d,fn=Vt,pn=on,vn=R,dn=k,gn=cn,yn=un,mn="Object already initialized",bn=hn.TypeError,wn=hn.WeakMap;if(ln||dn.state){var kn=dn.state||(dn.state=new wn);kn.get=kn.get,kn.has=kn.has,kn.set=kn.set,Ke=function(t,e){if(kn.has(t))throw new bn(mn);return e.facade=t,kn.set(t,e),e},Ze=function(t){return kn.get(t)||{}},tn=function(t){return kn.has(t)}}else{var Cn=gn("state");yn[Cn]=!0,Ke=function(t,e){if(vn(t,Cn))throw new bn(mn);return e.facade=t,pn(t,Cn,e),e},Ze=function(t){return vn(t,Cn)?t[Cn]:{}},tn=function(t){return vn(t,Cn)}}var En={set:Ke,get:Ze,has:tn,enforce:function(t){return tn(t)?Ze(t):Ke(t,{})},getterFor:function(t){return function(e){var n;if(!fn(e)||(n=Ze(e)).type!==t)throw new bn("Incompatible receiver, "+t+" required");return n}}},On=i,Tn=Function.prototype,Fn=Tn.apply,Sn=Tn.call,_n="object"==typeof Reflect&&Reflect.apply||(On?Sn.bind(Fn):function(){return Sn.apply(Fn,arguments)}),An=mt,xn=u,Pn=function(t){if("Function"===An(t))return xn(t)},Dn={},jn={},In={}.propertyIsEnumerable,Rn=Object.getOwnPropertyDescriptor,Nn=Rn&&!In.call({1:2},1);jn.f=Nn?function(t){var e=Rn(this,t);return!!e&&e.enumerable}:In;var Bn=r,Mn=mt,Ln=Object,Hn=u("".split),zn=Bn((function(){return!Ln("z").propertyIsEnumerable(0)}))?function(t){return"String"===Mn(t)?Hn(t,""):Ln(t)}:Ln,Wn=zn,Vn=A,qn=function(t){return Wn(Vn(t))},Yn=qt,Xn=oe,Gn=jn,Un=en,Qn=qn,$n=He,Jn=R,Kn=Jt,Zn=Object.getOwnPropertyDescriptor;Dn.f=Yn?Zn:function(t,e){if(t=Qn(t),e=$n(e),Kn)try{return Zn(t,e)}catch(t){}if(Jn(t,e))return Un(!Xn(Gn.f,t,e),t[e])};var tr=r,er=vt,nr=/#|\.prototype\./,rr=function(t,e){var n=or[ir(t)];return n===sr||n!==ar&&(er(e)?tr(e):!!e)},ir=rr.normalize=function(t){return String(t).replace(nr,".").toLowerCase()},or=rr.data={},ar=rr.NATIVE="N",sr=rr.POLYFILL="P",cr=rr,ur=Ee,lr=i,hr=Pn(Pn.bind),fr=function(t,e){return ur(t),void 0===e?t:lr?hr(t,e):function(){return t.apply(e,arguments)}},pr=d,vr=_n,dr=Pn,gr=vt,yr=Dn.f,mr=cr,br=ae,wr=fr,kr=on,Cr=R,Er=function(t){var e=function(n,r,i){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(n);case 2:return new t(n,r)}return new t(n,r,i)}return vr(t,this,arguments)};return e.prototype=t.prototype,e},Or=function(t,e){var n,r,i,o,a,s,c,u,l,h=t.target,f=t.global,p=t.stat,v=t.proto,d=f?pr:p?pr[h]:(pr[h]||{}).prototype,g=f?br:br[h]||kr(br,h,{})[h],y=g.prototype;for(o in e)r=!(n=mr(f?o:h+(p?".":"#")+o,t.forced))&&d&&Cr(d,o),s=g[o],r&&(c=t.dontCallGetSet?(l=yr(d,o))&&l.value:d[o]),a=r&&c?c:e[o],r&&typeof s==typeof a||(u=t.bind&&r?wr(a,pr):t.wrap&&r?Er(a):v&&gr(a)?dr(a):a,(t.sham||a&&a.sham||s&&s.sham)&&kr(u,"sham",!0),kr(g,o,u),v&&(Cr(br,i=h+"Prototype")||kr(br,i,{}),kr(br[i],o,a),t.real&&y&&(n||!y[o])&&kr(y,o,a)))},Tr=qt,Fr=R,Sr=Function.prototype,_r=Tr&&Object.getOwnPropertyDescriptor,Ar=Fr(Sr,"name"),xr={EXISTS:Ar,PROPER:Ar&&"something"===function(){}.name,CONFIGURABLE:Ar&&(!Tr||Tr&&_r(Sr,"name").configurable)},Pr={},Dr=p,jr=Math.max,Ir=Math.min,Rr=function(t,e){var n=Dr(t);return n<0?jr(n+e,0):Ir(n,e)},Nr=p,Br=Math.min,Mr=function(t){return t>0?Br(Nr(t),9007199254740991):0},Lr=function(t){return Mr(t.length)},Hr=qn,zr=Rr,Wr=Lr,Vr=function(t){return function(e,n,r){var i,o=Hr(e),a=Wr(o),s=zr(r,a);if(t&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((t||s in o)&&o[s]===n)return t||s||0;return!t&&-1}},qr={includes:Vr(!0),indexOf:Vr(!1)},Yr=R,Xr=qn,Gr=qr.indexOf,Ur=un,Qr=u([].push),$r=function(t,e){var n,r=Xr(t),i=0,o=[];for(n in r)!Yr(Ur,n)&&Yr(r,n)&&Qr(o,n);for(;e.length>i;)Yr(r,n=e[i++])&&(~Gr(o,n)||Qr(o,n));return o},Jr=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Kr=$r,Zr=Jr,ti=Object.keys||function(t){return Kr(t,Zr)},ei=qt,ni=Kt,ri=Yt,ii=ne,oi=qn,ai=ti;Pr.f=ei&&!ni?Object.defineProperties:function(t,e){ii(t);for(var n,r=oi(e),i=ai(e),o=i.length,a=0;o>a;)ri.f(t,n=i[a++],r[n]);return t};var si,ci=he("document","documentElement"),ui=ne,li=Pr,hi=Jr,fi=un,pi=ci,vi=Qt,di="prototype",gi="script",yi=cn("IE_PROTO"),mi=function(){},bi=function(t){return"<"+gi+">"+t+"</"+gi+">"},wi=function(t){t.write(bi("")),t.close();var e=t.parentWindow.Object;return t=null,e},ki=function(){try{si=new ActiveXObject("htmlfile")}catch(t){}var t,e,n;ki="undefined"!=typeof document?document.domain&&si?wi(si):(e=vi("iframe"),n="java"+gi+":",e.style.display="none",pi.appendChild(e),e.src=String(n),(t=e.contentWindow.document).open(),t.write(bi("document.F=Object")),t.close(),t.F):wi(si);for(var r=hi.length;r--;)delete ki[di][hi[r]];return ki()};fi[yi]=!0;var Ci,Ei,Oi,Ti=Object.create||function(t,e){var n;return null!==t?(mi[di]=ui(t),n=new mi,mi[di]=null,n[yi]=t):n=ki(),void 0===e?n:li.f(n,e)},Fi=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Si=R,_i=vt,Ai=D,xi=Fi,Pi=cn("IE_PROTO"),Di=Object,ji=Di.prototype,Ii=xi?Di.getPrototypeOf:function(t){var e=Ai(t);if(Si(e,Pi))return e[Pi];var n=e.constructor;return _i(n)&&e instanceof n?n.prototype:e instanceof Di?ji:null},Ri=on,Ni=function(t,e,n,r){return r&&r.enumerable?t[e]=n:Ri(t,e,n),t},Bi=r,Mi=vt,Li=Vt,Hi=Ti,zi=Ii,Wi=Ni,Vi=ct("iterator"),qi=!1;[].keys&&("next"in(Oi=[].keys())?(Ei=zi(zi(Oi)))!==Object.prototype&&(Ci=Ei):qi=!0);var Yi=!Li(Ci)||Bi((function(){var t={};return Ci[Vi].call(t)!==t}));Mi((Ci=Yi?{}:Hi(Ci))[Vi])||Wi(Ci,Vi,(function(){return this}));var Xi={IteratorPrototype:Ci,BUGGY_SAFARI_ITERATORS:qi},Gi=Tt,Ui=lt?{}.toString:function(){return"[object "+Gi(this)+"]"},Qi=lt,$i=Yt.f,Ji=on,Ki=R,Zi=Ui,to=ct("toStringTag"),eo=function(t,e,n,r){if(t){var i=n?t:t.prototype;Ki(i,to)||$i(i,to,{configurable:!0,value:e}),r&&!Qi&&Ji(i,"toString",Zi)}},no={},ro=Xi.IteratorPrototype,io=Ti,oo=en,ao=eo,so=no,co=function(){return this},uo=Or,lo=oe,ho=xr,fo=function(t,e,n,r){var i=e+" Iterator";return t.prototype=io(ro,{next:oo(+!r,n)}),ao(t,i,!1,!0),so[i]=co,t},po=Ii,vo=eo,go=Ni,yo=no,mo=Xi,bo=ho.PROPER,wo=mo.BUGGY_SAFARI_ITERATORS,ko=ct("iterator"),Co="keys",Eo="values",Oo="entries",To=function(){return this},Fo=function(t,e,n,r,i,o,a){fo(n,e,r);var s,c,u,l=function(t){if(t===i&&d)return d;if(!wo&&t&&t in p)return p[t];switch(t){case Co:case Eo:case Oo:return function(){return new n(this,t)}}return function(){return new n(this)}},h=e+" Iterator",f=!1,p=t.prototype,v=p[ko]||p["@@iterator"]||i&&p[i],d=!wo&&v||l(i),g="Array"===e&&p.entries||v;if(g&&(s=po(g.call(new t)))!==Object.prototype&&s.next&&(vo(s,h,!0,!0),yo[h]=To),bo&&i===Eo&&v&&v.name!==Eo&&(f=!0,d=function(){return lo(v,this)}),i)if(c={values:l(Eo),keys:o?d:l(Co),entries:l(Oo)},a)for(u in c)(wo||f||!(u in p))&&go(p,u,c[u]);else uo({target:e,proto:!0,forced:wo||f},c);return a&&p[ko]!==d&&go(p,ko,d,{name:i}),yo[e]=d,c},So=function(t,e){return{value:t,done:e}},_o=Bt.charAt,Ao=_t,xo=En,Po=Fo,Do=So,jo="String Iterator",Io=xo.set,Ro=xo.getterFor(jo);Po(String,"String",(function(t){Io(this,{type:jo,string:Ao(t),index:0})}),(function(){var t,e=Ro(this),n=e.string,r=e.index;return r>=n.length?Do(void 0,!0):(t=_o(n,r),e.index+=t.length,Do(t,!1))}));var No=oe,Bo=ne,Mo=Fe,Lo=ne,Ho=function(t,e,n){var r,i;Bo(t);try{if(!(r=Mo(t,"return"))){if("throw"===e)throw n;return n}r=No(r,t)}catch(t){i=!0,r=t}if("throw"===e)throw n;if(i)throw r;return Bo(r),n},zo=no,Wo=ct("iterator"),Vo=Array.prototype,qo=vt,Yo=k,Xo=u(Function.toString);qo(Yo.inspectSource)||(Yo.inspectSource=function(t){return Xo(t)});var Go=Yo.inspectSource,Uo=u,Qo=r,$o=vt,Jo=Tt,Ko=Go,Zo=function(){},ta=[],ea=he("Reflect","construct"),na=/^\s*(?:class|function)\b/,ra=Uo(na.exec),ia=!na.test(Zo),oa=function(t){if(!$o(t))return!1;try{return ea(Zo,ta,t),!0}catch(t){return!1}},aa=function(t){if(!$o(t))return!1;switch(Jo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return ia||!!ra(na,Ko(t))}catch(t){return!0}};aa.sham=!0;var sa=!ea||Qo((function(){var t;return oa(oa.call)||!oa(Object)||!oa((function(){t=!0}))||t}))?aa:oa,ca=He,ua=Yt,la=en,ha=function(t,e,n){var r=ca(e);r in t?ua.f(t,r,la(0,n)):t[r]=n},fa=Tt,pa=Fe,va=F,da=no,ga=ct("iterator"),ya=function(t){if(!va(t))return pa(t,ga)||pa(t,"@@iterator")||da[fa(t)]},ma=oe,ba=Ee,wa=ne,ka=be,Ca=ya,Ea=TypeError,Oa=fr,Ta=oe,Fa=D,Sa=function(t,e,n,r){try{return r?e(Lo(n)[0],n[1]):e(n)}catch(e){Ho(t,"throw",e)}},_a=function(t){return void 0!==t&&(zo.Array===t||Vo[Wo]===t)},Aa=sa,xa=Lr,Pa=ha,Da=function(t,e){var n=arguments.length<2?Ca(t):e;if(ba(n))return wa(ma(n,t));throw new Ea(ka(t)+" is not iterable")},ja=ya,Ia=Array,Ra=ct("iterator"),Na=!1;try{var Ba=0,Ma={next:function(){return{done:!!Ba++}},return:function(){Na=!0}};Ma[Ra]=function(){return this},Array.from(Ma,(function(){throw 2}))}catch(t){}var La=function(t){var e=Fa(t),n=Aa(this),r=arguments.length,i=r>1?arguments[1]:void 0,o=void 0!==i;o&&(i=Oa(i,r>2?arguments[2]:void 0));var a,s,c,u,l,h,f=ja(e),p=0;if(!f||this===Ia&&_a(f))for(a=xa(e),s=n?new this(a):Ia(a);a>p;p++)h=o?i(e[p],p):e[p],Pa(s,p,h);else for(l=(u=Da(e,f)).next,s=n?new this:[];!(c=Ta(l,u)).done;p++)h=o?Sa(u,i,[c.value,p],!0):c.value,Pa(s,p,h);return s.length=p,s},Ha=function(t,e){try{if(!e&&!Na)return!1}catch(t){return!1}var n=!1;try{var r={};r[Ra]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n};Or({target:"Array",stat:!0,forced:!Ha((function(t){Array.from(t)}))},{from:La});var za=ae.Array.from,Wa=n(za),Va=qn,qa=no,Ya=En;Yt.f;var Xa=Fo,Ga=So,Ua="Array Iterator",Qa=Ya.set,$a=Ya.getterFor(Ua);Xa(Array,"Array",(function(t,e){Qa(this,{type:Ua,target:Va(t),index:0,kind:e})}),(function(){var t=$a(this),e=t.target,n=t.kind,r=t.index++;if(!e||r>=e.length)return t.target=void 0,Ga(void 0,!0);switch(n){case"keys":return Ga(r,!1);case"values":return Ga(e[r],!1)}return Ga([r,e[r]],!1)}),"values"),qa.Arguments=qa.Array;var Ja=ya,Ka={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Za=d,ts=Tt,es=on,ns=no,rs=ct("toStringTag");for(var is in Ka){var os=Za[is],as=os&&os.prototype;as&&ts(as)!==rs&&es(as,rs,is),ns[is]=ns.Array}var ss=Ja,cs=n(ss),us=n(ss),ls=mt,hs=Array.isArray||function(t){return"Array"===ls(t)},fs=TypeError,ps=function(t){if(t>9007199254740991)throw fs("Maximum allowed index exceeded");return t},vs=hs,ds=sa,gs=Vt,ys=ct("species"),ms=Array,bs=function(t){var e;return vs(t)&&(e=t.constructor,(ds(e)&&(e===ms||vs(e.prototype))||gs(e)&&null===(e=e[ys]))&&(e=void 0)),void 0===e?ms:e},ws=function(t,e){return new(bs(t))(0===e?0:e)},ks=r,Cs=U,Es=ct("species"),Os=function(t){return Cs>=51||!ks((function(){var e=[];return(e.constructor={})[Es]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ts=Or,Fs=r,Ss=hs,_s=Vt,As=D,xs=Lr,Ps=ps,Ds=ha,js=ws,Is=Os,Rs=U,Ns=ct("isConcatSpreadable"),Bs=Rs>=51||!Fs((function(){var t=[];return t[Ns]=!1,t.concat()[0]!==t})),Ms=function(t){if(!_s(t))return!1;var e=t[Ns];return void 0!==e?!!e:Ss(t)};Ts({target:"Array",proto:!0,arity:1,forced:!Bs||!Is("concat")},{concat:function(t){var e,n,r,i,o,a=As(this),s=js(a,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(Ms(o=-1===e?a:arguments[e]))for(i=xs(o),Ps(c+i),n=0;n<i;n++,c++)n in o&&Ds(s,c,o[n]);else Ps(c+1),Ds(s,c++,o);return s.length=c,s}});var Ls={},Hs=$r,zs=Jr.concat("length","prototype");Ls.f=Object.getOwnPropertyNames||function(t){return Hs(t,zs)};var Ws={},Vs=Rr,qs=Lr,Ys=ha,Xs=Array,Gs=Math.max,Us=mt,Qs=qn,$s=Ls.f,Js=function(t,e,n){for(var r=qs(t),i=Vs(e,r),o=Vs(void 0===n?r:n,r),a=Xs(Gs(o-i,0)),s=0;i<o;i++,s++)Ys(a,s,t[i]);return a.length=s,a},Ks="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Ws.f=function(t){return Ks&&"Window"===Us(t)?function(t){try{return $s(t)}catch(t){return Js(Ks)}}(t):$s(Qs(t))};var Zs={};Zs.f=Object.getOwnPropertySymbols;var tc=Yt,ec={},nc=ct;ec.f=nc;var rc=ae,ic=R,oc=ec,ac=Yt.f,sc=function(t){var e=rc.Symbol||(rc.Symbol={});ic(e,t)||ac(e,t,{value:oc.f(t)})},cc=oe,uc=he,lc=ct,hc=Ni,fc=function(){var t=uc("Symbol"),e=t&&t.prototype,n=e&&e.valueOf,r=lc("toPrimitive");e&&!e[r]&&hc(e,r,(function(t){return cc(n,this)}),{arity:1})},pc=fr,vc=zn,dc=D,gc=Lr,yc=ws,mc=u([].push),bc=function(t){var e=1===t,n=2===t,r=3===t,i=4===t,o=6===t,a=7===t,s=5===t||o;return function(c,u,l,h){for(var f,p,v=dc(c),d=vc(v),g=pc(u,l),y=gc(d),m=0,b=h||yc,w=e?b(c,y):n||a?b(c,0):void 0;y>m;m++)if((s||m in d)&&(p=g(f=d[m],m,v),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return f;case 6:return m;case 2:mc(w,f)}else switch(t){case 4:return!1;case 7:mc(w,f)}return o?-1:r||i?i:w}},wc={forEach:bc(0),map:bc(1),filter:bc(2),some:bc(3),every:bc(4),find:bc(5),findIndex:bc(6),filterReject:bc(7)},kc=Or,Cc=d,Ec=oe,Oc=u,Tc=qt,Fc=K,Sc=r,_c=R,Ac=fe,xc=ne,Pc=qn,Dc=He,jc=_t,Ic=en,Rc=Ti,Nc=ti,Bc=Ls,Mc=Ws,Lc=Zs,Hc=Dn,zc=Yt,Wc=Pr,Vc=jn,qc=Ni,Yc=function(t,e,n){return tc.f(t,e,n)},Xc=T,Gc=un,Uc=H,Qc=ct,$c=ec,Jc=sc,Kc=fc,Zc=eo,tu=En,eu=wc.forEach,nu=cn("hidden"),ru="Symbol",iu="prototype",ou=tu.set,au=tu.getterFor(ru),su=Object[iu],cu=Cc.Symbol,uu=cu&&cu[iu],lu=Cc.RangeError,hu=Cc.TypeError,fu=Cc.QObject,pu=Hc.f,vu=zc.f,du=Mc.f,gu=Vc.f,yu=Oc([].push),mu=Xc("symbols"),bu=Xc("op-symbols"),wu=Xc("wks"),ku=!fu||!fu[iu]||!fu[iu].findChild,Cu=function(t,e,n){var r=pu(su,e);r&&delete su[e],vu(t,e,n),r&&t!==su&&vu(su,e,r)},Eu=Tc&&Sc((function(){return 7!==Rc(vu({},"a",{get:function(){return vu(this,"a",{value:7}).a}})).a}))?Cu:vu,Ou=function(t,e){var n=mu[t]=Rc(uu);return ou(n,{type:ru,tag:t,description:e}),Tc||(n.description=e),n},Tu=function(t,e,n){t===su&&Tu(bu,e,n),xc(t);var r=Dc(e);return xc(n),_c(mu,r)?(n.enumerable?(_c(t,nu)&&t[nu][r]&&(t[nu][r]=!1),n=Rc(n,{enumerable:Ic(0,!1)})):(_c(t,nu)||vu(t,nu,Ic(1,{})),t[nu][r]=!0),Eu(t,r,n)):vu(t,r,n)},Fu=function(t,e){xc(t);var n=Pc(e),r=Nc(n).concat(xu(n));return eu(r,(function(e){Tc&&!Ec(Su,n,e)||Tu(t,e,n[e])})),t},Su=function(t){var e=Dc(t),n=Ec(gu,this,e);return!(this===su&&_c(mu,e)&&!_c(bu,e))&&(!(n||!_c(this,e)||!_c(mu,e)||_c(this,nu)&&this[nu][e])||n)},_u=function(t,e){var n=Pc(t),r=Dc(e);if(n!==su||!_c(mu,r)||_c(bu,r)){var i=pu(n,r);return!i||!_c(mu,r)||_c(n,nu)&&n[nu][r]||(i.enumerable=!0),i}},Au=function(t){var e=du(Pc(t)),n=[];return eu(e,(function(t){_c(mu,t)||_c(Gc,t)||yu(n,t)})),n},xu=function(t){var e=t===su,n=du(e?bu:Pc(t)),r=[];return eu(n,(function(t){!_c(mu,t)||e&&!_c(su,t)||yu(r,mu[t])})),r};Fc||(cu=function(){if(Ac(uu,this))throw new hu("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?jc(arguments[0]):void 0,e=Uc(t),n=function(t){this===su&&Ec(n,bu,t),_c(this,nu)&&_c(this[nu],e)&&(this[nu][e]=!1);var r=Ic(1,t);try{Eu(this,e,r)}catch(t){if(!(t instanceof lu))throw t;Cu(this,e,r)}};return Tc&&ku&&Eu(su,e,{configurable:!0,set:n}),Ou(e,t)},qc(uu=cu[iu],"toString",(function(){return au(this).tag})),qc(cu,"withoutSetter",(function(t){return Ou(Uc(t),t)})),Vc.f=Su,zc.f=Tu,Wc.f=Fu,Hc.f=_u,Bc.f=Mc.f=Au,Lc.f=xu,$c.f=function(t){return Ou(Qc(t),t)},Tc&&Yc(uu,"description",{configurable:!0,get:function(){return au(this).description}})),kc({global:!0,constructor:!0,wrap:!0,forced:!Fc,sham:!Fc},{Symbol:cu}),eu(Nc(wu),(function(t){Jc(t)})),kc({target:ru,stat:!0,forced:!Fc},{useSetter:function(){ku=!0},useSimple:function(){ku=!1}}),kc({target:"Object",stat:!0,forced:!Fc,sham:!Tc},{create:function(t,e){return void 0===e?Rc(t):Fu(Rc(t),e)},defineProperty:Tu,defineProperties:Fu,getOwnPropertyDescriptor:_u}),kc({target:"Object",stat:!0,forced:!Fc},{getOwnPropertyNames:Au}),Kc(),Zc(cu,ru),Gc[nu]=!0;var Pu=K&&!!Symbol.for&&!!Symbol.keyFor,Du=Or,ju=he,Iu=R,Ru=_t,Nu=T,Bu=Pu,Mu=Nu("string-to-symbol-registry"),Lu=Nu("symbol-to-string-registry");Du({target:"Symbol",stat:!0,forced:!Bu},{for:function(t){var e=Ru(t);if(Iu(Mu,e))return Mu[e];var n=ju("Symbol")(e);return Mu[e]=n,Lu[n]=e,n}});var Hu=Or,zu=R,Wu=ye,Vu=be,qu=Pu,Yu=T("symbol-to-string-registry");Hu({target:"Symbol",stat:!0,forced:!qu},{keyFor:function(t){if(!Wu(t))throw new TypeError(Vu(t)+" is not a symbol");if(zu(Yu,t))return Yu[t]}});var Xu=u([].slice),Gu=hs,Uu=vt,Qu=mt,$u=_t,Ju=u([].push),Ku=Or,Zu=he,tl=_n,el=oe,nl=u,rl=r,il=vt,ol=ye,al=Xu,sl=function(t){if(Uu(t))return t;if(Gu(t)){for(var e=t.length,n=[],r=0;r<e;r++){var i=t[r];"string"==typeof i?Ju(n,i):"number"!=typeof i&&"Number"!==Qu(i)&&"String"!==Qu(i)||Ju(n,$u(i))}var o=n.length,a=!0;return function(t,e){if(a)return a=!1,e;if(Gu(this))return e;for(var r=0;r<o;r++)if(n[r]===t)return e}}},cl=K,ul=String,ll=Zu("JSON","stringify"),hl=nl(/./.exec),fl=nl("".charAt),pl=nl("".charCodeAt),vl=nl("".replace),dl=nl(1..toString),gl=/[\uD800-\uDFFF]/g,yl=/^[\uD800-\uDBFF]$/,ml=/^[\uDC00-\uDFFF]$/,bl=!cl||rl((function(){var t=Zu("Symbol")("stringify detection");return"[null]"!==ll([t])||"{}"!==ll({a:t})||"{}"!==ll(Object(t))})),wl=rl((function(){return'"\\udf06\\ud834"'!==ll("\udf06\ud834")||'"\\udead"'!==ll("\udead")})),kl=function(t,e){var n=al(arguments),r=sl(e);if(il(r)||void 0!==t&&!ol(t))return n[1]=function(t,e){if(il(r)&&(e=el(r,this,ul(t),e)),!ol(e))return e},tl(ll,null,n)},Cl=function(t,e,n){var r=fl(n,e-1),i=fl(n,e+1);return hl(yl,t)&&!hl(ml,i)||hl(ml,t)&&!hl(yl,r)?"\\u"+dl(pl(t,0),16):t};ll&&Ku({target:"JSON",stat:!0,arity:3,forced:bl||wl},{stringify:function(t,e,n){var r=al(arguments),i=tl(bl?kl:ll,null,r);return wl&&"string"==typeof i?vl(i,gl,Cl):i}});var El=Zs,Ol=D;Or({target:"Object",stat:!0,forced:!K||r((function(){El.f(1)}))},{getOwnPropertySymbols:function(t){var e=El.f;return e?e(Ol(t)):[]}}),sc("asyncIterator"),sc("hasInstance"),sc("isConcatSpreadable"),sc("iterator"),sc("match"),sc("matchAll"),sc("replace"),sc("search"),sc("species"),sc("split");var Tl=fc;sc("toPrimitive"),Tl();var Fl=he,Sl=eo;sc("toStringTag"),Sl(Fl("Symbol"),"Symbol"),sc("unscopables"),eo(d.JSON,"JSON",!0);var _l=ae.Symbol,Al=ct,xl=Yt.f,Pl=Al("metadata"),Dl=Function.prototype;void 0===Dl[Pl]&&xl(Dl,Pl,{value:null}),sc("asyncDispose"),sc("dispose"),sc("metadata");var jl=_l,Il=u,Rl=he("Symbol"),Nl=Rl.keyFor,Bl=Il(Rl.prototype.valueOf),Ml=Rl.isRegisteredSymbol||function(t){try{return void 0!==Nl(Bl(t))}catch(t){return!1}};Or({target:"Symbol",stat:!0},{isRegisteredSymbol:Ml});for(var Ll=T,Hl=he,zl=u,Wl=ye,Vl=ct,ql=Hl("Symbol"),Yl=ql.isWellKnownSymbol,Xl=Hl("Object","getOwnPropertyNames"),Gl=zl(ql.prototype.valueOf),Ul=Ll("wks"),Ql=0,$l=Xl(ql),Jl=$l.length;Ql<Jl;Ql++)try{var Kl=$l[Ql];Wl(ql[Kl])&&Vl(Kl)}catch(t){}var Zl=function(t){if(Yl&&Yl(t))return!0;try{for(var e=Gl(t),n=0,r=Xl(Ul),i=r.length;n<i;n++)if(Ul[r[n]]==e)return!0}catch(t){}return!1};Or({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:Zl}),sc("matcher"),sc("observable"),Or({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Ml}),Or({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:Zl}),sc("metadataKey"),sc("patternMatch"),sc("replaceAll");var th=n(jl),eh=n(ec.f("iterator"));function nh(t){return nh="function"==typeof th&&"symbol"==typeof eh?function(t){return typeof t}:function(t){return t&&"function"==typeof th&&t.constructor===th&&t!==th.prototype?"symbol":typeof t},nh(t)}Or({target:"Array",stat:!0},{isArray:hs});var rh=ae.Array.isArray,ih=n(rh);function oh(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var ah=n(za);var sh=Or,ch=hs,uh=sa,lh=Vt,hh=Rr,fh=Lr,ph=qn,vh=ha,dh=ct,gh=Xu,yh=Os("slice"),mh=dh("species"),bh=Array,wh=Math.max;sh({target:"Array",proto:!0,forced:!yh},{slice:function(t,e){var n,r,i,o=ph(this),a=fh(o),s=hh(t,a),c=hh(void 0===e?a:e,a);if(ch(o)&&(n=o.constructor,(uh(n)&&(n===bh||ch(n.prototype))||lh(n)&&null===(n=n[mh]))&&(n=void 0),n===bh||void 0===n))return gh(o,s,c);for(r=new(void 0===n?bh:n)(wh(c-s,0)),i=0;s<c;s++,i++)s in o&&vh(r,i,o[s]);return r.length=i,r}});var kh=ae,Ch=function(t){return kh[t+"Prototype"]},Eh=Ch("Array").slice,Oh=fe,Th=Eh,Fh=Array.prototype,Sh=function(t){var e=t.slice;return t===Fh||Oh(Fh,t)&&e===Fh.slice?Th:e},_h=n(Sh);function Ah(t,e){var n;if(t){if("string"==typeof t)return oh(t,e);var r=_h(n=Object.prototype.toString.call(t)).call(n,8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?ah(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?oh(t,e):void 0}}function xh(t){return function(t){if(ih(t))return oh(t)}(t)||function(t){if(void 0!==th&&null!=cs(t)||null!=t["@@iterator"])return ah(t)}(t)||Ah(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Ph=n(_l),Dh=Ch("Array").concat,jh=fe,Ih=Dh,Rh=Array.prototype,Nh=n((function(t){var e=t.concat;return t===Rh||jh(Rh,t)&&e===Rh.concat?Ih:e})),Bh=n(Sh),Mh=he,Lh=Ls,Hh=Zs,zh=ne,Wh=u([].concat),Vh=Mh("Reflect","ownKeys")||function(t){var e=Lh.f(zh(t)),n=Hh.f;return n?Wh(e,n(t)):e};Or({target:"Reflect",stat:!0},{ownKeys:Vh});var qh=n(ae.Reflect.ownKeys),Yh=n(rh),Xh=wc.map;Or({target:"Array",proto:!0,forced:!Os("map")},{map:function(t){return Xh(this,t,arguments.length>1?arguments[1]:void 0)}});var Gh=Ch("Array").map,Uh=fe,Qh=Gh,$h=Array.prototype,Jh=n((function(t){var e=t.map;return t===$h||Uh($h,t)&&e===$h.map?Qh:e})),Kh=D,Zh=ti;Or({target:"Object",stat:!0,forced:r((function(){Zh(1)}))},{keys:function(t){return Zh(Kh(t))}});var tf=n(ae.Object.keys);function ef(t,e){var n=void 0!==Ph&&us(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return nf(t,e);var r=Bh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Wa(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nf(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function nf(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var rf=Ph("DELETE");function of(){var t=af.apply(void 0,arguments);return cf(t),t}function af(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<2)return e[0];var r;if(e.length>2)return af.apply(void 0,Nh(r=[of(e[0],e[1])]).call(r,xh(Bh(e).call(e,2))));var i=e[0],o=e[1];if(i instanceof Date&&o instanceof Date)return i.setTime(o.getTime()),i;var a,s=ef(qh(o));try{for(s.s();!(a=s.n()).done;){var c=a.value;Object.prototype.propertyIsEnumerable.call(o,c)&&(o[c]===rf?delete i[c]:null===i[c]||null===o[c]||"object"!==nh(i[c])||"object"!==nh(o[c])||Yh(i[c])||Yh(o[c])?i[c]=sf(o[c]):i[c]=af(i[c],o[c]))}}catch(t){s.e(t)}finally{s.f()}return i}function sf(t){return Yh(t)?Jh(t).call(t,(function(t){return sf(t)})):"object"===nh(t)&&null!==t?t instanceof Date?new Date(t.getTime()):af({},t):t}function cf(t){for(var e=0,n=tf(t);e<n.length;e++){var r=n[e];t[r]===rf?delete t[r]:"object"===nh(t[r])&&null!==t[r]&&cf(t[r])}}var uf=qt,lf=hs,hf=TypeError,ff=Object.getOwnPropertyDescriptor,pf=uf&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,e){if(lf(t)&&!ff(t,"length").writable)throw new hf("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},vf=D,df=Lr,gf=pf,yf=ps;Or({target:"Array",proto:!0,arity:1,forced:r((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=vf(this),n=df(e),r=arguments.length;yf(n+r);for(var i=0;i<r;i++)e[n]=arguments[i],n++;return gf(e,n),n}});var mf=Ch("Array").push,bf=fe,wf=mf,kf=Array.prototype,Cf=n((function(t){var e=t.push;return t===kf||bf(kf,t)&&e===kf.push?wf:e}));function Ef(t,e){return function(t){if(ih(t))return t}(t)||function(t,e){var n=null==t?null:void 0!==th&&cs(t)||t["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,u=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(Cf(s).call(s,r.value),s.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return s}}(t,e)||Ah(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Of=Or,Tf=Date,Ff=u(Tf.prototype.getTime);Of({target:"Date",stat:!0},{now:function(){return Ff(new Tf)}});var Sf=n(ae.Date.now);var _f=u,Af=Ee,xf=Vt,Pf=R,Df=Xu,jf=i,If=Function,Rf=_f([].concat),Nf=_f([].join),Bf={},Mf=jf?If.bind:function(t){var e=Af(this),n=e.prototype,r=Df(arguments,1),i=function(){var n=Rf(r,Df(arguments));return this instanceof i?function(t,e,n){if(!Pf(Bf,e)){for(var r=[],i=0;i<e;i++)r[i]="a["+i+"]";Bf[e]=If("C,a","return new C("+Nf(r,",")+")")}return Bf[e](t,n)}(e,n.length,n):e.apply(t,n)};return xf(n)&&(i.prototype=n),i},Lf=Mf;Or({target:"Function",proto:!0,forced:Function.bind!==Lf},{bind:Lf});var Hf=Ch("Function").bind,zf=fe,Wf=Hf,Vf=Function.prototype,qf=n((function(t){var e=t.bind;return t===Vf||zf(Vf,t)&&e===Vf.bind?Wf:e})),Yf=r,Xf=function(t,e){var n=[][t];return!!n&&Yf((function(){n.call(null,e||function(){return 1},1)}))},Gf=wc.forEach,Uf=Xf("forEach")?[].forEach:function(t){return Gf(this,t,arguments.length>1?arguments[1]:void 0)};Or({target:"Array",proto:!0,forced:[].forEach!==Uf},{forEach:Uf});var Qf=Ch("Array").forEach,$f=Tt,Jf=R,Kf=fe,Zf=Qf,tp=Array.prototype,ep={DOMTokenList:!0,NodeList:!0},np=n((function(t){var e=t.forEach;return t===tp||Kf(tp,t)&&e===tp.forEach||Jf(ep,$f(t))?Zf:e})),rp=Or,ip=hs,op=u([].reverse),ap=[1,2];rp({target:"Array",proto:!0,forced:String(ap)===String(ap.reverse())},{reverse:function(){return ip(this)&&(this.length=this.length),op(this)}});var sp=Ch("Array").reverse,cp=fe,up=sp,lp=Array.prototype,hp=n((function(t){var e=t.reverse;return t===lp||cp(lp,t)&&e===lp.reverse?up:e})),fp=be,pp=TypeError,vp=Or,dp=D,gp=Rr,yp=p,mp=Lr,bp=pf,wp=ps,kp=ws,Cp=ha,Ep=function(t,e){if(!delete t[e])throw new pp("Cannot delete property "+fp(e)+" of "+fp(t))},Op=Os("splice"),Tp=Math.max,Fp=Math.min;vp({target:"Array",proto:!0,forced:!Op},{splice:function(t,e){var n,r,i,o,a,s,c=dp(this),u=mp(c),l=gp(t,u),h=arguments.length;for(0===h?n=r=0:1===h?(n=0,r=u-l):(n=h-2,r=Fp(Tp(yp(e),0),u-l)),wp(u+n-r),i=kp(c,r),o=0;o<r;o++)(a=l+o)in c&&Cp(i,o,c[a]);if(i.length=r,n<r){for(o=l;o<u-r;o++)s=o+n,(a=o+r)in c?c[s]=c[a]:Ep(c,s);for(o=u;o>u-r+n;o--)Ep(c,o-1)}else if(n>r)for(o=u-r;o>l;o--)s=o+n-1,(a=o+r-1)in c?c[s]=c[a]:Ep(c,s);for(o=0;o<n;o++)c[o+l]=arguments[o+2];return bp(c,u-r+n),i}});var Sp=Ch("Array").splice,_p=fe,Ap=Sp,xp=Array.prototype,Pp=n((function(t){var e=t.splice;return t===xp||_p(xp,t)&&e===xp.splice?Ap:e})),Dp={exports:{}};!function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const n=this._callbacks.get(t)??[];return n.push(e),this._callbacks.set(t,n),this},e.prototype.once=function(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return n.fn=e,this.on(t,n),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const n=this._callbacks.get(t);if(n){for(const[t,r]of n.entries())if(r===e||r.fn===e){n.splice(t,1);break}0===n.length?this._callbacks.delete(t):this._callbacks.set(t,n)}return this},e.prototype.emit=function(t,...e){const n=this._callbacks.get(t);if(n){const t=[...n];for(const n of t)n.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(Dp);var jp,Ip=n(Dp.exports);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
	 * http://naver.github.io/egjs
	 *
	 * Forked By Naver egjs
	 * Copyright (c) hammerjs
	 * Licensed under the MIT license */
function Rp(){return Rp=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Rp.apply(this,arguments)}function Np(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function Bp(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}jp="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var i in r)r.hasOwnProperty(i)&&(e[i]=r[i])}return e}:Object.assign;var Mp,Lp=jp,Hp=["","webkit","Moz","MS","ms","o"],zp="undefined"==typeof document?{style:{}}:document.createElement("div"),Wp=Math.round,Vp=Math.abs,qp=Date.now;function Yp(t,e){for(var n,r,i=e[0].toUpperCase()+e.slice(1),o=0;o<Hp.length;){if((r=(n=Hp[o])?n+i:e)in t)return r;o++}}Mp="undefined"==typeof window?{}:window;var Xp=Yp(zp.style,"touchAction"),Gp=void 0!==Xp;var Up="compute",Qp="auto",$p="manipulation",Jp="none",Kp="pan-x",Zp="pan-y",tv=function(){if(!Gp)return!1;var t={},e=Mp.CSS&&Mp.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){return t[n]=!e||Mp.CSS.supports("touch-action",n)})),t}(),ev="ontouchstart"in Mp,nv=void 0!==Yp(Mp,"PointerEvent"),rv=ev&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),iv="touch",ov="mouse",av=25,sv=1,cv=4,uv=8,lv=1,hv=2,fv=4,pv=8,vv=16,dv=hv|fv,gv=pv|vv,yv=dv|gv,mv=["x","y"],bv=["clientX","clientY"];function wv(t,e,n){var r;if(t)if(t.forEach)t.forEach(e,n);else if(void 0!==t.length)for(r=0;r<t.length;)e.call(n,t[r],r,t),r++;else for(r in t)t.hasOwnProperty(r)&&e.call(n,t[r],r,t)}function kv(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function Cv(t,e){return t.indexOf(e)>-1}var Ev=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Up&&(t=this.compute()),Gp&&this.manager.element.style&&tv[t]&&(this.manager.element.style[Xp]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return wv(this.manager.recognizers,(function(e){kv(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(Cv(t,Jp))return Jp;var e=Cv(t,Kp),n=Cv(t,Zp);return e&&n?Jp:e||n?e?Kp:Zp:Cv(t,$p)?$p:Qp}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var r=this.actions,i=Cv(r,Jp)&&!tv[Jp],o=Cv(r,Zp)&&!tv[Zp],a=Cv(r,Kp)&&!tv[Kp];if(i){var s=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(s&&c&&u)return}if(!a||!o)return i||o&&n&dv||a&&n&gv?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Ov(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function Tv(t){var e=t.length;if(1===e)return{x:Wp(t[0].clientX),y:Wp(t[0].clientY)};for(var n=0,r=0,i=0;i<e;)n+=t[i].clientX,r+=t[i].clientY,i++;return{x:Wp(n/e),y:Wp(r/e)}}function Fv(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:Wp(t.pointers[n].clientX),clientY:Wp(t.pointers[n].clientY)},n++;return{timeStamp:qp(),pointers:e,center:Tv(e),deltaX:t.deltaX,deltaY:t.deltaY}}function Sv(t,e,n){n||(n=mv);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return Math.sqrt(r*r+i*i)}function _v(t,e,n){n||(n=mv);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return 180*Math.atan2(i,r)/Math.PI}function Av(t,e){return t===e?lv:Vp(t)>=Vp(e)?t<0?hv:fv:e<0?pv:vv}function xv(t,e,n){return{x:e/t||0,y:n/t||0}}function Pv(t,e){var n=t.session,r=e.pointers,i=r.length;n.firstInput||(n.firstInput=Fv(e)),i>1&&!n.firstMultiple?n.firstMultiple=Fv(e):1===i&&(n.firstMultiple=!1);var o=n.firstInput,a=n.firstMultiple,s=a?a.center:o.center,c=e.center=Tv(r);e.timeStamp=qp(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=_v(s,c),e.distance=Sv(s,c),function(t,e){var n=e.center,r=t.offsetDelta||{},i=t.prevDelta||{},o=t.prevInput||{};e.eventType!==sv&&o.eventType!==cv||(i=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},r=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=i.x+(n.x-r.x),e.deltaY=i.y+(n.y-r.y)}(n,e),e.offsetDirection=Av(e.deltaX,e.deltaY);var u,l,h=xv(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=Vp(h.x)>Vp(h.y)?h.x:h.y,e.scale=a?(u=a.pointers,Sv((l=r)[0],l[1],bv)/Sv(u[0],u[1],bv)):1,e.rotation=a?function(t,e){return _v(e[1],e[0],bv)+_v(t[1],t[0],bv)}(a.pointers,r):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,r,i,o,a=t.lastInterval||e,s=e.timeStamp-a.timeStamp;if(e.eventType!==uv&&(s>av||void 0===a.velocity)){var c=e.deltaX-a.deltaX,u=e.deltaY-a.deltaY,l=xv(s,c,u);r=l.x,i=l.y,n=Vp(l.x)>Vp(l.y)?l.x:l.y,o=Av(c,u),t.lastInterval=e}else n=a.velocity,r=a.velocityX,i=a.velocityY,o=a.direction;e.velocity=n,e.velocityX=r,e.velocityY=i,e.direction=o}(n,e);var f,p=t.element,v=e.srcEvent;Ov(f=v.composedPath?v.composedPath()[0]:v.path?v.path[0]:v.target,p)&&(p=f),e.target=p}function Dv(t,e,n){var r=n.pointers.length,i=n.changedPointers.length,o=e&sv&&r-i==0,a=e&(cv|uv)&&r-i==0;n.isFirst=!!o,n.isFinal=!!a,o&&(t.session={}),n.eventType=e,Pv(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function jv(t){return t.trim().split(/\s+/g)}function Iv(t,e,n){wv(jv(e),(function(e){t.addEventListener(e,n,!1)}))}function Rv(t,e,n){wv(jv(e),(function(e){t.removeEventListener(e,n,!1)}))}function Nv(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var Bv=function(){function t(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){kv(t.options.enable,[t])&&n.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&Iv(this.element,this.evEl,this.domHandler),this.evTarget&&Iv(this.target,this.evTarget,this.domHandler),this.evWin&&Iv(Nv(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&Rv(this.element,this.evEl,this.domHandler),this.evTarget&&Rv(this.target,this.evTarget,this.domHandler),this.evWin&&Rv(Nv(this.element),this.evWin,this.domHandler)},t}();function Mv(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var r=0;r<t.length;){if(n&&t[r][n]==e||!n&&t[r]===e)return r;r++}return-1}var Lv={pointerdown:sv,pointermove:2,pointerup:cv,pointercancel:uv,pointerout:uv},Hv={2:iv,3:"pen",4:ov,5:"kinect"},zv="pointerdown",Wv="pointermove pointerup pointercancel";Mp.MSPointerEvent&&!Mp.PointerEvent&&(zv="MSPointerDown",Wv="MSPointerMove MSPointerUp MSPointerCancel");var Vv=function(t){function e(){var n,r=e.prototype;return r.evEl=zv,r.evWin=Wv,(n=t.apply(this,arguments)||this).store=n.manager.session.pointerEvents=[],n}return Np(e,t),e.prototype.handler=function(t){var e=this.store,n=!1,r=t.type.toLowerCase().replace("ms",""),i=Lv[r],o=Hv[t.pointerType]||t.pointerType,a=o===iv,s=Mv(e,t.pointerId,"pointerId");i&sv&&(0===t.button||a)?s<0&&(e.push(t),s=e.length-1):i&(cv|uv)&&(n=!0),s<0||(e[s]=t,this.callback(this.manager,i,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(s,1))},e}(Bv);function qv(t){return Array.prototype.slice.call(t,0)}function Yv(t,e,n){for(var r=[],i=[],o=0;o<t.length;){var a=e?t[o][e]:t[o];Mv(i,a)<0&&r.push(t[o]),i[o]=a,o++}return n&&(r=e?r.sort((function(t,n){return t[e]>n[e]})):r.sort()),r}var Xv={touchstart:sv,touchmove:2,touchend:cv,touchcancel:uv},Gv=function(t){function e(){var n;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).targetIds={},n}return Np(e,t),e.prototype.handler=function(t){var e=Xv[t.type],n=Uv.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:iv,srcEvent:t})},e}(Bv);function Uv(t,e){var n,r,i=qv(t.touches),o=this.targetIds;if(e&(2|sv)&&1===i.length)return o[i[0].identifier]=!0,[i,i];var a=qv(t.changedTouches),s=[],c=this.target;if(r=i.filter((function(t){return Ov(t.target,c)})),e===sv)for(n=0;n<r.length;)o[r[n].identifier]=!0,n++;for(n=0;n<a.length;)o[a[n].identifier]&&s.push(a[n]),e&(cv|uv)&&delete o[a[n].identifier],n++;return s.length?[Yv(r.concat(s),"identifier",!0),s]:void 0}var Qv={mousedown:sv,mousemove:2,mouseup:cv},$v=function(t){function e(){var n,r=e.prototype;return r.evEl="mousedown",r.evWin="mousemove mouseup",(n=t.apply(this,arguments)||this).pressed=!1,n}return Np(e,t),e.prototype.handler=function(t){var e=Qv[t.type];e&sv&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=cv),this.pressed&&(e&cv&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:ov,srcEvent:t}))},e}(Bv),Jv=2500;function Kv(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY},r=this.lastTouches;this.lastTouches.push(n);setTimeout((function(){var t=r.indexOf(n);t>-1&&r.splice(t,1)}),Jv)}}function Zv(t,e){t&sv?(this.primaryTouch=e.changedPointers[0].identifier,Kv.call(this,e)):t&(cv|uv)&&Kv.call(this,e)}function td(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,r=0;r<this.lastTouches.length;r++){var i=this.lastTouches[r],o=Math.abs(e-i.x),a=Math.abs(n-i.y);if(o<=25&&a<=25)return!0}return!1}var ed=function(){return function(t){function e(e,n){var r;return(r=t.call(this,e,n)||this).handler=function(t,e,n){var i=n.pointerType===iv,o=n.pointerType===ov;if(!(o&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)Zv.call(Bp(Bp(r)),e,n);else if(o&&td.call(Bp(Bp(r)),n))return;r.callback(t,e,n)}},r.touch=new Gv(r.manager,r.handler),r.mouse=new $v(r.manager,r.handler),r.primaryTouch=null,r.lastTouches=[],r}return Np(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(Bv)}();function nd(t,e,n){return!!Array.isArray(t)&&(wv(t,n[e],n),!0)}var rd=32,id=1;function od(t,e){var n=e.manager;return n?n.get(t):t}function ad(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var sd=function(){function t(t){void 0===t&&(t={}),this.options=Rp({enable:!0},t),this.id=id++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Lp(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(nd(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=od(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return nd(t,"dropRecognizeWith",this)||(t=od(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(nd(t,"requireFailure",this))return this;var e=this.requireFail;return-1===Mv(e,t=od(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(nd(t,"dropRequireFailure",this))return this;t=od(t,this);var e=Mv(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,n=this.state;function r(n){e.manager.emit(n,t)}n<8&&r(e.options.event+ad(n)),r(e.options.event),t.additionalEvent&&r(t.additionalEvent),n>=8&&r(e.options.event+ad(n))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=rd},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Lp({},t);if(!kv(this.options.enable,[this,e]))return this.reset(),void(this.state=rd);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),cd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,n.pCenter=!1,n._timer=null,n._input=null,n.count=0,n}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){return[$p]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime<n.time;if(this.reset(),t.eventType&sv&&0===this.count)return this.failTimeout();if(i&&o&&r){if(t.eventType!==cv)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<n.interval,s=!this.pCenter||Sv(this.pCenter,t.center)<n.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,s&&a?this.count+=1:this.count=1,this._input=t,0===this.count%n.taps)return this.hasRequireFailures()?(this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),n.interval),2):8}return rd},n.failTimeout=function(){var t=this;return this._timer=setTimeout((function(){t.state=rd}),this.options.interval),rd},n.reset=function(){clearTimeout(this._timer)},n.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(sd),ud=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({pointers:1},e))||this}Np(e,t);var n=e.prototype;return n.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},n.process=function(t){var e=this.state,n=t.eventType,r=6&e,i=this.attrTest(t);return r&&(n&uv||!i)?16|e:r||i?n&cv?8|e:2&e?4|e:2:rd},e}(sd);function ld(t){return t===vv?"down":t===pv?"up":t===hv?"left":t===fv?"right":""}var hd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"pan",threshold:10,pointers:1,direction:yv},e))||this).pX=null,n.pY=null,n}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){var t=this.options.direction,e=[];return t&dv&&e.push(Zp),t&gv&&e.push(Kp),e},n.directionTest=function(t){var e=this.options,n=!0,r=t.distance,i=t.direction,o=t.deltaX,a=t.deltaY;return i&e.direction||(e.direction&dv?(i=0===o?lv:o<0?hv:fv,n=o!==this.pX,r=Math.abs(t.deltaX)):(i=0===a?lv:a<0?pv:vv,n=a!==this.pY,r=Math.abs(t.deltaY))),t.direction=i,n&&r>e.threshold&&i&e.direction},n.attrTest=function(t){return ud.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},n.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var n=ld(e.direction);n&&(e.additionalEvent=this.options.event+n),t.prototype.emit.call(this,e)},e}(ud),fd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"swipe",threshold:10,velocity:.3,direction:dv|gv,pointers:1},e))||this}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){return hd.prototype.getTouchAction.call(this)},n.attrTest=function(e){var n,r=this.options.direction;return r&(dv|gv)?n=e.overallVelocity:r&dv?n=e.overallVelocityX:r&gv&&(n=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&r&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Vp(n)>this.options.velocity&&e.eventType&cv},n.emit=function(t){var e=ld(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(ud),pd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"pinch",threshold:0,pointers:2},e))||this}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Jp]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},n.emit=function(e){if(1!==e.scale){var n=e.scale<1?"in":"out";e.additionalEvent=this.options.event+n}t.prototype.emit.call(this,e)},e}(ud),vd=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Rp({event:"rotate",threshold:0,pointers:2},e))||this}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Jp]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(ud),dd=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Rp({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,n._input=null,n}Np(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Qp]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime>n.time;if(this._input=t,!i||!r||t.eventType&(cv|uv)&&!o)this.reset();else if(t.eventType&sv)this.reset(),this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),n.time);else if(t.eventType&cv)return 8;return rd},n.reset=function(){clearTimeout(this._timer)},n.emit=function(t){8===this.state&&(t&&t.eventType&cv?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=qp(),this.manager.emit(this.options.event,this._input)))},e}(sd),gd={domEvents:!1,touchAction:Up,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},yd=[[vd,{enable:!1}],[pd,{enable:!1},["rotate"]],[fd,{direction:dv}],[hd,{direction:dv},["swipe"]],[cd],[cd,{event:"doubletap",taps:2},["tap"]],[dd]];function md(t,e){var n,r=t.element;r.style&&(wv(t.options.cssProps,(function(i,o){n=Yp(r.style,o),e?(t.oldCssProps[n]=r.style[n],r.style[n]=i):r.style[n]=t.oldCssProps[n]||""})),e||(t.oldCssProps={}))}var bd=function(){function t(t,e){var n,r=this;this.options=Lp({},gd,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(nv?Vv:rv?Gv:ev?ed:$v))(n,Dv),this.touchAction=new Ev(this,this.options.touchAction),md(this,!0),wv(this.options.recognizers,(function(t){var e=r.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}var e=t.prototype;return e.set=function(t){return Lp(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var r=this.recognizers,i=e.curRecognizer;(!i||i&&8&i.state)&&(e.curRecognizer=null,i=null);for(var o=0;o<r.length;)n=r[o],2===e.stopped||i&&n!==i&&!n.canRecognizeWith(i)?n.reset():n.recognize(t),!i&&14&n.state&&(e.curRecognizer=n,i=n),o++}},e.get=function(t){if(t instanceof sd)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event===t)return e[n];return null},e.add=function(t){if(nd(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(nd(t,"remove",this))return this;var e=this.get(t);if(t){var n=this.recognizers,r=Mv(n,e);-1!==r&&(n.splice(r,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var n=this.handlers;return wv(jv(t),(function(t){n[t]=n[t]||[],n[t].push(e)})),this},e.off=function(t,e){if(void 0===t)return this;var n=this.handlers;return wv(jv(t),(function(t){e?n[t]&&n[t].splice(Mv(n[t],e),1):delete n[t]})),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var n=document.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<n.length;)n[r](e),r++}},e.destroy=function(){this.element&&md(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),wd={touchstart:sv,touchmove:2,touchend:cv,touchcancel:uv},kd=function(t){function e(){var n,r=e.prototype;return r.evTarget="touchstart",r.evWin="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).started=!1,n}return Np(e,t),e.prototype.handler=function(t){var e=wd[t.type];if(e===sv&&(this.started=!0),this.started){var n=Cd.call(this,t,e);e&(cv|uv)&&n[0].length-n[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:iv,srcEvent:t})}},e}(Bv);function Cd(t,e){var n=qv(t.touches),r=qv(t.changedTouches);return e&(cv|uv)&&(n=Yv(n.concat(r),"identifier",!0)),[n,r]}function Ed(t,e,n){var r="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=window.console&&(window.console.warn||window.console.log);return i&&i.call(window.console,r,n),t.apply(this,arguments)}}var Od=Ed((function(t,e,n){for(var r=Object.keys(e),i=0;i<r.length;)(!n||n&&void 0===t[r[i]])&&(t[r[i]]=e[r[i]]),i++;return t}),"extend","Use `assign`."),Td=Ed((function(t,e){return Od(t,e,!0)}),"merge","Use `assign`.");function Fd(t,e,n){var r,i=e.prototype;(r=t.prototype=Object.create(i)).constructor=t,r._super=i,n&&Lp(r,n)}function Sd(t,e){return function(){return t.apply(e,arguments)}}var _d=function(){var t=function(t,e){return void 0===e&&(e={}),new bd(t,Rp({recognizers:yd.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=yv,t.DIRECTION_DOWN=vv,t.DIRECTION_LEFT=hv,t.DIRECTION_RIGHT=fv,t.DIRECTION_UP=pv,t.DIRECTION_HORIZONTAL=dv,t.DIRECTION_VERTICAL=gv,t.DIRECTION_NONE=lv,t.DIRECTION_DOWN=vv,t.INPUT_START=sv,t.INPUT_MOVE=2,t.INPUT_END=cv,t.INPUT_CANCEL=uv,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=rd,t.Manager=bd,t.Input=Bv,t.TouchAction=Ev,t.TouchInput=Gv,t.MouseInput=$v,t.PointerEventInput=Vv,t.TouchMouseInput=ed,t.SingleTouchInput=kd,t.Recognizer=sd,t.AttrRecognizer=ud,t.Tap=cd,t.Pan=hd,t.Swipe=fd,t.Pinch=pd,t.Rotate=vd,t.Press=dd,t.on=Iv,t.off=Rv,t.each=wv,t.merge=Td,t.extend=Od,t.bindFn=Sd,t.assign=Lp,t.inherit=Fd,t.bindFn=Sd,t.prefixed=Yp,t.toArray=qv,t.inArray=Mv,t.uniqueArray=Yv,t.splitStr=jv,t.boolOrFn=kv,t.hasParent=Ov,t.addEventListeners=Iv,t.removeEventListeners=Rv,t.defaults=Lp({},gd,{preset:yd}),t}();_d.defaults;var Ad=_d;var xd="undefined"!=typeof window?window.Hammer||Ad:function(){return function(){var t=function(){};return{on:t,off:t,destroy:t,emit:t,get:function(){return{set:t}}}}()};function Pd(t,e){var n=void 0!==Ph&&us(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return Dd(t,e);var r=Bh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Wa(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dd(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Dd(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function jd(t){var e,n=this;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push((function(){n._dom.overlay.parentNode.removeChild(n._dom.overlay)}));var r=xd(this._dom.overlay);r.on("tap",qf(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push((function(){r.destroy()}));var i=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];np(i).call(i,(function(t){r.on(t,(function(t){t.srcEvent.stopPropagation()}))})),document&&document.body&&(this._onClick=function(e){(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||n.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push((function(){document.body.removeEventListener("click",n._onClick)}))),this._escListener=function(t){("key"in t?"Escape"===t.key:27===t.keyCode)&&n.deactivate()}}function Id(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Ip(jd.prototype),jd.current=null,jd.prototype.destroy=function(){var t,e;this.deactivate();var n,r=Pd(hp(t=Pp(e=this._cleanupQueue).call(e,0)).call(t));try{for(r.s();!(n=r.n()).done;){(0,n.value)()}}catch(t){r.e(t)}finally{r.f()}},jd.prototype.activate=function(){jd.current&&jd.current.deactivate(),jd.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},jd.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},jd.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var Rd={exports:{}},Nd=Or,Bd=qt,Md=Yt.f;Nd({target:"Object",stat:!0,forced:Object.defineProperty!==Md,sham:!Bd},{defineProperty:Md});var Ld=ae.Object,Hd=Rd.exports=function(t,e,n){return Ld.defineProperty(t,e,n)};Ld.defineProperty.sham&&(Hd.sham=!0);var zd=n(Rd.exports),Wd=n(ec.f("toPrimitive"));function Vd(t){var e=function(t,e){if("object"!==nh(t)||null===t)return t;var n=t[Wd];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==nh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nh(e)?e:String(e)}function qd(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),zd(t,Vd(r.key),r)}}function Yd(t,e,n){return e&&qd(t.prototype,e),n&&qd(t,n),zd(t,"prototype",{writable:!1}),t}var Xd=ae,Gd=_n;Xd.JSON||(Xd.JSON={stringify:JSON.stringify});var Ud=function(t,e,n){return Gd(Xd.JSON.stringify,null,arguments)},Qd=n(Ud),$d=qt,Jd=u,Kd=oe,Zd=r,tg=ti,eg=Zs,ng=jn,rg=D,ig=zn,og=Object.assign,ag=Object.defineProperty,sg=Jd([].concat),cg=!og||Zd((function(){if($d&&1!==og({b:1},og(ag({},"a",{enumerable:!0,get:function(){ag(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol("assign detection"),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!==og({},t)[n]||tg(og({},e)).join("")!==r}))?function(t,e){for(var n=rg(t),r=arguments.length,i=1,o=eg.f,a=ng.f;r>i;)for(var s,c=ig(arguments[i++]),u=o?sg(tg(c),o(c)):tg(c),l=u.length,h=0;l>h;)s=u[h++],$d&&!Kd(a,c,s)||(n[s]=c[s]);return n}:og,ug=cg;Or({target:"Object",stat:!0,arity:2,forced:Object.assign!==ug},{assign:ug});var lg=n(ae.Object.assign),hg="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,fg=TypeError,pg=d,vg=_n,dg=vt,gg=hg,yg=z,mg=Xu,bg=function(t,e){if(t<e)throw new fg("Not enough arguments");return t},wg=pg.Function,kg=/MSIE .\./.test(yg)||gg&&function(){var t=pg.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),Cg=function(t,e){var n=e?2:1;return kg?function(r,i){var o=bg(arguments.length,1)>n,a=dg(r)?r:wg(r),s=o?mg(arguments,n):[],c=o?function(){vg(a,this,s)}:a;return e?t(c,i):t(c)}:t},Eg=Or,Og=d,Tg=Cg(Og.setInterval,!0);Eg({global:!0,bind:!0,forced:Og.setInterval!==Tg},{setInterval:Tg});var Fg=Or,Sg=d,_g=Cg(Sg.setTimeout,!0);Fg({global:!0,bind:!0,forced:Sg.setTimeout!==_g},{setTimeout:_g});var Ag=n(ae.setTimeout),xg=D,Pg=Rr,Dg=Lr,jg=function(t){for(var e=xg(this),n=Dg(e),r=arguments.length,i=Pg(r>1?arguments[1]:void 0,n),o=r>2?arguments[2]:void 0,a=void 0===o?n:Pg(o,n);a>i;)e[i++]=t;return e};Or({target:"Array",proto:!0},{fill:jg});var Ig=Ch("Array").fill,Rg=fe,Ng=Ig,Bg=Array.prototype,Mg=n((function(t){var e=t.fill;return t===Bg||Rg(Bg,t)&&e===Bg.fill?Ng:e})),Lg=qr.includes;Or({target:"Array",proto:!0,forced:r((function(){return!Array(1).includes()}))},{includes:function(t){return Lg(this,t,arguments.length>1?arguments[1]:void 0)}});var Hg=Ch("Array").includes,zg=Vt,Wg=mt,Vg=ct("match"),qg=function(t){var e;return zg(t)&&(void 0!==(e=t[Vg])?!!e:"RegExp"===Wg(t))},Yg=TypeError,Xg=ct("match"),Gg=Or,Ug=function(t){if(qg(t))throw new Yg("The method doesn't accept regular expressions");return t},Qg=A,$g=_t,Jg=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[Xg]=!1,"/./"[t](e)}catch(t){}}return!1},Kg=u("".indexOf);Gg({target:"String",proto:!0,forced:!Jg("includes")},{includes:function(t){return!!~Kg($g(Qg(this)),$g(Ug(t)),arguments.length>1?arguments[1]:void 0)}});var Zg=Ch("String").includes,ty=fe,ey=Hg,ny=Zg,ry=Array.prototype,iy=String.prototype,oy=n((function(t){var e=t.includes;return t===ry||ty(ry,t)&&e===ry.includes?ey:"string"==typeof t||t===iy||ty(iy,t)&&e===iy.includes?ny:e})),ay=D,sy=Ii,cy=Fi;Or({target:"Object",stat:!0,forced:r((function(){sy(1)})),sham:!cy},{getPrototypeOf:function(t){return sy(ay(t))}});var uy=n(ae.Object.getPrototypeOf),ly=wc.filter;Or({target:"Array",proto:!0,forced:!Os("filter")},{filter:function(t){return ly(this,t,arguments.length>1?arguments[1]:void 0)}});var hy=Ch("Array").filter,fy=fe,py=hy,vy=Array.prototype,dy=n((function(t){var e=t.filter;return t===vy||fy(vy,t)&&e===vy.filter?py:e})),gy=qt,yy=r,my=u,by=Ii,wy=ti,ky=qn,Cy=my(jn.f),Ey=my([].push),Oy=gy&&yy((function(){var t=Object.create(null);return t[2]=2,!Cy(t,2)})),Ty=function(t){return function(e){for(var n,r=ky(e),i=wy(r),o=Oy&&null===by(r),a=i.length,s=0,c=[];a>s;)n=i[s++],gy&&!(o?n in r:Cy(r,n))||Ey(c,t?[n,r[n]]:r[n]);return c}},Fy={entries:Ty(!0),values:Ty(!1)},Sy=Fy.values;Or({target:"Object",stat:!0},{values:function(t){return Sy(t)}});var _y=n(ae.Object.values),Ay="\t\n\v\f\r                　\u2028\u2029\ufeff",xy=A,Py=_t,Dy=Ay,jy=u("".replace),Iy=RegExp("^["+Dy+"]+"),Ry=RegExp("(^|[^"+Dy+"])["+Dy+"]+$"),Ny=function(t){return function(e){var n=Py(xy(e));return 1&t&&(n=jy(n,Iy,"")),2&t&&(n=jy(n,Ry,"$1")),n}},By={start:Ny(1),end:Ny(2),trim:Ny(3)},My=d,Ly=r,Hy=u,zy=_t,Wy=By.trim,Vy=Ay,qy=My.parseInt,Yy=My.Symbol,Xy=Yy&&Yy.iterator,Gy=/^[+-]?0x/i,Uy=Hy(Gy.exec),Qy=8!==qy(Vy+"08")||22!==qy(Vy+"0x16")||Xy&&!Ly((function(){qy(Object(Xy))}))?function(t,e){var n=Wy(zy(t));return qy(n,e>>>0||(Uy(Gy,n)?16:10))}:qy;Or({global:!0,forced:parseInt!==Qy},{parseInt:Qy});var $y=n(ae.parseInt),Jy=Or,Ky=qr.indexOf,Zy=Xf,tm=Pn([].indexOf),em=!!tm&&1/tm([1],1,-0)<0;Jy({target:"Array",proto:!0,forced:em||!Zy("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return em?tm(this,t,e)||0:Ky(this,t,e)}});var nm=Ch("Array").indexOf,rm=fe,im=nm,om=Array.prototype,am=n((function(t){var e=t.indexOf;return t===om||rm(om,t)&&e===om.indexOf?im:e})),sm=Fy.entries;Or({target:"Object",stat:!0},{entries:function(t){return sm(t)}});var cm=n(ae.Object.entries);Or({target:"Object",stat:!0,sham:!qt},{create:Ti});var um=ae.Object,lm=n((function(t,e){return um.create(t,e)}));function hm(t,e){var n=void 0!==Ph&&us(t)||t["@@iterator"];if(!n){if(Yh(t)||(n=function(t,e){var n;if(!t)return;if("string"==typeof t)return fm(t,e);var r=Bh(n=Object.prototype.toString.call(t)).call(n,8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Wa(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fm(t,e)}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){s=!0,o=t},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function fm(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var pm=/^\/?Date\((-?\d+)/i,vm=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,dm=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,gm=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,ym=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function mm(t){return t instanceof Number||"number"==typeof t}function bm(t){return t instanceof String||"string"==typeof t}function wm(t){return"object"===nh(t)&&null!==t}function km(t,e,n,r){var i=!1;!0===r&&(i=null===e[n]&&void 0!==t[n]),i?delete t[n]:t[n]=e[n]}var Cm=lg;function Em(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)||!0===n)if("object"===nh(e[i])&&null!==e[i]&&uy(e[i])===Object.prototype)void 0===t[i]?t[i]=Em({},e[i],n):"object"===nh(t[i])&&null!==t[i]&&uy(t[i])===Object.prototype?Em(t[i],e[i],n):km(t,e,i,r);else if(Yh(e[i])){var o;t[i]=Bh(o=e[i]).call(o)}else km(t,e,i,r);return t}function Om(t,e){var n;return Nh(n=[]).call(n,xh(t),[e])}function Tm(t){return Bh(t).call(t)}var Fm=_y;var Sm={asBoolean:function(t,e){return"function"==typeof t&&(t=t()),null!=t?0!=t:e||null},asNumber:function(t,e){return"function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null},asString:function(t,e){return"function"==typeof t&&(t=t()),null!=t?String(t):e||null},asSize:function(t,e){return"function"==typeof t&&(t=t()),bm(t)?t:mm(t)?t+"px":e||null},asElement:function(t,e){return"function"==typeof t&&(t=t()),t||e||null}};function _m(t){var e;switch(t.length){case 3:case 4:return(e=dm.exec(t))?{r:$y(e[1]+e[1],16),g:$y(e[2]+e[2],16),b:$y(e[3]+e[3],16)}:null;case 6:case 7:return(e=vm.exec(t))?{r:$y(e[1],16),g:$y(e[2],16),b:$y(e[3],16)}:null;default:return null}}function Am(t,e,n){var r;return"#"+Bh(r=((1<<24)+(t<<16)+(e<<8)+n).toString(16)).call(r,1)}function xm(t,e,n){t/=255,e/=255,n/=255;var r=Math.min(t,Math.min(e,n)),i=Math.max(t,Math.max(e,n));return r===i?{h:0,s:0,v:r}:{h:60*((t===r?3:n===r?1:5)-(t===r?e-n:n===r?t-e:n-t)/(i-r))/360,s:(i-r)/i,v:i}}function Pm(t){var e=document.createElement("div"),n={};e.style.cssText=t;for(var r=0;r<e.style.length;++r)n[e.style[r]]=e.style.getPropertyValue(e.style[r]);return n}function Dm(t,e,n){var r,i,o,a=Math.floor(6*t),s=6*t-a,c=n*(1-e),u=n*(1-s*e),l=n*(1-(1-s)*e);switch(a%6){case 0:r=n,i=l,o=c;break;case 1:r=u,i=n,o=c;break;case 2:r=c,i=n,o=l;break;case 3:r=c,i=u,o=n;break;case 4:r=l,i=c,o=n;break;case 5:r=n,i=c,o=u}return{r:Math.floor(255*r),g:Math.floor(255*i),b:Math.floor(255*o)}}function jm(t,e,n){var r=Dm(t,e,n);return Am(r.r,r.g,r.b)}function Im(t){var e=_m(t);if(!e)throw new TypeError("'".concat(t,"' is not a valid color."));return xm(e.r,e.g,e.b)}function Rm(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function Nm(t){return gm.test(t)}function Bm(t){return ym.test(t)}function Mm(t){if(null===t||"object"!==nh(t))return null;if(t instanceof Element)return t;var e=lm(t);for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&"object"==nh(t[n])&&(e[n]=Mm(t[n]));return e}var Lm={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"},Hm=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;Id(this,t),this.pixelRatio=e,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=function(){},this.closeCallback=function(){},this._create()}return Yd(t,[{key:"insertTo",value:function(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}},{key:"setUpdateCallback",value:function(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}},{key:"setCloseCallback",value:function(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}},{key:"_isColorString",value:function(t){if("string"==typeof t)return Lm[t]}},{key:"setColor",value:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("none"!==t){var n,r=this._isColorString(t);if(void 0!==r&&(t=r),!0===bm(t)){if(!0===Nm(t)){var i=t.substr(4).substr(0,t.length-5).split(",");n={r:i[0],g:i[1],b:i[2],a:1}}else if(!0===Bm(t)){var o=t.substr(5).substr(0,t.length-6).split(",");n={r:o[0],g:o[1],b:o[2],a:o[3]}}else if(!0===Rm(t)){var a=_m(t);n={r:a.r,g:a.g,b:a.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){var s=void 0!==t.a?t.a:"1.0";n={r:t.r,g:t.g,b:t.b,a:s}}if(void 0===n)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+Qd(t));this._setColor(n,e)}}},{key:"show",value:function(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}},{key:"_hide",value:function(){var t=this;!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.previousColor=lg({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",Ag((function(){void 0!==t.closeCallback&&(t.closeCallback(),t.closeCallback=void 0)}),0)}},{key:"_save",value:function(){this.updateCallback(this.color),this.applied=!1,this._hide()}},{key:"_apply",value:function(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}},{key:"_loadLast",value:function(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}},{key:"_setColor",value:function(t){!0===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(this.initialColor=lg({},t)),this.color=t;var e=xm(t.r,t.g,t.b),n=2*Math.PI,r=this.r*e.s,i=this.centerCoordinates.x+r*Math.sin(n*e.h),o=this.centerCoordinates.y+r*Math.cos(n*e.h);this.colorPickerSelector.style.left=i-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=o-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}},{key:"_setOpacity",value:function(t){this.color.a=t/100,this._updatePicker(this.color)}},{key:"_setBrightness",value:function(t){var e=xm(this.color.r,this.color.g,this.color.b);e.v=t/100;var n=Dm(e.h,e.s,e.v);n.a=this.color.a,this.color=n,this._updatePicker()}},{key:"_updatePicker",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color,e=xm(t.r,t.g,t.b),n=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(n.webkitBackingStorePixelRatio||n.mozBackingStorePixelRatio||n.msBackingStorePixelRatio||n.oBackingStorePixelRatio||n.backingStorePixelRatio||1)),n.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var r=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;n.clearRect(0,0,r,i),n.putImageData(this.hueCircle,0,0),n.fillStyle="rgba(0,0,0,"+(1-e.v)+")",n.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),Mg(n).call(n),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}},{key:"_setSize",value:function(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}},{key:"_create",value:function(){var t,e,n,r;if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){var i=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{var o=document.createElement("DIV");o.style.color="red",o.style.fontWeight="bold",o.style.padding="10px",o.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(o)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);var a=this;this.opacityRange.onchange=function(){a._setOpacity(this.value)},this.opacityRange.oninput=function(){a._setOpacity(this.value)},this.brightnessRange.onchange=function(){a._setBrightness(this.value)},this.brightnessRange.oninput=function(){a._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=qf(t=this._hide).call(t,this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=qf(e=this._apply).call(e,this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=qf(n=this._save).call(n,this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=qf(r=this._loadLast).call(r,this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}},{key:"_bindHammer",value:function(){var t=this;this.drag={},this.pinch={},this.hammer=new xd(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",(function(e){e.isFirst&&t._moveSelector(e)})),this.hammer.on("tap",(function(e){t._moveSelector(e)})),this.hammer.on("panstart",(function(e){t._moveSelector(e)})),this.hammer.on("panmove",(function(e){t._moveSelector(e)})),this.hammer.on("panend",(function(e){t._moveSelector(e)}))}},{key:"_generateHueCircle",value:function(){if(!1===this.generated){var t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);var e,n,r,i,o=this.colorPickerCanvas.clientWidth,a=this.colorPickerCanvas.clientHeight;t.clearRect(0,0,o,a),this.centerCoordinates={x:.5*o,y:.5*a},this.r=.49*o;var s,c=2*Math.PI/360,u=1/this.r;for(r=0;r<360;r++)for(i=0;i<this.r;i++)e=this.centerCoordinates.x+i*Math.sin(c*r),n=this.centerCoordinates.y+i*Math.cos(c*r),s=Dm(.002777777777777778*r,i*u,1),t.fillStyle="rgb("+s.r+","+s.g+","+s.b+")",t.fillRect(e-.5,n-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,o,a)}this.generated=!0}},{key:"_moveSelector",value:function(t){var e=this.colorPickerDiv.getBoundingClientRect(),n=t.center.x-e.left,r=t.center.y-e.top,i=.5*this.colorPickerDiv.clientHeight,o=.5*this.colorPickerDiv.clientWidth,a=n-o,s=r-i,c=Math.atan2(a,s),u=.98*Math.min(Math.sqrt(a*a+s*s),o),l=Math.cos(c)*u+i,h=Math.sin(c)*u+o;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=h-.5*this.colorPickerSelector.clientWidth+"px";var f=c/(2*Math.PI);f=f<0?f+1:f;var p=u/this.r,v=xm(this.color.r,this.color.g,this.color.b);v.h=f,v.s=p;var d=Dm(v.h,v.s,v.v);d.a=this.color.a,this.color=d,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}}]),t}();function zm(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<1)throw new TypeError("Invalid arguments.");if(1===e.length)return document.createTextNode(e[0]);var r=document.createElement(e[0]);return r.appendChild(zm.apply(void 0,xh(Bh(e).call(e,1)))),r}var Wm,Vm=function(){function t(e,n,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:function(){return!1};Id(this,t),this.parent=e,this.changedOptions=[],this.container=n,this.allowCreation=!1,this.hideOption=o,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},lg(this.options,this.defaultOptions),this.configureOptions=r,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new Hm(i),this.wrapper=void 0}return Yd(t,[{key:"setOptions",value:function(t){if(void 0!==t){this.popupHistory={},this._removePopup();var e=!0;if("string"==typeof t)this.options.filter=t;else if(Yh(t))this.options.filter=t.join();else if("object"===nh(t)){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==dy(t)&&(this.options.filter=dy(t)),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===dy(this.options)&&(e=!1),this.options.enabled=e}this._clean()}},{key:"setModuleOptions",value:function(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}},{key:"_create",value:function(){this._clean(),this.changedOptions=[];var t=dy(this.options),e=0,n=!1;for(var r in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,r)&&(this.allowCreation=!1,n=!1,"function"==typeof t?n=(n=t(r,[]))||this._handleObject(this.configureOptions[r],[r],!0):!0!==t&&-1===am(t).call(t,r)||(n=!0),!1!==n&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(r),this._handleObject(this.configureOptions[r],[r])),e++);this._makeButton(),this._push()}},{key:"_push",value:function(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(var t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}},{key:"_clean",value:function(){for(var t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}},{key:"_getValue",value:function(t){for(var e=this.moduleOptions,n=0;n<t.length;n++){if(void 0===e[t[n]]){e=void 0;break}e=e[t[n]]}return e}},{key:"_makeItem",value:function(t){if(!0===this.allowCreation){var e=document.createElement("div");e.className="vis-configuration vis-config-item vis-config-s"+t.length;for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return np(r).call(r,(function(t){e.appendChild(t)})),this.domElements.push(e),this.domElements.length}return 0}},{key:"_makeHeader",value:function(t){var e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}},{key:"_makeLabel",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=document.createElement("div");if(r.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===n){for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(zm("i","b",t))}else r.innerText=t+":";return r}},{key:"_makeDropdown",value:function(t,e,n){var r=document.createElement("select");r.className="vis-configuration vis-config-select";var i=0;void 0!==e&&-1!==am(t).call(t,e)&&(i=am(t).call(t,e));for(var o=0;o<t.length;o++){var a=document.createElement("option");a.value=t[o],o===i&&(a.selected="selected"),a.innerText=t[o],r.appendChild(a)}var s=this;r.onchange=function(){s._update(this.value,n)};var c=this._makeLabel(n[n.length-1],n);this._makeItem(n,c,r)}},{key:"_makeRange",value:function(t,e,n){var r=t[0],i=t[1],o=t[2],a=t[3],s=document.createElement("input");s.className="vis-configuration vis-config-range";try{s.type="range",s.min=i,s.max=o}catch(t){}s.step=a;var c="",u=0;if(void 0!==e){var l=1.2;e<0&&e*l<i?(s.min=Math.ceil(e*l),u=s.min,c="range increased"):e/l<i&&(s.min=Math.ceil(e/l),u=s.min,c="range increased"),e*l>o&&1!==o&&(s.max=Math.ceil(e*l),u=s.max,c="range increased"),s.value=e}else s.value=r;var h=document.createElement("input");h.className="vis-configuration vis-config-rangeinput",h.value=s.value;var f=this;s.onchange=function(){h.value=this.value,f._update(Number(this.value),n)},s.oninput=function(){h.value=this.value};var p=this._makeLabel(n[n.length-1],n),v=this._makeItem(n,p,s,h);""!==c&&this.popupHistory[v]!==u&&(this.popupHistory[v]=u,this._setupPopup(c,v))}},{key:"_makeButton",value:function(){var t=this;if(!0===this.options.showButton){var e=document.createElement("div");e.className="vis-configuration vis-config-button",e.innerText="generate options",e.onclick=function(){t._printOptions()},e.onmouseover=function(){e.className="vis-configuration vis-config-button hover"},e.onmouseout=function(){e.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(e)}}},{key:"_setupPopup",value:function(t,e){var n=this;if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){var r=document.createElement("div");r.id="vis-configuration-popup",r.className="vis-configuration-popup",r.innerText=t,r.onclick=function(){n._removePopup()},this.popupCounter+=1,this.popupDiv={html:r,index:e}}}},{key:"_removePopup",value:function(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}},{key:"_showPopupIfNeeded",value:function(){var t=this;if(void 0!==this.popupDiv.html){var e=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=e.left+"px",this.popupDiv.html.style.top=e.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=Ag((function(){t.popupDiv.html.style.opacity=0}),1500),this.popupDiv.deleteTimeout=Ag((function(){t._removePopup()}),1800)}}},{key:"_makeCheckbox",value:function(t,e,n){var r=document.createElement("input");r.type="checkbox",r.className="vis-configuration vis-config-checkbox",r.checked=t,void 0!==e&&(r.checked=e,e!==t&&("object"===nh(t)?e!==t.enabled&&this.changedOptions.push({path:n,value:e}):this.changedOptions.push({path:n,value:e})));var i=this;r.onchange=function(){i._update(this.checked,n)};var o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}},{key:"_makeTextInput",value:function(t,e,n){var r=document.createElement("input");r.type="text",r.className="vis-configuration vis-config-text",r.value=e,e!==t&&this.changedOptions.push({path:n,value:e});var i=this;r.onchange=function(){i._update(this.value,n)};var o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}},{key:"_makeColorField",value:function(t,e,n){var r=this,i=t[1],o=document.createElement("div");"none"!==(e=void 0===e?i:e)?(o.className="vis-configuration vis-config-colorBlock",o.style.backgroundColor=e):o.className="vis-configuration vis-config-colorBlock none",e=void 0===e?i:e,o.onclick=function(){r._showColorPicker(e,o,n)};var a=this._makeLabel(n[n.length-1],n);this._makeItem(n,a,o)}},{key:"_showColorPicker",value:function(t,e,n){var r=this;e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback((function(t){var i="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=i,r._update(i,n)})),this.colorPicker.setCloseCallback((function(){e.onclick=function(){r._showColorPicker(t,e,n)}}))}},{key:"_handleObject",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!1,i=dy(this.options),o=!1;for(var a in t)if(Object.prototype.hasOwnProperty.call(t,a)){r=!0;var s=t[a],c=Om(e,a);if("function"==typeof i&&!1===(r=i(a,e))&&!Yh(s)&&"string"!=typeof s&&"boolean"!=typeof s&&s instanceof Object&&(this.allowCreation=!1,r=this._handleObject(s,c,!0),this.allowCreation=!1===n),!1!==r){o=!0;var u=this._getValue(c);if(Yh(s))this._handleArray(s,u,c);else if("string"==typeof s)this._makeTextInput(s,u,c);else if("boolean"==typeof s)this._makeCheckbox(s,u,c);else if(s instanceof Object){if(!this.hideOption(e,a,this.moduleOptions))if(void 0!==s.enabled){var l=Om(c,"enabled"),h=this._getValue(l);if(!0===h){var f=this._makeLabel(a,c,!0);this._makeItem(c,f),o=this._handleObject(s,c)||o}else this._makeCheckbox(s,h,c)}else{var p=this._makeLabel(a,c,!0);this._makeItem(c,p),o=this._handleObject(s,c)||o}}else console.error("dont know how to handle",s,a,c)}}return o}},{key:"_handleArray",value:function(t,e,n){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,n),t[1]!==e&&this.changedOptions.push({path:n,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:Number(e)}))}},{key:"_update",value:function(t,e){var n=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",n),this.initialized=!0,this.parent.setOptions(n)}},{key:"_constructOptions",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n;t="false"!==(t="true"===t||t)&&t;for(var i=0;i<e.length;i++)"global"!==e[i]&&(void 0===r[e[i]]&&(r[e[i]]={}),i!==e.length-1?r=r[e[i]]:r[e[i]]=t);return n}},{key:"_printOptions",value:function(){for(var t=this.getOptions();this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(zm("pre","const options = "+Qd(t,null,2)))}},{key:"getOptions",value:function(){for(var t={},e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}}]),t}(),qm=function(){function t(e,n){Id(this,t),this.container=e,this.overflowMethod=n||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}return Yd(t,[{key:"setPosition",value:function(t,e){this.x=$y(t),this.y=$y(e)}},{key:"setText",value:function(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}},{key:"show",value:function(t){if(void 0===t&&(t=!0),!0===t){var e=this.frame.clientHeight,n=this.frame.clientWidth,r=this.frame.parentNode.clientHeight,i=this.frame.parentNode.clientWidth,o=0,a=0;if("flip"==this.overflowMethod){var s=!1,c=!0;this.y-e<this.padding&&(c=!1),this.x+n>i-this.padding&&(s=!0),o=s?this.x-n:this.x,a=c?this.y-e:this.y}else(a=this.y-e)+e+this.padding>r&&(a=r-e-this.padding),a<this.padding&&(a=this.padding),(o=this.x)+n+this.padding>i&&(o=i-n-this.padding),o<this.padding&&(o=this.padding);this.frame.style.left=o+"px",this.frame.style.top=a+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}},{key:"hide",value:function(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}},{key:"destroy",value:function(){this.frame.parentNode.removeChild(this.frame)}}]),t}(),Ym=!1,Xm="background: #FFeeee; color: #dd0000",Gm=function(){function t(){Id(this,t)}return Yd(t,null,[{key:"validate",value:function(e,n,r){Ym=!1,Wm=n;var i=n;return void 0!==r&&(i=n[r]),t.parse(e,i,[]),Ym}},{key:"parse",value:function(e,n,r){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.check(i,e,n,r)}},{key:"check",value:function(e,n,r,i){if(void 0!==r[e]||void 0!==r.__any__){var o=e,a=!0;void 0===r[e]&&void 0!==r.__any__&&(o="__any__",a="object"===t.getType(n[e]));var s=r[o];a&&void 0!==s.__type__&&(s=s.__type__),t.checkFields(e,n,r,o,s,i)}else t.getSuggestion(e,r,i)}},{key:"checkFields",value:function(e,n,r,i,o,a){var s=function(n){console.error("%c"+n+t.printLocation(a,e),Xm)},c=t.getType(n[e]),u=o[c];void 0!==u?"array"===t.getType(u)&&-1===am(u).call(u,n[e])?(s('Invalid option detected in "'+e+'". Allowed values are:'+t.print(u)+' not "'+n[e]+'". '),Ym=!0):"object"===c&&"__any__"!==i&&(a=Om(a,e),t.parse(n[e],r[i],a)):void 0===o.any&&(s('Invalid type received for "'+e+'". Expected: '+t.print(tf(o))+". Received ["+c+'] "'+n[e]+'"'),Ym=!0)}},{key:"getType",value:function(t){var e=nh(t);return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Yh(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}},{key:"getSuggestion",value:function(e,n,r){var i,o=t.findInOptions(e,n,r,!1),a=t.findInOptions(e,Wm,[],!0);i=void 0!==o.indexMatch?" in "+t.printLocation(o.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+o.indexMatch+'"?\n\n':a.distance<=4&&o.distance>a.distance?" in "+t.printLocation(o.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(a.path,a.closestMatch,""):o.distance<=8?'. Did you mean "'+o.closestMatch+'"?'+t.printLocation(o.path,e):". Did you mean one of these: "+t.print(tf(n))+t.printLocation(r,e),console.error('%cUnknown option detected: "'+e+'"'+i,Xm),Ym=!0}},{key:"findInOptions",value:function(e,n,r){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=1e9,a="",s=[],c=e.toLowerCase(),u=void 0;for(var l in n){var h=void 0;if(void 0!==n[l].__type__&&!0===i){var f=t.findInOptions(e,n[l],Om(r,l));o>f.distance&&(a=f.closestMatch,s=f.path,o=f.distance,u=f.indexMatch)}else{var p;-1!==am(p=l.toLowerCase()).call(p,c)&&(u=l),o>(h=t.levenshteinDistance(e,l))&&(a=l,s=Tm(r),o=h)}}return{closestMatch:a,path:s,distance:o,indexMatch:u}}},{key:"printLocation",value:function(t,e){for(var n="\n\n"+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Problem value found at: \n")+"options = {\n",r=0;r<t.length;r++){for(var i=0;i<r+1;i++)n+="  ";n+=t[r]+": {\n"}for(var o=0;o<t.length+1;o++)n+="  ";n+=e+"\n";for(var a=0;a<t.length+1;a++){for(var s=0;s<t.length-a;s++)n+="  ";n+="}\n"}return n+"\n\n"}},{key:"print",value:function(t){return Qd(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}},{key:"levenshteinDistance",value:function(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;var n,r,i=[];for(n=0;n<=e.length;n++)i[n]=[n];for(r=0;r<=t.length;r++)i[0][r]=r;for(n=1;n<=e.length;n++)for(r=1;r<=t.length;r++)e.charAt(n-1)==t.charAt(r-1)?i[n][r]=i[n-1][r-1]:i[n][r]=Math.min(i[n-1][r-1]+1,Math.min(i[n][r-1]+1,i[n-1][r]+1));return i[e.length][t.length]}}]),t}(),Um=jd,Qm=Hm,$m=Vm,Jm=xd,Km=qm,Zm=Xm,tb=Gm;t.Activator=Um,t.Alea=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){var e=function(){for(var t=function(){var t=4022871197;return function(e){for(var n=e.toString(),r=0;r<n.length;r++){var i=.02519603282416938*(t+=n.charCodeAt(r));i-=t=i>>>0,t=(i*=t)>>>0,t+=4294967296*(i-=t)}return 2.3283064365386963e-10*(t>>>0)}}(),e=t(" "),n=t(" "),r=t(" "),i=0;i<arguments.length;i++)(e-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(e+=1),(n-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(n+=1),(r-=t(i<0||arguments.length<=i?void 0:arguments[i]))<0&&(r+=1);return[e,n,r]}(t),n=Ef(e,3),r=n[0],i=n[1],o=n[2],a=1,s=function(){var t=2091639*r+2.3283064365386963e-10*a;return r=i,i=o,o=t-(a=0|t)};return s.uint32=function(){return 4294967296*s()},s.fract53=function(){return s()+11102230246251565e-32*(2097152*s()|0)},s.algorithm="Alea",s.seed=t,s.version="0.9",s}(e.length?e:[Sf()])},t.ColorPicker=Qm,t.Configurator=$m,t.DELETE=rf,t.HSVToHex=jm,t.HSVToRGB=Dm,t.Hammer=Jm,t.Popup=Km,t.RGBToHSV=xm,t.RGBToHex=Am,t.VALIDATOR_PRINT_STYLE=Zm,t.Validator=tb,t.addClassName=function(t,e){var n=t.className.split(" "),r=e.split(" ");n=Nh(n).call(n,dy(r).call(r,(function(t){return!oy(n).call(n,t)}))),t.className=n.join(" ")},t.addCssText=function(t,e){for(var n=Pm(e),r=0,i=cm(n);r<i.length;r++){var o=Ef(i[r],2),a=o[0],s=o[1];t.style.setProperty(a,s)}},t.binarySearchCustom=function(t,e,n,r){for(var i=0,o=0,a=t.length-1;o<=a&&i<1e4;){var s=Math.floor((o+a)/2),c=t[s],u=e(void 0===r?c[n]:c[n][r]);if(0==u)return s;-1==u?o=s+1:a=s-1,i++}return-1},t.binarySearchValue=function(t,e,n,r,i){var o,a,s,c,u=0,l=0,h=t.length-1;for(i=null!=i?i:function(t,e){return t==e?0:t<e?-1:1};l<=h&&u<1e4;){if(c=Math.floor(.5*(h+l)),o=t[Math.max(0,c-1)][n],a=t[c][n],s=t[Math.min(t.length-1,c+1)][n],0==i(a,e))return c;if(i(o,e)<0&&i(a,e)>0)return"before"==r?Math.max(0,c-1):c;if(i(a,e)<0&&i(s,e)>0)return"before"==r?c:Math.min(t.length-1,c+1);i(a,e)<0?l=c+1:h=c-1,u++}return-1},t.bridgeObject=Mm,t.copyAndExtendArray=Om,t.copyArray=Tm,t.deepExtend=Em,t.deepObjectAssign=of,t.easingFunctions={linear:function(t){return t},easeInQuad:function(t){return t*t},easeOutQuad:function(t){return t*(2-t)},easeInOutQuad:function(t){return t<.5?2*t*t:(4-2*t)*t-1},easeInCubic:function(t){return t*t*t},easeOutCubic:function(t){return--t*t*t+1},easeInOutCubic:function(t){return t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1},easeInQuart:function(t){return t*t*t*t},easeOutQuart:function(t){return 1- --t*t*t*t},easeInOutQuart:function(t){return t<.5?8*t*t*t*t:1-8*--t*t*t*t},easeInQuint:function(t){return t*t*t*t*t},easeOutQuint:function(t){return 1+--t*t*t*t*t},easeInOutQuint:function(t){return t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t}},t.equalArray=function(t,e){if(t.length!==e.length)return!1;for(var n=0,r=t.length;n<r;n++)if(t[n]!=e[n])return!1;return!0},t.extend=Cm,t.fillIfDefined=function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var i in e)if(void 0!==n[i])if(null===n[i]||"object"!==nh(n[i]))km(e,n,i,r);else{var o=e[i],a=n[i];wm(o)&&wm(a)&&t(o,a,r)}},t.forEach=function(t,e){if(Yh(t))for(var n=t.length,r=0;r<n;r++)e(t[r],r,t);else for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)},t.getAbsoluteLeft=function(t){return t.getBoundingClientRect().left},t.getAbsoluteRight=function(t){return t.getBoundingClientRect().right},t.getAbsoluteTop=function(t){return t.getBoundingClientRect().top},t.getScrollBarWidth=function(){var t=document.createElement("p");t.style.width="100%",t.style.height="200px";var e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);var n=t.offsetWidth;e.style.overflow="scroll";var r=t.offsetWidth;return n==r&&(r=e.clientWidth),document.body.removeChild(e),n-r},t.getTarget=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event,e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode)instanceof Element)?e:null},t.getType=function(t){var e=nh(t);return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Yh(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e},t.hasParent=function(t,e){for(var n=t;n;){if(n===e)return!0;if(!n.parentNode)return!1;n=n.parentNode}return!1},t.hexToHSV=Im,t.hexToRGB=_m,t.insertSort=function(t,e){for(var n=0;n<t.length;n++){var r=t[n],i=void 0;for(i=n;i>0&&e(r,t[i-1])<0;i--)t[i]=t[i-1];t[i]=r}return t},t.isDate=function(t){if(t instanceof Date)return!0;if(bm(t)){if(pm.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1},t.isNumber=mm,t.isObject=wm,t.isString=bm,t.isValidHex=Rm,t.isValidRGB=Nm,t.isValidRGBA=Bm,t.mergeOptions=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=function(t){return null!=t},o=function(t){return null!==t&&"object"===nh(t)};if(!o(t))throw new Error("Parameter mergeTarget must be an object");if(!o(e))throw new Error("Parameter options must be an object");if(!i(n))throw new Error("Parameter option must have a value");if(!o(r))throw new Error("Parameter globalOptions must be an object");var a=e[n],s=o(r)&&!function(t){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(r)?r[n]:void 0,c=s?s.enabled:void 0;if(void 0!==a){if("boolean"==typeof a)return o(t[n])||(t[n]={}),void(t[n].enabled=a);if(null===a&&!o(t[n])){if(!i(s))return;t[n]=lm(s)}if(o(a)){var u=!0;void 0!==a.enabled?u=a.enabled:void 0!==c&&(u=s.enabled),function(t,e,n){o(t[n])||(t[n]={});var r=e[n],i=t[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(i[a]=r[a])}(t,e,n),t[n].enabled=u}}},t.option=Sm,t.overrideOpacity=function(t,e){if(oy(t).call(t,"rgba"))return t;if(oy(t).call(t,"rgb")){var n=t.substr(am(t).call(t,"(")+1).replace(")","").split(",");return"rgba("+n[0]+","+n[1]+","+n[2]+","+e+")"}var r=_m(t);return null==r?t:"rgba("+r.r+","+r.g+","+r.b+","+e+")"},t.parseColor=function(t,e){if(bm(t)){var n=t;if(Nm(n)){var r,i=Jh(r=n.substr(4).substr(0,n.length-5).split(",")).call(r,(function(t){return $y(t)}));n=Am(i[0],i[1],i[2])}if(!0===Rm(n)){var o=Im(n),a={h:o.h,s:.8*o.s,v:Math.min(1,1.02*o.v)},s={h:o.h,s:Math.min(1,1.25*o.s),v:.8*o.v},c=jm(s.h,s.s,s.v),u=jm(a.h,a.s,a.v);return{background:n,border:c,highlight:{background:u,border:c},hover:{background:u,border:c}}}return{background:n,border:n,highlight:{background:n,border:n},hover:{background:n,border:n}}}return e?{background:t.background||e.background,border:t.border||e.border,highlight:bm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:bm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}:{background:t.background||void 0,border:t.border||void 0,highlight:bm(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:bm(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}},t.preventDefault=function(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},t.pureDeepObjectAssign=function(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return of.apply(void 0,Nh(e=[{},t]).call(e,r))},t.recursiveDOMDelete=function t(e){if(e)for(;!0===e.hasChildNodes();){var n=e.firstChild;n&&(t(n),e.removeChild(n))}},t.removeClassName=function(t,e){var n=t.className.split(" "),r=e.split(" ");n=dy(n).call(n,(function(t){return!oy(r).call(r,t)})),t.className=n.join(" ")},t.removeCssText=function(t,e){for(var n=Pm(e),r=0,i=tf(n);r<i.length;r++){var o=i[r];t.style.removeProperty(o)}},t.selectiveBridgeObject=function(t,e){if(null!==e&&"object"===nh(e)){for(var n=lm(e),r=0;r<t.length;r++)Object.prototype.hasOwnProperty.call(e,t[r])&&"object"==nh(e[t[r]])&&(n[t[r]]=Mm(e[t[r]]));return n}return null},t.selectiveDeepExtend=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Yh(n))throw new TypeError("Arrays are not supported by deepExtend");for(var i=0;i<t.length;i++){var o=t[i];if(Object.prototype.hasOwnProperty.call(n,o))if(n[o]&&n[o].constructor===Object)void 0===e[o]&&(e[o]={}),e[o].constructor===Object?Em(e[o],n[o],!1,r):km(e,n,o,r);else{if(Yh(n[o]))throw new TypeError("Arrays are not supported by deepExtend");km(e,n,o,r)}}return e},t.selectiveExtend=function(t,e){if(!Yh(t))throw new Error("Array with property names expected as first argument");for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];for(var o=0,a=r;o<a.length;o++)for(var s=a[o],c=0;c<t.length;c++){var u=t[c];s&&Object.prototype.hasOwnProperty.call(s,u)&&(e[u]=s[u])}return e},t.selectiveNotDeepExtend=function(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Yh(n))throw new TypeError("Arrays are not supported by deepExtend");for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&!oy(t).call(t,i))if(n[i]&&n[i].constructor===Object)void 0===e[i]&&(e[i]={}),e[i].constructor===Object?Em(e[i],n[i]):km(e,n,i,r);else if(Yh(n[i])){e[i]=[];for(var o=0;o<n[i].length;o++)e[i].push(n[i][o])}else km(e,n,i,r);return e},t.throttle=function(t){var e=!1;return function(){e||(e=!0,requestAnimationFrame((function(){e=!1,t()})))}},t.toArray=Fm,t.topMost=function(t,e){var n;Yh(e)||(e=[e]);var r,i=hm(t);try{for(i.s();!(r=i.n()).done;){var o=r.value;if(o){n=o[e[0]];for(var a=1;a<e.length;a++)n&&(n=n[e[a]]);if(void 0!==n)break}}}catch(t){i.e(t)}finally{i.f()}return n},t.updateProperty=function(t,e,n){return t[e]!==n&&(t[e]=n,!0)}}));
//# sourceMappingURL=vis-util.min.js.map
