{"version": 3, "file": "vis-data.js", "sources": ["../../src/data-pipe.ts", "../../src/data-interface.ts", "../../src/queue.ts", "../../src/data-set-part.ts", "../../src/data-stream.ts", "../../src/data-set.ts", "../../src/data-view.ts", "../../src/data-set-check.ts", "../../src/data-view-check.ts"], "sourcesContent": ["import { DataInterface, EventCallbacks, PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * This interface is used to control the pipe.\n */\nexport interface DataPipe {\n  /**\n   * Take all items from the source data set or data view, transform them as\n   * configured and update the target data set.\n   */\n  all(): this;\n\n  /**\n   * Start observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   *\n   * @remarks\n   * The current content of the source data set will be ignored. If you for\n   * example want to process all the items that are already there use:\n   * `pipe.all().start()`.\n   */\n  start(): this;\n\n  /**\n   * Stop observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   */\n  stop(): this;\n}\n\n/**\n * This interface is used to construct the pipe.\n */\nexport type DataPipeFactory = InstanceType<typeof DataPipeUnderConstruction>;\n\n/**\n * Create new data pipe.\n *\n * @param from - The source data set or data view.\n * @remarks\n * Example usage:\n * ```typescript\n * interface AppItem {\n *   whoami: string;\n *   appData: unknown;\n *   visData: VisItem;\n * }\n * interface VisItem {\n *   id: number;\n *   label: string;\n *   color: string;\n *   x: number;\n *   y: number;\n * }\n *\n * const ds1 = new DataSet<AppItem, \"whoami\">([], { fieldId: \"whoami\" });\n * const ds2 = new DataSet<VisItem, \"id\">();\n *\n * const pipe = createNewDataPipeFrom(ds1)\n *   .filter((item): boolean => item.enabled === true)\n *   .map<VisItem, \"id\">((item): VisItem => item.visData)\n *   .to(ds2);\n *\n * pipe.start();\n * ```\n * @returns A factory whose methods can be used to configure the pipe.\n */\nexport function createNewDataPipeFrom<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n>(from: DataInterface<SI, SP>): DataPipeUnderConstruction<SI, SP> {\n  return new DataPipeUnderConstruction(from);\n}\n\ntype Transformer<T> = (input: T[]) => T[];\n\n/**\n * Internal implementation of the pipe. This should be accessible only through\n * `createNewDataPipeFrom` from the outside.\n *\n * @typeParam SI - Source item type.\n * @typeParam SP - Source item type's id property name.\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass SimpleDataPipe<\n  SI extends PartItem<SP>,\n  SP extends string,\n  TI extends PartItem<TP>,\n  TP extends string\n> implements DataPipe\n{\n  /**\n   * Bound listeners for use with `DataInterface['on' | 'off']`.\n   */\n  private readonly _listeners: EventCallbacks<SI, SP> = {\n    add: this._add.bind(this),\n    remove: this._remove.bind(this),\n    update: this._update.bind(this),\n  };\n\n  /**\n   * Create a new data pipe.\n   *\n   * @param _source - The data set or data view that will be observed.\n   * @param _transformers - An array of transforming functions to be used to\n   * filter or transform the items in the pipe.\n   * @param _target - The data set or data view that will receive the items.\n   */\n  public constructor(\n    private readonly _source: DataInterface<SI, SP>,\n    private readonly _transformers: readonly Transformer<unknown>[],\n    private readonly _target: DataSet<TI, TP>\n  ) {}\n\n  /** @inheritDoc */\n  public all(): this {\n    this._target.update(this._transformItems(this._source.get()));\n    return this;\n  }\n\n  /** @inheritDoc */\n  public start(): this {\n    this._source.on(\"add\", this._listeners.add);\n    this._source.on(\"remove\", this._listeners.remove);\n    this._source.on(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /** @inheritDoc */\n  public stop(): this {\n    this._source.off(\"add\", this._listeners.add);\n    this._source.off(\"remove\", this._listeners.remove);\n    this._source.off(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /**\n   * Apply the transformers to the items.\n   *\n   * @param items - The items to be transformed.\n   * @returns The transformed items.\n   */\n  private _transformItems(items: unknown[]): any[] {\n    return this._transformers.reduce((items, transform): unknown[] => {\n      return transform(items);\n    }, items);\n  }\n\n  /**\n   * Handle an add event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the added items.\n   */\n  private _add(\n    _name: Parameters<EventCallbacks<SI, SP>[\"add\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"add\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.add(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle an update event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the updated items.\n   */\n  private _update(\n    _name: Parameters<EventCallbacks<SI, SP>[\"update\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"update\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.update(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle a remove event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the data of the removed items.\n   */\n  private _remove(\n    _name: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.remove(this._transformItems(payload.oldData));\n  }\n}\n\n/**\n * Internal implementation of the pipe factory. This should be accessible\n * only through `createNewDataPipeFrom` from the outside.\n *\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass DataPipeUnderConstruction<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n> {\n  /**\n   * Array transformers used to transform items within the pipe. This is typed\n   * as any for the sake of simplicity.\n   */\n  private readonly _transformers: Transformer<any>[] = [];\n\n  /**\n   * Create a new data pipe factory. This is an internal constructor that\n   * should never be called from outside of this file.\n   *\n   * @param _source - The source data set or data view for this pipe.\n   */\n  public constructor(private readonly _source: DataInterface<SI, SP>) {}\n\n  /**\n   * Filter the items.\n   *\n   * @param callback - A filtering function that returns true if given item\n   * should be piped and false if not.\n   * @returns This factory for further configuration.\n   */\n  public filter(\n    callback: (item: SI) => boolean\n  ): DataPipeUnderConstruction<SI, SP> {\n    this._transformers.push((input): unknown[] => input.filter(callback));\n    return this;\n  }\n\n  /**\n   * Map each source item to a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * corresponding mapped item.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public map<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.map(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Map each source item to zero or more items of a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * an array of corresponding mapped items.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public flatMap<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI[]\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.flatMap(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Connect this pipe to given data set.\n   *\n   * @param target - The data set that will receive the items from this pipe.\n   * @returns The pipe connected between given data sets and performing\n   * configured transformation on the processed items.\n   */\n  public to(target: DataSet<SI, SP>): DataPipe {\n    return new SimpleDataPipe(this._source, this._transformers, target);\n  }\n}\n", "import { Assignable } from \"vis-util/esnext\";\nimport { DataSet } from \"./data-set\";\nimport { DataStream } from \"./data-stream\";\n\ntype ValueOf<T> = T[keyof T];\n\n/** Valid id type. */\nexport type Id = number | string;\n/** Nullable id type. */\nexport type OptId = undefined | null | Id;\n/**\n * Determine whether a value can be used as an id.\n *\n * @param value - Input value of unknown type.\n * @returns True if the value is valid id, false otherwise.\n */\nexport function isId(value: unknown): value is Id {\n  return typeof value === \"string\" || typeof value === \"number\";\n}\n\n/**\n * Make an object deeply partial.\n */\nexport type DeepPartial<T> = T extends any[] | Function | Node\n  ? T\n  : T extends object\n  ? { [key in keyof T]?: DeepPartial<T[key]> }\n  : T;\n\n/**\n * An item that may ({@link Id}) or may not (absent, undefined or null) have an id property.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type PartItem<IdProp extends string> = Partial<Record<IdProp, OptId>>;\n/**\n * An item that has a property containing an id and all other required properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type FullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Item & Record<IdProp, Id>;\n/**\n * An item that has a property containing an id and optionally other properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type UpdateItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Assignable<FullItem<Item, IdProp>> & Record<IdProp, Id>;\n\n/**\n * Test whether an item has an id (is a {@link FullItem}).\n *\n * @param item - The item to be tested.\n * @param idProp - Name of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns True if this value is a {@link FullItem}, false otherwise.\n */\nexport function isFullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n>(item: Item, idProp: IdProp): item is FullItem<Item, IdProp> {\n  return item[idProp] != null;\n}\n\n/** Add event payload. */\nexport interface AddEventPayload {\n  /** Ids of added items. */\n  items: Id[];\n}\n/** Update event payload. */\nexport interface UpdateEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of updated items. */\n  items: Id[];\n  /** Items as they were before this update. */\n  oldData: FullItem<Item, IdProp>[];\n  /**\n   * Items as they are now.\n   *\n   * @deprecated Just get the data from the data set or data view.\n   */\n  data: FullItem<Item, IdProp>[];\n}\n/** Remove event payload. */\nexport interface RemoveEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of removed items. */\n  items: Id[];\n  /** Items as they were before their removal. */\n  oldData: FullItem<Item, IdProp>[];\n}\n\n/**\n * Map of event payload types (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloads<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  add: AddEventPayload;\n  update: UpdateEventPayload<Item, IdProp>;\n  remove: RemoveEventPayload<Item, IdProp>;\n}\n/**\n * Map of event payload types including any event (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloadsWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventPayloads<Item, IdProp> {\n  \"*\": ValueOf<EventPayloads<Item, IdProp>>;\n}\n\n/**\n * Map of event callback types (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacks<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  add(name: \"add\", payload: AddEventPayload | null, senderId?: Id | null): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  update(\n    name: \"update\",\n    payload: UpdateEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  remove(\n    name: \"remove\",\n    payload: RemoveEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n}\n/**\n * Map of event callback types including any event (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacksWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventCallbacks<Item, IdProp> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  \"*\"<N extends keyof EventCallbacks<Item, IdProp>>(\n    name: N,\n    payload: EventPayloads<Item, IdProp>[N],\n    senderId?: Id | null\n  ): void;\n}\n\n/** Available event names. */\nexport type EventName = keyof EventPayloads<never, \"\">;\n/** Available event names and '*' to listen for all. */\nexport type EventNameWithAny = keyof EventPayloadsWithAny<never, \"\">;\n\n/**\n * Data interface order parameter.\n * - A string value determines which property will be used for sorting (using < and > operators for numeric comparison).\n * - A function will be used the same way as in Array.sort.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceOrder<Item> =\n  | keyof Item\n  | ((a: Item, b: Item) => number);\n\n/**\n * Data interface get options (return type independent).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsBase<Item> {\n  /**\n   * An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items.\n   *\n   * @remarks\n   * Warning**: There is no TypeScript support for this.\n   */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface get options (returns a single item or an array).\n *\n * @remarks\n * Whether an item or and array of items is returned is determined by the type of the id(s) argument.\n * If an array of ids is requested an array of items will be returned.\n * If a single id is requested a single item (or null if the id doesn't correspond to any item) will be returned.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsArray<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as a single item (if invoked with an id) or an array of items (if invoked with an array of ids). */\n  returnType?: undefined | \"Array\";\n}\n/**\n * Data interface get options (returns an object).\n *\n * @remarks\n * The returned object has ids as keys and items as values of corresponding ids.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsObject<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as an object map (id → item). */\n  returnType: \"Object\";\n}\n/**\n * Data interface get options (returns single item, an array or object).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceGetOptions<Item> =\n  | DataInterfaceGetOptionsArray<Item>\n  | DataInterfaceGetOptionsObject<Item>;\n\n/**\n * Data interface get ids options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetIdsOptions<Item> {\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface for each options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceForEachOptions<Item> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface map oprions.\n *\n * @typeParam Original - The original item type in the data.\n * @typeParam Mapped - The type after mapping.\n */\nexport interface DataInterfaceMapOptions<Original, Mapped> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Original) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Mapped>;\n}\n\n/**\n * Common interface for data sets and data view.\n *\n * @typeParam Item - Item type that may or may not have an id (missing ids will be generated upon insertion).\n * @typeParam IdProp - Name of the property on the Item type that contains the id.\n */\nexport interface DataInterface<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n> {\n  /** The number of items. */\n  length: number;\n\n  /** The key of id property. */\n  idProp: IdProp;\n\n  /**\n   * Add a universal event listener.\n   *\n   * @remarks The `*` event is triggered when any of the events `add`, `update`, and `remove` occurs.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Add an `add` event listener.\n   *\n   * @remarks The `add` event is triggered when an item or a set of items is added, or when an item is updated while not yet existing.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Add a `remove` event listener.\n   *\n   * @remarks The `remove` event is triggered when an item or a set of items is removed.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Add an `update` event listener.\n   *\n   * @remarks The `update` event is triggered when an existing item or a set of existing items is updated.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Remove a universal event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Remove an `add` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Remove a `remove` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Remove an `update` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Get all the items.\n   *\n   * @returns An array containing all the items.\n   */\n  get(): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items.\n   */\n  get(options: DataInterfaceGetOptionsArray<Item>): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items or if requested an object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(id: Id): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item if found or null otherwise. If requested an object map with 0 to 1 items.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(ids: Id[]): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   * If requested an object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get items.\n   *\n   * @param ids - Id or ids to be returned.\n   * @param options - Options to specify iteration details.\n   * @returns The items (format is determined by ids (single or array) and the options.\n   */\n  get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /**\n   * Get the DataSet to which the instance implementing this interface is connected.\n   * In case there is a chain of multiple DataViews, the root DataSet of this chain is returned.\n   *\n   * @returns The data set that actually contains the data.\n   */\n  getDataSet(): DataSet<Item, IdProp>;\n\n  /**\n   * Get ids of items.\n   *\n   * @remarks\n   * No guarantee is given about the order of returned ids unless an ordering function is supplied.\n   * @param options - Additional configuration.\n   * @returns An array of requested ids.\n   */\n  getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[];\n\n  /**\n   * Execute a callback function for each item.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration unless an ordering function is supplied.\n   * @param callback - Executed in similar fashion to Array.forEach callback, but instead of item, index, array receives item, id.\n   * @param options - Options to specify iteration details.\n   */\n  forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void;\n\n  /**\n   * Map each item into different item and return them as an array.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration even if ordering function is supplied (the items are sorted after the mapping).\n   * @param callback - Array.map-like callback, but only with the first two params.\n   * @param options - Options to specify iteration details.\n   * @returns The mapped items.\n   */\n  map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[];\n\n  /**\n   * Stream.\n   *\n   * @param ids - Ids of the items to be included in this stream (missing are ignored), all if omitted.\n   * @returns The data stream for this data set.\n   */\n  stream(ids?: Iterable<Id>): DataStream<Item>;\n}\n", "/** Queue configuration object. */\nexport interface QueueOptions {\n  /** The queue will be flushed automatically after an inactivity of this delay in milliseconds. By default there is no automatic flushing (`null`). */\n  delay?: null | number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value is `Infinity`. */\n  max?: number;\n}\n/**\n * Queue extending options.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport interface QueueExtendOptions<T> {\n  /** A list with method names of the methods on the object to be replaced with queued ones. */\n  replace: T[];\n  /** When provided, the queue will be flushed automatically after an inactivity of this delay in milliseconds. Default value is null. */\n  delay?: number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value of max is Infinity. */\n  max?: number;\n}\n/**\n * Queue call entry.\n * - A function to be executed.\n * - An object with function, args, context (like function.bind(context, ...args)).\n */\ntype QueueCallEntry =\n  | Function\n  | {\n      fn: Function;\n      args: unknown[];\n    }\n  | {\n      fn: Function;\n      args: unknown[];\n      context: unknown;\n    };\n\ninterface QueueExtended<O> {\n  object: O;\n  methods: {\n    name: string;\n    original: unknown;\n  }[];\n}\n\n/**\n * A queue.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport class Queue<T = never> {\n  /** Delay in milliseconds. If defined the queue will be periodically flushed. */\n  public delay: null | number;\n  /** Maximum number of entries in the queue before it will be flushed. */\n  public max: number;\n\n  private readonly _queue: {\n    fn: Function;\n    args?: unknown[];\n    context?: unknown;\n  }[] = [];\n\n  private _timeout: ReturnType<typeof setTimeout> | null = null;\n  private _extended: null | QueueExtended<T> = null;\n\n  /**\n   * Construct a new Queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public constructor(options?: QueueOptions) {\n    // options\n    this.delay = null;\n    this.max = Infinity;\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Update the configuration of the queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public setOptions(options?: QueueOptions): void {\n    if (options && typeof options.delay !== \"undefined\") {\n      this.delay = options.delay;\n    }\n    if (options && typeof options.max !== \"undefined\") {\n      this.max = options.max;\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Extend an object with queuing functionality.\n   * The object will be extended with a function flush, and the methods provided in options.replace will be replaced with queued ones.\n   *\n   * @param object - The object to be extended.\n   * @param options - Additional options.\n   * @returns The created queue.\n   */\n  public static extend<O extends { flush?: () => void }, K extends string>(\n    object: O,\n    options: QueueExtendOptions<K>\n  ): Queue<O> {\n    const queue = new Queue<O>(options);\n\n    if (object.flush !== undefined) {\n      throw new Error(\"Target object already has a property flush\");\n    }\n    object.flush = (): void => {\n      queue.flush();\n    };\n\n    const methods: QueueExtended<O>[\"methods\"] = [\n      {\n        name: \"flush\",\n        original: undefined,\n      },\n    ];\n\n    if (options && options.replace) {\n      for (let i = 0; i < options.replace.length; i++) {\n        const name = options.replace[i];\n        methods.push({\n          name: name,\n          // @TODO: better solution?\n          original: (object as unknown as Record<K, () => void>)[name],\n        });\n        // @TODO: better solution?\n        queue.replace(object as unknown as Record<K, () => void>, name);\n      }\n    }\n\n    queue._extended = {\n      object: object,\n      methods: methods,\n    };\n\n    return queue;\n  }\n\n  /**\n   * Destroy the queue. The queue will first flush all queued actions, and in case it has extended an object, will restore the original object.\n   */\n  public destroy(): void {\n    this.flush();\n\n    if (this._extended) {\n      const object = this._extended.object;\n      const methods = this._extended.methods;\n      for (let i = 0; i < methods.length; i++) {\n        const method = methods[i];\n        if (method.original) {\n          // @TODO: better solution?\n          (object as any)[method.name] = method.original;\n        } else {\n          // @TODO: better solution?\n          delete (object as any)[method.name];\n        }\n      }\n      this._extended = null;\n    }\n  }\n\n  /**\n   * Replace a method on an object with a queued version.\n   *\n   * @param object - Object having the method.\n   * @param method - The method name.\n   */\n  public replace<M extends string>(\n    object: Record<M, () => void>,\n    method: M\n  ): void {\n    /* eslint-disable-next-line @typescript-eslint/no-this-alias -- Function this is necessary in the function bellow, so class this has to be saved into a variable here. */\n    const me = this;\n    const original = object[method];\n    if (!original) {\n      throw new Error(\"Method \" + method + \" undefined\");\n    }\n\n    object[method] = function (...args: unknown[]): void {\n      // add this call to the queue\n      me.queue({\n        args: args,\n        fn: original,\n        context: this,\n      });\n    };\n  }\n\n  /**\n   * Queue a call.\n   *\n   * @param entry - The function or entry to be queued.\n   */\n  public queue(entry: QueueCallEntry): void {\n    if (typeof entry === \"function\") {\n      this._queue.push({ fn: entry });\n    } else {\n      this._queue.push(entry);\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Check whether the queue needs to be flushed.\n   */\n  private _flushIfNeeded(): void {\n    // flush when the maximum is exceeded.\n    if (this._queue.length > this.max) {\n      this.flush();\n    }\n\n    // flush after a period of inactivity when a delay is configured\n    if (this._timeout != null) {\n      clearTimeout(this._timeout);\n      this._timeout = null;\n    }\n    if (this.queue.length > 0 && typeof this.delay === \"number\") {\n      this._timeout = setTimeout((): void => {\n        this.flush();\n      }, this.delay);\n    }\n  }\n\n  /**\n   * Flush all queued calls\n   */\n  public flush(): void {\n    this._queue.splice(0).forEach((entry): void => {\n      entry.fn.apply(entry.context || entry.fn, entry.args || []);\n    });\n  }\n}\n", "import {\n  DataInterface,\n  EventCallbacksWithAny,\n  EventName,\n  EventNameWithAny,\n  EventPayloads,\n  Id,\n  PartItem,\n} from \"./data-interface\";\n\ntype EventSubscribers<Item extends PartItem<IdProp>, IdProp extends string> = {\n  [Name in keyof EventCallbacksWithAny<Item, IdProp>]: (...args: any[]) => void;\n};\n\n/**\n * {@link DataSet} code that can be reused in {@link DataView} or other similar implementations of {@link DataInterface}.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport abstract class DataSetPart<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> implements Pick<DataInterface<Item, IdProp>, \"on\" | \"off\">\n{\n  private readonly _subscribers: {\n    [Name in EventNameWithAny]: EventSubscribers<Item, IdProp>[Name][];\n  } = {\n    \"*\": [],\n    add: [],\n    remove: [],\n    update: [],\n  };\n\n  protected _trigger(\n    event: \"add\",\n    payload: EventPayloads<Item, IdProp>[\"add\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"update\",\n    payload: EventPayloads<Item, IdProp>[\"update\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"remove\",\n    payload: EventPayloads<Item, IdProp>[\"remove\"],\n    senderId?: Id | null\n  ): void;\n  /**\n   * Trigger an event\n   *\n   * @param event - Event name.\n   * @param payload - Event payload.\n   * @param senderId - Id of the sender.\n   */\n  protected _trigger<Name extends EventName>(\n    event: Name,\n    payload: EventPayloads<Item, IdProp>[Name],\n    senderId?: Id | null\n  ): void {\n    if ((event as string) === \"*\") {\n      throw new Error(\"Cannot trigger event *\");\n    }\n\n    [...this._subscribers[event], ...this._subscribers[\"*\"]].forEach(\n      (subscriber): void => {\n        subscriber(event, payload, senderId != null ? senderId : null);\n      }\n    );\n  }\n\n  /** @inheritDoc */\n  public on(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Subscribe to an event, add an event listener.\n   *\n   * @remarks Non-function callbacks are ignored.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public on<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    if (typeof callback === \"function\") {\n      this._subscribers[event].push(callback);\n    }\n    // @TODO: Maybe throw for invalid callbacks?\n  }\n\n  /** @inheritDoc */\n  public off(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Unsubscribe from an event, remove an event listener.\n   *\n   * @remarks If the same callback was subscribed more than once **all** occurences will be removed.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public off<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    this._subscribers[event] = this._subscribers[event].filter(\n      (subscriber): boolean => subscriber !== callback\n    );\n  }\n\n  /**\n   * @deprecated Use on instead (PS: DataView.subscribe === DataView.on).\n   */\n  public subscribe: DataSetPart<Item, IdProp>[\"on\"] = DataSetPart.prototype.on;\n  /**\n   * @deprecated Use off instead (PS: DataView.unsubscribe === DataView.off).\n   */\n  public unsubscribe: DataSetPart<Item, IdProp>[\"off\"] =\n    DataSetPart.prototype.off;\n\n  /* develblock:start */\n  public get testLeakSubscribers(): any {\n    return this._subscribers;\n  }\n  /* develblock:end */\n}\n", "import { Id } from \"./data-interface\";\n\n/**\n * Data stream\n *\n * @remarks\n * {@link DataStream} offers an always up to date stream of items from a {@link DataSet} or {@link DataView}.\n * That means that the stream is evaluated at the time of iteration, conversion to another data type or when {@link cache} is called, not when the {@link DataStream} was created.\n * Multiple invocations of for example {@link toItemArray} may yield different results (if the data source like for example {@link DataSet} gets modified).\n * @typeParam Item - The item type this stream is going to work with.\n */\nexport class DataStream<Item> implements Iterable<[Id, Item]> {\n  private readonly _pairs: Iterable<[Id, Item]>;\n\n  /**\n   * Create a new data stream.\n   *\n   * @param pairs - The id, item pairs.\n   */\n  public constructor(pairs: Iterable<[Id, Item]>) {\n    this._pairs = pairs;\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *entries(): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of keys in the stream.\n   */\n  public *keys(): IterableIterator<Id> {\n    for (const [id] of this._pairs) {\n      yield id;\n    }\n  }\n\n  /**\n   * Return an iterable of values in the stream.\n   */\n  public *values(): IterableIterator<Item> {\n    for (const [, item] of this._pairs) {\n      yield item;\n    }\n  }\n\n  /**\n   * Return an array containing all the ids in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all ids from this stream.\n   */\n  public toIdArray(): Id[] {\n    return [...this._pairs].map((pair): Id => pair[0]);\n  }\n\n  /**\n   * Return an array containing all the items in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all items from this stream.\n   */\n  public toItemArray(): Item[] {\n    return [...this._pairs].map((pair): Item => pair[1]);\n  }\n\n  /**\n   * Return an array containing all the entries in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all entries from this stream.\n   */\n  public toEntryArray(): [Id, Item][] {\n    return [...this._pairs];\n  }\n\n  /**\n   * Return an object map containing all the items in this stream accessible by ids.\n   *\n   * @remarks\n   * In case of duplicate ids (coerced to string so `7 == '7'`) the last encoutered appears in the returned object.\n   * @returns The object map of all id → item pairs from this stream.\n   */\n  public toObjectMap(): Record<Id, Item> {\n    const map: Record<Id, Item> = Object.create(null);\n    for (const [id, item] of this._pairs) {\n      map[id] = item;\n    }\n    return map;\n  }\n\n  /**\n   * Return a map containing all the items in this stream accessible by ids.\n   *\n   * @returns The map of all id → item pairs from this stream.\n   */\n  public toMap(): Map<Id, Item> {\n    return new Map(this._pairs);\n  }\n\n  /**\n   * Return a set containing all the (unique) ids in this stream.\n   *\n   * @returns The set of all ids from this stream.\n   */\n  public toIdSet(): Set<Id> {\n    return new Set(this.toIdArray());\n  }\n\n  /**\n   * Return a set containing all the (unique) items in this stream.\n   *\n   * @returns The set of all items from this stream.\n   */\n  public toItemSet(): Set<Item> {\n    return new Set(this.toItemArray());\n  }\n\n  /**\n   * Cache the items from this stream.\n   *\n   * @remarks\n   * This method allows for items to be fetched immediatelly and used (possibly multiple times) later.\n   * It can also be used to optimize performance as {@link DataStream} would otherwise reevaluate everything upon each iteration.\n   *\n   * ## Example\n   * ```javascript\n   * const ds = new DataSet([…])\n   *\n   * const cachedStream = ds.stream()\n   *   .filter(…)\n   *   .sort(…)\n   *   .map(…)\n   *   .cached(…) // Data are fetched, processed and cached here.\n   *\n   * ds.clear()\n   * chachedStream // Still has all the items.\n   * ```\n   * @returns A new {@link DataStream} with cached items (detached from the original {@link DataSet}).\n   */\n  public cache(): DataStream<Item> {\n    return new DataStream([...this._pairs]);\n  }\n\n  /**\n   * Get the distinct values of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @typeParam T - The type of the distinct value.\n   * @returns A set of all distinct properties.\n   */\n  public distinct<T>(callback: (item: Item, id: Id) => T): Set<T> {\n    const set = new Set<T>();\n\n    for (const [id, item] of this._pairs) {\n      set.add(callback(item, id));\n    }\n\n    return set;\n  }\n\n  /**\n   * Filter the items of the stream.\n   *\n   * @param callback - The function that decides whether an item will be included.\n   * @returns A new data stream with the filtered items.\n   */\n  public filter(callback: (item: Item, id: Id) => boolean): DataStream<Item> {\n    const pairs = this._pairs;\n    return new DataStream<Item>({\n      *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n        for (const [id, item] of pairs) {\n          if (callback(item, id)) {\n            yield [id, item];\n          }\n        }\n      },\n    });\n  }\n\n  /**\n   * Execute a callback for each item of the stream.\n   *\n   * @param callback - The function that will be invoked for each item.\n   */\n  public forEach(callback: (item: Item, id: Id) => boolean): void {\n    for (const [id, item] of this._pairs) {\n      callback(item, id);\n    }\n  }\n\n  /**\n   * Map the items into a different type.\n   *\n   * @param callback - The function that does the conversion.\n   * @typeParam Mapped - The type of the item after mapping.\n   * @returns A new data stream with the mapped items.\n   */\n  public map<Mapped>(\n    callback: (item: Item, id: Id) => Mapped\n  ): DataStream<Mapped> {\n    const pairs = this._pairs;\n    return new DataStream<Mapped>({\n      *[Symbol.iterator](): IterableIterator<[Id, Mapped]> {\n        for (const [id, item] of pairs) {\n          yield [id, callback(item, id)];\n        }\n      },\n    });\n  }\n\n  /**\n   * Get the item with the maximum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the maximum if found otherwise null.\n   */\n  public max(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let maxItem: Item = curr.value[1];\n    let maxValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value > maxValue) {\n        maxValue = value;\n        maxItem = item;\n      }\n    }\n\n    return maxItem;\n  }\n\n  /**\n   * Get the item with the minimum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the minimum if found otherwise null.\n   */\n  public min(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let minItem: Item = curr.value[1];\n    let minValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value < minValue) {\n        minValue = value;\n        minItem = item;\n      }\n    }\n\n    return minItem;\n  }\n\n  /**\n   * Reduce the items into a single value.\n   *\n   * @param callback - The function that does the reduction.\n   * @param accumulator - The initial value of the accumulator.\n   * @typeParam T - The type of the accumulated value.\n   * @returns The reduced value.\n   */\n  public reduce<T>(\n    callback: (accumulator: T, item: Item, id: Id) => T,\n    accumulator: T\n  ): T {\n    for (const [id, item] of this._pairs) {\n      accumulator = callback(accumulator, item, id);\n    }\n    return accumulator;\n  }\n\n  /**\n   * Sort the items.\n   *\n   * @param callback - Item comparator.\n   * @returns A new stream with sorted items.\n   */\n  public sort(\n    callback: (itemA: Item, itemB: Item, idA: Id, idB: Id) => number\n  ): DataStream<Item> {\n    return new DataStream({\n      [Symbol.iterator]: (): IterableIterator<[Id, Item]> =>\n        [...this._pairs]\n          .sort(([idA, itemA], [idB, itemB]): number =>\n            callback(itemA, itemB, idA, idB)\n          )\n          [Symbol.iterator](),\n    });\n  }\n}\n", "import { v4 as uuid4 } from \"uuid\";\nimport { pureDeepObjectAssign } from \"vis-util/esnext\";\n\nimport {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  DataInterfaceOrder,\n  DeepPartial,\n  EventPayloads,\n  FullItem,\n  Id,\n  OptId,\n  PartItem,\n  UpdateItem,\n  isId,\n} from \"./data-interface\";\n\nimport { Queue, QueueOptions } from \"./queue\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Initial DataSet configuration object.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataSetInitialOptions<IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /**\n   * Queue data changes ('add', 'update', 'remove') and flush them at once. The queue can be flushed manually by calling `DataSet.flush()`, or can be flushed after a configured delay or maximum number of entries.\n   *\n   * When queue is true, a queue is created with default options. Options can be specified by providing an object.\n   */\n  queue?: QueueOptions | false;\n}\n/** DataSet configuration object. */\nexport interface DataSetOptions {\n  /**\n   * Queue configuration object or false if no queue should be used.\n   *\n   * - If false and there was a queue before it will be flushed and then removed.\n   * - If {@link QueueOptions} the existing queue will be reconfigured or a new queue will be created.\n   */\n  queue?: Queue | QueueOptions | false;\n}\n\n/**\n * Add an id to given item if it doesn't have one already.\n *\n * @remarks\n * The item will be modified.\n * @param item - The item that will have an id after a call to this function.\n * @param idProp - The key of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns true\n */\nfunction ensureFullItem<Item extends PartItem<IdProp>, IdProp extends string>(\n  item: Item,\n  idProp: IdProp\n): FullItem<Item, IdProp> {\n  if (item[idProp] == null) {\n    // generate an id\n    item[idProp] = uuid4() as any;\n  }\n\n  return item as FullItem<Item, IdProp>;\n}\n\n/**\n * # DataSet\n *\n * Vis.js comes with a flexible DataSet, which can be used to hold and\n * manipulate unstructured data and listen for changes in the data. The DataSet\n * is key/value based. Data items can be added, updated and removed from the\n * DataSet, and one can subscribe to changes in the DataSet. The data in the\n * DataSet can be filtered and ordered. Data can be normalized when appending it\n * to the DataSet as well.\n *\n * ## Example\n *\n * The following example shows how to use a DataSet.\n *\n * ```javascript\n * // create a DataSet\n * var options = {};\n * var data = new vis.DataSet(options);\n *\n * // add items\n * // note that the data items can contain different properties and data formats\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // subscribe to any change in the DataSet\n * data.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an existing item\n * data.update({id: 2, group: 1});\n *\n * // remove an item\n * data.remove(4);\n *\n * // get all ids\n * var ids = data.getIds();\n * console.log('ids', ids);\n *\n * // get a specific item\n * var item1 = data.get(1);\n * console.log('item1', item1);\n *\n * // retrieve a filtered subset of the data\n * var items = data.get({\n *   filter: function (item) {\n *     return item.group == 1;\n *   }\n * });\n * console.log('filtered items', items);\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataSet<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** Flush all queued calls. */\n  public flush?: () => void;\n  /** @inheritDoc */\n  public length: number;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this._idProp;\n  }\n\n  private readonly _options: DataSetInitialOptions<IdProp>;\n  private readonly _data: Map<Id, FullItem<Item, IdProp>>;\n  private readonly _idProp: IdProp;\n  private _queue: Queue<this> | null = null;\n\n  /**\n   * @param options - DataSet configuration.\n   */\n  public constructor(options?: DataSetInitialOptions<IdProp>);\n  /**\n   * @param data - An initial set of items for the new instance.\n   * @param options - DataSet configuration.\n   */\n  public constructor(data: Item[], options?: DataSetInitialOptions<IdProp>);\n  /**\n   * Construct a new DataSet.\n   *\n   * @param data - Initial data or options.\n   * @param options - Options (type error if data is also options).\n   */\n  public constructor(\n    data?: Item[] | DataSetInitialOptions<IdProp>,\n    options?: DataSetInitialOptions<IdProp>\n  ) {\n    super();\n\n    // correctly read optional arguments\n    if (data && !Array.isArray(data)) {\n      options = data;\n      data = [];\n    }\n\n    this._options = options || {};\n    this._data = new Map(); // map with data indexed by id\n    this.length = 0; // number of items in the DataSet\n    this._idProp = this._options.fieldId || (\"id\" as IdProp); // name of the field containing id\n\n    // add initial data when provided\n    if (data && data.length) {\n      this.add(data);\n    }\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Set new options.\n   *\n   * @param options - The new options.\n   */\n  public setOptions(options?: DataSetOptions): void {\n    if (options && options.queue !== undefined) {\n      if (options.queue === false) {\n        // delete queue if loaded\n        if (this._queue) {\n          this._queue.destroy();\n          this._queue = null;\n        }\n      } else {\n        // create queue and update its options\n        if (!this._queue) {\n          this._queue = Queue.extend(this, {\n            replace: [\"add\", \"update\", \"remove\"],\n          });\n        }\n\n        if (options.queue && typeof options.queue === \"object\") {\n          this._queue.setOptions(options.queue);\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a data item or an array with items.\n   *\n   * After the items are added to the DataSet, the DataSet will trigger an event `add`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet()\n   *\n   * // add items\n   * const ids = data.add([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { text: 'item without an id' }\n   * ])\n   *\n   * console.log(ids) // [1, 2, '<UUIDv4>']\n   * ```\n   *\n   * @param data - Items to be added (ids will be generated if missing).\n   * @param senderId - Sender id.\n   * @returns addedIds - Array with the ids (generated if not present) of the added items.\n   * @throws When an item with the same id as any of the added items already exists.\n   */\n  public add(data: Item | Item[], senderId?: Id | null): (string | number)[] {\n    const addedIds: Id[] = [];\n    let id: Id;\n\n    if (Array.isArray(data)) {\n      // Array\n      const idsToAdd: Id[] = data.map((d) => d[this._idProp] as Id);\n      if (idsToAdd.some((id) => this._data.has(id))) {\n        throw new Error(\"A duplicate id was found in the parameter array.\");\n      }\n      for (let i = 0, len = data.length; i < len; i++) {\n        id = this._addItem(data[i]);\n        addedIds.push(id);\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      id = this._addItem(data);\n      addedIds.push(id);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n\n    return addedIds;\n  }\n\n  /**\n   * Update existing items. When an item does not exist, it will be created.\n   *\n   * @remarks\n   * The provided properties will be merged in the existing item. When an item does not exist, it will be created.\n   *\n   * After the items are updated, the DataSet will trigger an event `add` for the added items, and an event `update`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' },\n   *   { id: 4, text: 'item 4 (new)' }\n   * ])\n   *\n   * console.log(ids) // [2, 4]\n   * ```\n   *\n   * ## Warning for TypeScript users\n   * This method may introduce partial items into the data set. Use add or updateOnly instead for better type safety.\n   * @param data - Items to be updated (if the id is already present) or added (if the id is missing).\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the added (these may be newly generated if there was no id in the item from the data) or updated items.\n   * @throws When the supplied data is neither an item nor an array of items.\n   */\n  public update(\n    data: DeepPartial<Item> | DeepPartial<Item>[],\n    senderId?: Id | null\n  ): Id[] {\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const oldData: FullItem<Item, IdProp>[] = [];\n    const updatedData: FullItem<Item, IdProp>[] = [];\n    const idProp = this._idProp;\n\n    const addOrUpdate = (item: DeepPartial<Item>): void => {\n      const origId: OptId = item[idProp];\n      if (origId != null && this._data.has(origId)) {\n        const fullItem = item as FullItem<Item, IdProp>; // it has an id, therefore it is a fullitem\n        const oldItem = Object.assign({}, this._data.get(origId));\n        // update item\n        const id = this._updateItem(fullItem);\n        updatedIds.push(id);\n        updatedData.push(fullItem);\n        oldData.push(oldItem);\n      } else {\n        // add new item\n        const id = this._addItem(item as any);\n        addedIds.push(id);\n      }\n    };\n\n    if (Array.isArray(data)) {\n      // Array\n      for (let i = 0, len = data.length; i < len; i++) {\n        if (data[i] && typeof data[i] === \"object\") {\n          addOrUpdate(data[i]);\n        } else {\n          console.warn(\n            \"Ignoring input item, which is not an object at index \" + i\n          );\n        }\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      addOrUpdate(data);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      const props = { items: updatedIds, oldData: oldData, data: updatedData };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n    }\n\n    return addedIds.concat(updatedIds);\n  }\n\n  /**\n   * Update existing items. When an item does not exist, an error will be thrown.\n   *\n   * @remarks\n   * The provided properties will be deeply merged into the existing item.\n   * When an item does not exist (id not present in the data set or absent), an error will be thrown and nothing will be changed.\n   *\n   * After the items are updated, the DataSet will trigger an event `update`.\n   * When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' },\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' }, // works\n   *   // { id: 4, text: 'item 4 (new)' }, // would throw\n   *   // { text: 'item 4 (new)' }, // would also throw\n   * ])\n   *\n   * console.log(ids) // [2]\n   * ```\n   * @param data - Updates (the id and optionally other props) to the items in this data set.\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the updated items.\n   * @throws When the supplied data is neither an item nor an array of items, when the ids are missing.\n   */\n  public updateOnly(\n    data: UpdateItem<Item, IdProp> | UpdateItem<Item, IdProp>[],\n    senderId?: Id | null\n  ): Id[] {\n    if (!Array.isArray(data)) {\n      data = [data];\n    }\n\n    const updateEventData = data\n      .map(\n        (\n          update\n        ): {\n          oldData: FullItem<Item, IdProp>;\n          update: UpdateItem<Item, IdProp>;\n        } => {\n          const oldData = this._data.get(update[this._idProp]);\n          if (oldData == null) {\n            throw new Error(\"Updating non-existent items is not allowed.\");\n          }\n          return { oldData, update };\n        }\n      )\n      .map(\n        ({\n          oldData,\n          update,\n        }): {\n          id: Id;\n          oldData: FullItem<Item, IdProp>;\n          updatedData: FullItem<Item, IdProp>;\n        } => {\n          const id = oldData[this._idProp];\n          const updatedData = pureDeepObjectAssign(oldData, update);\n\n          this._data.set(id, updatedData);\n\n          return {\n            id,\n            oldData: oldData,\n            updatedData,\n          };\n        }\n      );\n\n    if (updateEventData.length) {\n      const props: EventPayloads<Item, IdProp>[\"update\"] = {\n        items: updateEventData.map((value): Id => value.id),\n        oldData: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.oldData\n        ),\n        data: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.updatedData\n        ),\n      };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n\n      return props.items;\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>> {\n    // @TODO: Woudn't it be better to split this into multiple methods?\n\n    // parse the arguments\n    let id: Id | undefined = undefined;\n    let ids: Id[] | undefined = undefined;\n    let options: DataInterfaceGetOptions<Item> | undefined = undefined;\n    if (isId(first)) {\n      // get(id [, options])\n      id = first;\n      options = second;\n    } else if (Array.isArray(first)) {\n      // get(ids [, options])\n      ids = first;\n      options = second;\n    } else {\n      // get([, options])\n      options = first;\n    }\n\n    // determine the return type\n    const returnType =\n      options && options.returnType === \"Object\" ? \"Object\" : \"Array\";\n    // @TODO: WTF is this? Or am I missing something?\n    // var returnType\n    // if (options && options.returnType) {\n    //   var allowedValues = ['Array', 'Object']\n    //   returnType =\n    //     allowedValues.indexOf(options.returnType) == -1\n    //       ? 'Array'\n    //       : options.returnType\n    // } else {\n    //   returnType = 'Array'\n    // }\n\n    // build options\n    const filter = options && options.filter;\n    const items: FullItem<Item, IdProp>[] = [];\n    let item: undefined | FullItem<Item, IdProp> = undefined;\n    let itemIds: undefined | Id[] = undefined;\n    let itemId: undefined | Id = undefined;\n\n    // convert items\n    if (id != null) {\n      // return a single item\n      item = this._data.get(id);\n      if (item && filter && !filter(item)) {\n        item = undefined;\n      }\n    } else if (ids != null) {\n      // return a subset of items\n      for (let i = 0, len = ids.length; i < len; i++) {\n        item = this._data.get(ids[i]);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    } else {\n      // return all items\n      itemIds = [...this._data.keys()];\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        itemId = itemIds[i];\n        item = this._data.get(itemId);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    }\n\n    // order the results\n    if (options && options.order && id == undefined) {\n      this._sort(items, options.order);\n    }\n\n    // filter fields of the items\n    if (options && options.fields) {\n      const fields = options.fields;\n      if (id != undefined && item != null) {\n        item = this._filterFields(item, fields) as FullItem<Item, IdProp>;\n      } else {\n        for (let i = 0, len = items.length; i < len; i++) {\n          items[i] = this._filterFields(items[i], fields) as FullItem<\n            Item,\n            IdProp\n          >;\n        }\n      }\n    }\n\n    // return the results\n    if (returnType == \"Object\") {\n      const result: Record<string, FullItem<Item, IdProp>> = {};\n      for (let i = 0, len = items.length; i < len; i++) {\n        const resultant = items[i];\n        // @TODO: Shoudn't this be this._fieldId?\n        // result[resultant.id] = resultant\n        const id: Id = resultant[this._idProp];\n        result[id] = resultant;\n      }\n      return result;\n    } else {\n      if (id != null) {\n        // a single item\n        return item ?? null;\n      } else {\n        // just return our array\n        return items;\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    const data = this._data;\n    const filter = options && options.filter;\n    const order = options && options.order;\n    const itemIds = [...data.keys()];\n    const ids: Id[] = [];\n\n    if (filter) {\n      // get filtered items\n      if (order) {\n        // create ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            items.push(item);\n          }\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    } else {\n      // get all items\n      if (order) {\n        // create an ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          items.push(data.get(id)!);\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = data.get(id);\n          if (item != null) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    }\n\n    return ids;\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this;\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    const filter = options && options.filter;\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    if (options && options.order) {\n      // execute forEach on ordered list\n      const items: FullItem<Item, IdProp>[] = this.get(options);\n\n      for (let i = 0, len = items.length; i < len; i++) {\n        const item = items[i];\n        const id = item[this._idProp];\n        callback(item, id);\n      }\n    } else {\n      // unordered\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        const id = itemIds[i];\n        const item = this._data.get(id);\n        if (item != null && (!filter || filter(item))) {\n          callback(item, id);\n        }\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    const filter = options && options.filter;\n    const mappedItems: T[] = [];\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    // convert and filter items\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = this._data.get(id);\n      if (item != null && (!filter || filter(item))) {\n        mappedItems.push(callback(item, id));\n      }\n    }\n\n    // order items\n    if (options && options.order) {\n      this._sort(mappedItems, options.order);\n    }\n\n    return mappedItems;\n  }\n\n  private _filterFields<K extends string>(item: null, fields: K[]): null;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[]\n  ): Record<K, unknown>;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[] | Record<K, string>\n  ): any;\n  /**\n   * Filter the fields of an item.\n   *\n   * @param item - The item whose fields should be filtered.\n   * @param fields - The names of the fields that will be kept.\n   * @typeParam K - Field name type.\n   * @returns The item without any additional fields.\n   */\n  private _filterFields<K extends string>(\n    item: Record<K, unknown> | null,\n    fields: K[] | Record<K, unknown>\n  ): Record<K, unknown> | null {\n    if (!item) {\n      // item is null\n      return item;\n    }\n\n    return (\n      Array.isArray(fields)\n        ? // Use the supplied array\n          fields\n        : // Use the keys of the supplied object\n          (Object.keys(fields) as K[])\n    ).reduce<Record<string, unknown>>(\n      (filteredItem, field): Record<string, unknown> => {\n        filteredItem[field] = item[field];\n        return filteredItem;\n      },\n      {}\n    );\n  }\n\n  /**\n   * Sort the provided array with items.\n   *\n   * @param items - Items to be sorted in place.\n   * @param order - A field name or custom sort function.\n   * @typeParam T - The type of the items in the items array.\n   */\n  private _sort<T>(items: T[], order: DataInterfaceOrder<T>): void {\n    if (typeof order === \"string\") {\n      // order by provided field name\n      const name = order; // field name\n      items.sort((a, b): -1 | 0 | 1 => {\n        // @TODO: How to treat missing properties?\n        const av = (a as any)[name];\n        const bv = (b as any)[name];\n        return av > bv ? 1 : av < bv ? -1 : 0;\n      });\n    } else if (typeof order === \"function\") {\n      // order by sort function\n      items.sort(order);\n    } else {\n      // TODO: extend order by an Object {field:string, direction:string}\n      //       where direction can be 'asc' or 'desc'\n      throw new TypeError(\"Order must be a function or a string\");\n    }\n  }\n\n  /**\n   * Remove an item or multiple items by “reference” (only the id is used) or by id.\n   *\n   * The method ignores removal of non-existing items, and returns an array containing the ids of the items which are actually removed from the DataSet.\n   *\n   * After the items are removed, the DataSet will trigger an event `remove` for the removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // remove items\n   * const ids = data.remove([2, { id: 3 }, 4])\n   *\n   * console.log(ids) // [2, 3]\n   * ```\n   *\n   * @param id - One or more items or ids of items to be removed.\n   * @param senderId - Sender id.\n   * @returns The ids of the removed items.\n   */\n  public remove(id: Id | Item | (Id | Item)[], senderId?: Id | null): Id[] {\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // force everything to be an array for simplicity\n    const ids = Array.isArray(id) ? id : [id];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const item = this._remove(ids[i]);\n      if (item) {\n        const itemId: OptId = item[this._idProp];\n        if (itemId != null) {\n          removedIds.push(itemId);\n          removedItems.push(item);\n        }\n      }\n    }\n\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n\n    return removedIds;\n  }\n\n  /**\n   * Remove an item by its id or reference.\n   *\n   * @param id - Id of an item or the item itself.\n   * @returns The removed item if removed, null otherwise.\n   */\n  private _remove(id: Id | Item): FullItem<Item, IdProp> | null {\n    // @TODO: It origianlly returned the item although the docs say id.\n    // The code expects the item, so probably an error in the docs.\n    let ident: OptId;\n\n    // confirm the id to use based on the args type\n    if (isId(id)) {\n      ident = id;\n    } else if (id && typeof id === \"object\") {\n      ident = id[this._idProp]; // look for the identifier field using ._idProp\n    }\n\n    // do the removing if the item is found\n    if (ident != null && this._data.has(ident)) {\n      const item = this._data.get(ident) || null;\n      this._data.delete(ident);\n      --this.length;\n      return item;\n    }\n\n    return null;\n  }\n\n  /**\n   * Clear the entire data set.\n   *\n   * After the items are removed, the {@link DataSet} will trigger an event `remove` for all removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * @param senderId - Sender id.\n   * @returns removedIds - The ids of all removed items.\n   */\n  public clear(senderId?: Id | null): Id[] {\n    const ids = [...this._data.keys()];\n    const items: FullItem<Item, IdProp>[] = [];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      items.push(this._data.get(ids[i])!);\n    }\n\n    this._data.clear();\n    this.length = 0;\n\n    this._trigger(\"remove\", { items: ids, oldData: items }, senderId);\n\n    return ids;\n  }\n\n  /**\n   * Find the item with maximum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for max value.\n   * @returns Item containing max value, or null if no items.\n   */\n  public max(field: keyof Item): Item | null {\n    let max = null;\n    let maxField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (maxField == null || itemField > maxField)\n      ) {\n        max = item;\n        maxField = itemField;\n      }\n    }\n\n    return max || null;\n  }\n\n  /**\n   * Find the item with minimum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for min value.\n   * @returns Item containing min value, or null if no items.\n   */\n  public min(field: keyof Item): Item | null {\n    let min = null;\n    let minField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (minField == null || itemField < minField)\n      ) {\n        min = item;\n        minField = itemField;\n      }\n    }\n\n    return min || null;\n  }\n\n  public distinct<T extends keyof Item>(prop: T): Item[T][];\n  public distinct(prop: string): unknown[];\n  /**\n   * Find all distinct values of a specified field\n   *\n   * @param prop - The property name whose distinct values should be returned.\n   * @returns Unordered array containing all distinct values. Items without specified property are ignored.\n   */\n  public distinct<T extends string>(prop: T): unknown[] {\n    const data = this._data;\n    const itemIds = [...data.keys()];\n    const values: unknown[] = [];\n    let count = 0;\n\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = data.get(id);\n      const value = (item as any)[prop];\n      let exists = false;\n      for (let j = 0; j < count; j++) {\n        if (values[j] == value) {\n          exists = true;\n          break;\n        }\n      }\n      if (!exists && value !== undefined) {\n        values[count] = value;\n        count++;\n      }\n    }\n\n    return values;\n  }\n\n  /**\n   * Add a single item. Will fail when an item with the same id already exists.\n   *\n   * @param item - A new item to be added.\n   * @returns Added item's id. An id is generated when it is not present in the item.\n   */\n  private _addItem(item: Item): Id {\n    const fullItem = ensureFullItem(item, this._idProp);\n    const id = fullItem[this._idProp];\n\n    // check whether this id is already taken\n    if (this._data.has(id)) {\n      // item already exists\n      throw new Error(\n        \"Cannot add item: item with id \" + id + \" already exists\"\n      );\n    }\n\n    this._data.set(id, fullItem);\n    ++this.length;\n\n    return id;\n  }\n\n  /**\n   * Update a single item: merge with existing item.\n   * Will fail when the item has no id, or when there does not exist an item with the same id.\n   *\n   * @param update - The new item\n   * @returns The id of the updated item.\n   */\n  private _updateItem(update: FullItem<Item, IdProp>): Id {\n    const id: OptId = update[this._idProp];\n    if (id == null) {\n      throw new Error(\n        \"Cannot update item: item has no id (item: \" +\n          JSON.stringify(update) +\n          \")\"\n      );\n    }\n    const item = this._data.get(id);\n    if (!item) {\n      // item doesn't exist\n      throw new Error(\"Cannot update item: no item with id \" + id + \" found\");\n    }\n\n    this._data.set(id, { ...item, ...update });\n\n    return id;\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    if (ids) {\n      const data = this._data;\n\n      return new DataStream<Item>({\n        *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n          for (const id of ids) {\n            const item = data.get(id);\n            if (item != null) {\n              yield [id, item];\n            }\n          }\n        },\n      });\n    } else {\n      return new DataStream({\n        [Symbol.iterator]: this._data.entries.bind(this._data),\n      });\n    }\n  }\n\n  /* develblock:start */\n  public get testLeakData(): Map<Id, FullItem<Item, IdProp>> {\n    return this._data;\n  }\n  public get testLeakIdProp(): IdProp {\n    return this._idProp;\n  }\n  public get testLeakOptions(): DataSetInitialOptions<IdProp> {\n    return this._options;\n  }\n  public get testLeakQueue(): Queue<this> | null {\n    return this._queue;\n  }\n  public set testLeakQueue(v: Queue<this> | null) {\n    this._queue = v;\n  }\n  /* develblock:end */\n}\n", "import {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  EventCallbacksWithAny,\n  EventName,\n  EventPayloads,\n  FullItem,\n  Id,\n  PartItem,\n  RemoveEventPayload,\n  UpdateEventPayload,\n  isId,\n} from \"./data-interface\";\n\nimport { DataSet } from \"./data-set\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Data view options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataViewOptions<Item, IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n}\n\n/**\n * DataView\n *\n * A DataView offers a filtered and/or formatted view on a DataSet. One can subscribe to changes in a DataView, and easily get filtered or formatted data without having to specify filters and field types all the time.\n *\n * ## Example\n * ```javascript\n * // create a DataSet\n * var data = new vis.DataSet();\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // create a DataView\n * // the view will only contain items having a property group with value 1,\n * // and will only output fields id, text, and date.\n * var view = new vis.DataView(data, {\n *   filter: function (item) {\n *     return (item.group == 1);\n *   },\n *   fields: ['id', 'text', 'date']\n * });\n *\n * // subscribe to any change in the DataView\n * view.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an item in the data set\n * data.update({id: 2, group: 1});\n *\n * // get all ids in the view\n * var ids = view.getIds();\n * console.log('ids', ids); // will output [1, 2]\n *\n * // get all items in the view\n * var items = view.get();\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataView<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** @inheritDoc */\n  public length = 0;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this.getDataSet().idProp;\n  }\n\n  private readonly _listener: EventCallbacksWithAny<Item, IdProp>[\"*\"];\n  private _data!: DataInterface<Item, IdProp>; // constructor → setData\n  private readonly _ids: Set<Id> = new Set(); // ids of the items currently in memory (just contains a boolean true)\n  private readonly _options: DataViewOptions<Item, IdProp>;\n\n  /**\n   * Create a DataView.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @param options - Options to configure this data view.\n   */\n  public constructor(\n    data: DataInterface<Item, IdProp>,\n    options?: DataViewOptions<Item, IdProp>\n  ) {\n    super();\n\n    this._options = options || {};\n\n    this._listener = this._onEvent.bind(this);\n\n    this.setData(data);\n  }\n\n  // TODO: implement a function .config() to dynamically update things like configured filter\n  // and trigger changes accordingly\n\n  /**\n   * Set a data source for the view.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @remarks\n   * Note that when the data view is bound to a data set it won't be garbage\n   * collected unless the data set is too. Use `dataView.setData(null)` or\n   * `dataView.dispose()` to enable garbage collection before you lose the last\n   * reference.\n   */\n  public setData(data: DataInterface<Item, IdProp>): void {\n    if (this._data) {\n      // unsubscribe from current dataset\n      if (this._data.off) {\n        this._data.off(\"*\", this._listener);\n      }\n\n      // trigger a remove of all items in memory\n      const ids = this._data.getIds({ filter: this._options.filter });\n      const items = this._data.get(ids);\n\n      this._ids.clear();\n      this.length = 0;\n      this._trigger(\"remove\", { items: ids, oldData: items });\n    }\n\n    if (data != null) {\n      this._data = data;\n\n      // trigger an add of all added items\n      const ids = this._data.getIds({ filter: this._options.filter });\n      for (let i = 0, len = ids.length; i < len; i++) {\n        const id = ids[i];\n        this._ids.add(id);\n      }\n      this.length = ids.length;\n      this._trigger(\"add\", { items: ids });\n    } else {\n      this._data = new DataSet<Item, IdProp>();\n    }\n\n    // subscribe to new dataset\n    if (this._data.on) {\n      this._data.on(\"*\", this._listener);\n    }\n  }\n\n  /**\n   * Refresh the DataView.\n   * Useful when the DataView has a filter function containing a variable parameter.\n   */\n  public refresh(): void {\n    const ids = this._data.getIds({\n      filter: this._options.filter,\n    });\n    const oldIds = [...this._ids];\n    const newIds: Record<Id, boolean> = {};\n    const addedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // check for additions\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const id = ids[i];\n      newIds[id] = true;\n      if (!this._ids.has(id)) {\n        addedIds.push(id);\n        this._ids.add(id);\n      }\n    }\n\n    // check for removals\n    for (let i = 0, len = oldIds.length; i < len; i++) {\n      const id = oldIds[i];\n      const item = this._data.get(id);\n      if (item == null) {\n        // @TODO: Investigate.\n        // Doesn't happen during tests or examples.\n        // Is it really impossible or could it eventually happen?\n        // How to handle it if it does? The types guarantee non-nullable items.\n        console.error(\"If you see this, report it please.\");\n      } else if (!newIds[id]) {\n        removedIds.push(id);\n        removedItems.push(item);\n        this._ids.delete(id);\n      }\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    // trigger events\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds });\n    }\n    if (removedIds.length) {\n      this._trigger(\"remove\", { items: removedIds, oldData: removedItems });\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<string, FullItem<Item, IdProp>> {\n    if (this._data == null) {\n      return null;\n    }\n\n    // parse the arguments\n    let ids: Id | Id[] | null = null;\n    let options: any;\n    if (isId(first) || Array.isArray(first)) {\n      ids = first;\n      options = second;\n    } else {\n      options = first;\n    }\n\n    // extend the options with the default options and provided options\n    const viewOptions: DataInterfaceGetOptions<Item> = Object.assign(\n      {},\n      this._options,\n      options\n    );\n\n    // create a combined filter method when needed\n    const thisFilter = this._options.filter;\n    const optionsFilter = options && options.filter;\n    if (thisFilter && optionsFilter) {\n      viewOptions.filter = (item): boolean => {\n        return thisFilter(item) && optionsFilter(item);\n      };\n    }\n\n    if (ids == null) {\n      return this._data.get(viewOptions);\n    } else {\n      return this._data.get(ids, viewOptions);\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    if (this._data.length) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options != null ? options.filter : null;\n      let filter: DataInterfaceGetIdsOptions<Item>[\"filter\"];\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): boolean => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.getIds({\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | ((item: Item) => boolean);\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = function (item: Item): boolean {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      this._data.forEach(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    type Filter = NonNullable<DataInterfaceMapOptions<Item, T>[\"filter\"]>;\n\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | Filter;\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): ReturnType<Filter> => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.map(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this._data.getDataSet();\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    return this._data.stream(\n      ids || {\n        [Symbol.iterator]: this._ids.keys.bind(this._ids),\n      }\n    );\n  }\n\n  /**\n   * Render the instance unusable prior to garbage collection.\n   *\n   * @remarks\n   * The intention of this method is to help discover scenarios where the data\n   * view is being used when the programmer thinks it has been garbage collected\n   * already. It's stricter version of `dataView.setData(null)`.\n   */\n  public dispose(): void {\n    if (this._data?.off) {\n      this._data.off(\"*\", this._listener);\n    }\n\n    const message = \"This data view has already been disposed of.\";\n    const replacement = {\n      get: (): void => {\n        throw new Error(message);\n      },\n      set: (): void => {\n        throw new Error(message);\n      },\n\n      configurable: false,\n    };\n    for (const key of Reflect.ownKeys(DataView.prototype)) {\n      Object.defineProperty(this, key, replacement);\n    }\n  }\n\n  /**\n   * Event listener. Will propagate all events from the connected data set to the subscribers of the DataView, but will filter the items and only trigger when there are changes in the filtered data set.\n   *\n   * @param event - The name of the event.\n   * @param params - Parameters of the event.\n   * @param senderId - Id supplied by the sender.\n   */\n  private _onEvent<EN extends EventName>(\n    event: EN,\n    params: EventPayloads<Item, IdProp>[EN],\n    senderId?: Id | null\n  ): void {\n    if (!params || !params.items || !this._data) {\n      return;\n    }\n\n    const ids = params.items;\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const oldItems: FullItem<Item, IdProp>[] = [];\n    const updatedItems: FullItem<Item, IdProp>[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    switch (event) {\n      case \"add\":\n        // filter the ids of the added items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n          if (item) {\n            this._ids.add(id);\n            addedIds.push(id);\n          }\n        }\n\n        break;\n\n      case \"update\":\n        // determine the event from the views viewpoint: an updated\n        // item can be added, updated, or removed from this view.\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n\n          if (item) {\n            if (this._ids.has(id)) {\n              updatedIds.push(id);\n              updatedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).data[i]\n              );\n              oldItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              this._ids.add(id);\n              addedIds.push(id);\n            }\n          } else {\n            if (this._ids.has(id)) {\n              this._ids.delete(id);\n              removedIds.push(id);\n              removedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              // nothing interesting for me :-(\n            }\n          }\n        }\n\n        break;\n\n      case \"remove\":\n        // filter the ids of the removed items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          if (this._ids.has(id)) {\n            this._ids.delete(id);\n            removedIds.push(id);\n            removedItems.push(\n              (params as RemoveEventPayload<Item, IdProp>).oldData[i]\n            );\n          }\n        }\n\n        break;\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      this._trigger(\n        \"update\",\n        { items: updatedIds, oldData: oldItems, data: updatedItems },\n        senderId\n      );\n    }\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n  }\n}\n", "import { PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * Check that given value is compatible with Vis Data Set interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataSetLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataSet<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.add === \"function\" &&\n    typeof v.clear === \"function\" &&\n    typeof v.distinct === \"function\" &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.max === \"function\" &&\n    typeof v.min === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.remove === \"function\" &&\n    typeof v.setOptions === \"function\" &&\n    typeof v.stream === \"function\" &&\n    typeof v.update === \"function\" &&\n    typeof v.updateOnly === \"function\"\n  );\n}\n", "import { DataView } from \"./data-view\";\nimport { PartItem } from \"./data-interface\";\nimport { isDataSetLike } from \"./data-set-check\";\n\n/**\n * Check that given value is compatible with Vis Data View interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataViewLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataView<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.stream === \"function\" &&\n    isDataSetLike(idProp, v.getDataSet())\n  );\n}\n"], "names": ["uuid4"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;AACG,SAAU,qBAAqB,CAGnC,IAA2B,EAAA;AAC3B,IAAA,OAAO,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;AAC7C,CAAC;AAID;;;;;;;;AAQG;AACH,MAAM,cAAc,CAAA;AAyBC,IAAA,OAAA,CAAA;AACA,IAAA,aAAA,CAAA;AACA,IAAA,OAAA,CAAA;AApBnB;;AAEG;AACc,IAAA,UAAU,GAA2B;QACpD,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;QAC/B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;KAChC,CAAC;AAEF;;;;;;;AAOG;AACH,IAAA,WAAA,CACmB,OAA8B,EAC9B,aAA8C,EAC9C,OAAwB,EAAA;QAFxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAuB;QAC9B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAiC;QAC9C,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;KACvC;;IAGG,GAAG,GAAA;AACR,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC9D,QAAA,OAAO,IAAI,CAAC;KACb;;IAGM,KAAK,GAAA;AACV,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5C,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAElD,QAAA,OAAO,IAAI,CAAC;KACb;;IAGM,IAAI,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAEnD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;AAKG;AACK,IAAA,eAAe,CAAC,KAAgB,EAAA;QACtC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,SAAS,KAAe;AAC/D,YAAA,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;SACzB,EAAE,KAAK,CAAC,CAAC;KACX;AAED;;;;;AAKG;IACK,IAAI,CACV,KAAmD,EACnD,OAAqD,EAAA;QAErD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO;AACR,SAAA;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACzE;AAED;;;;;AAKG;IACK,OAAO,CACb,KAAsD,EACtD,OAAwD,EAAA;QAExD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO;AACR,SAAA;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC5E;AAED;;;;;AAKG;IACK,OAAO,CACb,KAAsD,EACtD,OAAwD,EAAA;QAExD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;KAC5D;AACF,CAAA;AAED;;;;;;AAMG;AACH,MAAM,yBAAyB,CAAA;AAgBO,IAAA,OAAA,CAAA;AAZpC;;;AAGG;IACc,aAAa,GAAuB,EAAE,CAAC;AAExD;;;;;AAKG;AACH,IAAA,WAAA,CAAoC,OAA8B,EAAA;QAA9B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAuB;KAAI;AAEtE;;;;;;AAMG;AACI,IAAA,MAAM,CACX,QAA+B,EAAA;AAE/B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtE,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;;AAQG;AACI,IAAA,GAAG,CACR,QAA0B,EAAA;AAE1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnE,QAAA,OAAO,IAAoD,CAAC;KAC7D;AAED;;;;;;;;AAQG;AACI,IAAA,OAAO,CACZ,QAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,QAAA,OAAO,IAAoD,CAAC;KAC7D;AAED;;;;;;AAMG;AACI,IAAA,EAAE,CAAC,MAAuB,EAAA;AAC/B,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;KACrE;AACF;;ACnRD;;;;;AAKG;AACG,SAAU,IAAI,CAAC,KAAc,EAAA;IACjC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAChE;;AC2BA;;;;AAIG;MACU,KAAK,CAAA;;AAET,IAAA,KAAK,CAAgB;;AAErB,IAAA,GAAG,CAAS;IAEF,MAAM,GAIjB,EAAE,CAAC;IAED,QAAQ,GAAyC,IAAI,CAAC;IACtD,SAAS,GAA4B,IAAI,CAAC;AAElD;;;;AAIG;AACH,IAAA,WAAA,CAAmB,OAAsB,EAAA;;AAEvC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;AAEpB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC1B;AAED;;;;AAIG;AACI,IAAA,UAAU,CAAC,OAAsB,EAAA;QACtC,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,EAAE;AACnD,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5B,SAAA;QACD,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW,EAAE;AACjD,YAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,SAAA;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;KACvB;AAED;;;;;;;AAOG;AACI,IAAA,OAAO,MAAM,CAClB,MAAS,EACT,OAA8B,EAAA;AAE9B,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAI,OAAO,CAAC,CAAC;AAEpC,QAAA,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;AAC9B,YAAA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;AAC/D,SAAA;AACD,QAAA,MAAM,CAAC,KAAK,GAAG,MAAW;YACxB,KAAK,CAAC,KAAK,EAAE,CAAC;AAChB,SAAC,CAAC;AAEF,QAAA,MAAM,OAAO,GAAgC;AAC3C,YAAA;AACE,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,QAAQ,EAAE,SAAS;AACpB,aAAA;SACF,CAAC;AAEF,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;AAC9B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC;AACX,oBAAA,IAAI,EAAE,IAAI;;AAEV,oBAAA,QAAQ,EAAG,MAA2C,CAAC,IAAI,CAAC;AAC7D,iBAAA,CAAC,CAAC;;AAEH,gBAAA,KAAK,CAAC,OAAO,CAAC,MAA0C,EAAE,IAAI,CAAC,CAAC;AACjE,aAAA;AACF,SAAA;QAED,KAAK,CAAC,SAAS,GAAG;AAChB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,OAAO,EAAE,OAAO;SACjB,CAAC;AAEF,QAAA,OAAO,KAAK,CAAC;KACd;AAED;;AAEG;IACI,OAAO,GAAA;QACZ,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACrC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,gBAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,MAAM,CAAC,QAAQ,EAAE;;oBAElB,MAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;AAChD,iBAAA;AAAM,qBAAA;;AAEL,oBAAA,OAAQ,MAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,SAAA;KACF;AAED;;;;;AAKG;IACI,OAAO,CACZ,MAA6B,EAC7B,MAAS,EAAA;;QAGT,MAAM,EAAE,GAAG,IAAI,CAAC;AAChB,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,MAAM,GAAG,YAAY,CAAC,CAAC;AACpD,SAAA;AAED,QAAA,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,IAAe,EAAA;;YAE3C,EAAE,CAAC,KAAK,CAAC;AACP,gBAAA,IAAI,EAAE,IAAI;AACV,gBAAA,EAAE,EAAE,QAAQ;AACZ,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;KACH;AAED;;;;AAIG;AACI,IAAA,KAAK,CAAC,KAAqB,EAAA;AAChC,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACjC,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,SAAA;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;KACvB;AAED;;AAEG;IACK,cAAc,GAAA;;QAEpB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,KAAK,EAAE,CAAC;AACd,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;AACzB,YAAA,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACtB,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC3D,YAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAW;gBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;AACf,aAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAChB,SAAA;KACF;AAED;;AAEG;IACI,KAAK,GAAA;AACV,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAU;AAC5C,YAAA,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;AAC9D,SAAC,CAAC,CAAC;KACJ;AACF;;AC/ND;;;;;AAKG;MACmB,WAAW,CAAA;AAKd,IAAA,YAAY,GAEzB;AACF,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,GAAG,EAAE,EAAE;AACP,QAAA,MAAM,EAAE,EAAE;AACV,QAAA,MAAM,EAAE,EAAE;KACX,CAAC;AAiBF;;;;;;AAMG;AACO,IAAA,QAAQ,CAChB,KAAW,EACX,OAA0C,EAC1C,QAAoB,EAAA;QAEpB,IAAK,KAAgB,KAAK,GAAG,EAAE;AAC7B,YAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC3C,SAAA;QAED,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAC9D,CAAC,UAAU,KAAU;AACnB,YAAA,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;AACjE,SAAC,CACF,CAAC;KACH;AAsBD;;;;;;AAMG;IACI,EAAE,CACP,KAAW,EACX,QAAmD,EAAA;AAEnD,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC,SAAA;;KAEF;AAsBD;;;;;;AAMG;IACI,GAAG,CACR,KAAW,EACX,QAAmD,EAAA;QAEnD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CACxD,CAAC,UAAU,KAAc,UAAU,KAAK,QAAQ,CACjD,CAAC;KACH;AAED;;AAEG;AACI,IAAA,SAAS,GAAoC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;AAC7E;;AAEG;AACI,IAAA,WAAW,GAChB,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC;AAE7B;;ACzJD;;;;;;;;AAQG;MACU,UAAU,CAAA;AACJ,IAAA,MAAM,CAAuB;AAE9C;;;;AAIG;AACH,IAAA,WAAA,CAAmB,KAA2B,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACrB;AAED;;AAEG;AACI,IAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;QACvB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AACpC,YAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,SAAA;KACF;AAED;;AAEG;AACI,IAAA,CAAC,OAAO,GAAA;QACb,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AACpC,YAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,SAAA;KACF;AAED;;AAEG;AACI,IAAA,CAAC,IAAI,GAAA;QACV,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAC9B,YAAA,MAAM,EAAE,CAAC;AACV,SAAA;KACF;AAED;;AAEG;AACI,IAAA,CAAC,MAAM,GAAA;QACZ,KAAK,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAClC,YAAA,MAAM,IAAI,CAAC;AACZ,SAAA;KACF;AAED;;;;;;AAMG;IACI,SAAS,GAAA;AACd,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KACpD;AAED;;;;;;AAMG;IACI,WAAW,GAAA;AAChB,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KACtD;AAED;;;;;;AAMG;IACI,YAAY,GAAA;AACjB,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;AAED;;;;;;AAMG;IACI,WAAW,GAAA;QAChB,MAAM,GAAG,GAAqB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AACpC,YAAA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AAChB,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;;AAIG;IACI,KAAK,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC7B;AAED;;;;AAIG;IACI,OAAO,GAAA;QACZ,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KAClC;AAED;;;;AAIG;IACI,SAAS,GAAA;QACd,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KACpC;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;IACI,KAAK,GAAA;QACV,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KACzC;AAED;;;;;;AAMG;AACI,IAAA,QAAQ,CAAI,QAAmC,EAAA;AACpD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,EAAK,CAAC;QAEzB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACpC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7B,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;;;AAKG;AACI,IAAA,MAAM,CAAC,QAAyC,EAAA;AACrD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,IAAI,UAAU,CAAO;AAC1B,YAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;gBAChB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE;AAC9B,oBAAA,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;AACtB,wBAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,qBAAA;AACF,iBAAA;aACF;AACF,SAAA,CAAC,CAAC;KACJ;AAED;;;;AAIG;AACI,IAAA,OAAO,CAAC,QAAyC,EAAA;QACtD,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AACpC,YAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACpB,SAAA;KACF;AAED;;;;;;AAMG;AACI,IAAA,GAAG,CACR,QAAwC,EAAA;AAExC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,IAAI,UAAU,CAAS;AAC5B,YAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;gBAChB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE;oBAC9B,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAChC,iBAAA;aACF;AACF,SAAA,CAAC,CAAC;KACJ;AAED;;;;;AAKG;AACI,IAAA,GAAG,CAAC,QAAwC,EAAA;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,IAAI,OAAO,GAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,QAAA,IAAI,QAAQ,GAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;YACjC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjC,IAAI,KAAK,GAAG,QAAQ,EAAE;gBACpB,QAAQ,GAAG,KAAK,CAAC;gBACjB,OAAO,GAAG,IAAI,CAAC;AAChB,aAAA;AACF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;AAKG;AACI,IAAA,GAAG,CAAC,QAAwC,EAAA;QACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,IAAI,OAAO,GAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,QAAA,IAAI,QAAQ,GAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;YACjC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACjC,IAAI,KAAK,GAAG,QAAQ,EAAE;gBACpB,QAAQ,GAAG,KAAK,CAAC;gBACjB,OAAO,GAAG,IAAI,CAAC;AAChB,aAAA;AACF,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AAED;;;;;;;AAOG;IACI,MAAM,CACX,QAAmD,EACnD,WAAc,EAAA;QAEd,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;YACpC,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AAC/C,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;;;;AAKG;AACI,IAAA,IAAI,CACT,QAAgE,EAAA;QAEhE,OAAO,IAAI,UAAU,CAAC;AACpB,YAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,MACjB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACb,iBAAA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAC/B,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CACjC,CACA,MAAM,CAAC,QAAQ,CAAC,EAAE;AACxB,SAAA,CAAC,CAAC;KACJ;AACF;;ACvQD;;;;;;;;;;AAUG;AACH,SAAS,cAAc,CACrB,IAAU,EACV,MAAc,EAAA;AAEd,IAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;;AAExB,QAAA,IAAI,CAAC,MAAM,CAAC,GAAGA,EAAK,EAAS,CAAC;AAC/B,KAAA;AAED,IAAA,OAAO,IAA8B,CAAC;AACxC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DG;AACG,MAAO,OAIX,SAAQ,WAAyB,CAAA;;AAI1B,IAAA,KAAK,CAAc;;AAEnB,IAAA,MAAM,CAAS;;AAEtB,IAAA,IAAW,MAAM,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAEgB,IAAA,QAAQ,CAAgC;AACxC,IAAA,KAAK,CAAkC;AACvC,IAAA,OAAO,CAAS;IACzB,MAAM,GAAuB,IAAI,CAAC;AAW1C;;;;;AAKG;IACH,WACE,CAAA,IAA6C,EAC7C,OAAuC,EAAA;AAEvC,QAAA,KAAK,EAAE,CAAC;;QAGR,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,GAAG,IAAI,CAAC;YACf,IAAI,GAAG,EAAE,CAAC;AACX,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAK,IAAe,CAAC;;AAGzD,QAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACvB,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC1B;AAED;;;;AAIG;AACI,IAAA,UAAU,CAAC,OAAwB,EAAA;AACxC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;AAC1C,YAAA,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;;gBAE3B,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AACtB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACpB,iBAAA;AACF,aAAA;AAAM,iBAAA;;AAEL,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AAC/B,wBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACrC,qBAAA,CAAC,CAAC;AACJ,iBAAA;gBAED,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;oBACtD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,iBAAA;AACF,aAAA;AACF,SAAA;KACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;IACI,GAAG,CAAC,IAAmB,EAAE,QAAoB,EAAA;QAClD,MAAM,QAAQ,GAAS,EAAE,CAAC;AAC1B,QAAA,IAAI,EAAM,CAAC;AAEX,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;AAEvB,YAAA,MAAM,QAAQ,GAAS,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAO,CAAC,CAAC;AAC9D,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;AAC7C,gBAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACrE,aAAA;AACD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC/C,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;AAE3C,YAAA,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACzB,YAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;IACI,MAAM,CACX,IAA6C,EAC7C,QAAoB,EAAA;QAEpB,MAAM,QAAQ,GAAS,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;QAC5B,MAAM,OAAO,GAA6B,EAAE,CAAC;QAC7C,MAAM,WAAW,GAA6B,EAAE,CAAC;AACjD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAE5B,QAAA,MAAM,WAAW,GAAG,CAAC,IAAuB,KAAU;AACpD,YAAA,MAAM,MAAM,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC;AACnC,YAAA,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC5C,gBAAA,MAAM,QAAQ,GAAG,IAA8B,CAAC;AAChD,gBAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;gBAE1D,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACtC,gBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,gBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B,gBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,aAAA;AAAM,iBAAA;;gBAEL,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAW,CAAC,CAAC;AACtC,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,aAAA;AACH,SAAC,CAAC;AAEF,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;AAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC/C,gBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC1C,oBAAA,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,iBAAA;AAAM,qBAAA;AACL,oBAAA,OAAO,CAAC,IAAI,CACV,uDAAuD,GAAG,CAAC,CAC5D,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;YAE3C,WAAW,CAAC,IAAI,CAAC,CAAC;AACnB,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACrC,SAAA;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAA;QACD,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,MAAM,KAAK,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;;;;;;;YAQzE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC1C,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACpC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;IACI,UAAU,CACf,IAA2D,EAC3D,QAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACxB,YAAA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AACf,SAAA;QAED,MAAM,eAAe,GAAG,IAAI;AACzB,aAAA,GAAG,CACF,CACE,MAAM,KAIJ;AACF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACrD,IAAI,OAAO,IAAI,IAAI,EAAE;AACnB,gBAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAChE,aAAA;AACD,YAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AAC7B,SAAC,CACF;aACA,GAAG,CACF,CAAC,EACC,OAAO,EACP,MAAM,GACP,KAIG;YACF,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,MAAM,WAAW,GAAG,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAE1D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;YAEhC,OAAO;gBACL,EAAE;AACF,gBAAA,OAAO,EAAE,OAAO;gBAChB,WAAW;aACZ,CAAC;AACJ,SAAC,CACF,CAAC;QAEJ,IAAI,eAAe,CAAC,MAAM,EAAE;AAC1B,YAAA,MAAM,KAAK,GAA0C;AACnD,gBAAA,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,KAAS,KAAK,CAAC,EAAE,CAAC;AACnD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,CAC1B,CAAC,KAAK,KAA6B,KAAK,CAAC,OAAO,CACjD;AACD,gBAAA,IAAI,EAAE,eAAe,CAAC,GAAG,CACvB,CAAC,KAAK,KAA6B,KAAK,CAAC,WAAW,CACrD;aACF,CAAC;;;;;;;;YAQF,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEzC,OAAO,KAAK,CAAC,KAAK,CAAC;AACpB,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;KACF;;IA6DM,GAAG,CACR,KAAiD,EACjD,MAAsC,EAAA;;;QAStC,IAAI,EAAE,GAAmB,SAAS,CAAC;QACnC,IAAI,GAAG,GAAqB,SAAS,CAAC;QACtC,IAAI,OAAO,GAA8C,SAAS,CAAC;AACnE,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;;YAEf,EAAE,GAAG,KAAK,CAAC;YACX,OAAO,GAAG,MAAM,CAAC;AAClB,SAAA;AAAM,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;YAE/B,GAAG,GAAG,KAAK,CAAC;YACZ,OAAO,GAAG,MAAM,CAAC;AAClB,SAAA;AAAM,aAAA;;YAEL,OAAO,GAAG,KAAK,CAAC;AACjB,SAAA;;AAGD,QAAA,MAAM,UAAU,GACd,OAAO,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;;;;;;;;;;;;;AAclE,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;QACzC,MAAM,KAAK,GAA6B,EAAE,CAAC;QAC3C,IAAI,IAAI,GAAuC,SAAS,CAAC;QACzD,IAAI,OAAO,GAAqB,SAAS,CAAC;QAC1C,IAAI,MAAM,GAAmB,SAAS,CAAC;;QAGvC,IAAI,EAAE,IAAI,IAAI,EAAE;;YAEd,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACnC,IAAI,GAAG,SAAS,CAAC;AAClB,aAAA;AACF,SAAA;aAAM,IAAI,GAAG,IAAI,IAAI,EAAE;;AAEtB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,gBAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC7C,oBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;;YAEL,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AACjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,gBAAA,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC9B,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC7C,oBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,iBAAA;AACF,aAAA;AACF,SAAA;;QAGD,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,EAAE,IAAI,SAAS,EAAE;YAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AAClC,SAAA;;AAGD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;AAC7B,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC9B,YAAA,IAAI,EAAE,IAAI,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE;gBACnC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAA2B,CAAC;AACnE,aAAA;AAAM,iBAAA;AACL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,oBAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAG7C,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;;QAGD,IAAI,UAAU,IAAI,QAAQ,EAAE;YAC1B,MAAM,MAAM,GAA2C,EAAE,CAAC;AAC1D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;;gBAG3B,MAAM,EAAE,GAAO,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvC,gBAAA,MAAM,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;AACxB,aAAA;AACD,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAAM,aAAA;YACL,IAAI,EAAE,IAAI,IAAI,EAAE;;gBAEd,OAAO,IAAI,IAAI,IAAI,CAAC;AACrB,aAAA;AAAM,iBAAA;;AAEL,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;KACF;;AAGM,IAAA,MAAM,CAAC,OAA0C,EAAA;AACtD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACxB,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;AACzC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC;QACvC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjC,MAAM,GAAG,GAAS,EAAE,CAAC;AAErB,QAAA,IAAI,MAAM,EAAE;;AAEV,YAAA,IAAI,KAAK,EAAE;;gBAET,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAChC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,wBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClB,qBAAA;AACF,iBAAA;AAED,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEzB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,oBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAClC,iBAAA;AACF,aAAA;AAAM,iBAAA;;AAEL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAChC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;wBAChC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9B,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,KAAK,EAAE;;gBAET,MAAM,KAAK,GAAG,EAAE,CAAC;AACjB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,CAAC;AAC3B,iBAAA;AAED,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEzB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,oBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAClC,iBAAA;AACF,aAAA;AAAM,iBAAA;;AAEL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;wBAChB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9B,qBAAA;AACF,iBAAA;AACF,aAAA;AACF,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACZ;;IAGM,UAAU,GAAA;AACf,QAAA,OAAO,IAAI,CAAC;KACb;;IAGM,OAAO,CACZ,QAAsC,EACtC,OAA2C,EAAA;AAE3C,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;AACzC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAEjC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;;YAE5B,MAAM,KAAK,GAA6B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAE1D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAChD,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9B,gBAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACpB,aAAA;AACF,SAAA;AAAM,aAAA;;AAEL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,gBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC7C,oBAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACpB,iBAAA;AACF,aAAA;AACF,SAAA;KACF;;IAGM,GAAG,CACR,QAAmC,EACnC,OAA0C,EAAA;AAE1C,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;QACzC,MAAM,WAAW,GAAQ,EAAE,CAAC;AAC5B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;AAGjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,YAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;gBAC7C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACtC,aAAA;AACF,SAAA;;AAGD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACxC,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACpB;AAWD;;;;;;;AAOG;IACK,aAAa,CACnB,IAA+B,EAC/B,MAAgC,EAAA;QAEhC,IAAI,CAAC,IAAI,EAAE;;AAET,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AACnB;gBACE,MAAM;AACR;AACG,gBAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAS,EAChC,MAAM,CACN,CAAC,YAAY,EAAE,KAAK,KAA6B;YAC/C,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,YAAA,OAAO,YAAY,CAAC;SACrB,EACD,EAAE,CACH,CAAC;KACH;AAED;;;;;;AAMG;IACK,KAAK,CAAI,KAAU,EAAE,KAA4B,EAAA;AACvD,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;;AAE7B,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAgB;;AAE9B,gBAAA,MAAM,EAAE,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;AAC5B,gBAAA,MAAM,EAAE,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;gBAC5B,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACxC,aAAC,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;;AAEtC,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnB,SAAA;AAAM,aAAA;;;AAGL,YAAA,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;AAC7D,SAAA;KACF;AAED;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;IACI,MAAM,CAAC,EAA6B,EAAE,QAAoB,EAAA;QAC/D,MAAM,UAAU,GAAS,EAAE,CAAC;QAC5B,MAAM,YAAY,GAA6B,EAAE,CAAC;;AAGlD,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,YAAA,IAAI,IAAI,EAAE;gBACR,MAAM,MAAM,GAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzC,IAAI,MAAM,IAAI,IAAI,EAAE;AAClB,oBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxB,oBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB,iBAAA;AACF,aAAA;AACF,SAAA;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,EAC5C,QAAQ,CACT,CAAC;AACH,SAAA;AAED,QAAA,OAAO,UAAU,CAAC;KACnB;AAED;;;;;AAKG;AACK,IAAA,OAAO,CAAC,EAAa,EAAA;;;AAG3B,QAAA,IAAI,KAAY,CAAC;;AAGjB,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE;YACZ,KAAK,GAAG,EAAE,CAAC;AACZ,SAAA;AAAM,aAAA,IAAI,EAAE,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YACvC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1B,SAAA;;AAGD,QAAA,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1C,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;AAC3C,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACzB,EAAE,IAAI,CAAC,MAAM,CAAC;AACd,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;;AAOG;AACI,IAAA,KAAK,CAAC,QAAoB,EAAA;QAC/B,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACnC,MAAM,KAAK,GAA6B,EAAE,CAAC;AAE3C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;AACrC,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACnB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAEhB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;AAElE,QAAA,OAAO,GAAG,CAAC;KACZ;AAED;;;;;AAKG;AACI,IAAA,GAAG,CAAC,KAAiB,EAAA;QAC1B,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;AACtC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,IACE,OAAO,SAAS,KAAK,QAAQ;iBAC5B,QAAQ,IAAI,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,EAC1C;gBACA,GAAG,GAAG,IAAI,CAAC;gBACX,QAAQ,GAAG,SAAS,CAAC;AACtB,aAAA;AACF,SAAA;QAED,OAAO,GAAG,IAAI,IAAI,CAAC;KACpB;AAED;;;;;AAKG;AACI,IAAA,GAAG,CAAC,KAAiB,EAAA;QAC1B,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;AACtC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9B,IACE,OAAO,SAAS,KAAK,QAAQ;iBAC5B,QAAQ,IAAI,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,EAC1C;gBACA,GAAG,GAAG,IAAI,CAAC;gBACX,QAAQ,GAAG,SAAS,CAAC;AACtB,aAAA;AACF,SAAA;QAED,OAAO,GAAG,IAAI,IAAI,CAAC;KACpB;AAID;;;;;AAKG;AACI,IAAA,QAAQ,CAAmB,IAAO,EAAA;AACvC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjC,MAAM,MAAM,GAAc,EAAE,CAAC;QAC7B,IAAI,KAAK,GAAG,CAAC,CAAC;AAEd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,YAAA,MAAM,KAAK,GAAI,IAAY,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AAC9B,gBAAA,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;oBACtB,MAAM,GAAG,IAAI,CAAC;oBACd,MAAM;AACP,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE;AAClC,gBAAA,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACtB,gBAAA,KAAK,EAAE,CAAC;AACT,aAAA;AACF,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;AAKG;AACK,IAAA,QAAQ,CAAC,IAAU,EAAA;QACzB,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;QAGlC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;;YAEtB,MAAM,IAAI,KAAK,CACb,gCAAgC,GAAG,EAAE,GAAG,iBAAiB,CAC1D,CAAC;AACH,SAAA;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC7B,EAAE,IAAI,CAAC,MAAM,CAAC;AAEd,QAAA,OAAO,EAAE,CAAC;KACX;AAED;;;;;;AAMG;AACK,IAAA,WAAW,CAAC,MAA8B,EAAA;QAChD,MAAM,EAAE,GAAU,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,MAAM,IAAI,KAAK,CACb,4CAA4C;AAC1C,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AACtB,gBAAA,GAAG,CACN,CAAC;AACH,SAAA;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,EAAE;;YAET,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC;AACzE,SAAA;AAED,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;AAE3C,QAAA,OAAO,EAAE,CAAC;KACX;;AAGM,IAAA,MAAM,CAAC,GAAkB,EAAA;AAC9B,QAAA,IAAI,GAAG,EAAE;AACP,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YAExB,OAAO,IAAI,UAAU,CAAO;AAC1B,gBAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;AAChB,oBAAA,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE;wBACpB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,4BAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClB,yBAAA;AACF,qBAAA;iBACF;AACF,aAAA,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA;YACL,OAAO,IAAI,UAAU,CAAC;AACpB,gBAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AACvD,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;AAEF;;ACvjCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CG;AACG,MAAO,QAIX,SAAQ,WAAyB,CAAA;;IAI1B,MAAM,GAAG,CAAC,CAAC;;AAElB,IAAA,IAAW,MAAM,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;KACjC;AAEgB,IAAA,SAAS,CAA2C;IAC7D,KAAK,CAA+B;AAC3B,IAAA,IAAI,GAAY,IAAI,GAAG,EAAE,CAAC;AAC1B,IAAA,QAAQ,CAAgC;AAEzD;;;;;AAKG;IACH,WACE,CAAA,IAAiC,EACjC,OAAuC,EAAA;AAEvC,QAAA,KAAK,EAAE,CAAC;AAER,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE1C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpB;;;AAKD;;;;;;;;;AASG;AACI,IAAA,OAAO,CAAC,IAAiC,EAAA;QAC9C,IAAI,IAAI,CAAC,KAAK,EAAE;;AAEd,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;gBAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,aAAA;;AAGD,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAChE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAElC,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAClB,YAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;AACzD,SAAA;QAED,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;AAGlB,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;AAChE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,gBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAClB,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACnB,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;AACtC,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,EAAgB,CAAC;AAC1C,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACpC,SAAA;KACF;AAED;;;AAGG;IACI,OAAO,GAAA;AACZ,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC5B,YAAA,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;AAC7B,SAAA,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAwB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAS,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;QAC5B,MAAM,YAAY,GAA6B,EAAE,CAAC;;AAGlD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,YAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAClB,YAAA,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACtB,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACnB,aAAA;AACF,SAAA;;AAGD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,IAAI,IAAI,IAAI,EAAE;;;;;AAKhB,gBAAA,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACrD,aAAA;AAAM,iBAAA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;AACtB,gBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpB,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACtB,aAAA;AACF,SAAA;QAED,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;;QAGnD,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC3C,SAAA;QACD,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;AACvE,SAAA;KACF;;IA6DM,GAAG,CACR,KAAiD,EACjD,MAAsC,EAAA;AAMtC,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;AACtB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;;QAGD,IAAI,GAAG,GAAqB,IAAI,CAAC;AACjC,QAAA,IAAI,OAAY,CAAC;QACjB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACvC,GAAG,GAAG,KAAK,CAAC;YACZ,OAAO,GAAG,MAAM,CAAC;AAClB,SAAA;AAAM,aAAA;YACL,OAAO,GAAG,KAAK,CAAC;AACjB,SAAA;;AAGD,QAAA,MAAM,WAAW,GAAkC,MAAM,CAAC,MAAM,CAC9D,EAAE,EACF,IAAI,CAAC,QAAQ,EACb,OAAO,CACR,CAAC;;AAGF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACxC,QAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;QAChD,IAAI,UAAU,IAAI,aAAa,EAAE;AAC/B,YAAA,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,KAAa;gBACrC,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACjD,aAAC,CAAC;AACH,SAAA;QAED,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,SAAA;AAAM,aAAA;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACzC,SAAA;KACF;;AAGM,IAAA,MAAM,CAAC,OAA0C,EAAA;AACtD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACrB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AAC9D,YAAA,IAAI,MAAkD,CAAC;AAEvD,YAAA,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,aAAa,EAAE;AACjB,oBAAA,MAAM,GAAG,CAAC,IAAI,KAAa;wBACzB,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACpD,qBAAC,CAAC;AACH,iBAAA;AAAM,qBAAA;oBACL,MAAM,GAAG,aAAa,CAAC;AACxB,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,MAAM,GAAG,aAAa,CAAC;AACxB,aAAA;AAED,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACvB,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;AAChC,aAAA,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;KACF;;IAGM,OAAO,CACZ,QAAsC,EACtC,OAA2C,EAAA;QAE3C,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;AAChD,YAAA,IAAI,MAA6C,CAAC;AAElD,YAAA,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,aAAa,EAAE;oBACjB,MAAM,GAAG,UAAU,IAAU,EAAA;wBAC3B,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACpD,qBAAC,CAAC;AACH,iBAAA;AAAM,qBAAA;oBACL,MAAM,GAAG,aAAa,CAAC;AACxB,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,MAAM,GAAG,aAAa,CAAC;AACxB,aAAA;AAED,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;AAC3B,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;AAChC,aAAA,CAAC,CAAC;AACJ,SAAA;KACF;;IAGM,GAAG,CACR,QAAmC,EACnC,OAA0C,EAAA;QAI1C,IAAI,IAAI,CAAC,KAAK,EAAE;AACd,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;AAChD,YAAA,IAAI,MAA0B,CAAC;AAE/B,YAAA,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,aAAa,EAAE;AACjB,oBAAA,MAAM,GAAG,CAAC,IAAI,KAAwB;wBACpC,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;AACpD,qBAAC,CAAC;AACH,iBAAA;AAAM,qBAAA;oBACL,MAAM,GAAG,aAAa,CAAC;AACxB,iBAAA;AACF,aAAA;AAAM,iBAAA;gBACL,MAAM,GAAG,aAAa,CAAC;AACxB,aAAA;AAED,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;AAC9B,gBAAA,MAAM,EAAE,MAAM;AACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;AAChC,aAAA,CAAC,CAAC;AACJ,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;KACF;;IAGM,UAAU,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;KAChC;;AAGM,IAAA,MAAM,CAAC,GAAkB,EAAA;AAC9B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CACtB,GAAG,IAAI;AACL,YAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD,SAAA,CACF,CAAC;KACH;AAED;;;;;;;AAOG;IACI,OAAO,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;YACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAA;QAED,MAAM,OAAO,GAAG,8CAA8C,CAAC;AAC/D,QAAA,MAAM,WAAW,GAAG;YAClB,GAAG,EAAE,MAAW;AACd,gBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;YACD,GAAG,EAAE,MAAW;AACd,gBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;AAED,YAAA,YAAY,EAAE,KAAK;SACpB,CAAC;QACF,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YACrD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;AAC/C,SAAA;KACF;AAED;;;;;;AAMG;AACK,IAAA,QAAQ,CACd,KAAS,EACT,MAAuC,EACvC,QAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YAC3C,OAAO;AACR,SAAA;AAED,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;QACzB,MAAM,QAAQ,GAAS,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;QAC5B,MAAM,UAAU,GAAS,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAA6B,EAAE,CAAC;QAC9C,MAAM,YAAY,GAA6B,EAAE,CAAC;QAClD,MAAM,YAAY,GAA6B,EAAE,CAAC;AAElD,QAAA,QAAQ,KAAK;AACX,YAAA,KAAK,KAAK;;AAER,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1B,oBAAA,IAAI,IAAI,EAAE;AACR,wBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB,wBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,qBAAA;AACF,iBAAA;gBAED,MAAM;AAER,YAAA,KAAK,QAAQ;;;AAGX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAE1B,oBAAA,IAAI,IAAI,EAAE;wBACR,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACrB,4BAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,IAAI,CAAC,CAAC,CAAC,CACrD,CAAC;4BACF,QAAQ,CAAC,IAAI,CACV,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;AACH,yBAAA;AAAM,6BAAA;AACL,4BAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAClB,4BAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnB,yBAAA;AACF,qBAAA;AAAM,yBAAA;wBACL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACrB,4BAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACrB,4BAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;AACH,yBAEA;AACF,qBAAA;AACF,iBAAA;gBAED,MAAM;AAER,YAAA,KAAK,QAAQ;;AAEX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAClB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACrB,wBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACrB,wBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;AACH,qBAAA;AACF,iBAAA;gBAED,MAAM;AACT,SAAA;QAED,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QAEnD,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAA;QACD,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,EAC5D,QAAQ,CACT,CAAC;AACH,SAAA;QACD,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,EAC5C,QAAQ,CACT,CAAC;AACH,SAAA;KACF;AACF;;ACtjBD;;;;;;AAMG;AACa,SAAA,aAAa,CAG3B,MAAc,EAAE,CAAM,EAAA;AACtB,IAAA,QACE,OAAO,CAAC,KAAK,QAAQ;AACrB,QAAA,CAAC,KAAK,IAAI;QACV,MAAM,KAAK,CAAC,CAAC,MAAM;AACnB,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,KAAK,KAAK,UAAU;AAC7B,QAAA,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU;AAChC,QAAA,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU;AAC/B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;AAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;AAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;AAC5B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU;AAC1B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;AAC9B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;AAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;AAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;AAC9B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU,EAClC;AACJ;;ACjCA;;;;;;AAMG;AACa,SAAA,cAAc,CAG5B,MAAc,EAAE,CAAM,EAAA;AACtB,IAAA,QACE,OAAO,CAAC,KAAK,QAAQ;AACrB,QAAA,CAAC,KAAK,IAAI;QACV,MAAM,KAAK,CAAC,CAAC,MAAM;AACnB,QAAA,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU;AAC/B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;AAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;AAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;AAC5B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;AAC3B,QAAA,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU;AAC1B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;QAC9B,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,EACrC;AACJ;;;;"}