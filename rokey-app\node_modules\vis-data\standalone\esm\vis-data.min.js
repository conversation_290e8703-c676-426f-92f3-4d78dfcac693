/**
 * vis-data
 * http://visjs.org/
 *
 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
 *
 * @version 7.1.9
 * @date    2023-11-24T17:53:34.179Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
function t(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n={exports:{}},o=function(t){return t&&t.Math===Math&&t},i=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof e&&e)||function(){return this}()||e||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},u=!a((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),s=u,c=Function.prototype,f=c.apply,l=c.call,h="object"==typeof Reflect&&Reflect.apply||(s?l.bind(f):function(){return l.apply(f,arguments)}),p=u,v=Function.prototype,d=v.call,y=p&&v.bind.bind(d,d),g=p?y:function(t){return function(){return d.apply(t,arguments)}},m=g,b=m({}.toString),w=m("".slice),_=function(t){return w(b(t),8,-1)},T=_,E=g,O=function(t){if("Function"===T(t))return E(t)},S="object"==typeof document&&document.all,x={all:S,IS_HTMLDDA:void 0===S&&void 0!==S},k=x.all,j=x.IS_HTMLDDA?function(t){return"function"==typeof t||t===k}:function(t){return"function"==typeof t},A={},P=!a((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),I=u,D=Function.prototype.call,C=I?D.bind(D):function(){return D.apply(D,arguments)},L={},R={}.propertyIsEnumerable,M=Object.getOwnPropertyDescriptor,N=M&&!R.call({1:2},1);L.f=N?function(t){var e=M(this,t);return!!e&&e.enumerable}:R;var F,z,U=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},q=a,W=_,Y=Object,G=g("".split),X=q((function(){return!Y("z").propertyIsEnumerable(0)}))?function(t){return"String"===W(t)?G(t,""):Y(t)}:Y,V=function(t){return null==t},B=V,H=TypeError,K=function(t){if(B(t))throw new H("Can't call method on "+t);return t},J=X,$=K,Q=function(t){return J($(t))},Z=j,tt=x.all,et=x.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:Z(t)||t===tt}:function(t){return"object"==typeof t?null!==t:Z(t)},rt={},nt=rt,ot=i,it=j,at=function(t){return it(t)?t:void 0},ut=function(t,e){return arguments.length<2?at(nt[t])||at(ot[t]):nt[t]&&nt[t][e]||ot[t]&&ot[t][e]},st=g({}.isPrototypeOf),ct="undefined"!=typeof navigator&&String(navigator.userAgent)||"",ft=i,lt=ct,ht=ft.process,pt=ft.Deno,vt=ht&&ht.versions||pt&&pt.version,dt=vt&&vt.v8;dt&&(z=(F=dt.split("."))[0]>0&&F[0]<4?1:+(F[0]+F[1])),!z&&lt&&(!(F=lt.match(/Edge\/(\d+)/))||F[1]>=74)&&(F=lt.match(/Chrome\/(\d+)/))&&(z=+F[1]);var yt=z,gt=yt,mt=a,bt=i.String,wt=!!Object.getOwnPropertySymbols&&!mt((function(){var t=Symbol("symbol detection");return!bt(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&gt&&gt<41})),_t=wt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Tt=ut,Et=j,Ot=st,St=Object,xt=_t?function(t){return"symbol"==typeof t}:function(t){var e=Tt("Symbol");return Et(e)&&Ot(e.prototype,St(t))},kt=String,jt=function(t){try{return kt(t)}catch(t){return"Object"}},At=j,Pt=jt,It=TypeError,Dt=function(t){if(At(t))return t;throw new It(Pt(t)+" is not a function")},Ct=Dt,Lt=V,Rt=function(t,e){var r=t[e];return Lt(r)?void 0:Ct(r)},Mt=C,Nt=j,Ft=et,zt=TypeError,Ut={exports:{}},qt=i,Wt=Object.defineProperty,Yt=function(t,e){try{Wt(qt,t,{value:e,configurable:!0,writable:!0})}catch(r){qt[t]=e}return e},Gt="__core-js_shared__",Xt=i[Gt]||Yt(Gt,{}),Vt=Xt;(Ut.exports=function(t,e){return Vt[t]||(Vt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.33.2",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.2/LICENSE",source:"https://github.com/zloirock/core-js"});var Bt=Ut.exports,Ht=K,Kt=Object,Jt=function(t){return Kt(Ht(t))},$t=Jt,Qt=g({}.hasOwnProperty),Zt=Object.hasOwn||function(t,e){return Qt($t(t),e)},te=g,ee=0,re=Math.random(),ne=te(1..toString),oe=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ne(++ee+re,36)},ie=Bt,ae=Zt,ue=oe,se=wt,ce=_t,fe=i.Symbol,le=ie("wks"),he=ce?fe.for||fe:fe&&fe.withoutSetter||ue,pe=function(t){return ae(le,t)||(le[t]=se&&ae(fe,t)?fe[t]:he("Symbol."+t)),le[t]},ve=C,de=et,ye=xt,ge=Rt,me=function(t,e){var r,n;if("string"===e&&Nt(r=t.toString)&&!Ft(n=Mt(r,t)))return n;if(Nt(r=t.valueOf)&&!Ft(n=Mt(r,t)))return n;if("string"!==e&&Nt(r=t.toString)&&!Ft(n=Mt(r,t)))return n;throw new zt("Can't convert object to primitive value")},be=TypeError,we=pe("toPrimitive"),_e=function(t,e){if(!de(t)||ye(t))return t;var r,n=ge(t,we);if(n){if(void 0===e&&(e="default"),r=ve(n,t,e),!de(r)||ye(r))return r;throw new be("Can't convert object to primitive value")}return void 0===e&&(e="number"),me(t,e)},Te=xt,Ee=function(t){var e=_e(t,"string");return Te(e)?e:e+""},Oe=et,Se=i.document,xe=Oe(Se)&&Oe(Se.createElement),ke=function(t){return xe?Se.createElement(t):{}},je=ke,Ae=!P&&!a((function(){return 7!==Object.defineProperty(je("div"),"a",{get:function(){return 7}}).a})),Pe=P,Ie=C,De=L,Ce=U,Le=Q,Re=Ee,Me=Zt,Ne=Ae,Fe=Object.getOwnPropertyDescriptor;A.f=Pe?Fe:function(t,e){if(t=Le(t),e=Re(e),Ne)try{return Fe(t,e)}catch(t){}if(Me(t,e))return Ce(!Ie(De.f,t,e),t[e])};var ze=a,Ue=j,qe=/#|\.prototype\./,We=function(t,e){var r=Ge[Ye(t)];return r===Ve||r!==Xe&&(Ue(e)?ze(e):!!e)},Ye=We.normalize=function(t){return String(t).replace(qe,".").toLowerCase()},Ge=We.data={},Xe=We.NATIVE="N",Ve=We.POLYFILL="P",Be=We,He=Dt,Ke=u,Je=O(O.bind),$e=function(t,e){return He(t),void 0===e?t:Ke?Je(t,e):function(){return t.apply(e,arguments)}},Qe={},Ze=P&&a((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),tr=et,er=String,rr=TypeError,nr=function(t){if(tr(t))return t;throw new rr(er(t)+" is not an object")},or=P,ir=Ae,ar=Ze,ur=nr,sr=Ee,cr=TypeError,fr=Object.defineProperty,lr=Object.getOwnPropertyDescriptor,hr="enumerable",pr="configurable",vr="writable";Qe.f=or?ar?function(t,e,r){if(ur(t),e=sr(e),ur(r),"function"==typeof t&&"prototype"===e&&"value"in r&&vr in r&&!r[vr]){var n=lr(t,e);n&&n[vr]&&(t[e]=r.value,r={configurable:pr in r?r[pr]:n[pr],enumerable:hr in r?r[hr]:n[hr],writable:!1})}return fr(t,e,r)}:fr:function(t,e,r){if(ur(t),e=sr(e),ur(r),ir)try{return fr(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new cr("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var dr=Qe,yr=U,gr=P?function(t,e,r){return dr.f(t,e,yr(1,r))}:function(t,e,r){return t[e]=r,t},mr=i,br=h,wr=O,_r=j,Tr=A.f,Er=Be,Or=rt,Sr=$e,xr=gr,kr=Zt,jr=function(t){var e=function(r,n,o){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,n)}return new t(r,n,o)}return br(t,this,arguments)};return e.prototype=t.prototype,e},Ar=function(t,e){var r,n,o,i,a,u,s,c,f,l=t.target,h=t.global,p=t.stat,v=t.proto,d=h?mr:p?mr[l]:(mr[l]||{}).prototype,y=h?Or:Or[l]||xr(Or,l,{})[l],g=y.prototype;for(i in e)n=!(r=Er(h?i:l+(p?".":"#")+i,t.forced))&&d&&kr(d,i),u=y[i],n&&(s=t.dontCallGetSet?(f=Tr(d,i))&&f.value:d[i]),a=n&&s?s:e[i],n&&typeof u==typeof a||(c=t.bind&&n?Sr(a,mr):t.wrap&&n?jr(a):v&&_r(a)?wr(a):a,(t.sham||a&&a.sham||u&&u.sham)&&xr(c,"sham",!0),xr(y,i,c),v&&(kr(Or,o=l+"Prototype")||xr(Or,o,{}),xr(Or[o],i,a),t.real&&g&&(r||!g[i])&&xr(g,i,a)))},Pr=Ar,Ir=P,Dr=Qe.f;Pr({target:"Object",stat:!0,forced:Object.defineProperty!==Dr,sham:!Ir},{defineProperty:Dr});var Cr=rt.Object,Lr=n.exports=function(t,e,r){return Cr.defineProperty(t,e,r)};Cr.defineProperty.sham&&(Lr.sham=!0);var Rr=n.exports,Mr=Rr,Nr=r(Mr),Fr=_,zr=Array.isArray||function(t){return"Array"===Fr(t)},Ur=Math.ceil,qr=Math.floor,Wr=Math.trunc||function(t){var e=+t;return(e>0?qr:Ur)(e)},Yr=function(t){var e=+t;return e!=e||0===e?0:Wr(e)},Gr=Yr,Xr=Math.min,Vr=function(t){return t>0?Xr(Gr(t),9007199254740991):0},Br=function(t){return Vr(t.length)},Hr=TypeError,Kr=function(t){if(t>9007199254740991)throw Hr("Maximum allowed index exceeded");return t},Jr=Ee,$r=Qe,Qr=U,Zr=function(t,e,r){var n=Jr(e);n in t?$r.f(t,n,Qr(0,r)):t[n]=r},tn={};tn[pe("toStringTag")]="z";var en="[object z]"===String(tn),rn=en,nn=j,on=_,an=pe("toStringTag"),un=Object,sn="Arguments"===on(function(){return arguments}()),cn=rn?on:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=un(t),an))?r:sn?on(e):"Object"===(n=on(e))&&nn(e.callee)?"Arguments":n},fn=j,ln=Xt,hn=g(Function.toString);fn(ln.inspectSource)||(ln.inspectSource=function(t){return hn(t)});var pn=ln.inspectSource,vn=g,dn=a,yn=j,gn=cn,mn=pn,bn=function(){},wn=[],_n=ut("Reflect","construct"),Tn=/^\s*(?:class|function)\b/,En=vn(Tn.exec),On=!Tn.test(bn),Sn=function(t){if(!yn(t))return!1;try{return _n(bn,wn,t),!0}catch(t){return!1}},xn=function(t){if(!yn(t))return!1;switch(gn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return On||!!En(Tn,mn(t))}catch(t){return!0}};xn.sham=!0;var kn=!_n||dn((function(){var t;return Sn(Sn.call)||!Sn(Object)||!Sn((function(){t=!0}))||t}))?xn:Sn,jn=zr,An=kn,Pn=et,In=pe("species"),Dn=Array,Cn=function(t){var e;return jn(t)&&(e=t.constructor,(An(e)&&(e===Dn||jn(e.prototype))||Pn(e)&&null===(e=e[In]))&&(e=void 0)),void 0===e?Dn:e},Ln=function(t,e){return new(Cn(t))(0===e?0:e)},Rn=a,Mn=yt,Nn=pe("species"),Fn=function(t){return Mn>=51||!Rn((function(){var e=[];return(e.constructor={})[Nn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},zn=Ar,Un=a,qn=zr,Wn=et,Yn=Jt,Gn=Br,Xn=Kr,Vn=Zr,Bn=Ln,Hn=Fn,Kn=yt,Jn=pe("isConcatSpreadable"),$n=Kn>=51||!Un((function(){var t=[];return t[Jn]=!1,t.concat()[0]!==t})),Qn=function(t){if(!Wn(t))return!1;var e=t[Jn];return void 0!==e?!!e:qn(t)};zn({target:"Array",proto:!0,arity:1,forced:!$n||!Hn("concat")},{concat:function(t){var e,r,n,o,i,a=Yn(this),u=Bn(a,0),s=0;for(e=-1,n=arguments.length;e<n;e++)if(Qn(i=-1===e?a:arguments[e]))for(o=Gn(i),Xn(s+o),r=0;r<o;r++,s++)r in i&&Vn(u,s,i[r]);else Xn(s+1),Vn(u,s++,i);return u.length=s,u}});var Zn=cn,to=String,eo=function(t){if("Symbol"===Zn(t))throw new TypeError("Cannot convert a Symbol value to a string");return to(t)},ro={},no=Yr,oo=Math.max,io=Math.min,ao=function(t,e){var r=no(t);return r<0?oo(r+e,0):io(r,e)},uo=Q,so=ao,co=Br,fo=function(t){return function(e,r,n){var o,i=uo(e),a=co(i),u=so(n,a);if(t&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===r)return t||u||0;return!t&&-1}},lo={includes:fo(!0),indexOf:fo(!1)},ho={},po=Zt,vo=Q,yo=lo.indexOf,go=ho,mo=g([].push),bo=function(t,e){var r,n=vo(t),o=0,i=[];for(r in n)!po(go,r)&&po(n,r)&&mo(i,r);for(;e.length>o;)po(n,r=e[o++])&&(~yo(i,r)||mo(i,r));return i},wo=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_o=bo,To=wo,Eo=Object.keys||function(t){return _o(t,To)},Oo=P,So=Ze,xo=Qe,ko=nr,jo=Q,Ao=Eo;ro.f=Oo&&!So?Object.defineProperties:function(t,e){ko(t);for(var r,n=jo(e),o=Ao(e),i=o.length,a=0;i>a;)xo.f(t,r=o[a++],n[r]);return t};var Po,Io=ut("document","documentElement"),Do=oe,Co=Bt("keys"),Lo=function(t){return Co[t]||(Co[t]=Do(t))},Ro=nr,Mo=ro,No=wo,Fo=ho,zo=Io,Uo=ke,qo="prototype",Wo="script",Yo=Lo("IE_PROTO"),Go=function(){},Xo=function(t){return"<"+Wo+">"+t+"</"+Wo+">"},Vo=function(t){t.write(Xo("")),t.close();var e=t.parentWindow.Object;return t=null,e},Bo=function(){try{Po=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;Bo="undefined"!=typeof document?document.domain&&Po?Vo(Po):(e=Uo("iframe"),r="java"+Wo+":",e.style.display="none",zo.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(Xo("document.F=Object")),t.close(),t.F):Vo(Po);for(var n=No.length;n--;)delete Bo[qo][No[n]];return Bo()};Fo[Yo]=!0;var Ho=Object.create||function(t,e){var r;return null!==t?(Go[qo]=Ro(t),r=new Go,Go[qo]=null,r[Yo]=t):r=Bo(),void 0===e?r:Mo.f(r,e)},Ko={},Jo=bo,$o=wo.concat("length","prototype");Ko.f=Object.getOwnPropertyNames||function(t){return Jo(t,$o)};var Qo={},Zo=ao,ti=Br,ei=Zr,ri=Array,ni=Math.max,oi=function(t,e,r){for(var n=ti(t),o=Zo(e,n),i=Zo(void 0===r?n:r,n),a=ri(ni(i-o,0)),u=0;o<i;o++,u++)ei(a,u,t[o]);return a.length=u,a},ii=_,ai=Q,ui=Ko.f,si=oi,ci="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Qo.f=function(t){return ci&&"Window"===ii(t)?function(t){try{return ui(t)}catch(t){return si(ci)}}(t):ui(ai(t))};var fi={};fi.f=Object.getOwnPropertySymbols;var li=gr,hi=function(t,e,r,n){return n&&n.enumerable?t[e]=r:li(t,e,r),t},pi=Qe,vi=function(t,e,r){return pi.f(t,e,r)},di={},yi=pe;di.f=yi;var gi,mi,bi,wi=rt,_i=Zt,Ti=di,Ei=Qe.f,Oi=function(t){var e=wi.Symbol||(wi.Symbol={});_i(e,t)||Ei(e,t,{value:Ti.f(t)})},Si=C,xi=ut,ki=pe,ji=hi,Ai=function(){var t=xi("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,n=ki("toPrimitive");e&&!e[n]&&ji(e,n,(function(t){return Si(r,this)}),{arity:1})},Pi=cn,Ii=en?{}.toString:function(){return"[object "+Pi(this)+"]"},Di=en,Ci=Qe.f,Li=gr,Ri=Zt,Mi=Ii,Ni=pe("toStringTag"),Fi=function(t,e,r,n){if(t){var o=r?t:t.prototype;Ri(o,Ni)||Ci(o,Ni,{configurable:!0,value:e}),n&&!Di&&Li(o,"toString",Mi)}},zi=j,Ui=i.WeakMap,qi=zi(Ui)&&/native code/.test(String(Ui)),Wi=i,Yi=et,Gi=gr,Xi=Zt,Vi=Xt,Bi=Lo,Hi=ho,Ki="Object already initialized",Ji=Wi.TypeError,$i=Wi.WeakMap;if(qi||Vi.state){var Qi=Vi.state||(Vi.state=new $i);Qi.get=Qi.get,Qi.has=Qi.has,Qi.set=Qi.set,gi=function(t,e){if(Qi.has(t))throw new Ji(Ki);return e.facade=t,Qi.set(t,e),e},mi=function(t){return Qi.get(t)||{}},bi=function(t){return Qi.has(t)}}else{var Zi=Bi("state");Hi[Zi]=!0,gi=function(t,e){if(Xi(t,Zi))throw new Ji(Ki);return e.facade=t,Gi(t,Zi,e),e},mi=function(t){return Xi(t,Zi)?t[Zi]:{}},bi=function(t){return Xi(t,Zi)}}var ta={set:gi,get:mi,has:bi,enforce:function(t){return bi(t)?mi(t):gi(t,{})},getterFor:function(t){return function(e){var r;if(!Yi(e)||(r=mi(e)).type!==t)throw new Ji("Incompatible receiver, "+t+" required");return r}}},ea=$e,ra=X,na=Jt,oa=Br,ia=Ln,aa=g([].push),ua=function(t){var e=1===t,r=2===t,n=3===t,o=4===t,i=6===t,a=7===t,u=5===t||i;return function(s,c,f,l){for(var h,p,v=na(s),d=ra(v),y=ea(c,f),g=oa(d),m=0,b=l||ia,w=e?b(s,g):r||a?b(s,0):void 0;g>m;m++)if((u||m in d)&&(p=y(h=d[m],m,v),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return h;case 6:return m;case 2:aa(w,h)}else switch(t){case 4:return!1;case 7:aa(w,h)}return i?-1:n||o?o:w}},sa={forEach:ua(0),map:ua(1),filter:ua(2),some:ua(3),every:ua(4),find:ua(5),findIndex:ua(6),filterReject:ua(7)},ca=Ar,fa=i,la=C,ha=g,pa=P,va=wt,da=a,ya=Zt,ga=st,ma=nr,ba=Q,wa=Ee,_a=eo,Ta=U,Ea=Ho,Oa=Eo,Sa=Ko,xa=Qo,ka=fi,ja=A,Aa=Qe,Pa=ro,Ia=L,Da=hi,Ca=vi,La=Bt,Ra=ho,Ma=oe,Na=pe,Fa=di,za=Oi,Ua=Ai,qa=Fi,Wa=ta,Ya=sa.forEach,Ga=Lo("hidden"),Xa="Symbol",Va="prototype",Ba=Wa.set,Ha=Wa.getterFor(Xa),Ka=Object[Va],Ja=fa.Symbol,$a=Ja&&Ja[Va],Qa=fa.RangeError,Za=fa.TypeError,tu=fa.QObject,eu=ja.f,ru=Aa.f,nu=xa.f,ou=Ia.f,iu=ha([].push),au=La("symbols"),uu=La("op-symbols"),su=La("wks"),cu=!tu||!tu[Va]||!tu[Va].findChild,fu=function(t,e,r){var n=eu(Ka,e);n&&delete Ka[e],ru(t,e,r),n&&t!==Ka&&ru(Ka,e,n)},lu=pa&&da((function(){return 7!==Ea(ru({},"a",{get:function(){return ru(this,"a",{value:7}).a}})).a}))?fu:ru,hu=function(t,e){var r=au[t]=Ea($a);return Ba(r,{type:Xa,tag:t,description:e}),pa||(r.description=e),r},pu=function(t,e,r){t===Ka&&pu(uu,e,r),ma(t);var n=wa(e);return ma(r),ya(au,n)?(r.enumerable?(ya(t,Ga)&&t[Ga][n]&&(t[Ga][n]=!1),r=Ea(r,{enumerable:Ta(0,!1)})):(ya(t,Ga)||ru(t,Ga,Ta(1,{})),t[Ga][n]=!0),lu(t,n,r)):ru(t,n,r)},vu=function(t,e){ma(t);var r=ba(e),n=Oa(r).concat(mu(r));return Ya(n,(function(e){pa&&!la(du,r,e)||pu(t,e,r[e])})),t},du=function(t){var e=wa(t),r=la(ou,this,e);return!(this===Ka&&ya(au,e)&&!ya(uu,e))&&(!(r||!ya(this,e)||!ya(au,e)||ya(this,Ga)&&this[Ga][e])||r)},yu=function(t,e){var r=ba(t),n=wa(e);if(r!==Ka||!ya(au,n)||ya(uu,n)){var o=eu(r,n);return!o||!ya(au,n)||ya(r,Ga)&&r[Ga][n]||(o.enumerable=!0),o}},gu=function(t){var e=nu(ba(t)),r=[];return Ya(e,(function(t){ya(au,t)||ya(Ra,t)||iu(r,t)})),r},mu=function(t){var e=t===Ka,r=nu(e?uu:ba(t)),n=[];return Ya(r,(function(t){!ya(au,t)||e&&!ya(Ka,t)||iu(n,au[t])})),n};va||(Da($a=(Ja=function(){if(ga($a,this))throw new Za("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?_a(arguments[0]):void 0,e=Ma(t),r=function(t){var n=void 0===this?fa:this;n===Ka&&la(r,uu,t),ya(n,Ga)&&ya(n[Ga],e)&&(n[Ga][e]=!1);var o=Ta(1,t);try{lu(n,e,o)}catch(t){if(!(t instanceof Qa))throw t;fu(n,e,o)}};return pa&&cu&&lu(Ka,e,{configurable:!0,set:r}),hu(e,t)})[Va],"toString",(function(){return Ha(this).tag})),Da(Ja,"withoutSetter",(function(t){return hu(Ma(t),t)})),Ia.f=du,Aa.f=pu,Pa.f=vu,ja.f=yu,Sa.f=xa.f=gu,ka.f=mu,Fa.f=function(t){return hu(Na(t),t)},pa&&Ca($a,"description",{configurable:!0,get:function(){return Ha(this).description}})),ca({global:!0,constructor:!0,wrap:!0,forced:!va,sham:!va},{Symbol:Ja}),Ya(Oa(su),(function(t){za(t)})),ca({target:Xa,stat:!0,forced:!va},{useSetter:function(){cu=!0},useSimple:function(){cu=!1}}),ca({target:"Object",stat:!0,forced:!va,sham:!pa},{create:function(t,e){return void 0===e?Ea(t):vu(Ea(t),e)},defineProperty:pu,defineProperties:vu,getOwnPropertyDescriptor:yu}),ca({target:"Object",stat:!0,forced:!va},{getOwnPropertyNames:gu}),Ua(),qa(Ja,Xa),Ra[Ga]=!0;var bu=wt&&!!Symbol.for&&!!Symbol.keyFor,wu=Ar,_u=ut,Tu=Zt,Eu=eo,Ou=Bt,Su=bu,xu=Ou("string-to-symbol-registry"),ku=Ou("symbol-to-string-registry");wu({target:"Symbol",stat:!0,forced:!Su},{for:function(t){var e=Eu(t);if(Tu(xu,e))return xu[e];var r=_u("Symbol")(e);return xu[e]=r,ku[r]=e,r}});var ju=Ar,Au=Zt,Pu=xt,Iu=jt,Du=bu,Cu=Bt("symbol-to-string-registry");ju({target:"Symbol",stat:!0,forced:!Du},{keyFor:function(t){if(!Pu(t))throw new TypeError(Iu(t)+" is not a symbol");if(Au(Cu,t))return Cu[t]}});var Lu=g([].slice),Ru=zr,Mu=j,Nu=_,Fu=eo,zu=g([].push),Uu=Ar,qu=ut,Wu=h,Yu=C,Gu=g,Xu=a,Vu=j,Bu=xt,Hu=Lu,Ku=function(t){if(Mu(t))return t;if(Ru(t)){for(var e=t.length,r=[],n=0;n<e;n++){var o=t[n];"string"==typeof o?zu(r,o):"number"!=typeof o&&"Number"!==Nu(o)&&"String"!==Nu(o)||zu(r,Fu(o))}var i=r.length,a=!0;return function(t,e){if(a)return a=!1,e;if(Ru(this))return e;for(var n=0;n<i;n++)if(r[n]===t)return e}}},Ju=wt,$u=String,Qu=qu("JSON","stringify"),Zu=Gu(/./.exec),ts=Gu("".charAt),es=Gu("".charCodeAt),rs=Gu("".replace),ns=Gu(1..toString),os=/[\uD800-\uDFFF]/g,is=/^[\uD800-\uDBFF]$/,as=/^[\uDC00-\uDFFF]$/,us=!Ju||Xu((function(){var t=qu("Symbol")("stringify detection");return"[null]"!==Qu([t])||"{}"!==Qu({a:t})||"{}"!==Qu(Object(t))})),ss=Xu((function(){return'"\\udf06\\ud834"'!==Qu("\udf06\ud834")||'"\\udead"'!==Qu("\udead")})),cs=function(t,e){var r=Hu(arguments),n=Ku(e);if(Vu(n)||void 0!==t&&!Bu(t))return r[1]=function(t,e){if(Vu(n)&&(e=Yu(n,this,$u(t),e)),!Bu(e))return e},Wu(Qu,null,r)},fs=function(t,e,r){var n=ts(r,e-1),o=ts(r,e+1);return Zu(is,t)&&!Zu(as,o)||Zu(as,t)&&!Zu(is,n)?"\\u"+ns(es(t,0),16):t};Qu&&Uu({target:"JSON",stat:!0,arity:3,forced:us||ss},{stringify:function(t,e,r){var n=Hu(arguments),o=Wu(us?cs:Qu,null,n);return ss&&"string"==typeof o?rs(o,os,fs):o}});var ls=fi,hs=Jt;Ar({target:"Object",stat:!0,forced:!wt||a((function(){ls.f(1)}))},{getOwnPropertySymbols:function(t){var e=ls.f;return e?e(hs(t)):[]}}),Oi("asyncIterator"),Oi("hasInstance"),Oi("isConcatSpreadable"),Oi("iterator"),Oi("match"),Oi("matchAll"),Oi("replace"),Oi("search"),Oi("species"),Oi("split");var ps=Ai;Oi("toPrimitive"),ps();var vs=ut,ds=Fi;Oi("toStringTag"),ds(vs("Symbol"),"Symbol"),Oi("unscopables"),Fi(i.JSON,"JSON",!0);var ys,gs,ms,bs=rt.Symbol,ws={},_s=P,Ts=Zt,Es=Function.prototype,Os=_s&&Object.getOwnPropertyDescriptor,Ss=Ts(Es,"name"),xs={EXISTS:Ss,PROPER:Ss&&"something"===function(){}.name,CONFIGURABLE:Ss&&(!_s||_s&&Os(Es,"name").configurable)},ks=!a((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),js=Zt,As=j,Ps=Jt,Is=ks,Ds=Lo("IE_PROTO"),Cs=Object,Ls=Cs.prototype,Rs=Is?Cs.getPrototypeOf:function(t){var e=Ps(t);if(js(e,Ds))return e[Ds];var r=e.constructor;return As(r)&&e instanceof r?r.prototype:e instanceof Cs?Ls:null},Ms=a,Ns=j,Fs=et,zs=Ho,Us=Rs,qs=hi,Ws=pe("iterator"),Ys=!1;[].keys&&("next"in(ms=[].keys())?(gs=Us(Us(ms)))!==Object.prototype&&(ys=gs):Ys=!0);var Gs=!Fs(ys)||Ms((function(){var t={};return ys[Ws].call(t)!==t}));Ns((ys=Gs?{}:zs(ys))[Ws])||qs(ys,Ws,(function(){return this}));var Xs={IteratorPrototype:ys,BUGGY_SAFARI_ITERATORS:Ys},Vs=Xs.IteratorPrototype,Bs=Ho,Hs=U,Ks=Fi,Js=ws,$s=function(){return this},Qs=g,Zs=Dt,tc=j,ec=String,rc=TypeError,nc=function(t,e,r){try{return Qs(Zs(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}},oc=nr,ic=function(t){if("object"==typeof t||tc(t))return t;throw new rc("Can't set "+ec(t)+" as a prototype")},ac=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=nc(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return oc(r),ic(n),e?t(r,n):r.__proto__=n,r}}():void 0),uc=Ar,sc=C,cc=xs,fc=function(t,e,r,n){var o=e+" Iterator";return t.prototype=Bs(Vs,{next:Hs(+!n,r)}),Ks(t,o,!1,!0),Js[o]=$s,t},lc=Rs,hc=Fi,pc=hi,vc=ws,dc=Xs,yc=cc.PROPER,gc=dc.BUGGY_SAFARI_ITERATORS,mc=pe("iterator"),bc="keys",wc="values",_c="entries",Tc=function(){return this},Ec=function(t,e,r,n,o,i,a){fc(r,e,n);var u,s,c,f=function(t){if(t===o&&d)return d;if(!gc&&t&&t in p)return p[t];switch(t){case bc:case wc:case _c:return function(){return new r(this,t)}}return function(){return new r(this)}},l=e+" Iterator",h=!1,p=t.prototype,v=p[mc]||p["@@iterator"]||o&&p[o],d=!gc&&v||f(o),y="Array"===e&&p.entries||v;if(y&&(u=lc(y.call(new t)))!==Object.prototype&&u.next&&(hc(u,l,!0,!0),vc[l]=Tc),yc&&o===wc&&v&&v.name!==wc&&(h=!0,d=function(){return sc(v,this)}),o)if(s={values:f(wc),keys:i?d:f(bc),entries:f(_c)},a)for(c in s)(gc||h||!(c in p))&&pc(p,c,s[c]);else uc({target:e,proto:!0,forced:gc||h},s);return a&&p[mc]!==d&&pc(p,mc,d,{name:o}),vc[e]=d,s},Oc=function(t,e){return{value:t,done:e}},Sc=Q,xc=ws,kc=ta;Qe.f;var jc=Ec,Ac=Oc,Pc="Array Iterator",Ic=kc.set,Dc=kc.getterFor(Pc);jc(Array,"Array",(function(t,e){Ic(this,{type:Pc,target:Sc(t),index:0,kind:e})}),(function(){var t=Dc(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=void 0,Ac(void 0,!0);switch(t.kind){case"keys":return Ac(r,!1);case"values":return Ac(e[r],!1)}return Ac([r,e[r]],!1)}),"values"),xc.Arguments=xc.Array;var Cc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Lc=i,Rc=cn,Mc=gr,Nc=ws,Fc=pe("toStringTag");for(var zc in Cc){var Uc=Lc[zc],qc=Uc&&Uc.prototype;qc&&Rc(qc)!==Fc&&Mc(qc,Fc,zc),Nc[zc]=Nc.Array}var Wc=bs,Yc=pe,Gc=Qe.f,Xc=Yc("metadata"),Vc=Function.prototype;void 0===Vc[Xc]&&Gc(Vc,Xc,{value:null}),Oi("asyncDispose"),Oi("dispose"),Oi("metadata");var Bc=Wc,Hc=g,Kc=ut("Symbol"),Jc=Kc.keyFor,$c=Hc(Kc.prototype.valueOf),Qc=Kc.isRegisteredSymbol||function(t){try{return void 0!==Jc($c(t))}catch(t){return!1}};Ar({target:"Symbol",stat:!0},{isRegisteredSymbol:Qc});for(var Zc=Bt,tf=ut,ef=g,rf=xt,nf=pe,of=tf("Symbol"),af=of.isWellKnownSymbol,uf=tf("Object","getOwnPropertyNames"),sf=ef(of.prototype.valueOf),cf=Zc("wks"),ff=0,lf=uf(of),hf=lf.length;ff<hf;ff++)try{var pf=lf[ff];rf(of[pf])&&nf(pf)}catch(t){}var vf=function(t){if(af&&af(t))return!0;try{for(var e=sf(t),r=0,n=uf(cf),o=n.length;r<o;r++)if(cf[n[r]]==e)return!0}catch(t){}return!1};Ar({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:vf}),Oi("matcher"),Oi("observable"),Ar({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Qc}),Ar({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:vf}),Oi("metadataKey"),Oi("patternMatch"),Oi("replaceAll");var df=Bc,yf=r(df),gf=g,mf=Yr,bf=eo,wf=K,_f=gf("".charAt),Tf=gf("".charCodeAt),Ef=gf("".slice),Of=function(t){return function(e,r){var n,o,i=bf(wf(e)),a=mf(r),u=i.length;return a<0||a>=u?t?"":void 0:(n=Tf(i,a))<55296||n>56319||a+1===u||(o=Tf(i,a+1))<56320||o>57343?t?_f(i,a):n:t?Ef(i,a,a+2):o-56320+(n-55296<<10)+65536}},Sf={codeAt:Of(!1),charAt:Of(!0)}.charAt,xf=eo,kf=ta,jf=Ec,Af=Oc,Pf="String Iterator",If=kf.set,Df=kf.getterFor(Pf);jf(String,"String",(function(t){If(this,{type:Pf,string:xf(t),index:0})}),(function(){var t,e=Df(this),r=e.string,n=e.index;return n>=r.length?Af(void 0,!0):(t=Sf(r,n),e.index+=t.length,Af(t,!1))}));var Cf=di.f("iterator"),Lf=Cf,Rf=r(Lf);function Mf(t){return Mf="function"==typeof yf&&"symbol"==typeof Rf?function(t){return typeof t}:function(t){return t&&"function"==typeof yf&&t.constructor===yf&&t!==yf.prototype?"symbol":typeof t},Mf(t)}var Nf=r(di.f("toPrimitive"));function Ff(t){var e=function(t,e){if("object"!==Mf(t)||null===t)return t;var r=t[Nf];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Mf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Mf(e)?e:String(e)}function zf(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Nr(t,Ff(n.key),n)}}function Uf(t,e,r){return e&&zf(t.prototype,e),r&&zf(t,r),Nr(t,"prototype",{writable:!1}),t}function qf(t,e,r){return(e=Ff(e))in t?Nr(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Wf=g,Yf=Dt,Gf=et,Xf=Zt,Vf=Lu,Bf=u,Hf=Function,Kf=Wf([].concat),Jf=Wf([].join),$f={},Qf=Bf?Hf.bind:function(t){var e=Yf(this),r=e.prototype,n=Vf(arguments,1),o=function(){var r=Kf(n,Vf(arguments));return this instanceof o?function(t,e,r){if(!Xf($f,e)){for(var n=[],o=0;o<e;o++)n[o]="a["+o+"]";$f[e]=Hf("C,a","return new C("+Jf(n,",")+")")}return $f[e](t,r)}(e,r.length,r):e.apply(t,r)};return Gf(r)&&(o.prototype=r),o},Zf=Qf;Ar({target:"Function",proto:!0,forced:Function.bind!==Zf},{bind:Zf});var tl=i,el=rt,rl=function(t,e){var r=el[t+"Prototype"],n=r&&r[e];if(n)return n;var o=tl[t],i=o&&o.prototype;return i&&i[e]},nl=rl("Function","bind"),ol=st,il=nl,al=Function.prototype,ul=function(t){var e=t.bind;return t===al||ol(al,t)&&e===al.bind?il:e},sl=r(ul),cl=Dt,fl=Jt,ll=X,hl=Br,pl=TypeError,vl=function(t){return function(e,r,n,o){cl(r);var i=fl(e),a=ll(i),u=hl(i),s=t?u-1:0,c=t?-1:1;if(n<2)for(;;){if(s in a){o=a[s],s+=c;break}if(s+=c,t?s<0:u<=s)throw new pl("Reduce of empty array with no initial value")}for(;t?s>=0:u>s;s+=c)s in a&&(o=r(o,a[s],s,i));return o}},dl={left:vl(!1),right:vl(!0)},yl=a,gl=function(t,e){var r=[][t];return!!r&&yl((function(){r.call(null,e||function(){return 1},1)}))},ml="process"===_(i.process),bl=dl.left;Ar({target:"Array",proto:!0,forced:!ml&&yt>79&&yt<83||!gl("reduce")},{reduce:function(t){var e=arguments.length;return bl(this,t,e,e>1?arguments[1]:void 0)}});var wl=rl("Array","reduce"),_l=st,Tl=wl,El=Array.prototype,Ol=r((function(t){var e=t.reduce;return t===El||_l(El,t)&&e===El.reduce?Tl:e})),Sl=sa.filter;Ar({target:"Array",proto:!0,forced:!Fn("filter")},{filter:function(t){return Sl(this,t,arguments.length>1?arguments[1]:void 0)}});var xl=rl("Array","filter"),kl=st,jl=xl,Al=Array.prototype,Pl=r((function(t){var e=t.filter;return t===Al||kl(Al,t)&&e===Al.filter?jl:e})),Il=sa.map;Ar({target:"Array",proto:!0,forced:!Fn("map")},{map:function(t){return Il(this,t,arguments.length>1?arguments[1]:void 0)}});var Dl=rl("Array","map"),Cl=st,Ll=Dl,Rl=Array.prototype,Ml=r((function(t){var e=t.map;return t===Rl||Cl(Rl,t)&&e===Rl.map?Ll:e})),Nl=zr,Fl=Br,zl=Kr,Ul=$e,ql=function(t,e,r,n,o,i,a,u){for(var s,c,f=o,l=0,h=!!a&&Ul(a,u);l<n;)l in r&&(s=h?h(r[l],l,e):r[l],i>0&&Nl(s)?(c=Fl(s),f=ql(t,e,s,c,f,i-1)-1):(zl(f+1),t[f]=s),f++),l++;return f},Wl=ql,Yl=Dt,Gl=Jt,Xl=Br,Vl=Ln;Ar({target:"Array",proto:!0},{flatMap:function(t){var e,r=Gl(this),n=Xl(r);return Yl(t),(e=Vl(r,0)).length=Wl(e,r,r,n,0,1,t,arguments.length>1?arguments[1]:void 0),e}});var Bl=rl("Array","flatMap"),Hl=st,Kl=Bl,Jl=Array.prototype,$l=r((function(t){var e=t.flatMap;return t===Jl||Hl(Jl,t)&&e===Jl.flatMap?Kl:e}));function Ql(t){return new th(t)}var Zl=function(){function e(r,n,o){var i,a,u;t(this,e),qf(this,"_listeners",{add:sl(i=this._add).call(i,this),remove:sl(a=this._remove).call(a,this),update:sl(u=this._update).call(u,this)}),this._source=r,this._transformers=n,this._target=o}return Uf(e,[{key:"all",value:function(){return this._target.update(this._transformItems(this._source.get())),this}},{key:"start",value:function(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}},{key:"stop",value:function(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}},{key:"_transformItems",value:function(t){var e;return Ol(e=this._transformers).call(e,(function(t,e){return e(t)}),t)}},{key:"_add",value:function(t,e){null!=e&&this._target.add(this._transformItems(this._source.get(e.items)))}},{key:"_update",value:function(t,e){null!=e&&this._target.update(this._transformItems(this._source.get(e.items)))}},{key:"_remove",value:function(t,e){null!=e&&this._target.remove(this._transformItems(e.oldData))}}]),e}(),th=function(){function e(r){t(this,e),qf(this,"_transformers",[]),this._source=r}return Uf(e,[{key:"filter",value:function(t){return this._transformers.push((function(e){return Pl(e).call(e,t)})),this}},{key:"map",value:function(t){return this._transformers.push((function(e){return Ml(e).call(e,t)})),this}},{key:"flatMap",value:function(t){return this._transformers.push((function(e){return $l(e).call(e,t)})),this}},{key:"to",value:function(t){return new Zl(this._source,this._transformers,t)}}]),e}(),eh=C,rh=nr,nh=Rt,oh=function(t,e,r){var n,o;rh(t);try{if(!(n=nh(t,"return"))){if("throw"===e)throw r;return r}n=eh(n,t)}catch(t){o=!0,n=t}if("throw"===e)throw r;if(o)throw n;return rh(n),r},ih=nr,ah=oh,uh=ws,sh=pe("iterator"),ch=Array.prototype,fh=function(t){return void 0!==t&&(uh.Array===t||ch[sh]===t)},lh=cn,hh=Rt,ph=V,vh=ws,dh=pe("iterator"),yh=function(t){if(!ph(t))return hh(t,dh)||hh(t,"@@iterator")||vh[lh(t)]},gh=C,mh=Dt,bh=nr,wh=jt,_h=yh,Th=TypeError,Eh=function(t,e){var r=arguments.length<2?_h(t):e;if(mh(r))return bh(gh(r,t));throw new Th(wh(t)+" is not iterable")},Oh=$e,Sh=C,xh=Jt,kh=function(t,e,r,n){try{return n?e(ih(r)[0],r[1]):e(r)}catch(e){ah(t,"throw",e)}},jh=fh,Ah=kn,Ph=Br,Ih=Zr,Dh=Eh,Ch=yh,Lh=Array,Rh=pe("iterator"),Mh=!1;try{var Nh=0,Fh={next:function(){return{done:!!Nh++}},return:function(){Mh=!0}};Fh[Rh]=function(){return this},Array.from(Fh,(function(){throw 2}))}catch(t){}var zh=function(t,e){try{if(!e&&!Mh)return!1}catch(t){return!1}var r=!1;try{var n={};n[Rh]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r},Uh=function(t){var e=xh(t),r=Ah(this),n=arguments.length,o=n>1?arguments[1]:void 0,i=void 0!==o;i&&(o=Oh(o,n>2?arguments[2]:void 0));var a,u,s,c,f,l,h=Ch(e),p=0;if(!h||this===Lh&&jh(h))for(a=Ph(e),u=r?new this(a):Lh(a);a>p;p++)l=i?o(e[p],p):e[p],Ih(u,p,l);else for(f=(c=Dh(e,h)).next,u=r?new this:[];!(s=Sh(f,c)).done;p++)l=i?kh(c,o,[s.value,p],!0):s.value,Ih(u,p,l);return u.length=p,u};Ar({target:"Array",stat:!0,forced:!zh((function(t){Array.from(t)}))},{from:Uh});var qh=rt.Array.from,Wh=r(qh),Yh=yh,Gh=r(Yh),Xh=r(Yh);Ar({target:"Array",stat:!0},{isArray:zr});var Vh=rt.Array.isArray,Bh=r(Vh);var Hh=P,Kh=zr,Jh=TypeError,$h=Object.getOwnPropertyDescriptor,Qh=Hh&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,e){if(Kh(t)&&!$h(t,"length").writable)throw new Jh("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},Zh=Jt,tp=Br,ep=Qh,rp=Kr;Ar({target:"Array",proto:!0,arity:1,forced:a((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=Zh(this),r=tp(e),n=arguments.length;rp(r+n);for(var o=0;o<n;o++)e[r]=arguments[o],r++;return ep(e,r),r}});var np=rl("Array","push"),op=st,ip=np,ap=Array.prototype,up=function(t){var e=t.push;return t===ap||op(ap,t)&&e===ap.push?ip:e},sp=r(up);var cp=Ar,fp=zr,lp=kn,hp=et,pp=ao,vp=Br,dp=Q,yp=Zr,gp=pe,mp=Lu,bp=Fn("slice"),wp=gp("species"),_p=Array,Tp=Math.max;cp({target:"Array",proto:!0,forced:!bp},{slice:function(t,e){var r,n,o,i=dp(this),a=vp(i),u=pp(t,a),s=pp(void 0===e?a:e,a);if(fp(i)&&(r=i.constructor,(lp(r)&&(r===_p||fp(r.prototype))||hp(r)&&null===(r=r[wp]))&&(r=void 0),r===_p||void 0===r))return mp(i,u,s);for(n=new(void 0===r?_p:r)(Tp(s-u,0)),o=0;u<s;u++,o++)u in i&&yp(n,o,i[u]);return n.length=o,n}});var Ep=rl("Array","slice"),Op=st,Sp=Ep,xp=Array.prototype,kp=function(t){var e=t.slice;return t===xp||Op(xp,t)&&e===xp.slice?Sp:e},jp=kp,Ap=r(jp),Pp=r(qh);function Ip(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Dp(t,e){var r;if(t){if("string"==typeof t)return Ip(t,e);var n=Ap(r=Object.prototype.toString.call(t)).call(r,8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Pp(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ip(t,e):void 0}}function Cp(t,e){return function(t){if(Bh(t))return t}(t)||function(t,e){var r=null==t?null:void 0!==yf&&Gh(t)||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],s=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(sp(u).call(u,n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||Dp(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Lp(t){return function(t){if(Bh(t))return Ip(t)}(t)||function(t){if(void 0!==yf&&null!=Gh(t)||null!=t["@@iterator"])return Pp(t)}(t)||Dp(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Rp=r(Wc),Mp=rl("Array","concat"),Np=st,Fp=Mp,zp=Array.prototype,Up=r((function(t){var e=t.concat;return t===zp||Np(zp,t)&&e===zp.concat?Fp:e})),qp=r(kp),Wp=ut,Yp=Ko,Gp=fi,Xp=nr,Vp=g([].concat),Bp=Wp("Reflect","ownKeys")||function(t){var e=Yp.f(Xp(t)),r=Gp.f;return r?Vp(e,r(t)):e};Ar({target:"Reflect",stat:!0},{ownKeys:Bp});var Hp=r(rt.Reflect.ownKeys),Kp=r(Vh),Jp=Jt,$p=Eo;Ar({target:"Object",stat:!0,forced:a((function(){$p(1)}))},{keys:function(t){return $p(Jp(t))}});var Qp=r(rt.Object.keys),Zp=sa.forEach,tv=gl("forEach")?[].forEach:function(t){return Zp(this,t,arguments.length>1?arguments[1]:void 0)};Ar({target:"Array",proto:!0,forced:[].forEach!==tv},{forEach:tv});var ev=rl("Array","forEach"),rv=cn,nv=Zt,ov=st,iv=ev,av=Array.prototype,uv={DOMTokenList:!0,NodeList:!0},sv=function(t){var e=t.forEach;return t===av||ov(av,t)&&e===av.forEach||nv(uv,rv(t))?iv:e},cv=r(sv),fv=Ar,lv=zr,hv=g([].reverse),pv=[1,2];fv({target:"Array",proto:!0,forced:String(pv)===String(pv.reverse())},{reverse:function(){return lv(this)&&(this.length=this.length),hv(this)}});var vv=rl("Array","reverse"),dv=st,yv=vv,gv=Array.prototype,mv=function(t){var e=t.reverse;return t===gv||dv(gv,t)&&e===gv.reverse?yv:e},bv=r(mv),wv=jt,_v=TypeError,Tv=function(t,e){if(!delete t[e])throw new _v("Cannot delete property "+wv(e)+" of "+wv(t))},Ev=Ar,Ov=Jt,Sv=ao,xv=Yr,kv=Br,jv=Qh,Av=Kr,Pv=Ln,Iv=Zr,Dv=Tv,Cv=Fn("splice"),Lv=Math.max,Rv=Math.min;Ev({target:"Array",proto:!0,forced:!Cv},{splice:function(t,e){var r,n,o,i,a,u,s=Ov(this),c=kv(s),f=Sv(t,c),l=arguments.length;for(0===l?r=n=0:1===l?(r=0,n=c-f):(r=l-2,n=Rv(Lv(xv(e),0),c-f)),Av(c+r-n),o=Pv(s,n),i=0;i<n;i++)(a=f+i)in s&&Iv(o,i,s[a]);if(o.length=n,r<n){for(i=f;i<c-n;i++)u=i+r,(a=i+n)in s?s[u]=s[a]:Dv(s,u);for(i=c;i>c-n+r;i--)Dv(s,i-1)}else if(r>n)for(i=c-n;i>f;i--)u=i+r-1,(a=i+n-1)in s?s[u]=s[a]:Dv(s,u);for(i=0;i<r;i++)s[i+f]=arguments[i+2];return jv(s,c-n+r),o}});var Mv=rl("Array","splice"),Nv=st,Fv=Mv,zv=Array.prototype,Uv=r((function(t){var e=t.splice;return t===zv||Nv(zv,t)&&e===zv.splice?Fv:e})),qv=P,Wv=g,Yv=C,Gv=a,Xv=Eo,Vv=fi,Bv=L,Hv=Jt,Kv=X,Jv=Object.assign,$v=Object.defineProperty,Qv=Wv([].concat),Zv=!Jv||Gv((function(){if(qv&&1!==Jv({b:1},Jv($v({},"a",{enumerable:!0,get:function(){$v(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol("assign detection"),n="abcdefghijklmnopqrst";return t[r]=7,n.split("").forEach((function(t){e[t]=t})),7!==Jv({},t)[r]||Xv(Jv({},e)).join("")!==n}))?function(t,e){for(var r=Hv(t),n=arguments.length,o=1,i=Vv.f,a=Bv.f;n>o;)for(var u,s=Kv(arguments[o++]),c=i?Qv(Xv(s),i(s)):Xv(s),f=c.length,l=0;f>l;)u=c[l++],qv&&!Yv(a,s,u)||(r[u]=s[u]);return r}:Jv,td=Zv;Ar({target:"Object",stat:!0,arity:2,forced:Object.assign!==td},{assign:td});var ed=r(rt.Object.assign),rd=Jt,nd=Rs,od=ks;Ar({target:"Object",stat:!0,forced:a((function(){nd(1)})),sham:!od},{getPrototypeOf:function(t){return nd(rd(t))}});var id=rt.Object.getPrototypeOf;Ar({target:"Object",stat:!0,sham:!P},{create:Ho});var ad=rt.Object,ud=function(t,e){return ad.create(t,e)},sd=r(ud),cd=rt,fd=h;cd.JSON||(cd.JSON={stringify:JSON.stringify});var ld=r((function(t,e,r){return fd(cd.JSON.stringify,null,arguments)})),hd="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,pd=TypeError,vd=function(t,e){if(t<e)throw new pd("Not enough arguments");return t},dd=i,yd=h,gd=j,md=hd,bd=ct,wd=Lu,_d=vd,Td=dd.Function,Ed=/MSIE .\./.test(bd)||md&&function(){var t=dd.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),Od=function(t,e){var r=e?2:1;return Ed?function(n,o){var i=_d(arguments.length,1)>r,a=gd(n)?n:Td(n),u=i?wd(arguments,r):[],s=i?function(){yd(a,this,u)}:a;return e?t(s,o):t(s)}:t},Sd=Ar,xd=i,kd=Od(xd.setInterval,!0);Sd({global:!0,bind:!0,forced:xd.setInterval!==kd},{setInterval:kd});var jd=Ar,Ad=i,Pd=Od(Ad.setTimeout,!0);jd({global:!0,bind:!0,forced:Ad.setTimeout!==Pd},{setTimeout:Pd});var Id=r(rt.setTimeout),Dd={exports:{}};!function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const r=this._callbacks.get(t)??[];return r.push(e),this._callbacks.set(t,r),this},e.prototype.once=function(t,e){const r=(...n)=>{this.off(t,r),e.apply(this,n)};return r.fn=e,this.on(t,r),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const r=this._callbacks.get(t);if(r){for(const[t,n]of r.entries())if(n===e||n.fn===e){r.splice(t,1);break}0===r.length?this._callbacks.delete(t):this._callbacks.set(t,r)}return this},e.prototype.emit=function(t,...e){const r=this._callbacks.get(t);if(r){const t=[...r];for(const r of t)r.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(Dd);var Cd=r(Dd.exports);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
 * http://naver.github.io/egjs
 *
 * Forked By Naver egjs
 * Copyright (c) hammerjs
 * Licensed under the MIT license */
function Ld(){return Ld=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ld.apply(this,arguments)}function Rd(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function Md(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var Nd,Fd="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),r=1;r<arguments.length;r++){var n=arguments[r];if(null!=n)for(var o in n)n.hasOwnProperty(o)&&(e[o]=n[o])}return e}:Object.assign,zd=["","webkit","Moz","MS","ms","o"],Ud="undefined"==typeof document?{style:{}}:document.createElement("div"),qd=Math.round,Wd=Math.abs,Yd=Date.now;function Gd(t,e){for(var r,n,o=e[0].toUpperCase()+e.slice(1),i=0;i<zd.length;){if((n=(r=zd[i])?r+o:e)in t)return n;i++}}Nd="undefined"==typeof window?{}:window;var Xd=Gd(Ud.style,"touchAction"),Vd=void 0!==Xd;var Bd="compute",Hd="auto",Kd="manipulation",Jd="none",$d="pan-x",Qd="pan-y",Zd=function(){if(!Vd)return!1;var t={},e=Nd.CSS&&Nd.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(r){return t[r]=!e||Nd.CSS.supports("touch-action",r)})),t}(),ty="ontouchstart"in Nd,ey=void 0!==Gd(Nd,"PointerEvent"),ry=ty&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),ny="touch",oy="mouse",iy=25,ay=1,uy=4,sy=8,cy=1,fy=2,ly=4,hy=8,py=16,vy=fy|ly,dy=hy|py,yy=vy|dy,gy=["x","y"],my=["clientX","clientY"];function by(t,e,r){var n;if(t)if(t.forEach)t.forEach(e,r);else if(void 0!==t.length)for(n=0;n<t.length;)e.call(r,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&e.call(r,t[n],n,t)}function wy(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function _y(t,e){return t.indexOf(e)>-1}var Ty=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Bd&&(t=this.compute()),Vd&&this.manager.element.style&&Zd[t]&&(this.manager.element.style[Xd]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return by(this.manager.recognizers,(function(e){wy(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(_y(t,Jd))return Jd;var e=_y(t,$d),r=_y(t,Qd);return e&&r?Jd:e||r?e?$d:Qd:_y(t,Kd)?Kd:Hd}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,r=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var n=this.actions,o=_y(n,Jd)&&!Zd[Jd],i=_y(n,Qd)&&!Zd[Qd],a=_y(n,$d)&&!Zd[$d];if(o){var u=1===t.pointers.length,s=t.distance<2,c=t.deltaTime<250;if(u&&s&&c)return}if(!a||!i)return o||i&&r&vy||a&&r&dy?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Ey(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function Oy(t){var e=t.length;if(1===e)return{x:qd(t[0].clientX),y:qd(t[0].clientY)};for(var r=0,n=0,o=0;o<e;)r+=t[o].clientX,n+=t[o].clientY,o++;return{x:qd(r/e),y:qd(n/e)}}function Sy(t){for(var e=[],r=0;r<t.pointers.length;)e[r]={clientX:qd(t.pointers[r].clientX),clientY:qd(t.pointers[r].clientY)},r++;return{timeStamp:Yd(),pointers:e,center:Oy(e),deltaX:t.deltaX,deltaY:t.deltaY}}function xy(t,e,r){r||(r=gy);var n=e[r[0]]-t[r[0]],o=e[r[1]]-t[r[1]];return Math.sqrt(n*n+o*o)}function ky(t,e,r){r||(r=gy);var n=e[r[0]]-t[r[0]],o=e[r[1]]-t[r[1]];return 180*Math.atan2(o,n)/Math.PI}function jy(t,e){return t===e?cy:Wd(t)>=Wd(e)?t<0?fy:ly:e<0?hy:py}function Ay(t,e,r){return{x:e/t||0,y:r/t||0}}function Py(t,e){var r=t.session,n=e.pointers,o=n.length;r.firstInput||(r.firstInput=Sy(e)),o>1&&!r.firstMultiple?r.firstMultiple=Sy(e):1===o&&(r.firstMultiple=!1);var i=r.firstInput,a=r.firstMultiple,u=a?a.center:i.center,s=e.center=Oy(n);e.timeStamp=Yd(),e.deltaTime=e.timeStamp-i.timeStamp,e.angle=ky(u,s),e.distance=xy(u,s),function(t,e){var r=e.center,n=t.offsetDelta||{},o=t.prevDelta||{},i=t.prevInput||{};e.eventType!==ay&&i.eventType!==uy||(o=t.prevDelta={x:i.deltaX||0,y:i.deltaY||0},n=t.offsetDelta={x:r.x,y:r.y}),e.deltaX=o.x+(r.x-n.x),e.deltaY=o.y+(r.y-n.y)}(r,e),e.offsetDirection=jy(e.deltaX,e.deltaY);var c,f,l=Ay(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=l.x,e.overallVelocityY=l.y,e.overallVelocity=Wd(l.x)>Wd(l.y)?l.x:l.y,e.scale=a?(c=a.pointers,xy((f=n)[0],f[1],my)/xy(c[0],c[1],my)):1,e.rotation=a?function(t,e){return ky(e[1],e[0],my)+ky(t[1],t[0],my)}(a.pointers,n):0,e.maxPointers=r.prevInput?e.pointers.length>r.prevInput.maxPointers?e.pointers.length:r.prevInput.maxPointers:e.pointers.length,function(t,e){var r,n,o,i,a=t.lastInterval||e,u=e.timeStamp-a.timeStamp;if(e.eventType!==sy&&(u>iy||void 0===a.velocity)){var s=e.deltaX-a.deltaX,c=e.deltaY-a.deltaY,f=Ay(u,s,c);n=f.x,o=f.y,r=Wd(f.x)>Wd(f.y)?f.x:f.y,i=jy(s,c),t.lastInterval=e}else r=a.velocity,n=a.velocityX,o=a.velocityY,i=a.direction;e.velocity=r,e.velocityX=n,e.velocityY=o,e.direction=i}(r,e);var h,p=t.element,v=e.srcEvent;Ey(h=v.composedPath?v.composedPath()[0]:v.path?v.path[0]:v.target,p)&&(p=h),e.target=p}function Iy(t,e,r){var n=r.pointers.length,o=r.changedPointers.length,i=e&ay&&n-o==0,a=e&(uy|sy)&&n-o==0;r.isFirst=!!i,r.isFinal=!!a,i&&(t.session={}),r.eventType=e,Py(t,r),t.emit("hammer.input",r),t.recognize(r),t.session.prevInput=r}function Dy(t){return t.trim().split(/\s+/g)}function Cy(t,e,r){by(Dy(e),(function(e){t.addEventListener(e,r,!1)}))}function Ly(t,e,r){by(Dy(e),(function(e){t.removeEventListener(e,r,!1)}))}function Ry(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var My=function(){function t(t,e){var r=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){wy(t.options.enable,[t])&&r.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&Cy(this.element,this.evEl,this.domHandler),this.evTarget&&Cy(this.target,this.evTarget,this.domHandler),this.evWin&&Cy(Ry(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&Ly(this.element,this.evEl,this.domHandler),this.evTarget&&Ly(this.target,this.evTarget,this.domHandler),this.evWin&&Ly(Ry(this.element),this.evWin,this.domHandler)},t}();function Ny(t,e,r){if(t.indexOf&&!r)return t.indexOf(e);for(var n=0;n<t.length;){if(r&&t[n][r]==e||!r&&t[n]===e)return n;n++}return-1}var Fy={pointerdown:ay,pointermove:2,pointerup:uy,pointercancel:sy,pointerout:sy},zy={2:ny,3:"pen",4:oy,5:"kinect"},Uy="pointerdown",qy="pointermove pointerup pointercancel";Nd.MSPointerEvent&&!Nd.PointerEvent&&(Uy="MSPointerDown",qy="MSPointerMove MSPointerUp MSPointerCancel");var Wy=function(t){function e(){var r,n=e.prototype;return n.evEl=Uy,n.evWin=qy,(r=t.apply(this,arguments)||this).store=r.manager.session.pointerEvents=[],r}return Rd(e,t),e.prototype.handler=function(t){var e=this.store,r=!1,n=t.type.toLowerCase().replace("ms",""),o=Fy[n],i=zy[t.pointerType]||t.pointerType,a=i===ny,u=Ny(e,t.pointerId,"pointerId");o&ay&&(0===t.button||a)?u<0&&(e.push(t),u=e.length-1):o&(uy|sy)&&(r=!0),u<0||(e[u]=t,this.callback(this.manager,o,{pointers:e,changedPointers:[t],pointerType:i,srcEvent:t}),r&&e.splice(u,1))},e}(My);function Yy(t){return Array.prototype.slice.call(t,0)}function Gy(t,e,r){for(var n=[],o=[],i=0;i<t.length;){var a=e?t[i][e]:t[i];Ny(o,a)<0&&n.push(t[i]),o[i]=a,i++}return r&&(n=e?n.sort((function(t,r){return t[e]>r[e]})):n.sort()),n}var Xy={touchstart:ay,touchmove:2,touchend:uy,touchcancel:sy},Vy=function(t){function e(){var r;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(r=t.apply(this,arguments)||this).targetIds={},r}return Rd(e,t),e.prototype.handler=function(t){var e=Xy[t.type],r=By.call(this,t,e);r&&this.callback(this.manager,e,{pointers:r[0],changedPointers:r[1],pointerType:ny,srcEvent:t})},e}(My);function By(t,e){var r,n,o=Yy(t.touches),i=this.targetIds;if(e&(2|ay)&&1===o.length)return i[o[0].identifier]=!0,[o,o];var a=Yy(t.changedTouches),u=[],s=this.target;if(n=o.filter((function(t){return Ey(t.target,s)})),e===ay)for(r=0;r<n.length;)i[n[r].identifier]=!0,r++;for(r=0;r<a.length;)i[a[r].identifier]&&u.push(a[r]),e&(uy|sy)&&delete i[a[r].identifier],r++;return u.length?[Gy(n.concat(u),"identifier",!0),u]:void 0}var Hy={mousedown:ay,mousemove:2,mouseup:uy},Ky=function(t){function e(){var r,n=e.prototype;return n.evEl="mousedown",n.evWin="mousemove mouseup",(r=t.apply(this,arguments)||this).pressed=!1,r}return Rd(e,t),e.prototype.handler=function(t){var e=Hy[t.type];e&ay&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=uy),this.pressed&&(e&uy&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:oy,srcEvent:t}))},e}(My),Jy=2500;function $y(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var r={x:e.clientX,y:e.clientY},n=this.lastTouches;this.lastTouches.push(r);setTimeout((function(){var t=n.indexOf(r);t>-1&&n.splice(t,1)}),Jy)}}function Qy(t,e){t&ay?(this.primaryTouch=e.changedPointers[0].identifier,$y.call(this,e)):t&(uy|sy)&&$y.call(this,e)}function Zy(t){for(var e=t.srcEvent.clientX,r=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var o=this.lastTouches[n],i=Math.abs(e-o.x),a=Math.abs(r-o.y);if(i<=25&&a<=25)return!0}return!1}var tg=function(){return function(t){function e(e,r){var n;return(n=t.call(this,e,r)||this).handler=function(t,e,r){var o=r.pointerType===ny,i=r.pointerType===oy;if(!(i&&r.sourceCapabilities&&r.sourceCapabilities.firesTouchEvents)){if(o)Qy.call(Md(Md(n)),e,r);else if(i&&Zy.call(Md(Md(n)),r))return;n.callback(t,e,r)}},n.touch=new Vy(n.manager,n.handler),n.mouse=new Ky(n.manager,n.handler),n.primaryTouch=null,n.lastTouches=[],n}return Rd(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(My)}();function eg(t,e,r){return!!Array.isArray(t)&&(by(t,r[e],r),!0)}var rg=32,ng=1;function og(t,e){var r=e.manager;return r?r.get(t):t}function ig(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var ag=function(){function t(t){void 0===t&&(t={}),this.options=Ld({enable:!0},t),this.id=ng++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Fd(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(eg(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=og(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return eg(t,"dropRecognizeWith",this)||(t=og(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(eg(t,"requireFailure",this))return this;var e=this.requireFail;return-1===Ny(e,t=og(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(eg(t,"dropRequireFailure",this))return this;t=og(t,this);var e=Ny(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,r=this.state;function n(r){e.manager.emit(r,t)}r<8&&n(e.options.event+ig(r)),n(e.options.event),t.additionalEvent&&n(t.additionalEvent),r>=8&&n(e.options.event+ig(r))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=rg},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Fd({},t);if(!wy(this.options.enable,[this,e]))return this.reset(),void(this.state=rg);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),ug=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Ld({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,r.pCenter=!1,r._timer=null,r._input=null,r.count=0,r}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Kd]},r.process=function(t){var e=this,r=this.options,n=t.pointers.length===r.pointers,o=t.distance<r.threshold,i=t.deltaTime<r.time;if(this.reset(),t.eventType&ay&&0===this.count)return this.failTimeout();if(o&&i&&n){if(t.eventType!==uy)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<r.interval,u=!this.pCenter||xy(this.pCenter,t.center)<r.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,u&&a?this.count+=1:this.count=1,this._input=t,0===this.count%r.taps)return this.hasRequireFailures()?(this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),r.interval),2):8}return rg},r.failTimeout=function(){var t=this;return this._timer=setTimeout((function(){t.state=rg}),this.options.interval),rg},r.reset=function(){clearTimeout(this._timer)},r.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(ag),sg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Ld({pointers:1},e))||this}Rd(e,t);var r=e.prototype;return r.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},r.process=function(t){var e=this.state,r=t.eventType,n=6&e,o=this.attrTest(t);return n&&(r&sy||!o)?16|e:n||o?r&uy?8|e:2&e?4|e:2:rg},e}(ag);function cg(t){return t===py?"down":t===hy?"up":t===fy?"left":t===ly?"right":""}var fg=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Ld({event:"pan",threshold:10,pointers:1,direction:yy},e))||this).pX=null,r.pY=null,r}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){var t=this.options.direction,e=[];return t&vy&&e.push(Qd),t&dy&&e.push($d),e},r.directionTest=function(t){var e=this.options,r=!0,n=t.distance,o=t.direction,i=t.deltaX,a=t.deltaY;return o&e.direction||(e.direction&vy?(o=0===i?cy:i<0?fy:ly,r=i!==this.pX,n=Math.abs(t.deltaX)):(o=0===a?cy:a<0?hy:py,r=a!==this.pY,n=Math.abs(t.deltaY))),t.direction=o,r&&n>e.threshold&&o&e.direction},r.attrTest=function(t){return sg.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},r.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var r=cg(e.direction);r&&(e.additionalEvent=this.options.event+r),t.prototype.emit.call(this,e)},e}(sg),lg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Ld({event:"swipe",threshold:10,velocity:.3,direction:vy|dy,pointers:1},e))||this}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){return fg.prototype.getTouchAction.call(this)},r.attrTest=function(e){var r,n=this.options.direction;return n&(vy|dy)?r=e.overallVelocity:n&vy?r=e.overallVelocityX:n&dy&&(r=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&n&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Wd(r)>this.options.velocity&&e.eventType&uy},r.emit=function(t){var e=cg(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(sg),hg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Ld({event:"pinch",threshold:0,pointers:2},e))||this}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Jd]},r.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},r.emit=function(e){if(1!==e.scale){var r=e.scale<1?"in":"out";e.additionalEvent=this.options.event+r}t.prototype.emit.call(this,e)},e}(sg),pg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Ld({event:"rotate",threshold:0,pointers:2},e))||this}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Jd]},r.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(sg),vg=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Ld({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,r._input=null,r}Rd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Hd]},r.process=function(t){var e=this,r=this.options,n=t.pointers.length===r.pointers,o=t.distance<r.threshold,i=t.deltaTime>r.time;if(this._input=t,!o||!n||t.eventType&(uy|sy)&&!i)this.reset();else if(t.eventType&ay)this.reset(),this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),r.time);else if(t.eventType&uy)return 8;return rg},r.reset=function(){clearTimeout(this._timer)},r.emit=function(t){8===this.state&&(t&&t.eventType&uy?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Yd(),this.manager.emit(this.options.event,this._input)))},e}(ag),dg={domEvents:!1,touchAction:Bd,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},yg=[[pg,{enable:!1}],[hg,{enable:!1},["rotate"]],[lg,{direction:vy}],[fg,{direction:vy},["swipe"]],[ug],[ug,{event:"doubletap",taps:2},["tap"]],[vg]];function gg(t,e){var r,n=t.element;n.style&&(by(t.options.cssProps,(function(o,i){r=Gd(n.style,i),e?(t.oldCssProps[r]=n.style[r],n.style[r]=o):n.style[r]=t.oldCssProps[r]||""})),e||(t.oldCssProps={}))}var mg=function(){function t(t,e){var r,n=this;this.options=Fd({},dg,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((r=this).options.inputClass||(ey?Wy:ry?Vy:ty?tg:Ky))(r,Iy),this.touchAction=new Ty(this,this.options.touchAction),gg(this,!0),by(this.options.recognizers,(function(t){var e=n.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}var e=t.prototype;return e.set=function(t){return Fd(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var r;this.touchAction.preventDefaults(t);var n=this.recognizers,o=e.curRecognizer;(!o||o&&8&o.state)&&(e.curRecognizer=null,o=null);for(var i=0;i<n.length;)r=n[i],2===e.stopped||o&&r!==o&&!r.canRecognizeWith(o)?r.reset():r.recognize(t),!o&&14&r.state&&(e.curRecognizer=r,o=r),i++}},e.get=function(t){if(t instanceof ag)return t;for(var e=this.recognizers,r=0;r<e.length;r++)if(e[r].options.event===t)return e[r];return null},e.add=function(t){if(eg(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(eg(t,"remove",this))return this;var e=this.get(t);if(t){var r=this.recognizers,n=Ny(r,e);-1!==n&&(r.splice(n,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var r=this.handlers;return by(Dy(t),(function(t){r[t]=r[t]||[],r[t].push(e)})),this},e.off=function(t,e){if(void 0===t)return this;var r=this.handlers;return by(Dy(t),(function(t){e?r[t]&&r[t].splice(Ny(r[t],e),1):delete r[t]})),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var r=document.createEvent("Event");r.initEvent(t,!0,!0),r.gesture=e,e.target.dispatchEvent(r)}(t,e);var r=this.handlers[t]&&this.handlers[t].slice();if(r&&r.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var n=0;n<r.length;)r[n](e),n++}},e.destroy=function(){this.element&&gg(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),bg={touchstart:ay,touchmove:2,touchend:uy,touchcancel:sy},wg=function(t){function e(){var r,n=e.prototype;return n.evTarget="touchstart",n.evWin="touchstart touchmove touchend touchcancel",(r=t.apply(this,arguments)||this).started=!1,r}return Rd(e,t),e.prototype.handler=function(t){var e=bg[t.type];if(e===ay&&(this.started=!0),this.started){var r=_g.call(this,t,e);e&(uy|sy)&&r[0].length-r[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:r[0],changedPointers:r[1],pointerType:ny,srcEvent:t})}},e}(My);function _g(t,e){var r=Yy(t.touches),n=Yy(t.changedTouches);return e&(uy|sy)&&(r=Gy(r.concat(n),"identifier",!0)),[r,n]}function Tg(t,e,r){var n="DEPRECATED METHOD: "+e+"\n"+r+" AT \n";return function(){var e=new Error("get-stack-trace"),r=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",o=window.console&&(window.console.warn||window.console.log);return o&&o.call(window.console,n,r),t.apply(this,arguments)}}var Eg=Tg((function(t,e,r){for(var n=Object.keys(e),o=0;o<n.length;)(!r||r&&void 0===t[n[o]])&&(t[n[o]]=e[n[o]]),o++;return t}),"extend","Use `assign`."),Og=Tg((function(t,e){return Eg(t,e,!0)}),"merge","Use `assign`.");function Sg(t,e,r){var n,o=e.prototype;(n=t.prototype=Object.create(o)).constructor=t,n._super=o,r&&Fd(n,r)}function xg(t,e){return function(){return t.apply(e,arguments)}}var kg=function(){var t=function(t,e){return void 0===e&&(e={}),new mg(t,Ld({recognizers:yg.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=yy,t.DIRECTION_DOWN=py,t.DIRECTION_LEFT=fy,t.DIRECTION_RIGHT=ly,t.DIRECTION_UP=hy,t.DIRECTION_HORIZONTAL=vy,t.DIRECTION_VERTICAL=dy,t.DIRECTION_NONE=cy,t.DIRECTION_DOWN=py,t.INPUT_START=ay,t.INPUT_MOVE=2,t.INPUT_END=uy,t.INPUT_CANCEL=sy,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=rg,t.Manager=mg,t.Input=My,t.TouchAction=Ty,t.TouchInput=Vy,t.MouseInput=Ky,t.PointerEventInput=Wy,t.TouchMouseInput=tg,t.SingleTouchInput=wg,t.Recognizer=ag,t.AttrRecognizer=sg,t.Tap=ug,t.Pan=fg,t.Swipe=lg,t.Pinch=hg,t.Rotate=pg,t.Press=vg,t.on=Cy,t.off=Ly,t.each=by,t.merge=Og,t.extend=Eg,t.bindFn=xg,t.assign=Fd,t.inherit=Sg,t.bindFn=xg,t.prefixed=Gd,t.toArray=Yy,t.inArray=Ny,t.uniqueArray=Gy,t.splitStr=Dy,t.boolOrFn=wy,t.hasParent=Ey,t.addEventListeners=Cy,t.removeEventListeners=Ly,t.defaults=Fd({},dg,{preset:yg}),t}();function jg(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return Ag(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ag(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function Ag(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Pg=Rp("DELETE");function Ig(){var t=Dg.apply(void 0,arguments);return Lg(t),t}function Dg(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(e.length<2)return e[0];var n;if(e.length>2)return Dg.apply(void 0,Up(n=[Ig(e[0],e[1])]).call(n,Lp(qp(e).call(e,2))));var o=e[0],i=e[1];if(o instanceof Date&&i instanceof Date)return o.setTime(i.getTime()),o;var a,u=jg(Hp(i));try{for(u.s();!(a=u.n()).done;){var s=a.value;Object.prototype.propertyIsEnumerable.call(i,s)&&(i[s]===Pg?delete o[s]:null===o[s]||null===i[s]||"object"!=typeof o[s]||"object"!=typeof i[s]||Kp(o[s])||Kp(i[s])?o[s]=Cg(i[s]):o[s]=Dg(o[s],i[s]))}}catch(t){u.e(t)}finally{u.f()}return o}function Cg(t){return Kp(t)?Ml(t).call(t,(function(t){return Cg(t)})):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):Dg({},t):t}function Lg(t){for(var e=0,r=Qp(t);e<r.length;e++){var n=r[e];t[n]===Pg?delete t[n]:"object"==typeof t[n]&&null!==t[n]&&Lg(t[n])}}var Rg="undefined"!=typeof window?window.Hammer||kg:function(){return function(){var t=function(){};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function Mg(t){var e,r=this;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push((function(){r._dom.overlay.parentNode.removeChild(r._dom.overlay)}));var n=Rg(this._dom.overlay);n.on("tap",sl(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push((function(){n.destroy()}));var o=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];cv(o).call(o,(function(t){n.on(t,(function(t){t.srcEvent.stopPropagation()}))})),document&&document.body&&(this._onClick=function(e){(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||r.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push((function(){document.body.removeEventListener("click",r._onClick)}))),this._escListener=function(t){("key"in t?"Escape"===t.key:27===t.keyCode)&&r.deactivate()}}Cd(Mg.prototype),Mg.current=null,Mg.prototype.destroy=function(){var t,e;this.deactivate();var r,n=jg(bv(t=Uv(e=this._cleanupQueue).call(e,0)).call(t));try{for(n.s();!(r=n.n()).done;){(0,r.value)()}}catch(t){n.e(t)}finally{n.f()}},Mg.prototype.activate=function(){Mg.current&&Mg.current.deactivate(),Mg.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},Mg.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},Mg.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var Ng=kn,Fg=jt,zg=TypeError,Ug=function(t){if(Ng(t))return t;throw new zg(Fg(t)+" is not a constructor")},qg=Ar,Wg=h,Yg=Qf,Gg=Ug,Xg=nr,Vg=et,Bg=Ho,Hg=a,Kg=ut("Reflect","construct"),Jg=Object.prototype,$g=[].push,Qg=Hg((function(){function t(){}return!(Kg((function(){}),[],t)instanceof t)})),Zg=!Hg((function(){Kg((function(){}))})),tm=Qg||Zg;qg({target:"Reflect",stat:!0,forced:tm,sham:tm},{construct:function(t,e){Gg(t),Xg(e);var r=arguments.length<3?t:Gg(arguments[2]);if(Zg&&!Qg)return Kg(t,e,r);if(t===r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return Wg($g,n,e),new(Wg(Yg,t,n))}var o=r.prototype,i=Bg(Vg(o)?o:Jg),a=Wg(t,i,e);return Vg(a)?a:i}});var em=r(rt.Reflect.construct),rm=r(rt.Object.getOwnPropertySymbols),nm={exports:{}},om=Ar,im=a,am=Q,um=A.f,sm=P;om({target:"Object",stat:!0,forced:!sm||im((function(){um(1)})),sham:!sm},{getOwnPropertyDescriptor:function(t,e){return um(am(t),e)}});var cm=rt.Object,fm=nm.exports=function(t,e){return cm.getOwnPropertyDescriptor(t,e)};cm.getOwnPropertyDescriptor.sham&&(fm.sham=!0);var lm=r(nm.exports),hm=Bp,pm=Q,vm=A,dm=Zr;Ar({target:"Object",stat:!0,sham:!P},{getOwnPropertyDescriptors:function(t){for(var e,r,n=pm(t),o=vm.f,i=hm(n),a={},u=0;i.length>u;)void 0!==(r=o(n,e=i[u++]))&&dm(a,e,r);return a}});var ym=r(rt.Object.getOwnPropertyDescriptors),gm={exports:{}},mm=Ar,bm=P,wm=ro.f;mm({target:"Object",stat:!0,forced:Object.defineProperties!==wm,sham:!bm},{defineProperties:wm});var _m=rt.Object,Tm=gm.exports=function(t,e){return _m.defineProperties(t,e)};_m.defineProperties.sham&&(Tm.sham=!0);var Em=r(gm.exports),Om=r(Rr);function Sm(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var xm=ud,km=r(xm);Ar({target:"Object",stat:!0},{setPrototypeOf:ac});var jm=rt.Object.setPrototypeOf,Am=r(jm),Pm=r(ul);function Im(t,e){var r;return Im=Am?Pm(r=Am).call(r):function(t,e){return t.__proto__=e,t},Im(t,e)}function Dm(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=km(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Nr(t,"prototype",{writable:!1}),e&&Im(t,e)}function Cm(t,e){if(e&&("object"===Mf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Sm(t)}var Lm=id,Rm=r(Lm);function Mm(t){var e;return Mm=Am?Pm(e=Rm).call(e):function(t){return t.__proto__||Rm(t)},Mm(t)}var Nm={exports:{}},Fm={exports:{}};!function(t){var e=df,r=Lf;function n(o){return t.exports=n="function"==typeof e&&"symbol"==typeof r?function(t){return typeof t}:function(t){return t&&"function"==typeof e&&t.constructor===e&&t!==e.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,n(o)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}(Fm);var zm=Fm.exports,Um=sv,qm=Zt,Wm=Bp,Ym=A,Gm=Qe,Xm=et,Vm=gr,Bm=Error,Hm=g("".replace),Km=String(new Bm("zxcasd").stack),Jm=/\n\s*at [^:]*:[^\n]*/,$m=Jm.test(Km),Qm=U,Zm=!a((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",Qm(1,7)),7!==t.stack)})),tb=gr,eb=function(t,e){if($m&&"string"==typeof t&&!Bm.prepareStackTrace)for(;e--;)t=Hm(t,Jm,"");return t},rb=Zm,nb=Error.captureStackTrace,ob=$e,ib=C,ab=nr,ub=jt,sb=fh,cb=Br,fb=st,lb=Eh,hb=yh,pb=oh,vb=TypeError,db=function(t,e){this.stopped=t,this.result=e},yb=db.prototype,gb=function(t,e,r){var n,o,i,a,u,s,c,f=r&&r.that,l=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_RECORD),p=!(!r||!r.IS_ITERATOR),v=!(!r||!r.INTERRUPTED),d=ob(e,f),y=function(t){return n&&pb(n,"normal",t),new db(!0,t)},g=function(t){return l?(ab(t),v?d(t[0],t[1],y):d(t[0],t[1])):v?d(t,y):d(t)};if(h)n=t.iterator;else if(p)n=t;else{if(!(o=hb(t)))throw new vb(ub(t)+" is not iterable");if(sb(o)){for(i=0,a=cb(t);a>i;i++)if((u=g(t[i]))&&fb(yb,u))return u;return new db(!1)}n=lb(t,o)}for(s=h?t.next:n.next;!(c=ib(s,n)).done;){try{u=g(c.value)}catch(t){pb(n,"throw",t)}if("object"==typeof u&&u&&fb(yb,u))return u}return new db(!1)},mb=eo,bb=Ar,wb=st,_b=Rs,Tb=ac,Eb=function(t,e,r){for(var n=Wm(e),o=Gm.f,i=Ym.f,a=0;a<n.length;a++){var u=n[a];qm(t,u)||r&&qm(r,u)||o(t,u,i(e,u))}},Ob=Ho,Sb=gr,xb=U,kb=function(t,e){Xm(e)&&"cause"in e&&Vm(t,"cause",e.cause)},jb=function(t,e,r,n){rb&&(nb?nb(t,e):tb(t,"stack",eb(r,n)))},Ab=gb,Pb=function(t,e){return void 0===t?arguments.length<2?"":e:mb(t)},Ib=pe("toStringTag"),Db=Error,Cb=[].push,Lb=function(t,e){var r,n=wb(Rb,this);Tb?r=Tb(new Db,n?_b(this):Rb):(r=n?this:Ob(Rb),Sb(r,Ib,"Error")),void 0!==e&&Sb(r,"message",Pb(e)),jb(r,Lb,r.stack,1),arguments.length>2&&kb(r,arguments[2]);var o=[];return Ab(t,Cb,{that:o}),Sb(r,"errors",o),r};Tb?Tb(Lb,Db):Eb(Lb,Db,{name:!0});var Rb=Lb.prototype=Ob(Db.prototype,{constructor:xb(1,Lb),message:xb(1,""),name:xb(1,"AggregateError")});bb({global:!0,constructor:!0,arity:2},{AggregateError:Lb});var Mb,Nb,Fb,zb,Ub=ut,qb=vi,Wb=P,Yb=pe("species"),Gb=function(t){var e=Ub(t);Wb&&e&&!e[Yb]&&qb(e,Yb,{configurable:!0,get:function(){return this}})},Xb=st,Vb=TypeError,Bb=function(t,e){if(Xb(e,t))return t;throw new Vb("Incorrect invocation")},Hb=nr,Kb=Ug,Jb=V,$b=pe("species"),Qb=function(t,e){var r,n=Hb(t).constructor;return void 0===n||Jb(r=Hb(n)[$b])?e:Kb(r)},Zb=/(?:ipad|iphone|ipod).*applewebkit/i.test(ct),tw=i,ew=h,rw=$e,nw=j,ow=Zt,iw=a,aw=Io,uw=Lu,sw=ke,cw=vd,fw=Zb,lw=ml,hw=tw.setImmediate,pw=tw.clearImmediate,vw=tw.process,dw=tw.Dispatch,yw=tw.Function,gw=tw.MessageChannel,mw=tw.String,bw=0,ww={},_w="onreadystatechange";iw((function(){Mb=tw.location}));var Tw=function(t){if(ow(ww,t)){var e=ww[t];delete ww[t],e()}},Ew=function(t){return function(){Tw(t)}},Ow=function(t){Tw(t.data)},Sw=function(t){tw.postMessage(mw(t),Mb.protocol+"//"+Mb.host)};hw&&pw||(hw=function(t){cw(arguments.length,1);var e=nw(t)?t:yw(t),r=uw(arguments,1);return ww[++bw]=function(){ew(e,void 0,r)},Nb(bw),bw},pw=function(t){delete ww[t]},lw?Nb=function(t){vw.nextTick(Ew(t))}:dw&&dw.now?Nb=function(t){dw.now(Ew(t))}:gw&&!fw?(zb=(Fb=new gw).port2,Fb.port1.onmessage=Ow,Nb=rw(zb.postMessage,zb)):tw.addEventListener&&nw(tw.postMessage)&&!tw.importScripts&&Mb&&"file:"!==Mb.protocol&&!iw(Sw)?(Nb=Sw,tw.addEventListener("message",Ow,!1)):Nb=_w in sw("script")?function(t){aw.appendChild(sw("script"))[_w]=function(){aw.removeChild(this),Tw(t)}}:function(t){setTimeout(Ew(t),0)});var xw={set:hw,clear:pw},kw=function(){this.head=null,this.tail=null};kw.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}};var jw,Aw,Pw,Iw,Dw,Cw=kw,Lw=/ipad|iphone|ipod/i.test(ct)&&"undefined"!=typeof Pebble,Rw=/web0s(?!.*chrome)/i.test(ct),Mw=i,Nw=$e,Fw=A.f,zw=xw.set,Uw=Cw,qw=Zb,Ww=Lw,Yw=Rw,Gw=ml,Xw=Mw.MutationObserver||Mw.WebKitMutationObserver,Vw=Mw.document,Bw=Mw.process,Hw=Mw.Promise,Kw=Fw(Mw,"queueMicrotask"),Jw=Kw&&Kw.value;if(!Jw){var $w=new Uw,Qw=function(){var t,e;for(Gw&&(t=Bw.domain)&&t.exit();e=$w.get();)try{e()}catch(t){throw $w.head&&jw(),t}t&&t.enter()};qw||Gw||Yw||!Xw||!Vw?!Ww&&Hw&&Hw.resolve?((Iw=Hw.resolve(void 0)).constructor=Hw,Dw=Nw(Iw.then,Iw),jw=function(){Dw(Qw)}):Gw?jw=function(){Bw.nextTick(Qw)}:(zw=Nw(zw,Mw),jw=function(){zw(Qw)}):(Aw=!0,Pw=Vw.createTextNode(""),new Xw(Qw).observe(Pw,{characterData:!0}),jw=function(){Pw.data=Aw=!Aw}),Jw=function(t){$w.head||jw(),$w.add(t)}}var Zw=Jw,t_=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},e_=i.Promise,r_="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,n_=!r_&&!ml&&"object"==typeof window&&"object"==typeof document,o_=i,i_=e_,a_=j,u_=Be,s_=pn,c_=pe,f_=n_,l_=r_,h_=yt,p_=i_&&i_.prototype,v_=c_("species"),d_=!1,y_=a_(o_.PromiseRejectionEvent),g_=u_("Promise",(function(){var t=s_(i_),e=t!==String(i_);if(!e&&66===h_)return!0;if(!p_.catch||!p_.finally)return!0;if(!h_||h_<51||!/native code/.test(t)){var r=new i_((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[v_]=n,!(d_=r.then((function(){}))instanceof n))return!0}return!e&&(f_||l_)&&!y_})),m_={CONSTRUCTOR:g_,REJECTION_EVENT:y_,SUBCLASSING:d_},b_={},w_=Dt,__=TypeError,T_=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new __("Bad Promise constructor");e=t,r=n})),this.resolve=w_(e),this.reject=w_(r)};b_.f=function(t){return new T_(t)};var E_,O_,S_=Ar,x_=ml,k_=i,j_=C,A_=hi,P_=Fi,I_=Gb,D_=Dt,C_=j,L_=et,R_=Bb,M_=Qb,N_=xw.set,F_=Zw,z_=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}},U_=t_,q_=Cw,W_=ta,Y_=e_,G_=m_,X_=b_,V_="Promise",B_=G_.CONSTRUCTOR,H_=G_.REJECTION_EVENT,K_=W_.getterFor(V_),J_=W_.set,$_=Y_&&Y_.prototype,Q_=Y_,Z_=$_,tT=k_.TypeError,eT=k_.document,rT=k_.process,nT=X_.f,oT=nT,iT=!!(eT&&eT.createEvent&&k_.dispatchEvent),aT="unhandledrejection",uT=function(t){var e;return!(!L_(t)||!C_(e=t.then))&&e},sT=function(t,e){var r,n,o,i=e.value,a=1===e.state,u=a?t.ok:t.fail,s=t.resolve,c=t.reject,f=t.domain;try{u?(a||(2===e.rejection&&pT(e),e.rejection=1),!0===u?r=i:(f&&f.enter(),r=u(i),f&&(f.exit(),o=!0)),r===t.promise?c(new tT("Promise-chain cycle")):(n=uT(r))?j_(n,r,s,c):s(r)):c(i)}catch(t){f&&!o&&f.exit(),c(t)}},cT=function(t,e){t.notified||(t.notified=!0,F_((function(){for(var r,n=t.reactions;r=n.get();)sT(r,t);t.notified=!1,e&&!t.rejection&&lT(t)})))},fT=function(t,e,r){var n,o;iT?((n=eT.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),k_.dispatchEvent(n)):n={promise:e,reason:r},!H_&&(o=k_["on"+t])?o(n):t===aT&&z_("Unhandled promise rejection",r)},lT=function(t){j_(N_,k_,(function(){var e,r=t.facade,n=t.value;if(hT(t)&&(e=U_((function(){x_?rT.emit("unhandledRejection",n,r):fT(aT,r,n)})),t.rejection=x_||hT(t)?2:1,e.error))throw e.value}))},hT=function(t){return 1!==t.rejection&&!t.parent},pT=function(t){j_(N_,k_,(function(){var e=t.facade;x_?rT.emit("rejectionHandled",e):fT("rejectionhandled",e,t.value)}))},vT=function(t,e,r){return function(n){t(e,n,r)}},dT=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,cT(t,!0))},yT=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new tT("Promise can't be resolved itself");var n=uT(e);n?F_((function(){var r={done:!1};try{j_(n,e,vT(yT,r,t),vT(dT,r,t))}catch(e){dT(r,e,t)}})):(t.value=e,t.state=1,cT(t,!1))}catch(e){dT({done:!1},e,t)}}};B_&&(Z_=(Q_=function(t){R_(this,Z_),D_(t),j_(E_,this);var e=K_(this);try{t(vT(yT,e),vT(dT,e))}catch(t){dT(e,t)}}).prototype,(E_=function(t){J_(this,{type:V_,done:!1,notified:!1,parent:!1,reactions:new q_,rejection:!1,state:0,value:void 0})}).prototype=A_(Z_,"then",(function(t,e){var r=K_(this),n=nT(M_(this,Q_));return r.parent=!0,n.ok=!C_(t)||t,n.fail=C_(e)&&e,n.domain=x_?rT.domain:void 0,0===r.state?r.reactions.add(n):F_((function(){sT(n,r)})),n.promise})),O_=function(){var t=new E_,e=K_(t);this.promise=t,this.resolve=vT(yT,e),this.reject=vT(dT,e)},X_.f=nT=function(t){return t===Q_||undefined===t?new O_(t):oT(t)}),S_({global:!0,constructor:!0,wrap:!0,forced:B_},{Promise:Q_}),P_(Q_,V_,!1,!0),I_(V_);var gT=e_,mT=m_.CONSTRUCTOR||!zh((function(t){gT.all(t).then(void 0,(function(){}))})),bT=C,wT=Dt,_T=b_,TT=t_,ET=gb;Ar({target:"Promise",stat:!0,forced:mT},{all:function(t){var e=this,r=_T.f(e),n=r.resolve,o=r.reject,i=TT((function(){var r=wT(e.resolve),i=[],a=0,u=1;ET(t,(function(t){var s=a++,c=!1;u++,bT(r,e,t).then((function(t){c||(c=!0,i[s]=t,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),r.promise}});var OT=Ar,ST=m_.CONSTRUCTOR;e_&&e_.prototype,OT({target:"Promise",proto:!0,forced:ST,real:!0},{catch:function(t){return this.then(void 0,t)}});var xT=C,kT=Dt,jT=b_,AT=t_,PT=gb;Ar({target:"Promise",stat:!0,forced:mT},{race:function(t){var e=this,r=jT.f(e),n=r.reject,o=AT((function(){var o=kT(e.resolve);PT(t,(function(t){xT(o,e,t).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var IT=C,DT=b_;Ar({target:"Promise",stat:!0,forced:m_.CONSTRUCTOR},{reject:function(t){var e=DT.f(this);return IT(e.reject,void 0,t),e.promise}});var CT=nr,LT=et,RT=b_,MT=function(t,e){if(CT(t),LT(e)&&e.constructor===t)return e;var r=RT.f(t);return(0,r.resolve)(e),r.promise},NT=Ar,FT=e_,zT=m_.CONSTRUCTOR,UT=MT,qT=ut("Promise"),WT=!zT;NT({target:"Promise",stat:!0,forced:true},{resolve:function(t){return UT(WT&&this===qT?FT:this,t)}});var YT=C,GT=Dt,XT=b_,VT=t_,BT=gb;Ar({target:"Promise",stat:!0,forced:mT},{allSettled:function(t){var e=this,r=XT.f(e),n=r.resolve,o=r.reject,i=VT((function(){var r=GT(e.resolve),o=[],i=0,a=1;BT(t,(function(t){var u=i++,s=!1;a++,YT(r,e,t).then((function(t){s||(s=!0,o[u]={status:"fulfilled",value:t},--a||n(o))}),(function(t){s||(s=!0,o[u]={status:"rejected",reason:t},--a||n(o))}))})),--a||n(o)}));return i.error&&o(i.value),r.promise}});var HT=C,KT=Dt,JT=ut,$T=b_,QT=t_,ZT=gb,tE="No one promise resolved";Ar({target:"Promise",stat:!0,forced:mT},{any:function(t){var e=this,r=JT("AggregateError"),n=$T.f(e),o=n.resolve,i=n.reject,a=QT((function(){var n=KT(e.resolve),a=[],u=0,s=1,c=!1;ZT(t,(function(t){var f=u++,l=!1;s++,HT(n,e,t).then((function(t){l||c||(c=!0,o(t))}),(function(t){l||c||(l=!0,a[f]=t,--s||i(new r(a,tE)))}))})),--s||i(new r(a,tE))}));return a.error&&i(a.value),n.promise}});var eE=Ar,rE=e_,nE=a,oE=ut,iE=j,aE=Qb,uE=MT,sE=rE&&rE.prototype;eE({target:"Promise",proto:!0,real:!0,forced:!!rE&&nE((function(){sE.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=aE(this,oE("Promise")),r=iE(t);return this.then(r?function(r){return uE(e,t()).then((function(){return r}))}:t,r?function(r){return uE(e,t()).then((function(){throw r}))}:t)}});var cE=rt.Promise,fE=b_;Ar({target:"Promise",stat:!0},{withResolvers:function(){var t=fE.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}});var lE=cE,hE=b_,pE=t_;Ar({target:"Promise",stat:!0,forced:!0},{try:function(t){var e=hE.f(this),r=pE(t);return(r.error?e.reject:e.resolve)(r.value),e.promise}});var vE=lE,dE=mv;!function(t){var e=zm.default,r=Mr,n=df,o=xm,i=Lm,a=Um,u=up,s=jm,c=vE,f=dE,l=jp;function h(){t.exports=h=function(){return v},t.exports.__esModule=!0,t.exports.default=t.exports;var p,v={},d=Object.prototype,y=d.hasOwnProperty,g=r||function(t,e,r){t[e]=r.value},m="function"==typeof n?n:{},b=m.iterator||"@@iterator",w=m.asyncIterator||"@@asyncIterator",_=m.toStringTag||"@@toStringTag";function T(t,e,n){return r(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{T({},"")}catch(p){T=function(t,e,r){return t[e]=r}}function E(t,e,r,n){var i=e&&e.prototype instanceof P?e:P,a=o(i.prototype),u=new W(n||[]);return g(a,"_invoke",{value:F(t,r,u)}),a}function O(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}v.wrap=E;var S="suspendedStart",x="suspendedYield",k="executing",j="completed",A={};function P(){}function I(){}function D(){}var C={};T(C,b,(function(){return this}));var L=i&&i(i(Y([])));L&&L!==d&&y.call(L,b)&&(C=L);var R=D.prototype=P.prototype=o(C);function M(t){var e;a(e=["next","throw","return"]).call(e,(function(e){T(t,e,(function(t){return this._invoke(e,t)}))}))}function N(t,r){function n(o,i,a,u){var s=O(t[o],t,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==e(f)&&y.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):r.resolve(f).then((function(t){c.value=t,a(c)}),(function(t){return n("throw",t,a,u)}))}u(s.arg)}var o;g(this,"_invoke",{value:function(t,e){function i(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(i,i):i()}})}function F(t,e,r){var n=S;return function(o,i){if(n===k)throw new Error("Generator is already running");if(n===j){if("throw"===o)throw i;return{value:p,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=z(a,r);if(u){if(u===A)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===S)throw n=j,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=k;var s=O(t,e,r);if("normal"===s.type){if(n=r.done?j:x,s.arg===A)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n=j,r.method="throw",r.arg=s.arg)}}}function z(t,e){var r=e.method,n=t.iterator[r];if(n===p)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=p,z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),A;var o=O(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,A;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=p),e.delegate=null,A):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,A)}function U(t){var e,r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),u(e=this.tryEntries).call(e,r)}function q(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function W(t){this.tryEntries=[{tryLoc:"root"}],a(t).call(t,U,this),this.reset(!0)}function Y(t){if(t||""===t){var r=t[b];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(y.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=p,e.done=!0,e};return o.next=o}}throw new TypeError(e(t)+" is not iterable")}return I.prototype=D,g(R,"constructor",{value:D,configurable:!0}),g(D,"constructor",{value:I,configurable:!0}),I.displayName=T(D,_,"GeneratorFunction"),v.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===I||"GeneratorFunction"===(e.displayName||e.name))},v.mark=function(t){return s?s(t,D):(t.__proto__=D,T(t,_,"GeneratorFunction")),t.prototype=o(R),t},v.awrap=function(t){return{__await:t}},M(N.prototype),T(N.prototype,w,(function(){return this})),v.AsyncIterator=N,v.async=function(t,e,r,n,o){void 0===o&&(o=c);var i=new N(E(t,e,r,n),o);return v.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},M(R),T(R,_,"Generator"),T(R,b,(function(){return this})),T(R,"toString",(function(){return"[object Generator]"})),v.keys=function(t){var e=Object(t),r=[];for(var n in e)u(r).call(r,n);return f(r).call(r),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},v.values=Y,W.prototype={constructor:W,reset:function(t){var e;if(this.prev=0,this.next=0,this.sent=this._sent=p,this.done=!1,this.delegate=null,this.method="next",this.arg=p,a(e=this.tryEntries).call(e,q),!t)for(var r in this)"t"===r.charAt(0)&&y.call(this,r)&&!isNaN(+l(r).call(r,1))&&(this[r]=p)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=p),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var a=y.call(o,"catchLoc"),u=y.call(o,"finallyLoc");if(a&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&y.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,A):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),A},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),q(r),A}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;q(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:Y(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=p),A}},v}t.exports=h,t.exports.__esModule=!0,t.exports.default=t.exports}(Nm);var yE=(0,Nm.exports)(),gE=yE;try{regeneratorRuntime=yE}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=yE:Function("r","regeneratorRuntime = r")(yE)}var mE=r(gE),bE={exports:{}},wE=a((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),_E=a,TE=et,EE=_,OE=wE,SE=Object.isExtensible,xE=_E((function(){SE(1)}))||OE?function(t){return!!TE(t)&&((!OE||"ArrayBuffer"!==EE(t))&&(!SE||SE(t)))}:SE,kE=!a((function(){return Object.isExtensible(Object.preventExtensions({}))})),jE=Ar,AE=g,PE=ho,IE=et,DE=Zt,CE=Qe.f,LE=Ko,RE=Qo,ME=xE,NE=kE,FE=!1,zE=oe("meta"),UE=0,qE=function(t){CE(t,zE,{value:{objectID:"O"+UE++,weakData:{}}})},WE=bE.exports={enable:function(){WE.enable=function(){},FE=!0;var t=LE.f,e=AE([].splice),r={};r[zE]=1,t(r).length&&(LE.f=function(r){for(var n=t(r),o=0,i=n.length;o<i;o++)if(n[o]===zE){e(n,o,1);break}return n},jE({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:RE.f}))},fastKey:function(t,e){if(!IE(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!DE(t,zE)){if(!ME(t))return"F";if(!e)return"E";qE(t)}return t[zE].objectID},getWeakData:function(t,e){if(!DE(t,zE)){if(!ME(t))return!0;if(!e)return!1;qE(t)}return t[zE].weakData},onFreeze:function(t){return NE&&FE&&ME(t)&&!DE(t,zE)&&qE(t),t}};PE[zE]=!0;var YE=bE.exports,GE=Ar,XE=i,VE=YE,BE=a,HE=gr,KE=gb,JE=Bb,$E=j,QE=et,ZE=V,tO=Fi,eO=Qe.f,rO=sa.forEach,nO=P,oO=ta.set,iO=ta.getterFor,aO=function(t,e,r){var n,o=-1!==t.indexOf("Map"),i=-1!==t.indexOf("Weak"),a=o?"set":"add",u=XE[t],s=u&&u.prototype,c={};if(nO&&$E(u)&&(i||s.forEach&&!BE((function(){(new u).entries().next()})))){var f=(n=e((function(e,r){oO(JE(e,f),{type:t,collection:new u}),ZE(r)||KE(r,e[a],{that:e,AS_ENTRIES:o})}))).prototype,l=iO(t);rO(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var e="add"===t||"set"===t;!(t in s)||i&&"clear"===t||HE(f,t,(function(r,n){var o=l(this).collection;if(!e&&i&&!QE(r))return"get"===t&&void 0;var a=o[t](0===r?0:r,n);return e?this:a}))})),i||eO(f,"size",{configurable:!0,get:function(){return l(this).collection.size}})}else n=r.getConstructor(e,t,o,a),VE.enable();return tO(n,t,!1,!0),c[t]=n,GE({global:!0,forced:!0},c),i||r.setStrong(n,t,o),n},uO=hi,sO=Ho,cO=vi,fO=function(t,e,r){for(var n in e)r&&r.unsafe&&t[n]?t[n]=e[n]:uO(t,n,e[n],r);return t},lO=$e,hO=Bb,pO=V,vO=gb,dO=Ec,yO=Oc,gO=Gb,mO=P,bO=YE.fastKey,wO=ta.set,_O=ta.getterFor,TO={getConstructor:function(t,e,r,n){var o=t((function(t,o){hO(t,i),wO(t,{type:e,index:sO(null),first:void 0,last:void 0,size:0}),mO||(t.size=0),pO(o)||vO(o,t[n],{that:t,AS_ENTRIES:r})})),i=o.prototype,a=_O(e),u=function(t,e,r){var n,o,i=a(t),u=s(t,e);return u?u.value=r:(i.last=u={index:o=bO(e,!0),key:e,value:r,previous:n=i.last,next:void 0,removed:!1},i.first||(i.first=u),n&&(n.next=u),mO?i.size++:t.size++,"F"!==o&&(i.index[o]=u)),t},s=function(t,e){var r,n=a(t),o=bO(e);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key===e)return r};return fO(i,{clear:function(){for(var t=a(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,mO?t.size=0:this.size=0},delete:function(t){var e=this,r=a(e),n=s(e,t);if(n){var o=n.next,i=n.previous;delete r.index[n.index],n.removed=!0,i&&(i.next=o),o&&(o.previous=i),r.first===n&&(r.first=o),r.last===n&&(r.last=i),mO?r.size--:e.size--}return!!n},forEach:function(t){for(var e,r=a(this),n=lO(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!s(this,t)}}),fO(i,r?{get:function(t){var e=s(this,t);return e&&e.value},set:function(t,e){return u(this,0===t?0:t,e)}}:{add:function(t){return u(this,t=0===t?0:t,t)}}),mO&&cO(i,"size",{configurable:!0,get:function(){return a(this).size}}),o},setStrong:function(t,e,r){var n=e+" Iterator",o=_O(e),i=_O(n);dO(t,e,(function(t,e){wO(this,{type:n,target:t,state:o(t),kind:e,last:void 0})}),(function(){for(var t=i(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?yO("keys"===e?r.key:"values"===e?r.value:[r.key,r.value],!1):(t.target=void 0,yO(void 0,!0))}),r?"entries":"values",!r,!0),gO(e)}};aO("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),TO);var EO=r(rt.Map),OO=sa.some;Ar({target:"Array",proto:!0,forced:!gl("some")},{some:function(t){return OO(this,t,arguments.length>1?arguments[1]:void 0)}});var SO=rl("Array","some"),xO=st,kO=SO,jO=Array.prototype,AO=r((function(t){var e=t.some;return t===jO||xO(jO,t)&&e===jO.some?kO:e})),PO=rl("Array","keys"),IO=cn,DO=Zt,CO=st,LO=PO,RO=Array.prototype,MO={DOMTokenList:!0,NodeList:!0},NO=r((function(t){var e=t.keys;return t===RO||CO(RO,t)&&e===RO.keys||DO(MO,IO(t))?LO:e})),FO=oi,zO=Math.floor,UO=function(t,e){var r=t.length,n=zO(r/2);return r<8?qO(t,e):WO(t,UO(FO(t,0,n),e),UO(FO(t,n),e),e)},qO=function(t,e){for(var r,n,o=t.length,i=1;i<o;){for(n=i,r=t[i];n&&e(t[n-1],r)>0;)t[n]=t[--n];n!==i++&&(t[n]=r)}return t},WO=function(t,e,r,n){for(var o=e.length,i=r.length,a=0,u=0;a<o||u<i;)t[a+u]=a<o&&u<i?n(e[a],r[u])<=0?e[a++]:r[u++]:a<o?e[a++]:r[u++];return t},YO=UO,GO=ct.match(/firefox\/(\d+)/i),XO=!!GO&&+GO[1],VO=/MSIE|Trident/.test(ct),BO=ct.match(/AppleWebKit\/(\d+)\./),HO=!!BO&&+BO[1],KO=Ar,JO=g,$O=Dt,QO=Jt,ZO=Br,tS=Tv,eS=eo,rS=a,nS=YO,oS=gl,iS=XO,aS=VO,uS=yt,sS=HO,cS=[],fS=JO(cS.sort),lS=JO(cS.push),hS=rS((function(){cS.sort(void 0)})),pS=rS((function(){cS.sort(null)})),vS=oS("sort"),dS=!rS((function(){if(uS)return uS<70;if(!(iS&&iS>3)){if(aS)return!0;if(sS)return sS<603;var t,e,r,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)cS.push({k:e+n,v:r})}for(cS.sort((function(t,e){return e.v-t.v})),n=0;n<cS.length;n++)e=cS[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));KO({target:"Array",proto:!0,forced:hS||!pS||!vS||!dS},{sort:function(t){void 0!==t&&$O(t);var e=QO(this);if(dS)return void 0===t?fS(e):fS(e,t);var r,n,o=[],i=ZO(e);for(n=0;n<i;n++)n in e&&lS(o,e[n]);for(nS(o,function(t){return function(e,r){return void 0===r?-1:void 0===e?1:void 0!==t?+t(e,r)||0:eS(e)>eS(r)?1:-1}}(t)),r=ZO(o),n=0;n<r;)e[n]=o[n++];for(;n<i;)tS(e,n++);return e}});var yS=rl("Array","sort"),gS=st,mS=yS,bS=Array.prototype,wS=r((function(t){var e=t.sort;return t===bS||gS(bS,t)&&e===bS.sort?mS:e})),_S=rl("Array","values"),TS=cn,ES=Zt,OS=st,SS=_S,xS=Array.prototype,kS={DOMTokenList:!0,NodeList:!0},jS=r((function(t){var e=t.values;return t===xS||OS(xS,t)&&e===xS.values||ES(kS,TS(t))?SS:e})),AS=r(Cf),PS=rl("Array","entries"),IS=cn,DS=Zt,CS=st,LS=PS,RS=Array.prototype,MS={DOMTokenList:!0,NodeList:!0},NS=r((function(t){var e=t.entries;return t===RS||CS(RS,t)&&e===RS.entries||DS(MS,IS(t))?LS:e}));let FS;const zS=new Uint8Array(16);function US(){if(!FS&&(FS="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!FS))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return FS(zS)}const qS=[];for(let t=0;t<256;++t)qS.push((t+256).toString(16).slice(1));var WS={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function YS(t,e,r){if(WS.randomUUID&&!e&&!t)return WS.randomUUID();const n=(t=t||{}).random||(t.rng||US)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,e){r=r||0;for(let t=0;t<16;++t)e[r+t]=n[t];return e}return function(t,e=0){return qS[t[e+0]]+qS[t[e+1]]+qS[t[e+2]]+qS[t[e+3]]+"-"+qS[t[e+4]]+qS[t[e+5]]+"-"+qS[t[e+6]]+qS[t[e+7]]+"-"+qS[t[e+8]]+qS[t[e+9]]+"-"+qS[t[e+10]]+qS[t[e+11]]+qS[t[e+12]]+qS[t[e+13]]+qS[t[e+14]]+qS[t[e+15]]}(n)}function GS(t){return"string"==typeof t||"number"==typeof t}var XS=function(){function e(r){t(this,e),qf(this,"_queue",[]),qf(this,"_timeout",null),qf(this,"_extended",null),this.delay=null,this.max=1/0,this.setOptions(r)}return Uf(e,[{key:"setOptions",value:function(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}},{key:"destroy",value:function(){if(this.flush(),this._extended){for(var t=this._extended.object,e=this._extended.methods,r=0;r<e.length;r++){var n=e[r];n.original?t[n.name]=n.original:delete t[n.name]}this._extended=null}}},{key:"replace",value:function(t,e){var r=this,n=t[e];if(!n)throw new Error("Method "+e+" undefined");t[e]=function(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];r.queue({args:e,fn:n,context:this})}}},{key:"queue",value:function(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}},{key:"_flushIfNeeded",value:function(){var t=this;this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=Id((function(){t.flush()}),this.delay))}},{key:"flush",value:function(){var t,e;cv(t=Uv(e=this._queue).call(e,0)).call(t,(function(t){t.fn.apply(t.context||t.fn,t.args||[])}))}}],[{key:"extend",value:function(t,r){var n=new e(r);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=function(){n.flush()};var o=[{name:"flush",original:void 0}];if(r&&r.replace)for(var i=0;i<r.replace.length;i++){var a=r.replace[i];o.push({name:a,original:t[a]}),n.replace(t,a)}return n._extended={object:t,methods:o},n}}]),e}(),VS=function(){function e(){t(this,e),qf(this,"_subscribers",{"*":[],add:[],remove:[],update:[]}),qf(this,"subscribe",e.prototype.on),qf(this,"unsubscribe",e.prototype.off)}return Uf(e,[{key:"_trigger",value:function(t,e,r){var n,o;if("*"===t)throw new Error("Cannot trigger event *");cv(n=Up(o=[]).call(o,Lp(this._subscribers[t]),Lp(this._subscribers["*"]))).call(n,(function(n){n(t,e,null!=r?r:null)}))}},{key:"on",value:function(t,e){"function"==typeof e&&this._subscribers[t].push(e)}},{key:"off",value:function(t,e){var r;this._subscribers[t]=Pl(r=this._subscribers[t]).call(r,(function(t){return t!==e}))}}]),e}();aO("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),TO);var BS=r(rt.Set),HS=r(Eh);function KS(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return JS(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return JS(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function JS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var $S=function(e){function r(e){t(this,r),this._pairs=e}return Uf(r,[{key:AS,value:mE.mark((function t(){var e,r,n,o,i;return mE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=KS(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Cp(r.value,2),o=n[0],i=n[1],t.next=7,[o,i];case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"entries",value:mE.mark((function t(){var e,r,n,o,i;return mE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=KS(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Cp(r.value,2),o=n[0],i=n[1],t.next=7,[o,i];case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"keys",value:mE.mark((function t(){var e,r,n,o;return mE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=KS(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Cp(r.value,1),o=n[0],t.next=7,o;case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"values",value:mE.mark((function t(){var e,r,n,o;return mE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=KS(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Cp(r.value,2),o=n[1],t.next=7,o;case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"toIdArray",value:function(){var t;return Ml(t=Lp(this._pairs)).call(t,(function(t){return t[0]}))}},{key:"toItemArray",value:function(){var t;return Ml(t=Lp(this._pairs)).call(t,(function(t){return t[1]}))}},{key:"toEntryArray",value:function(){return Lp(this._pairs)}},{key:"toObjectMap",value:function(){var t,e=sd(null),r=KS(this._pairs);try{for(r.s();!(t=r.n()).done;){var n=Cp(t.value,2),o=n[0],i=n[1];e[o]=i}}catch(t){r.e(t)}finally{r.f()}return e}},{key:"toMap",value:function(){return new EO(this._pairs)}},{key:"toIdSet",value:function(){return new BS(this.toIdArray())}},{key:"toItemSet",value:function(){return new BS(this.toItemArray())}},{key:"cache",value:function(){return new r(Lp(this._pairs))}},{key:"distinct",value:function(t){var e,r=new BS,n=KS(this._pairs);try{for(n.s();!(e=n.n()).done;){var o=Cp(e.value,2),i=o[0],a=o[1];r.add(t(a,i))}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"filter",value:function(t){var e=this._pairs;return new r({[AS]:()=>mE.mark((function r(){var n,o,i,a,u;return mE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=KS(e),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=10;break}if(i=Cp(o.value,2),a=i[0],u=i[1],!t(u,a)){r.next=8;break}return r.next=8,[a,u];case 8:r.next=3;break;case 10:r.next=15;break;case 12:r.prev=12,r.t0=r.catch(1),n.e(r.t0);case 15:return r.prev=15,n.f(),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[1,12,15,18]])}))()})}},{key:"forEach",value:function(t){var e,r=KS(this._pairs);try{for(r.s();!(e=r.n()).done;){var n=Cp(e.value,2),o=n[0];t(n[1],o)}}catch(t){r.e(t)}finally{r.f()}}},{key:"map",value:function(t){var e=this._pairs;return new r({[AS]:()=>mE.mark((function r(){var n,o,i,a,u;return mE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=KS(e),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=9;break}return i=Cp(o.value,2),a=i[0],u=i[1],r.next=7,[a,t(u,a)];case 7:r.next=3;break;case 9:r.next=14;break;case 11:r.prev=11,r.t0=r.catch(1),n.e(r.t0);case 14:return r.prev=14,n.f(),r.finish(14);case 17:case"end":return r.stop()}}),r,null,[[1,11,14,17]])}))()})}},{key:"max",value:function(t){var e=HS(this._pairs),r=e.next();if(r.done)return null;for(var n=r.value[1],o=t(r.value[1],r.value[0]);!(r=e.next()).done;){var i=Cp(r.value,2),a=i[0],u=i[1],s=t(u,a);s>o&&(o=s,n=u)}return n}},{key:"min",value:function(t){var e=HS(this._pairs),r=e.next();if(r.done)return null;for(var n=r.value[1],o=t(r.value[1],r.value[0]);!(r=e.next()).done;){var i=Cp(r.value,2),a=i[0],u=i[1],s=t(u,a);s<o&&(o=s,n=u)}return n}},{key:"reduce",value:function(t,e){var r,n=KS(this._pairs);try{for(n.s();!(r=n.n()).done;){var o=Cp(r.value,2),i=o[0];e=t(e,o[1],i)}}catch(t){n.e(t)}finally{n.f()}return e}},{key:"sort",value:function(t){var e=this;return new r({[AS]:function(){var r;return HS(wS(r=Lp(e._pairs)).call(r,(function(e,r){var n=Cp(e,2),o=n[0],i=n[1],a=Cp(r,2),u=a[0],s=a[1];return t(i,s,o,u)})))}})}}]),r}();function QS(t,e){var r=Qp(t);if(rm){var n=rm(t);e&&(n=Pl(n).call(n,(function(e){return lm(t,e).enumerable}))),r.push.apply(r,n)}return r}function ZS(t){for(var e=1;e<arguments.length;e++){var r,n,o=null!=arguments[e]?arguments[e]:{};e%2?cv(r=QS(Object(o),!0)).call(r,(function(e){qf(t,e,o[e])})):ym?Em(t,ym(o)):cv(n=QS(Object(o))).call(n,(function(e){Om(t,e,lm(o,e))}))}return t}function tx(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return ex(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ex(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function ex(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function rx(t){var e=function(){if("undefined"==typeof Reflect||!em)return!1;if(em.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(em(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Mm(t);if(e){var o=Mm(this).constructor;r=em(n,arguments,o)}else r=n.apply(this,arguments);return Cm(this,r)}}var nx=function(e){Dm(n,VS);var r=rx(n);function n(e,o){var i;return t(this,n),qf(Sm(i=r.call(this)),"_queue",null),e&&!Kp(e)&&(o=e,e=[]),i._options=o||{},i._data=new EO,i.length=0,i._idProp=i._options.fieldId||"id",e&&e.length&&i.add(e),i.setOptions(o),i}return Uf(n,[{key:"idProp",get:function(){return this._idProp}},{key:"setOptions",value:function(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=XS.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}},{key:"add",value:function(t,e){var r,n=this,o=[];if(Kp(t)){var i=Ml(t).call(t,(function(t){return t[n._idProp]}));if(AO(i).call(i,(function(t){return n._data.has(t)})))throw new Error("A duplicate id was found in the parameter array.");for(var a=0,u=t.length;a<u;a++)r=this._addItem(t[a]),o.push(r)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");r=this._addItem(t),o.push(r)}return o.length&&this._trigger("add",{items:o},e),o}},{key:"update",value:function(t,e){var r=this,n=[],o=[],i=[],a=[],u=this._idProp,s=function(t){var e=t[u];if(null!=e&&r._data.has(e)){var s=t,c=ed({},r._data.get(e)),f=r._updateItem(s);o.push(f),a.push(s),i.push(c)}else{var l=r._addItem(t);n.push(l)}};if(Kp(t))for(var c=0,f=t.length;c<f;c++)t[c]&&"object"==typeof t[c]?s(t[c]):console.warn("Ignoring input item, which is not an object at index "+c);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");s(t)}if(n.length&&this._trigger("add",{items:n},e),o.length){var l={items:o,oldData:i,data:a};this._trigger("update",l,e)}return Up(n).call(n,o)}},{key:"updateOnly",value:function(t,e){var r,n=this;Kp(t)||(t=[t]);var o=Ml(r=Ml(t).call(t,(function(t){var e=n._data.get(t[n._idProp]);if(null==e)throw new Error("Updating non-existent items is not allowed.");return{oldData:e,update:t}}))).call(r,(function(t){var e=t.oldData,r=t.update,o=e[n._idProp],i=function(t){for(var e,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return Ig.apply(void 0,Up(e=[{},t]).call(e,n))}(e,r);return n._data.set(o,i),{id:o,oldData:e,updatedData:i}}));if(o.length){var i={items:Ml(o).call(o,(function(t){return t.id})),oldData:Ml(o).call(o,(function(t){return t.oldData})),data:Ml(o).call(o,(function(t){return t.updatedData}))};return this._trigger("update",i,e),i.items}return[]}},{key:"get",value:function(t,e){var r=void 0,n=void 0,o=void 0;GS(t)?(r=t,o=e):Kp(t)?(n=t,o=e):o=t;var i,a=o&&"Object"===o.returnType?"Object":"Array",u=o&&Pl(o),s=[],c=void 0,f=void 0,l=void 0;if(null!=r)(c=this._data.get(r))&&u&&!u(c)&&(c=void 0);else if(null!=n)for(var h=0,p=n.length;h<p;h++)null==(c=this._data.get(n[h]))||u&&!u(c)||s.push(c);else for(var v,d=0,y=(f=Lp(NO(v=this._data).call(v))).length;d<y;d++)l=f[d],null==(c=this._data.get(l))||u&&!u(c)||s.push(c);if(o&&o.order&&null==r&&this._sort(s,o.order),o&&o.fields){var g=o.fields;if(null!=r&&null!=c)c=this._filterFields(c,g);else for(var m=0,b=s.length;m<b;m++)s[m]=this._filterFields(s[m],g)}if("Object"==a){for(var w={},_=0,T=s.length;_<T;_++){var E=s[_];w[E[this._idProp]]=E}return w}return null!=r?null!==(i=c)&&void 0!==i?i:null:s}},{key:"getIds",value:function(t){var e=this._data,r=t&&Pl(t),n=t&&t.order,o=Lp(NO(e).call(e)),i=[];if(r)if(n){for(var a=[],u=0,s=o.length;u<s;u++){var c=o[u],f=this._data.get(c);null!=f&&r(f)&&a.push(f)}this._sort(a,n);for(var l=0,h=a.length;l<h;l++)i.push(a[l][this._idProp])}else for(var p=0,v=o.length;p<v;p++){var d=o[p],y=this._data.get(d);null!=y&&r(y)&&i.push(y[this._idProp])}else if(n){for(var g=[],m=0,b=o.length;m<b;m++){var w=o[m];g.push(e.get(w))}this._sort(g,n);for(var _=0,T=g.length;_<T;_++)i.push(g[_][this._idProp])}else for(var E=0,O=o.length;E<O;E++){var S=o[E],x=e.get(S);null!=x&&i.push(x[this._idProp])}return i}},{key:"getDataSet",value:function(){return this}},{key:"forEach",value:function(t,e){var r=e&&Pl(e),n=this._data,o=Lp(NO(n).call(n));if(e&&e.order)for(var i=this.get(e),a=0,u=i.length;a<u;a++){var s=i[a];t(s,s[this._idProp])}else for(var c=0,f=o.length;c<f;c++){var l=o[c],h=this._data.get(l);null==h||r&&!r(h)||t(h,l)}}},{key:"map",value:function(t,e){for(var r=e&&Pl(e),n=[],o=this._data,i=Lp(NO(o).call(o)),a=0,u=i.length;a<u;a++){var s=i[a],c=this._data.get(s);null==c||r&&!r(c)||n.push(t(c,s))}return e&&e.order&&this._sort(n,e.order),n}},{key:"_filterFields",value:function(t,e){var r;return t?Ol(r=Kp(e)?e:Qp(e)).call(r,(function(e,r){return e[r]=t[r],e}),{}):t}},{key:"_sort",value:function(t,e){if("string"==typeof e){var r=e;wS(t).call(t,(function(t,e){var n=t[r],o=e[r];return n>o?1:n<o?-1:0}))}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");wS(t).call(t,e)}}},{key:"remove",value:function(t,e){for(var r=[],n=[],o=Kp(t)?t:[t],i=0,a=o.length;i<a;i++){var u=this._remove(o[i]);if(u){var s=u[this._idProp];null!=s&&(r.push(s),n.push(u))}}return r.length&&this._trigger("remove",{items:r,oldData:n},e),r}},{key:"_remove",value:function(t){var e;if(GS(t)?e=t:t&&"object"==typeof t&&(e=t[this._idProp]),null!=e&&this._data.has(e)){var r=this._data.get(e)||null;return this._data.delete(e),--this.length,r}return null}},{key:"clear",value:function(t){for(var e,r=Lp(NO(e=this._data).call(e)),n=[],o=0,i=r.length;o<i;o++)n.push(this._data.get(r[o]));return this._data.clear(),this.length=0,this._trigger("remove",{items:r,oldData:n},t),r}},{key:"max",value:function(t){var e,r,n=null,o=null,i=tx(jS(e=this._data).call(e));try{for(i.s();!(r=i.n()).done;){var a=r.value,u=a[t];"number"==typeof u&&(null==o||u>o)&&(n=a,o=u)}}catch(t){i.e(t)}finally{i.f()}return n||null}},{key:"min",value:function(t){var e,r,n=null,o=null,i=tx(jS(e=this._data).call(e));try{for(i.s();!(r=i.n()).done;){var a=r.value,u=a[t];"number"==typeof u&&(null==o||u<o)&&(n=a,o=u)}}catch(t){i.e(t)}finally{i.f()}return n||null}},{key:"distinct",value:function(t){for(var e=this._data,r=Lp(NO(e).call(e)),n=[],o=0,i=0,a=r.length;i<a;i++){for(var u=r[i],s=e.get(u)[t],c=!1,f=0;f<o;f++)if(n[f]==s){c=!0;break}c||void 0===s||(n[o]=s,o++)}return n}},{key:"_addItem",value:function(t){var e=function(t,e){return null==t[e]&&(t[e]=YS()),t}(t,this._idProp),r=e[this._idProp];if(this._data.has(r))throw new Error("Cannot add item: item with id "+r+" already exists");return this._data.set(r,e),++this.length,r}},{key:"_updateItem",value:function(t){var e=t[this._idProp];if(null==e)throw new Error("Cannot update item: item has no id (item: "+ld(t)+")");var r=this._data.get(e);if(!r)throw new Error("Cannot update item: no item with id "+e+" found");return this._data.set(e,ZS(ZS({},r),t)),e}},{key:"stream",value:function(t){if(t){var e=this._data;return new $S({[AS]:()=>mE.mark((function r(){var n,o,i,a;return mE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=tx(t),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=11;break}if(i=o.value,null==(a=e.get(i))){r.next=9;break}return r.next=9,[i,a];case 9:r.next=3;break;case 11:r.next=16;break;case 13:r.prev=13,r.t0=r.catch(1),n.e(r.t0);case 16:return r.prev=16,n.f(),r.finish(16);case 19:case"end":return r.stop()}}),r,null,[[1,13,16,19]])}))()})}var r;return new $S({[AS]:sl(r=NS(this._data)).call(r,this._data)})}}]),n}();function ox(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return ix(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ix(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function ix(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ax(t){var e=function(){if("undefined"==typeof Reflect||!em)return!1;if(em.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(em(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Mm(t);if(e){var o=Mm(this).constructor;r=em(n,arguments,o)}else r=n.apply(this,arguments);return Cm(this,r)}}var ux=function(e){Dm(n,VS);var r=ax(n);function n(e,o){var i,a;return t(this,n),qf(Sm(a=r.call(this)),"length",0),qf(Sm(a),"_ids",new BS),a._options=o||{},a._listener=sl(i=a._onEvent).call(i,Sm(a)),a.setData(e),a}return Uf(n,[{key:"idProp",get:function(){return this.getDataSet().idProp}},{key:"setData",value:function(t){if(this._data){this._data.off&&this._data.off("*",this._listener);var e=this._data.getIds({filter:Pl(this._options)}),r=this._data.get(e);this._ids.clear(),this.length=0,this._trigger("remove",{items:e,oldData:r})}if(null!=t){this._data=t;for(var n=this._data.getIds({filter:Pl(this._options)}),o=0,i=n.length;o<i;o++){var a=n[o];this._ids.add(a)}this.length=n.length,this._trigger("add",{items:n})}else this._data=new nx;this._data.on&&this._data.on("*",this._listener)}},{key:"refresh",value:function(){for(var t=this._data.getIds({filter:Pl(this._options)}),e=Lp(this._ids),r={},n=[],o=[],i=[],a=0,u=t.length;a<u;a++){var s=t[a];r[s]=!0,this._ids.has(s)||(n.push(s),this._ids.add(s))}for(var c=0,f=e.length;c<f;c++){var l=e[c],h=this._data.get(l);null==h?console.error("If you see this, report it please."):r[l]||(o.push(l),i.push(h),this._ids.delete(l))}this.length+=n.length-o.length,n.length&&this._trigger("add",{items:n}),o.length&&this._trigger("remove",{items:o,oldData:i})}},{key:"get",value:function(t,e){if(null==this._data)return null;var r,n=null;GS(t)||Kp(t)?(n=t,r=e):r=t;var o=ed({},this._options,r),i=Pl(this._options),a=r&&Pl(r);return i&&a&&(o.filter=function(t){return i(t)&&a(t)}),null==n?this._data.get(o):this._data.get(n,o)}},{key:"getIds",value:function(t){if(this._data.length){var e,r=Pl(this._options),n=null!=t?Pl(t):null;return e=n?r?function(t){return r(t)&&n(t)}:n:r,this._data.getIds({filter:e,order:t&&t.order})}return[]}},{key:"forEach",value:function(t,e){if(this._data){var r,n,o=Pl(this._options),i=e&&Pl(e);n=i?o?function(t){return o(t)&&i(t)}:i:o,cv(r=this._data).call(r,t,{filter:n,order:e&&e.order})}}},{key:"map",value:function(t,e){if(this._data){var r,n,o=Pl(this._options),i=e&&Pl(e);return n=i?o?function(t){return o(t)&&i(t)}:i:o,Ml(r=this._data).call(r,t,{filter:n,order:e&&e.order})}return[]}},{key:"getDataSet",value:function(){return this._data.getDataSet()}},{key:"stream",value:function(t){var e;return this._data.stream(t||{[AS]:sl(e=NO(this._ids)).call(e,this._ids)})}},{key:"dispose",value:function(){var t;null!==(t=this._data)&&void 0!==t&&t.off&&this._data.off("*",this._listener);var e,r="This data view has already been disposed of.",o={get:function(){throw new Error(r)},set:function(){throw new Error(r)},configurable:!1},i=ox(Hp(n.prototype));try{for(i.s();!(e=i.n()).done;){var a=e.value;Om(this,a,o)}}catch(t){i.e(t)}finally{i.f()}}},{key:"_onEvent",value:function(t,e,r){if(e&&e.items&&this._data){var n=e.items,o=[],i=[],a=[],u=[],s=[],c=[];switch(t){case"add":for(var f=0,l=n.length;f<l;f++){var h=n[f];this.get(h)&&(this._ids.add(h),o.push(h))}break;case"update":for(var p=0,v=n.length;p<v;p++){var d=n[p];this.get(d)?this._ids.has(d)?(i.push(d),s.push(e.data[p]),u.push(e.oldData[p])):(this._ids.add(d),o.push(d)):this._ids.has(d)&&(this._ids.delete(d),a.push(d),c.push(e.oldData[p]))}break;case"remove":for(var y=0,g=n.length;y<g;y++){var m=n[y];this._ids.has(m)&&(this._ids.delete(m),a.push(m),c.push(e.oldData[y]))}}this.length+=o.length-a.length,o.length&&this._trigger("add",{items:o},r),i.length&&this._trigger("update",{items:i,oldData:u,data:s},r),a.length&&this._trigger("remove",{items:a,oldData:c},r)}}}]),n}();function sx(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.add&&"function"==typeof e.clear&&"function"==typeof e.distinct&&"function"==typeof cv(e)&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof Ml(e)&&"function"==typeof e.max&&"function"==typeof e.min&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.remove&&"function"==typeof e.setOptions&&"function"==typeof e.stream&&"function"==typeof e.update&&"function"==typeof e.updateOnly}function cx(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof cv(e)&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof Ml(e)&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.stream&&sx(t,e.getDataSet())}export{Pg as DELETE,nx as DataSet,$S as DataStream,ux as DataView,XS as Queue,Ql as createNewDataPipeFrom,sx as isDataSetLike,cx as isDataViewLike};
//# sourceMappingURL=vis-data.min.js.map
