{"version": 3, "file": "data-stream.d.ts", "sourceRoot": "", "sources": ["../src/data-stream.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,EAAE,EAAE,MAAM,kBAAkB,CAAC;AAEtC;;;;;;;;GAQG;AACH,qBAAa,UAAU,CAAC,IAAI,CAAE,YAAW,QAAQ,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC3D,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAuB;IAE9C;;;;OAIG;gBACgB,KAAK,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAI9C;;OAEG;IACK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAMzD;;OAEG;IACK,OAAO,IAAI,gBAAgB,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAM/C;;OAEG;IACK,IAAI,IAAI,gBAAgB,CAAC,EAAE,CAAC;IAMpC;;OAEG;IACK,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC;IAMxC;;;;;;OAMG;IACI,SAAS,IAAI,EAAE,EAAE;IAIxB;;;;;;OAMG;IACI,WAAW,IAAI,IAAI,EAAE;IAI5B;;;;;;OAMG;IACI,YAAY,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAInC;;;;;;OAMG;IACI,WAAW,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC;IAQtC;;;;OAIG;IACI,KAAK,IAAI,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC;IAI7B;;;;OAIG;IACI,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC;IAIzB;;;;OAIG;IACI,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC;IAI7B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,KAAK,IAAI,UAAU,CAAC,IAAI,CAAC;IAIhC;;;;;;OAMG;IACI,QAAQ,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAU/D;;;;;OAKG;IACI,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;IAa1E;;;;OAIG;IACI,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,OAAO,GAAG,IAAI;IAM/D;;;;;;OAMG;IACI,GAAG,CAAC,MAAM,EACf,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,MAAM,GACvC,UAAU,CAAC,MAAM,CAAC;IAWrB;;;;;OAKG;IACI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;IAqBjE;;;;;OAKG;IACI,GAAG,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;IAqBjE;;;;;;;OAOG;IACI,MAAM,CAAC,CAAC,EACb,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EACnD,WAAW,EAAE,CAAC,GACb,CAAC;IAOJ;;;;;OAKG;IACI,IAAI,CACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,KAAK,MAAM,GAC/D,UAAU,CAAC,IAAI,CAAC;CAUpB"}