{"name": "vis-util", "version": "5.0.7", "description": "utilitie collection for visjs", "browser": "peer/umd/vis-util.min.js", "jsnext": "esnext/esm/vis-util.js", "main": "peer/umd/vis-util.js", "module": "peer/esm/vis-util.js", "types": "declarations/index.d.ts", "repository": {"type": "git", "url": "https://github.com/visjs/vis-util.git"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "jos <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "wimrijn<PERSON> <<EMAIL>>", "macleodbroad-wf <<EMAIL>>", "yotamberk <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Preetham G R <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Max <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "scripts": {"build": "run-s build:types build:code build:docs", "build:code": "rollup --bundleConfigAsCjs --config rollup.build.js", "build:docs": "typedoc", "build:types": "tsc -p tsconfig.types.json", "clean": "rimraf --glob \"{coverage,declarations,docs,esnext,peer,reports,standalone}\" \"index.{d.ts,js}\"", "style": "prettier --check .", "style-fix": "prettier --write .", "lint": "eslint --ext .js,.ts .", "lint-fix": "eslint --fix --ext .js,.ts .", "prepublishOnly": "npm run build", "test": "run-s test:unit test:types:check-dts test:types:tsc test:interop", "test:coverage": "cross-env BABEL_ENV=test-cov nyc mocha", "test:interop": "node interop.js", "test:interop:debug": "npm run test:interop -- --fail-command \"$SHELL\"", "test:types:check-dts": "cd test && check-dts", "test:types:tsc": "tsc --noemit --project tsconfig.check.json", "test:unit": "cross-env BABEL_ENV=test mocha", "type-check": "run-s test:types:*", "version": "npm run contributors:update && git add package.json", "contributors:update": "git-authors-cli", "preparepublish": "npm run contributors:update", "prepare": "husky install"}, "lint-staged": {"*.*": "prettier --write", "*.{js,ts}": "eslint --fix", ".*.*": "prettier --write", ".*.{js,ts}": "eslint --fix"}, "config": {"snap-shot-it": {"sortSnapshots": true, "useRelativePath": true}}, "engines": {"node": ">=8"}, "keywords": ["util", "vis", "vis.js"], "license": "(Apache-2.0 OR MIT)", "bugs": {"url": "https://github.com/visjs/vis-util/issues"}, "homepage": "https://github.com/visjs/vis-util", "files": ["LICENSE*", "standalone", "peer", "esnext", "declarations"], "funding": {"type": "opencollective", "url": "https://opencollective.com/visjs"}, "peerDependencies": {"@egjs/hammerjs": "^2.0.0", "component-emitter": "^1.3.0 || ^2.0.0"}, "devDependencies": {"@egjs/hammerjs": "2.0.17", "@types/chai": "4.3.10", "@types/jsdom-global": "^3.0.4", "@types/mocha": "10.0.4", "@types/node": "20.9.2", "@types/sinon": "17.0.1", "assert": "2.1.0", "check-dts": "0.7.2", "component-emitter": "2.0.0", "cross-env": "^7.0.3", "eslint": "8.54.0", "git-authors-cli": "1.0.47", "husky": "8.0.3", "jsdom": "22.1.0", "jsdom-global": "3.0.2", "lint-staged": "15.1.0", "mocha": "10.2.0", "npm-run-all": "4.1.5", "nyc": "15.1.0", "rimraf": "5.0.5", "sazerac": "2.0.0", "semantic-release": "22.0.8", "sinon": "17.0.1", "snap-shot-it": "7.9.10", "typedoc": "0.25.3", "vis-dev-utils": "4.0.45"}}