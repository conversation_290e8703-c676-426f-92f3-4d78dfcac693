{"version": 3, "file": "vis-util.js", "sources": ["../../src/deep-object-assign.ts", "../../src/random/alea.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/popup.js", "../../src/shared/validator.js", "../../src/shared/index.ts"], "sourcesContent": ["/**\n * Use this symbol to delete properies in deepObjectAssign.\n */\nexport const DELETE = Symbol(\"DELETE\");\n\n/**\n * Turns `undefined` into `undefined | typeof DELETE` and makes everything\n * partial. Intended to be used with `deepObjectAssign`.\n */\nexport type Assignable<T> = T extends undefined\n  ?\n      | (T extends Function\n          ? T\n          : T extends object\n          ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n          : T)\n      | typeof DELETE\n  : T extends Function\n  ? T | undefined\n  : T extends object\n  ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n  : T | undefined;\n\n/**\n * Pure version of deepObjectAssign, it doesn't modify any of it's arguments.\n *\n * @param base - The base object that fullfils the whole interface T.\n * @param updates - Updates that may change or delete props.\n * @returns A brand new instance with all the supplied objects deeply merged.\n */\nexport function pureDeepObjectAssign<T>(\n  base: T,\n  ...updates: Assignable<T>[]\n): T {\n  return deepObjectAssign({} as any, base, ...updates);\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param target - The object that will be augmented using the sources.\n * @param sources - Objects to be deeply merged into the target.\n * @returns The target (same instance).\n */\nexport function deepObjectAssign<T>(target: T, ...sources: Assignable<T>[]): T;\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nexport function deepObjectAssign(...values: readonly any[]): any {\n  const merged = deepObjectAssignNonentry(...values);\n  stripDelete(merged);\n  return merged;\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @remarks\n * This doesn't strip the DELETE symbols so they may end up in the final object.\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nfunction deepObjectAssignNonentry(...values: readonly any[]): any {\n  if (values.length < 2) {\n    return values[0];\n  } else if (values.length > 2) {\n    return deepObjectAssignNonentry(\n      deepObjectAssign(values[0], values[1]),\n      ...values.slice(2)\n    );\n  }\n\n  const a = values[0];\n  const b = values[1];\n\n  if (a instanceof Date && b instanceof Date) {\n    a.setTime(b.getTime());\n    return a;\n  }\n\n  for (const prop of Reflect.ownKeys(b)) {\n    if (!Object.prototype.propertyIsEnumerable.call(b, prop)) {\n      // Ignore nonenumerable props, Object.assign() would do the same.\n    } else if (b[prop] === DELETE) {\n      delete a[prop];\n    } else if (\n      a[prop] !== null &&\n      b[prop] !== null &&\n      typeof a[prop] === \"object\" &&\n      typeof b[prop] === \"object\" &&\n      !Array.isArray(a[prop]) &&\n      !Array.isArray(b[prop])\n    ) {\n      a[prop] = deepObjectAssignNonentry(a[prop], b[prop]);\n    } else {\n      a[prop] = clone(b[prop]);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep clone given object or array. In case of primitive simply return.\n *\n * @param a - Anything.\n * @returns Deep cloned object/array or unchanged a.\n */\nfunction clone(a: any): any {\n  if (Array.isArray(a)) {\n    return a.map((value: any): any => clone(value));\n  } else if (typeof a === \"object\" && a !== null) {\n    if (a instanceof Date) {\n      return new Date(a.getTime());\n    }\n    return deepObjectAssignNonentry({}, a);\n  } else {\n    return a;\n  }\n}\n\n/**\n * Strip DELETE from given object.\n *\n * @param a - Object which may contain DELETE but won't after this is executed.\n */\nfunction stripDelete(a: any): void {\n  for (const prop of Object.keys(a)) {\n    if (a[prop] === DELETE) {\n      delete a[prop];\n    } else if (typeof a[prop] === \"object\" && a[prop] !== null) {\n      stripDelete(a[prop]);\n    }\n  }\n}\n", "/**\n * Seedable, fast and reasonably good (not crypto but more than okay for our\n * needs) random number generator.\n *\n * @remarks\n * Adapted from {@link https://web.archive.org/web/20110429100736/http://baagoe.com:80/en/RandomMusings/javascript}.\n * Original algorithm created by <PERSON> \\<baagoe\\@baagoe.com\\> in 2010.\n */\n\n/**\n * Random number generator.\n */\nexport interface RNG {\n  /** Returns \\<0, 1). Faster than [[fract53]]. */\n  (): number;\n  /** Returns \\<0, 1). Provides more precise data. */\n  fract53(): number;\n  /** Returns \\<0, 2^32). */\n  uint32(): number;\n\n  /** The algorithm gehind this instance. */\n  algorithm: string;\n  /** The seed used to seed this instance. */\n  seed: Mashable[];\n  /** The version of this instance. */\n  version: string;\n}\n\n/**\n * Create a seeded pseudo random generator based on <PERSON><PERSON> by <PERSON>.\n *\n * @param seed - All supplied arguments will be used as a seed. In case nothing\n * is supplied the current time will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nexport function Alea(...seed: Mashable[]): RNG {\n  return AleaImplementation(seed.length ? seed : [Date.now()]);\n}\n\n/**\n * An implementation of [[Alea]] without user input validation.\n *\n * @param seed - The data that will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nfunction AleaImplementation(seed: Mashable[]): RNG {\n  let [s0, s1, s2] = mashSeed(seed);\n  let c = 1;\n\n  const random: RNG = (): number => {\n    const t = 2091639 * s0 + c * 2.3283064365386963e-10; // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return (s2 = t - (c = t | 0));\n  };\n\n  random.uint32 = (): number => random() * 0x100000000; // 2^32\n\n  random.fract53 = (): number =>\n    random() + ((random() * 0x200000) | 0) * 1.1102230246251565e-16; // 2^-53\n\n  random.algorithm = \"Alea\";\n  random.seed = seed;\n  random.version = \"0.9\";\n\n  return random;\n}\n\n/**\n * Turn arbitrary data into values [[AleaImplementation]] can use to generate\n * random numbers.\n *\n * @param seed - Arbitrary data that will be used as the seed.\n * @returns Three numbers to use as initial values for [[AleaImplementation]].\n */\nfunction mashSeed(...seed: Mashable[]): [number, number, number] {\n  const mash = Mash();\n\n  let s0 = mash(\" \");\n  let s1 = mash(\" \");\n  let s2 = mash(\" \");\n\n  for (let i = 0; i < seed.length; i++) {\n    s0 -= mash(seed[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seed[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seed[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n\n  return [s0, s1, s2];\n}\n\n/**\n * Values of these types can be used as a seed.\n */\nexport type Mashable = number | string | boolean | object | bigint;\n\n/**\n * Create a new mash function.\n *\n * @returns A nonpure function that takes arbitrary [[Mashable]] data and turns\n * them into numbers.\n */\nfunction Mash(): (data: Mashable) => number {\n  let n = 0xefc8249d;\n\n  return function (data): number {\n    const string = data.toString();\n    for (let i = 0; i < string.length; i++) {\n      n += string.charCodeAt(i);\n      let h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n}\n", "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n *\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n *\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n *\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n *\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", "// utility functions\n\n// parse ASP.Net Date pattern,\n// for example '/Date(1198908717056)/' or '/Date(1198908717056-0700)/'\n// code from http://momentjs.com/\nconst ASPDateRegex = /^\\/?Date\\((-?\\d+)/i;\n\n// Color REs\nconst fullHexRE = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\nconst shortHexRE = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\nconst rgbRE =\n  /^rgb\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *\\)$/i;\nconst rgbaRE =\n  /^rgba\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *([01]|0?\\.\\d+) *\\)$/i;\n\n/**\n * Hue, Saturation, Value.\n */\nexport interface HSV {\n  /**\n   * Hue \\<0, 1\\>.\n   */\n  h: number;\n  /**\n   * Saturation \\<0, 1\\>.\n   */\n  s: number;\n  /**\n   * Value \\<0, 1\\>.\n   */\n  v: number;\n}\n\n/**\n * Red, Green, Blue.\n */\nexport interface RGB {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n}\n\n/**\n * Red, Green, Blue, Alpha.\n */\nexport interface RGBA {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n  /**\n   * Alpha \\<0, 1\\>.\n   */\n  a: number;\n}\n\n/**\n * Test whether given object is a number.\n *\n * @param value - Input value of unknown type.\n * @returns True if number, false otherwise.\n */\nexport function isNumber(value: unknown): value is number {\n  return value instanceof Number || typeof value === \"number\";\n}\n\n/**\n * Remove everything in the DOM object.\n *\n * @param DOMobject - Node whose child nodes will be recursively deleted.\n */\nexport function recursiveDOMDelete(DOMobject: Node | null | undefined): void {\n  if (DOMobject) {\n    while (DOMobject.hasChildNodes() === true) {\n      const child = DOMobject.firstChild;\n      if (child) {\n        recursiveDOMDelete(child);\n        DOMobject.removeChild(child);\n      }\n    }\n  }\n}\n\n/**\n * Test whether given object is a string.\n *\n * @param value - Input value of unknown type.\n * @returns True if string, false otherwise.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === \"string\";\n}\n\n/**\n * Test whether given object is a object (not primitive or null).\n *\n * @param value - Input value of unknown type.\n * @returns True if not null object, false otherwise.\n */\nexport function isObject(value: unknown): value is object {\n  return typeof value === \"object\" && value !== null;\n}\n\n/**\n * Test whether given object is a Date, or a String containing a Date.\n *\n * @param value - Input value of unknown type.\n * @returns True if Date instance or string date representation, false otherwise.\n */\nexport function isDate(value: unknown): value is Date | string {\n  if (value instanceof Date) {\n    return true;\n  } else if (isString(value)) {\n    // test whether this string contains a date\n    const match = ASPDateRegex.exec(value);\n    if (match) {\n      return true;\n    } else if (!isNaN(Date.parse(value))) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Copy property from b to a if property present in a.\n * If property in b explicitly set to null, delete it if `allowDeletion` set.\n *\n * Internal helper routine, should not be exported. Not added to `exports` for that reason.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param prop - Name of property to copy from b to a.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n */\nfunction copyOrDelete(\n  a: any,\n  b: any,\n  prop: string,\n  allowDeletion: boolean\n): void {\n  let doDeletion = false;\n  if (allowDeletion === true) {\n    doDeletion = b[prop] === null && a[prop] !== undefined;\n  }\n\n  if (doDeletion) {\n    delete a[prop];\n  } else {\n    a[prop] = b[prop]; // Remember, this is a reference copy!\n  }\n}\n\n/**\n * Fill an object with a possibly partially defined other object.\n *\n * Only copies values for the properties already present in a.\n * That means an object is not created on a property if only the b object has it.\n *\n * @param a - The object that will have it's properties updated.\n * @param b - The object with property updates.\n * @param allowDeletion - If true, delete properties in a that are explicitly set to null in b.\n */\nexport function fillIfDefined<T extends object>(\n  a: T,\n  b: Partial<T>,\n  allowDeletion = false\n): void {\n  // NOTE: iteration of properties of a\n  // NOTE: prototype properties iterated over as well\n  for (const prop in a) {\n    if (b[prop] !== undefined) {\n      if (b[prop] === null || typeof b[prop] !== \"object\") {\n        // Note: typeof null === 'object'\n        copyOrDelete(a, b, prop, allowDeletion);\n      } else {\n        const aProp = a[prop];\n        const bProp = b[prop];\n        if (isObject(aProp) && isObject(bProp)) {\n          fillIfDefined(aProp, bProp, allowDeletion);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Copy the values of all of the enumerable own properties from one or more source objects to a\n * target object. Returns the target object.\n *\n * @param target - The target object to copy to.\n * @param source - The source object from which to copy properties.\n * @returns The target object.\n */\nexport const extend = Object.assign;\n\n/**\n * Extend object a with selected properties of object b or a series of objects.\n *\n * @remarks\n * Only properties with defined values are copied.\n * @param props - Properties to be copied to a.\n * @param a - The target.\n * @param others - The sources.\n * @returns Argument a.\n */\nexport function selectiveExtend(\n  props: string[],\n  a: any,\n  ...others: any[]\n): any {\n  if (!Array.isArray(props)) {\n    throw new Error(\"Array with property names expected as first argument\");\n  }\n\n  for (const other of others) {\n    for (let p = 0; p < props.length; p++) {\n      const prop = props[p];\n      if (other && Object.prototype.hasOwnProperty.call(other, prop)) {\n        a[prop] = other[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object a with selected properties of object b.\n * Only properties with defined values are copied.\n *\n * @remarks\n * Previous version of this routine implied that multiple source objects could\n * be used; however, the implementation was **wrong**. Since multiple (\\>1)\n * sources weren't used anywhere in the `vis.js` code, this has been removed\n * @param props - Names of first-level properties to copy over.\n * @param a - Target object.\n * @param b - Source object.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n * @returns Argument a.\n */\nexport function selectiveDeepExtend(\n  props: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (let p = 0; p < props.length; p++) {\n    const prop = props[p];\n    if (Object.prototype.hasOwnProperty.call(b, prop)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop], false, allowDeletion);\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError(\"Arrays are not supported by deepExtend\");\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object `a` with properties of object `b`, ignoring properties which\n * are explicitly specified to be excluded.\n *\n * @remarks\n * The properties of `b` are considered for copying. Properties which are\n * themselves objects are are also extended. Only properties with defined\n * values are copied.\n * @param propsToExclude - Names of properties which should *not* be copied.\n * @param a - Object to extend.\n * @param b - Object to take properties from for extension.\n * @param allowDeletion - If true, delete properties in a that are explicitly\n * set to null in b.\n * @returns Argument a.\n */\nexport function selectiveNotDeepExtend(\n  propsToExclude: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  // NOTE: array properties have an else-below; apparently, there is a problem here.\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (const prop in b) {\n    if (!Object.prototype.hasOwnProperty.call(b, prop)) {\n      continue;\n    } // Handle local properties only\n    if (propsToExclude.includes(prop)) {\n      continue;\n    } // In exclusion list, skip\n\n    if (b[prop] && b[prop].constructor === Object) {\n      if (a[prop] === undefined) {\n        a[prop] = {};\n      }\n      if (a[prop].constructor === Object) {\n        deepExtend(a[prop], b[prop]); // NOTE: allowDeletion not propagated!\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    } else if (Array.isArray(b[prop])) {\n      a[prop] = [];\n      for (let i = 0; i < b[prop].length; i++) {\n        a[prop].push(b[prop][i]);\n      }\n    } else {\n      copyOrDelete(a, b, prop, allowDeletion);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param protoExtend - If true, the prototype values will also be extended.\n * (That is the options objects that inherit from others will also get the\n * inherited options).\n * @param allowDeletion - If true, the values of fields that are null will be deleted.\n * @returns Argument a.\n */\nexport function deepExtend(\n  a: any,\n  b: any,\n  protoExtend = false,\n  allowDeletion = false\n): any {\n  for (const prop in b) {\n    if (Object.prototype.hasOwnProperty.call(b, prop) || protoExtend === true) {\n      if (\n        typeof b[prop] === \"object\" &&\n        b[prop] !== null &&\n        Object.getPrototypeOf(b[prop]) === Object.prototype\n      ) {\n        if (a[prop] === undefined) {\n          a[prop] = deepExtend({}, b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else if (\n          typeof a[prop] === \"object\" &&\n          a[prop] !== null &&\n          Object.getPrototypeOf(a[prop]) === Object.prototype\n        ) {\n          deepExtend(a[prop], b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        a[prop] = b[prop].slice();\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Test whether all elements in two arrays are equal.\n *\n * @param a - First array.\n * @param b - Second array.\n * @returns True if both arrays have the same length and same elements (1 = '1').\n */\nexport function equalArray(a: unknown[], b: unknown[]): boolean {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0, len = a.length; i < len; i++) {\n    if (a[i] != b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Get the type of an object, for example exports.getType([]) returns 'Array'.\n *\n * @param object - Input value of unknown type.\n * @returns Detected type.\n */\nexport function getType(object: unknown): string {\n  const type = typeof object;\n\n  if (type === \"object\") {\n    if (object === null) {\n      return \"null\";\n    }\n    if (object instanceof Boolean) {\n      return \"Boolean\";\n    }\n    if (object instanceof Number) {\n      return \"Number\";\n    }\n    if (object instanceof String) {\n      return \"String\";\n    }\n    if (Array.isArray(object)) {\n      return \"Array\";\n    }\n    if (object instanceof Date) {\n      return \"Date\";\n    }\n\n    return \"Object\";\n  }\n  if (type === \"number\") {\n    return \"Number\";\n  }\n  if (type === \"boolean\") {\n    return \"Boolean\";\n  }\n  if (type === \"string\") {\n    return \"String\";\n  }\n  if (type === undefined) {\n    return \"undefined\";\n  }\n\n  return type;\n}\n\nexport function copyAndExtendArray<T>(arr: ReadonlyArray<T>, newValue: T): T[];\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[];\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - First part.\n * @param newValue - The value to be aadded into the array.\n * @returns A new array with all items from arr and newValue (which is last).\n */\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[] {\n  return [...arr, newValue];\n}\n\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - The array to be copied.\n * @returns Shallow copy of arr.\n */\nexport function copyArray<T>(arr: ReadonlyArray<T>): T[] {\n  return arr.slice();\n}\n\n/**\n * Retrieve the absolute left value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute left position of this element in the browser page.\n */\nexport function getAbsoluteLeft(elem: Element): number {\n  return elem.getBoundingClientRect().left;\n}\n\n/**\n * Retrieve the absolute right value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute right position of this element in the browser page.\n */\nexport function getAbsoluteRight(elem: Element): number {\n  return elem.getBoundingClientRect().right;\n}\n\n/**\n * Retrieve the absolute top value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute top position of this element in the browser page.\n */\nexport function getAbsoluteTop(elem: Element): number {\n  return elem.getBoundingClientRect().top;\n}\n\n/**\n * Add a className to the given elements style.\n *\n * @param elem - The element to which the classes will be added.\n * @param classNames - Space separated list of classes.\n */\nexport function addClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const newClasses = classNames.split(\" \");\n  classes = classes.concat(\n    newClasses.filter(function (className): boolean {\n      return !classes.includes(className);\n    })\n  );\n  elem.className = classes.join(\" \");\n}\n\n/**\n * Remove a className from the given elements style.\n *\n * @param elem - The element from which the classes will be removed.\n * @param classNames - Space separated list of classes.\n */\nexport function removeClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const oldClasses = classNames.split(\" \");\n  classes = classes.filter(function (className): boolean {\n    return !oldClasses.includes(className);\n  });\n  elem.className = classes.join(\" \");\n}\n\nexport function forEach<V>(\n  array: undefined | null | V[],\n  callback: (value: V, index: number, object: V[]) => void\n): void;\nexport function forEach<O extends object>(\n  object: undefined | null | O,\n  callback: <Key extends keyof O>(value: O[Key], key: Key, object: O) => void\n): void;\n/**\n * For each method for both arrays and objects.\n * In case of an array, the built-in Array.forEach() is applied (**No, it's not!**).\n * In case of an Object, the method loops over all properties of the object.\n *\n * @param object - An Object or Array to be iterated over.\n * @param callback - Array.forEach-like callback.\n */\nexport function forEach(object: any, callback: any): void {\n  if (Array.isArray(object)) {\n    // array\n    const len = object.length;\n    for (let i = 0; i < len; i++) {\n      callback(object[i], i, object);\n    }\n  } else {\n    // object\n    for (const key in object) {\n      if (Object.prototype.hasOwnProperty.call(object, key)) {\n        callback(object[key], key, object);\n      }\n    }\n  }\n}\n\n/**\n * Convert an object into an array: all objects properties are put into the array. The resulting array is unordered.\n *\n * @param o - Object that contains the properties and methods.\n * @returns An array of unordered values.\n */\nexport const toArray = Object.values;\n\n/**\n * Update a property in an object.\n *\n * @param object - The object whose property will be updated.\n * @param key - Name of the property to be updated.\n * @param value - The new value to be assigned.\n * @returns Whether the value was updated (true) or already strictly the same in the original object (false).\n */\nexport function updateProperty<K extends string, V>(\n  object: Record<K, V>,\n  key: K,\n  value: V\n): boolean {\n  if (object[key] !== value) {\n    object[key] = value;\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Throttle the given function to be only executed once per animation frame.\n *\n * @param fn - The original function.\n * @returns The throttled function.\n */\nexport function throttle(fn: () => void): () => void {\n  let scheduled = false;\n\n  return (): void => {\n    if (!scheduled) {\n      scheduled = true;\n      requestAnimationFrame((): void => {\n        scheduled = false;\n        fn();\n      });\n    }\n  };\n}\n\n/**\n * Cancels the event's default action if it is cancelable, without stopping further propagation of the event.\n *\n * @param event - The event whose default action should be prevented.\n */\nexport function preventDefault(event: Event | undefined): void {\n  if (!event) {\n    event = window.event;\n  }\n\n  if (!event) {\n    // No event, no work.\n  } else if (event.preventDefault) {\n    event.preventDefault(); // non-IE browsers\n  } else {\n    // @TODO: IE types? Does anyone care?\n    (event as any).returnValue = false; // IE browsers\n  }\n}\n\n/**\n * Get HTML element which is the target of the event.\n *\n * @param event - The event.\n * @returns The element or null if not obtainable.\n */\nexport function getTarget(\n  event: Event | undefined = window.event\n): Element | null {\n  // code from http://www.quirksmode.org/js/events_properties.html\n  // @TODO: EventTarget can be almost anything, is it okay to return only Elements?\n\n  let target: null | EventTarget = null;\n  if (!event) {\n    // No event, no target.\n  } else if (event.target) {\n    target = event.target;\n  } else if (event.srcElement) {\n    target = event.srcElement;\n  }\n\n  if (!(target instanceof Element)) {\n    return null;\n  }\n\n  if (target.nodeType != null && target.nodeType == 3) {\n    // defeat Safari bug\n    target = target.parentNode;\n    if (!(target instanceof Element)) {\n      return null;\n    }\n  }\n\n  return target;\n}\n\n/**\n * Check if given element contains given parent somewhere in the DOM tree.\n *\n * @param element - The element to be tested.\n * @param parent - The ancestor (not necessarily parent) of the element.\n * @returns True if parent is an ancestor of the element, false otherwise.\n */\nexport function hasParent(element: Element, parent: Element): boolean {\n  let elem: Node = element;\n\n  while (elem) {\n    if (elem === parent) {\n      return true;\n    } else if (elem.parentNode) {\n      elem = elem.parentNode;\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n\nexport const option = {\n  /**\n   * Convert a value into a boolean.\n   *\n   * @param value - Value to be converted intoboolean, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding boolean value, if none then the default value, if none then null.\n   */\n  asBoolean(value: unknown, defaultValue?: boolean): boolean | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return value != false;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a number.\n   *\n   * @param value - Value to be converted intonumber, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** number value, if none then the default value, if none then null.\n   */\n  asNumber(value: unknown, defaultValue?: number): number | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return Number(value) || defaultValue || null;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a string.\n   *\n   * @param value - Value to be converted intostring, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** string value, if none then the default value, if none then null.\n   */\n  asString(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return String(value);\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a size.\n   *\n   * @param value - Value to be converted intosize, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding string value (number + 'px'), if none then the default value, if none then null.\n   */\n  asSize(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (isString(value)) {\n      return value;\n    } else if (isNumber(value)) {\n      return value + \"px\";\n    } else {\n      return defaultValue || null;\n    }\n  },\n\n  /**\n   * Convert a value into a DOM Element.\n   *\n   * @param value - Value to be converted into DOM Element, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns The DOM Element, if none then the default value, if none then null.\n   */\n  asElement<T extends Node>(\n    value: T | (() => T | undefined) | undefined,\n    defaultValue: T\n  ): T | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    return value || defaultValue || null;\n  },\n};\n\n/**\n * Convert hex color string into RGB color object.\n *\n * @remarks\n * {@link http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb}\n * @param hex - Hex color string (3 or 6 digits, with or without #).\n * @returns RGB color object.\n */\nexport function hexToRGB(hex: string): RGB | null {\n  let result;\n  switch (hex.length) {\n    case 3:\n    case 4:\n      result = shortHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1] + result[1], 16),\n            g: parseInt(result[2] + result[2], 16),\n            b: parseInt(result[3] + result[3], 16),\n          }\n        : null;\n    case 6:\n    case 7:\n      result = fullHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16),\n          }\n        : null;\n    default:\n      return null;\n  }\n}\n\n/**\n * This function takes string color in hex or RGB format and adds the opacity, RGBA is passed through unchanged.\n *\n * @param color - The color string (hex, RGB, RGBA).\n * @param opacity - The new opacity.\n * @returns RGBA string, for example 'rgba(255, 0, 127, 0.3)'.\n */\nexport function overrideOpacity(color: string, opacity: number): string {\n  if (color.includes(\"rgba\")) {\n    return color;\n  } else if (color.includes(\"rgb\")) {\n    const rgb = color\n      .substr(color.indexOf(\"(\") + 1)\n      .replace(\")\", \"\")\n      .split(\",\");\n    return \"rgba(\" + rgb[0] + \",\" + rgb[1] + \",\" + rgb[2] + \",\" + opacity + \")\";\n  } else {\n    const rgb = hexToRGB(color);\n    if (rgb == null) {\n      return color;\n    } else {\n      return \"rgba(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \",\" + opacity + \")\";\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into hex color string.\n *\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns Hex color string (for example: '#0acdc0').\n */\nexport function RGBToHex(red: number, green: number, blue: number): string {\n  return (\n    \"#\" + ((1 << 24) + (red << 16) + (green << 8) + blue).toString(16).slice(1)\n  );\n}\n\nexport interface ColorObject {\n  background?: string;\n  border?: string;\n  hover?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n  highlight?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n}\nexport interface FullColorObject {\n  background: string;\n  border: string;\n  hover: {\n    border: string;\n    background: string;\n  };\n  highlight: {\n    border: string;\n    background: string;\n  };\n}\n\nexport function parseColor(inputColor: string): FullColorObject;\nexport function parseColor(inputColor: FullColorObject): FullColorObject;\nexport function parseColor(inputColor: ColorObject): ColorObject;\nexport function parseColor(\n  inputColor: ColorObject,\n  defaultColor: FullColorObject\n): FullColorObject;\n/**\n * Parse a color property into an object with border, background, and highlight colors.\n *\n * @param inputColor - Shorthand color string or input color object.\n * @param defaultColor - Full color object to fill in missing values in inputColor.\n * @returns Color object.\n */\nexport function parseColor(\n  inputColor: ColorObject | string,\n  defaultColor?: FullColorObject\n): ColorObject | FullColorObject {\n  if (isString(inputColor)) {\n    let colorStr: string = inputColor;\n    if (isValidRGB(colorStr)) {\n      const rgb = colorStr\n        .substr(4)\n        .substr(0, colorStr.length - 5)\n        .split(\",\")\n        .map(function (value): number {\n          return parseInt(value);\n        });\n      colorStr = RGBToHex(rgb[0], rgb[1], rgb[2]);\n    }\n    if (isValidHex(colorStr) === true) {\n      const hsv = hexToHSV(colorStr);\n      const lighterColorHSV = {\n        h: hsv.h,\n        s: hsv.s * 0.8,\n        v: Math.min(1, hsv.v * 1.02),\n      };\n      const darkerColorHSV = {\n        h: hsv.h,\n        s: Math.min(1, hsv.s * 1.25),\n        v: hsv.v * 0.8,\n      };\n      const darkerColorHex = HSVToHex(\n        darkerColorHSV.h,\n        darkerColorHSV.s,\n        darkerColorHSV.v\n      );\n      const lighterColorHex = HSVToHex(\n        lighterColorHSV.h,\n        lighterColorHSV.s,\n        lighterColorHSV.v\n      );\n      return {\n        background: colorStr,\n        border: darkerColorHex,\n        highlight: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n        hover: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n      };\n    } else {\n      return {\n        background: colorStr,\n        border: colorStr,\n        highlight: {\n          background: colorStr,\n          border: colorStr,\n        },\n        hover: {\n          background: colorStr,\n          border: colorStr,\n        },\n      };\n    }\n  } else {\n    if (defaultColor) {\n      const color: FullColorObject = {\n        background: inputColor.background || defaultColor.background,\n        border: inputColor.border || defaultColor.border,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                defaultColor.highlight.background,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                defaultColor.highlight.border,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) ||\n                defaultColor.hover.border,\n              background:\n                (inputColor.hover && inputColor.hover.background) ||\n                defaultColor.hover.background,\n            },\n      };\n      return color;\n    } else {\n      const color: ColorObject = {\n        background: inputColor.background || undefined,\n        border: inputColor.border || undefined,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                undefined,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                undefined,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) || undefined,\n              background:\n                (inputColor.hover && inputColor.hover.background) || undefined,\n            },\n      };\n      return color;\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into HSV object.\n *\n * @remarks\n * {@link http://www.javascripter.net/faq/rgb2hsv.htm}\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns HSV color object.\n */\nexport function RGBToHSV(red: number, green: number, blue: number): HSV {\n  red = red / 255;\n  green = green / 255;\n  blue = blue / 255;\n  const minRGB = Math.min(red, Math.min(green, blue));\n  const maxRGB = Math.max(red, Math.max(green, blue));\n\n  // Black-gray-white\n  if (minRGB === maxRGB) {\n    return { h: 0, s: 0, v: minRGB };\n  }\n\n  // Colors other than black-gray-white:\n  const d =\n    red === minRGB ? green - blue : blue === minRGB ? red - green : blue - red;\n  const h = red === minRGB ? 3 : blue === minRGB ? 1 : 5;\n  const hue = (60 * (h - d / (maxRGB - minRGB))) / 360;\n  const saturation = (maxRGB - minRGB) / maxRGB;\n  const value = maxRGB;\n  return { h: hue, s: saturation, v: value };\n}\n\ninterface CSSStyles {\n  [key: string]: string;\n}\n\n/**\n * Split a string with css styles into an object with key/values.\n *\n * @param cssText - CSS source code to split into key/value object.\n * @returns Key/value object corresponding to {@link cssText}.\n */\nfunction splitCSSText(cssText: string): CSSStyles {\n  const tmpEllement = document.createElement(\"div\");\n\n  const styles: CSSStyles = {};\n\n  tmpEllement.style.cssText = cssText;\n\n  for (let i = 0; i < tmpEllement.style.length; ++i) {\n    styles[tmpEllement.style[i]] = tmpEllement.style.getPropertyValue(\n      tmpEllement.style[i]\n    );\n  }\n\n  return styles;\n}\n\n/**\n * Append a string with css styles to an element.\n *\n * @param element - The element that will receive new styles.\n * @param cssText - The styles to be appended.\n */\nexport function addCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const [key, value] of Object.entries(cssStyle)) {\n    element.style.setProperty(key, value);\n  }\n}\n\n/**\n * Remove a string with css styles from an element.\n *\n * @param element - The element from which styles should be removed.\n * @param cssText - The styles to be removed.\n */\nexport function removeCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const key of Object.keys(cssStyle)) {\n    element.style.removeProperty(key);\n  }\n}\n\n/**\n * Convert HSV \\<0, 1\\> into RGB color object.\n *\n * @remarks\n * {@link https://gist.github.com/mjijackson/5311256}\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns RGB color object.\n */\nexport function HSVToRGB(h: number, s: number, v: number): RGB {\n  let r: undefined | number;\n  let g: undefined | number;\n  let b: undefined | number;\n\n  const i = Math.floor(h * 6);\n  const f = h * 6 - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n\n  switch (i % 6) {\n    case 0:\n      (r = v), (g = t), (b = p);\n      break;\n    case 1:\n      (r = q), (g = v), (b = p);\n      break;\n    case 2:\n      (r = p), (g = v), (b = t);\n      break;\n    case 3:\n      (r = p), (g = q), (b = v);\n      break;\n    case 4:\n      (r = t), (g = p), (b = v);\n      break;\n    case 5:\n      (r = v), (g = p), (b = q);\n      break;\n  }\n\n  return {\n    r: Math.floor((r as number) * 255),\n    g: Math.floor((g as number) * 255),\n    b: Math.floor((b as number) * 255),\n  };\n}\n\n/**\n * Convert HSV \\<0, 1\\> into hex color string.\n *\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns Hex color string.\n */\nexport function HSVToHex(h: number, s: number, v: number): string {\n  const rgb = HSVToRGB(h, s, v);\n  return RGBToHex(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Convert hex color string into HSV \\<0, 1\\>.\n *\n * @param hex - Hex color string.\n * @returns HSV color object.\n */\nexport function hexToHSV(hex: string): HSV {\n  const rgb = hexToRGB(hex);\n  if (!rgb) {\n    throw new TypeError(`'${hex}' is not a valid color.`);\n  }\n  return RGBToHSV(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Validate hex color string.\n *\n * @param hex - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidHex(hex: string): boolean {\n  const isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(hex);\n  return isOk;\n}\n\n/**\n * Validate RGB color string.\n *\n * @param rgb - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGB(rgb: string): boolean {\n  return rgbRE.test(rgb);\n}\n\n/**\n * Validate RGBA color string.\n *\n * @param rgba - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGBA(rgba: string): boolean {\n  return rgbaRE.test(rgba);\n}\n\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param fields - Names of properties to be bridged.\n * @param referenceObject - The original object.\n * @returns A new object inheriting from the referenceObject.\n */\nexport function selectiveBridgeObject<F extends string, V>(\n  fields: F[],\n  referenceObject: Record<F, V>\n): Record<F, V> | null {\n  if (referenceObject !== null && typeof referenceObject === \"object\") {\n    // !!! typeof null === 'object'\n    const objectTo = Object.create(referenceObject);\n    for (let i = 0; i < fields.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(referenceObject, fields[i])) {\n        if (typeof referenceObject[fields[i]] == \"object\") {\n          objectTo[fields[i]] = bridgeObject(referenceObject[fields[i]]);\n        }\n      }\n    }\n    return objectTo;\n  } else {\n    return null;\n  }\n}\n\nexport function bridgeObject<T extends object>(referenceObject: T): T;\nexport function bridgeObject<T>(referenceObject: T): null;\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param referenceObject - The original object.\n * @returns The Element if the referenceObject is an Element, or a new object inheriting from the referenceObject.\n */\nexport function bridgeObject<T extends object | null>(\n  referenceObject: T\n): T | null {\n  if (referenceObject === null || typeof referenceObject !== \"object\") {\n    return null;\n  }\n\n  if (referenceObject instanceof Element) {\n    // Avoid bridging DOM objects\n    return referenceObject;\n  }\n\n  const objectTo = Object.create(referenceObject);\n  for (const i in referenceObject) {\n    if (Object.prototype.hasOwnProperty.call(referenceObject, i)) {\n      if (typeof (referenceObject as any)[i] == \"object\") {\n        objectTo[i] = bridgeObject((referenceObject as any)[i]);\n      }\n    }\n  }\n\n  return objectTo;\n}\n\n/**\n * This method provides a stable sort implementation, very fast for presorted data.\n *\n * @param a - The array to be sorted (in-place).\n * @param compare - An order comparator.\n * @returns The argument a.\n */\nexport function insertSort<T>(a: T[], compare: (a: T, b: T) => number): T[] {\n  for (let i = 0; i < a.length; i++) {\n    const k = a[i];\n    let j;\n    for (j = i; j > 0 && compare(k, a[j - 1]) < 0; j--) {\n      a[j] = a[j - 1];\n    }\n    a[j] = k;\n  }\n  return a;\n}\n\n/**\n * This is used to set the options of subobjects in the options object.\n *\n * A requirement of these subobjects is that they have an 'enabled' element\n * which is optional for the user but mandatory for the program.\n *\n * The added value here of the merge is that option 'enabled' is set as required.\n *\n * @param mergeTarget - Either this.options or the options used for the groups.\n * @param options - Options.\n * @param option - Option key in the options argument.\n * @param globalOptions - Global options, passed in to determine value of option 'enabled'.\n */\nexport function mergeOptions(\n  mergeTarget: any,\n  options: any,\n  option: string,\n  globalOptions: any = {}\n): void {\n  // Local helpers\n  const isPresent = function (obj: any): boolean {\n    return obj !== null && obj !== undefined;\n  };\n\n  const isObject = function (obj: unknown): boolean {\n    return obj !== null && typeof obj === \"object\";\n  };\n\n  // https://stackoverflow.com/a/34491287/1223531\n  const isEmpty = function (obj: object): obj is {} {\n    for (const x in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, x)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  // Guards\n  if (!isObject(mergeTarget)) {\n    throw new Error(\"Parameter mergeTarget must be an object\");\n  }\n\n  if (!isObject(options)) {\n    throw new Error(\"Parameter options must be an object\");\n  }\n\n  if (!isPresent(option)) {\n    throw new Error(\"Parameter option must have a value\");\n  }\n\n  if (!isObject(globalOptions)) {\n    throw new Error(\"Parameter globalOptions must be an object\");\n  }\n\n  //\n  // Actual merge routine, separated from main logic\n  // Only a single level of options is merged. Deeper levels are ref'd. This may actually be an issue.\n  //\n  const doMerge = function (target: any, options: any, option: string): void {\n    if (!isObject(target[option])) {\n      target[option] = {};\n    }\n\n    const src = options[option];\n    const dst = target[option];\n    for (const prop in src) {\n      if (Object.prototype.hasOwnProperty.call(src, prop)) {\n        dst[prop] = src[prop];\n      }\n    }\n  };\n\n  // Local initialization\n  const srcOption = options[option];\n  const globalPassed = isObject(globalOptions) && !isEmpty(globalOptions);\n  const globalOption = globalPassed ? globalOptions[option] : undefined;\n  const globalEnabled = globalOption ? globalOption.enabled : undefined;\n\n  /////////////////////////////////////////\n  // Main routine\n  /////////////////////////////////////////\n  if (srcOption === undefined) {\n    return; // Nothing to do\n  }\n\n  if (typeof srcOption === \"boolean\") {\n    if (!isObject(mergeTarget[option])) {\n      mergeTarget[option] = {};\n    }\n\n    mergeTarget[option].enabled = srcOption;\n    return;\n  }\n\n  if (srcOption === null && !isObject(mergeTarget[option])) {\n    // If possible, explicit copy from globals\n    if (isPresent(globalOption)) {\n      mergeTarget[option] = Object.create(globalOption);\n    } else {\n      return; // Nothing to do\n    }\n  }\n\n  if (!isObject(srcOption)) {\n    return;\n  }\n\n  //\n  // Ensure that 'enabled' is properly set. It is required internally\n  // Note that the value from options will always overwrite the existing value\n  //\n  let enabled = true; // default value\n\n  if (srcOption.enabled !== undefined) {\n    enabled = srcOption.enabled;\n  } else {\n    // Take from globals, if present\n    if (globalEnabled !== undefined) {\n      enabled = globalOption.enabled;\n    }\n  }\n\n  doMerge(mergeTarget, options, option);\n  mergeTarget[option].enabled = enabled;\n}\n\nexport function binarySearchCustom<\n  O extends object,\n  K1 extends keyof O,\n  K2 extends keyof O[K1]\n>(\n  orderedItems: O[],\n  comparator: (v: O[K1][K2]) => -1 | 0 | 1,\n  field: K1,\n  field2: K2\n): number;\nexport function binarySearchCustom<O extends object, K1 extends keyof O>(\n  orderedItems: O[],\n  comparator: (v: O[K1]) => -1 | 0 | 1,\n  field: K1\n): number;\n/**\n * This function does a binary search for a visible item in a sorted list. If we find a visible item, the code that uses\n * this function will then iterate in both directions over this sorted list to find all visible items.\n *\n * @param orderedItems - Items ordered by start.\n * @param comparator - -1 is lower, 0 is equal, 1 is higher.\n * @param field - Property name on an item (That is item[field]).\n * @param field2 - Second property name on an item (That is item[field][field2]).\n * @returns Index of the found item or -1 if nothing was found.\n */\nexport function binarySearchCustom(\n  orderedItems: any[],\n  comparator: (v: unknown) => -1 | 0 | 1,\n  field: string,\n  field2?: string\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n\n  while (low <= high && iteration < maxIterations) {\n    const middle = Math.floor((low + high) / 2);\n\n    const item = orderedItems[middle];\n    const value = field2 === undefined ? item[field] : item[field][field2];\n\n    const searchResult = comparator(value);\n    if (searchResult == 0) {\n      // jihaa, found a visible item!\n      return middle;\n    } else if (searchResult == -1) {\n      // it is too small --> increase low\n      low = middle + 1;\n    } else {\n      // it is too big --> decrease high\n      high = middle - 1;\n    }\n\n    iteration++;\n  }\n\n  return -1;\n}\n\n/**\n * This function does a binary search for a specific value in a sorted array.\n * If it does not exist but is in between of two values, we return either the\n * one before or the one after, depending on user input If it is found, we\n * return the index, else -1.\n *\n * @param orderedItems - Sorted array.\n * @param target - The searched value.\n * @param field - Name of the property in items to be searched.\n * @param sidePreference - If the target is between two values, should the index of the before or the after be returned?\n * @param comparator - An optional comparator, returning -1, 0, 1 for \\<, ===, \\>.\n * @returns The index of found value or -1 if nothing was found.\n */\nexport function binarySearchValue<T extends string>(\n  orderedItems: { [K in T]: number }[],\n  target: number,\n  field: T,\n  sidePreference: \"before\" | \"after\",\n  comparator?: (a: number, b: number) => -1 | 0 | 1\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n  let prevValue;\n  let value;\n  let nextValue;\n  let middle;\n\n  comparator =\n    comparator != undefined\n      ? comparator\n      : function (a: number, b: number): -1 | 0 | 1 {\n          return a == b ? 0 : a < b ? -1 : 1;\n        };\n\n  while (low <= high && iteration < maxIterations) {\n    // get a new guess\n    middle = Math.floor(0.5 * (high + low));\n    prevValue = orderedItems[Math.max(0, middle - 1)][field];\n    value = orderedItems[middle][field];\n    nextValue =\n      orderedItems[Math.min(orderedItems.length - 1, middle + 1)][field];\n\n    if (comparator(value, target) == 0) {\n      // we found the target\n      return middle;\n    } else if (\n      comparator(prevValue, target) < 0 &&\n      comparator(value, target) > 0\n    ) {\n      // target is in between of the previous and the current\n      return sidePreference == \"before\" ? Math.max(0, middle - 1) : middle;\n    } else if (\n      comparator(value, target) < 0 &&\n      comparator(nextValue, target) > 0\n    ) {\n      // target is in between of the current and the next\n      return sidePreference == \"before\"\n        ? middle\n        : Math.min(orderedItems.length - 1, middle + 1);\n    } else {\n      // didnt find the target, we need to change our boundaries.\n      if (comparator(value, target) < 0) {\n        // it is too small --> increase low\n        low = middle + 1;\n      } else {\n        // it is too big --> decrease high\n        high = middle - 1;\n      }\n    }\n    iteration++;\n  }\n\n  // didnt find anything. Return -1.\n  return -1;\n}\n\n/*\n * Easing Functions.\n * Only considering the t value for the range [0, 1] => [0, 1].\n *\n * Inspiration: from http://gizma.com/easing/\n * https://gist.github.com/gre/1650294\n */\nexport const easingFunctions = {\n  /**\n   * Provides no easing and no acceleration.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  linear(t: number): number {\n    return t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuad(t: number): number {\n    return t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuad(t: number): number {\n    return t * (2 - t);\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuad(t: number): number {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInCubic(t: number): number {\n    return t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutCubic(t: number): number {\n    return --t * t * t + 1;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutCubic(t: number): number {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuart(t: number): number {\n    return t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuart(t: number): number {\n    return 1 - --t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuart(t: number): number {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuint(t: number): number {\n    return t * t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuint(t: number): number {\n    return 1 + --t * t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuint(t: number): number {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n  },\n};\n\n/**\n * Experimentaly compute the width of the scrollbar for this browser.\n *\n * @returns The width in pixels.\n */\nexport function getScrollBarWidth(): number {\n  const inner = document.createElement(\"p\");\n  inner.style.width = \"100%\";\n  inner.style.height = \"200px\";\n\n  const outer = document.createElement(\"div\");\n  outer.style.position = \"absolute\";\n  outer.style.top = \"0px\";\n  outer.style.left = \"0px\";\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"200px\";\n  outer.style.height = \"150px\";\n  outer.style.overflow = \"hidden\";\n  outer.appendChild(inner);\n\n  document.body.appendChild(outer);\n  const w1 = inner.offsetWidth;\n  outer.style.overflow = \"scroll\";\n  let w2 = inner.offsetWidth;\n  if (w1 == w2) {\n    w2 = outer.clientWidth;\n  }\n\n  document.body.removeChild(outer);\n\n  return w1 - w2;\n}\n\n// @TODO: This doesn't work properly.\n// It works only for single property objects,\n// otherwise it combines all of the types in a union.\n// export function topMost<K1 extends string, V1> (\n//   pile: Record<K1, undefined | V1>[],\n//   accessors: K1 | [K1]\n// ): undefined | V1\n// export function topMost<K1 extends string, K2 extends string, V1, V2> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2>>[],\n//   accessors: [K1, K2]\n// ): undefined | V1 | V2\n// export function topMost<K1 extends string, K2 extends string, K3 extends string, V1, V2, V3> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2 | Record<K3, undefined | V3>>>[],\n//   accessors: [K1, K2, K3]\n// ): undefined | V1 | V2 | V3\n/**\n * Get the top most property value from a pile of objects.\n *\n * @param pile - Array of objects, no required format.\n * @param accessors - Array of property names.\n * For example `object['foo']['bar']` → `['foo', 'bar']`.\n * @returns Value of the property with given accessors path from the first pile item where it's not undefined.\n */\nexport function topMost(pile: any, accessors: any): any {\n  let candidate;\n  if (!Array.isArray(accessors)) {\n    accessors = [accessors];\n  }\n  for (const member of pile) {\n    if (member) {\n      candidate = member[accessors[0]];\n      for (let i = 1; i < accessors.length; i++) {\n        if (candidate) {\n          candidate = candidate[accessors[i]];\n        }\n      }\n      if (typeof candidate !== \"undefined\") {\n        break;\n      }\n    }\n  }\n  return candidate;\n}\n", "import { Hammer } from \"./hammer\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio=1]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   *\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   *\n   * @param {string | object} color\n   * @param {boolean} [setInitial=true]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color)\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   *\n   * @param {boolean} [storePrevious=true]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   *\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   *\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   *\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   *\n   * @param {object} rgba\n   * @param {boolean} [setInitial=true]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   *\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   *\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   *\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   *\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   *\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   *\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util\";\n\nimport { ColorPicker } from \"./color-picker\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n *\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   *\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   *\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   *\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   *\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   *\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   *\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   *\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   *\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   *\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   *\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   *\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   *\n   * @param {object} obj\n   * @param {Array} [path=[]]    | where to look for the actual option\n   * @param {boolean} [checkOnly=false]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   *\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2))\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options\n      );\n    }\n    return options;\n  }\n}\n", "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   *\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   *\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". '\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"'\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\"\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive=false]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op)\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   * http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   * Copyright (c) 2011 Andrei Mackenzie\n   *\n   * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   *\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1\n            )\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", "import { Activator as ActivatorJS } from \"./activator\";\nimport { ColorPicker as ColorPickerJS } from \"./color-picker\";\nimport { Configurator as ConfiguratorJS } from \"./configurator\";\nimport { Hammer as HammerJS } from \"./hammer\";\nimport { Popup as PopupJS } from \"./popup\";\nimport { VALIDATOR_PRINT_STYLE as VALIDATOR_PRINT_STYLE_JS } from \"./validator\";\nimport { Validator as ValidatorJS } from \"./validator\";\n\nexport const Activator: any = ActivatorJS;\nexport const ColorPicker: any = ColorPickerJS;\nexport const Configurator: any = ConfiguratorJS;\nexport const Hammer: HammerStatic = HammerJS;\nexport const Popup: any = PopupJS;\nexport const VALIDATOR_PRINT_STYLE: string = VALIDATOR_PRINT_STYLE_JS;\nexport const Validator: any = ValidatorJS;\n\nexport * from \"./configurator-types\";\n"], "names": ["Hammer", "Activator", "ColorPicker", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPickerJS", "ConfiguratorJS", "HammerJS", "PopupJS", "VALIDATOR_PRINT_STYLE_JS", "ValidatorJS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;;EAEG;QACU,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE;EAoBvC;;;;;;EAMG;WACa,oBAAoB,CAClC,IAAO,EACP,GAAG,OAAwB,EAAA;MAE3B,OAAO,gBAAgB,CAAC,EAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC;EACvD,CAAC;EAUD;;;;;EAKG;EACa,SAAA,gBAAgB,CAAC,GAAG,MAAsB,EAAA;EACxD,IAAA,MAAM,MAAM,GAAG,wBAAwB,CAAC,GAAG,MAAM,CAAC,CAAC;MACnD,WAAW,CAAC,MAAM,CAAC,CAAC;EACpB,IAAA,OAAO,MAAM,CAAC;EAChB,CAAC;EAED;;;;;;;EAOG;EACH,SAAS,wBAAwB,CAAC,GAAG,MAAsB,EAAA;EACzD,IAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;EACrB,QAAA,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;EAClB,KAAA;EAAM,SAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;UAC5B,OAAO,wBAAwB,CAC7B,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EACtC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CACnB,CAAC;EACH,KAAA;EAED,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EACpB,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAEpB,IAAA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE;UAC1C,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;EACvB,QAAA,OAAO,CAAC,CAAC;EACV,KAAA;MAED,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;EACrC,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAEzD;EAAM,aAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;EAC7B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EAChB,SAAA;EAAM,aAAA,IACL,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;EAChB,YAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;EAChB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;EAC3B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;cAC3B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;cACvB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB;EACA,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EACtD,SAAA;EAAM,aAAA;cACL,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1B,SAAA;EACF,KAAA;EAED,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;EAKG;EACH,SAAS,KAAK,CAAC,CAAM,EAAA;EACnB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;EACpB,QAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,KAAU,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;EACjD,KAAA;WAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;UAC9C,IAAI,CAAC,YAAY,IAAI,EAAE;cACrB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;EAC9B,SAAA;EACD,QAAA,OAAO,wBAAwB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACxC,KAAA;EAAM,SAAA;EACL,QAAA,OAAO,CAAC,CAAC;EACV,KAAA;EACH,CAAC;EAED;;;;EAIG;EACH,SAAS,WAAW,CAAC,CAAM,EAAA;MACzB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;EACjC,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;EACtB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EAChB,SAAA;EAAM,aAAA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;EAC1D,YAAA,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EACtB,SAAA;EACF,KAAA;EACH;;ECzIA;;;;;;;EAOG;EAqBH;;;;;;EAMG;EACa,SAAA,IAAI,CAAC,GAAG,IAAgB,EAAA;EACtC,IAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED;;;;;EAKG;EACH,SAAS,kBAAkB,CAAC,IAAgB,EAAA;EAC1C,IAAA,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;MAClC,IAAI,CAAC,GAAG,CAAC,CAAC;MAEV,MAAM,MAAM,GAAQ,MAAa;UAC/B,MAAM,CAAC,GAAG,OAAO,GAAG,EAAE,GAAG,CAAC,GAAG,sBAAsB,CAAC;UACpD,EAAE,GAAG,EAAE,CAAC;UACR,EAAE,GAAG,EAAE,CAAC;EACR,QAAA,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;EAChC,KAAC,CAAC;EAEF,IAAA,MAAM,CAAC,MAAM,GAAG,MAAc,MAAM,EAAE,GAAG,WAAW,CAAC;MAErD,MAAM,CAAC,OAAO,GAAG,MACf,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,sBAAsB,CAAC;EAElE,IAAA,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC;EAC1B,IAAA,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;EACnB,IAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;EAEvB,IAAA,OAAO,MAAM,CAAC;EAChB,CAAC;EAED;;;;;;EAMG;EACH,SAAS,QAAQ,CAAC,GAAG,IAAgB,EAAA;EACnC,IAAA,MAAM,IAAI,GAAG,IAAI,EAAE,CAAC;EAEpB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EACnB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EACnB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;EAEnB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;UACpC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,IAAI,EAAE,GAAG,CAAC,EAAE;cACV,EAAE,IAAI,CAAC,CAAC;EACT,SAAA;UACD,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,IAAI,EAAE,GAAG,CAAC,EAAE;cACV,EAAE,IAAI,CAAC,CAAC;EACT,SAAA;UACD,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,IAAI,EAAE,GAAG,CAAC,EAAE;cACV,EAAE,IAAI,CAAC,CAAC;EACT,SAAA;EACF,KAAA;EAED,IAAA,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACtB,CAAC;EAOD;;;;;EAKG;EACH,SAAS,IAAI,GAAA;MACX,IAAI,CAAC,GAAG,UAAU,CAAC;EAEnB,IAAA,OAAO,UAAU,IAAI,EAAA;EACnB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;EAC/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACtC,YAAA,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC1B,YAAA,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC;EAChC,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;cACZ,CAAC,IAAI,CAAC,CAAC;cACP,CAAC,IAAI,CAAC,CAAC;EACP,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;cACZ,CAAC,IAAI,CAAC,CAAC;EACP,YAAA,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;EACtB,SAAA;UACD,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;EAC5C,KAAC,CAAC;EACJ;;EC9HA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,GAAG;EACtB,EAAE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC;AACxB;EACA,EAAE,OAAO;EACT,IAAI,EAAE,EAAE,IAAI;EACZ,IAAI,GAAG,EAAE,IAAI;EACb,IAAI,OAAO,EAAE,IAAI;EACjB,IAAI,IAAI,EAAE,IAAI;AACd;EACA,IAAI,GAAG,GAAG;EACV,MAAM,OAAO;EACb,QAAQ,GAAG,EAAE,IAAI;EACjB,OAAO,CAAC;EACR,KAAK;EACL,GAAG,CAAC;EACJ,CAAC;AACD;EACA,MAAMA,QAAM;EACZ,EAAE,OAAO,MAAM,KAAK,WAAW;EAC/B,MAAM,MAAM,CAAC,MAAM,IAAI,UAAU;EACjC,MAAM,YAAY;EAClB;EACA,QAAQ,OAAO,UAAU,EAAE,CAAC;EAC5B,OAAO;;EC7BP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,WAAS,CAAC,SAAS,EAAE;EACrC,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B;EACA,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;EACA,EAAE,IAAI,CAAC,IAAI,GAAG;EACd,IAAI,SAAS;EACb,IAAI,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;EAC1C,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACjD;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACrD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;EAChC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAChE,GAAG,CAAC,CAAC;AACL;EACA,EAAE,MAAM,MAAM,GAAGD,QAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC3C,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;EAChC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;EACrB;EACA;EACA,GAAG,CAAC,CAAC;AACL;EACA;EACA,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,KAAK;EACT,IAAI,WAAW;EACf,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,UAAU;EACd,IAAI,SAAS;EACb,IAAI,QAAQ;EACZ,GAAG,CAAC;EACJ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;EAC5B,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;EAChC,MAAM,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;EACvC,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;AACL;EACA;EACA,EAAE,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE;EACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,KAAK;EAC/B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;EAChD,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;EAC1B,OAAO;EACP,KAAK,CAAC;EACN,IAAI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC3D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;EAClC,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChE,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;EACjC,IAAI;EACJ,MAAM,KAAK,IAAI,KAAK;EACpB,UAAU,KAAK,CAAC,GAAG,KAAK,QAAQ;EAChC,UAAU,KAAK,CAAC,OAAO,KAAK,EAAE;EAC9B,MAAM;EACN,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;EACxB,KAAK;EACL,GAAG,CAAC;EACJ,CAAC;AACD;EACA;EACA,OAAO,CAACC,WAAS,CAAC,SAAS,CAAC,CAAC;AAC7B;EACA;AACAA,aAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AACzB;EACA;EACA;EACA;AACAA,aAAS,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;EAC1C,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AACpB;EACA,EAAE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE;EACjE,IAAI,QAAQ,EAAE,CAAC;EACf,GAAG;EACH,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;AACAA,aAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;EAC3C;EACA,EAAE,IAAIA,WAAS,CAAC,OAAO,EAAE;EACzB,IAAIA,WAAS,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;EACnC,GAAG;EACH,EAAEA,WAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B;EACA,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACrB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;EAC3C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAClD;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACtB,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxB;EACA;EACA;EACA,EAAE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;EAC/D,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;AACAA,aAAS,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;EAC7C,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;EACtB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;EAC5C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACrD,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAClE;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACtB,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC1B,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;EACA;EACA;AACAA,aAAS,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,KAAK,EAAE;EACrD;EACA,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;EAClB,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;EACnC,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;EACrC,EAAE,OAAO,OAAO,EAAE;EAClB,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;EAC5B,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;EACjC,GAAG;EACH,EAAE,OAAO,KAAK,CAAC;EACf;;ECrKA;EAEA;EACA;EACA;EACA,MAAM,YAAY,GAAG,oBAAoB,CAAC;EAE1C;EACA,MAAM,SAAS,GAAG,2CAA2C,CAAC;EAC9D,MAAM,UAAU,GAAG,kCAAkC,CAAC;EACtD,MAAM,KAAK,GACT,8GAA8G,CAAC;EACjH,MAAM,MAAM,GACV,kIAAkI,CAAC;EA4DrI;;;;;EAKG;EACG,SAAU,QAAQ,CAAC,KAAc,EAAA;MACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;EAC9D,CAAC;EAED;;;;EAIG;EACG,SAAU,kBAAkB,CAAC,SAAkC,EAAA;EACnE,IAAA,IAAI,SAAS,EAAE;EACb,QAAA,OAAO,SAAS,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;EACzC,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC;EACnC,YAAA,IAAI,KAAK,EAAE;kBACT,kBAAkB,CAAC,KAAK,CAAC,CAAC;EAC1B,gBAAA,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EAC9B,aAAA;EACF,SAAA;EACF,KAAA;EACH,CAAC;EAED;;;;;EAKG;EACG,SAAU,QAAQ,CAAC,KAAc,EAAA;MACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;EAC9D,CAAC;EAED;;;;;EAKG;EACG,SAAU,QAAQ,CAAC,KAAc,EAAA;MACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;EACrD,CAAC;EAED;;;;;EAKG;EACG,SAAU,MAAM,CAAC,KAAc,EAAA;MACnC,IAAI,KAAK,YAAY,IAAI,EAAE;EACzB,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;EAAM,SAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;;UAE1B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACvC,QAAA,IAAI,KAAK,EAAE;EACT,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;eAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;EACpC,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;EACF,KAAA;EAED,IAAA,OAAO,KAAK,CAAC;EACf,CAAC;EAED;;;;;;;;;;EAUG;EACH,SAAS,YAAY,CACnB,CAAM,EACN,CAAM,EACN,IAAY,EACZ,aAAsB,EAAA;MAEtB,IAAI,UAAU,GAAG,KAAK,CAAC;MACvB,IAAI,aAAa,KAAK,IAAI,EAAE;EAC1B,QAAA,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;EACxD,KAAA;EAED,IAAA,IAAI,UAAU,EAAE;EACd,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;EAChB,KAAA;EAAM,SAAA;UACL,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EACnB,KAAA;EACH,CAAC;EAED;;;;;;;;;EASG;EACG,SAAU,aAAa,CAC3B,CAAI,EACJ,CAAa,EACb,aAAa,GAAG,KAAK,EAAA;;;EAIrB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;EACpB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;EACzB,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;;kBAEnD,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,aAAA;EAAM,iBAAA;EACL,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EACtB,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;kBACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;EACtC,oBAAA,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;EAC5C,iBAAA;EACF,aAAA;EACF,SAAA;EACF,KAAA;EACH,CAAC;EAED;;;;;;;EAOG;AACU,QAAA,MAAM,GAAG,MAAM,CAAC,OAAO;EAEpC;;;;;;;;;EASG;EACG,SAAU,eAAe,CAC7B,KAAe,EACf,CAAM,EACN,GAAG,MAAa,EAAA;EAEhB,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;EACzB,QAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;EACzE,KAAA;EAED,IAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;EAC1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACrC,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACtB,YAAA,IAAI,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;kBAC9D,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;EACvB,aAAA;EACF,SAAA;EACF,KAAA;EACD,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;;;;;;;;EAaG;EACG,SAAU,mBAAmB,CACjC,KAAe,EACf,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;EAGrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;EACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;EAC/D,KAAA;EAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACrC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EACtB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;EACjD,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;EAC7C,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;EACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;EACd,iBAAA;kBACD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;EAClC,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;EACpD,iBAAA;EAAM,qBAAA;sBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,iBAAA;EACF,aAAA;mBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;EACjC,gBAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;EAC/D,aAAA;EAAM,iBAAA;kBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,aAAA;EACF,SAAA;EACF,KAAA;EACD,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;;;;;;;;;EAcG;EACG,SAAU,sBAAsB,CACpC,cAAwB,EACxB,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;;EAIrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;EACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;EAC/D,KAAA;EAED,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;EACpB,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;cAClD,SAAS;EACV,SAAA;EACD,QAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;cACjC,SAAS;EACV,SAAA;EAED,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;EAC7C,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;EACzB,gBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;EACd,aAAA;cACD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;EAClC,gBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9B,aAAA;EAAM,iBAAA;kBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,aAAA;EACF,SAAA;eAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;EACjC,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;EACb,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACvC,gBAAA,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1B,aAAA;EACF,SAAA;EAAM,aAAA;cACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,SAAA;EACF,KAAA;EAED,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;;;;;EAUG;EACa,SAAA,UAAU,CACxB,CAAM,EACN,CAAM,EACN,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,KAAK,EAAA;EAErB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;EACpB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,WAAW,KAAK,IAAI,EAAE;EACzE,YAAA,IACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;EAC3B,gBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;EAChB,gBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;EACA,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;EACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;EAChD,iBAAA;EAAM,qBAAA,IACL,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;EAC3B,oBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;EAChB,oBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;EACA,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;EAC3C,iBAAA;EAAM,qBAAA;sBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,iBAAA;EACF,aAAA;mBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;kBACjC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;EAC3B,aAAA;EAAM,iBAAA;kBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;EACzC,aAAA;EACF,SAAA;EACF,KAAA;EACD,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;EAMG;EACa,SAAA,UAAU,CAAC,CAAY,EAAE,CAAY,EAAA;EACnD,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;EACzB,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;UAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAChB,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EACF,KAAA;EAED,IAAA,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;EAKG;EACG,SAAU,OAAO,CAAC,MAAe,EAAA;EACrC,IAAA,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;MAE3B,IAAI,IAAI,KAAK,QAAQ,EAAE;UACrB,IAAI,MAAM,KAAK,IAAI,EAAE;EACnB,YAAA,OAAO,MAAM,CAAC;EACf,SAAA;UACD,IAAI,MAAM,YAAY,OAAO,EAAE;EAC7B,YAAA,OAAO,SAAS,CAAC;EAClB,SAAA;UACD,IAAI,MAAM,YAAY,MAAM,EAAE;EAC5B,YAAA,OAAO,QAAQ,CAAC;EACjB,SAAA;UACD,IAAI,MAAM,YAAY,MAAM,EAAE;EAC5B,YAAA,OAAO,QAAQ,CAAC;EACjB,SAAA;EACD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;EACzB,YAAA,OAAO,OAAO,CAAC;EAChB,SAAA;UACD,IAAI,MAAM,YAAY,IAAI,EAAE;EAC1B,YAAA,OAAO,MAAM,CAAC;EACf,SAAA;EAED,QAAA,OAAO,QAAQ,CAAC;EACjB,KAAA;MACD,IAAI,IAAI,KAAK,QAAQ,EAAE;EACrB,QAAA,OAAO,QAAQ,CAAC;EACjB,KAAA;MACD,IAAI,IAAI,KAAK,SAAS,EAAE;EACtB,QAAA,OAAO,SAAS,CAAC;EAClB,KAAA;MACD,IAAI,IAAI,KAAK,QAAQ,EAAE;EACrB,QAAA,OAAO,QAAQ,CAAC;EACjB,KAAA;MACD,IAAI,IAAI,KAAK,SAAS,EAAE;EACtB,QAAA,OAAO,WAAW,CAAC;EACpB,KAAA;EAED,IAAA,OAAO,IAAI,CAAC;EACd,CAAC;EAOD;;;;;;EAMG;EACa,SAAA,kBAAkB,CAChC,GAAqB,EACrB,QAAW,EAAA;EAEX,IAAA,OAAO,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC;EAC5B,CAAC;EAED;;;;;EAKG;EACG,SAAU,SAAS,CAAI,GAAqB,EAAA;EAChD,IAAA,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC;EACrB,CAAC;EAED;;;;;EAKG;EACG,SAAU,eAAe,CAAC,IAAa,EAAA;EAC3C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;EAC3C,CAAC;EAED;;;;;EAKG;EACG,SAAU,gBAAgB,CAAC,IAAa,EAAA;EAC5C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;EAC5C,CAAC;EAED;;;;;EAKG;EACG,SAAU,cAAc,CAAC,IAAa,EAAA;EAC1C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;EAC1C,CAAC;EAED;;;;;EAKG;EACa,SAAA,YAAY,CAAC,IAAa,EAAE,UAAkB,EAAA;MAC5D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MACxC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MACzC,OAAO,GAAG,OAAO,CAAC,MAAM,CACtB,UAAU,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;EACnC,QAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;OACrC,CAAC,CACH,CAAC;MACF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACrC,CAAC;EAED;;;;;EAKG;EACa,SAAA,eAAe,CAAC,IAAa,EAAE,UAAkB,EAAA;MAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MACxC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EACzC,IAAA,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;EAC1C,QAAA,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;EACzC,KAAC,CAAC,CAAC;MACH,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACrC,CAAC;EAUD;;;;;;;EAOG;EACa,SAAA,OAAO,CAAC,MAAW,EAAE,QAAa,EAAA;EAChD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;EAEzB,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;UAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;cAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;EAChC,SAAA;EACF,KAAA;EAAM,SAAA;;EAEL,QAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;EACxB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;kBACrD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;EACpC,aAAA;EACF,SAAA;EACF,KAAA;EACH,CAAC;EAED;;;;;EAKG;AACU,QAAA,OAAO,GAAG,MAAM,CAAC,OAAO;EAErC;;;;;;;EAOG;WACa,cAAc,CAC5B,MAAoB,EACpB,GAAM,EACN,KAAQ,EAAA;EAER,IAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE;EACzB,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EACpB,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;EAAM,SAAA;EACL,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EACH,CAAC;EAED;;;;;EAKG;EACG,SAAU,QAAQ,CAAC,EAAc,EAAA;MACrC,IAAI,SAAS,GAAG,KAAK,CAAC;EAEtB,IAAA,OAAO,MAAW;UAChB,IAAI,CAAC,SAAS,EAAE;cACd,SAAS,GAAG,IAAI,CAAC;cACjB,qBAAqB,CAAC,MAAW;kBAC/B,SAAS,GAAG,KAAK,CAAC;EAClB,gBAAA,EAAE,EAAE,CAAC;EACP,aAAC,CAAC,CAAC;EACJ,SAAA;EACH,KAAC,CAAC;EACJ,CAAC;EAED;;;;EAIG;EACG,SAAU,cAAc,CAAC,KAAwB,EAAA;MACrD,IAAI,CAAC,KAAK,EAAE;EACV,QAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;EACtB,KAAA;MAED,IAAI,CAAC,KAAK,EAAE,CAEX;WAAM,IAAI,KAAK,CAAC,cAAc,EAAE;EAC/B,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;EACxB,KAAA;EAAM,SAAA;;EAEJ,QAAA,KAAa,CAAC,WAAW,GAAG,KAAK,CAAC;EACpC,KAAA;EACH,CAAC;EAED;;;;;EAKG;WACa,SAAS,CACvB,KAA2B,GAAA,MAAM,CAAC,KAAK,EAAA;;;MAKvC,IAAI,MAAM,GAAuB,IAAI,CAAC;MACtC,IAAI,CAAC,KAAK,EAAE,CAEX;WAAM,IAAI,KAAK,CAAC,MAAM,EAAE;EACvB,QAAA,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;EACvB,KAAA;WAAM,IAAI,KAAK,CAAC,UAAU,EAAE;EAC3B,QAAA,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;EAC3B,KAAA;EAED,IAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;EAChC,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;MAED,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,EAAE;;EAEnD,QAAA,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;EAC3B,QAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;EAChC,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;EACF,KAAA;EAED,IAAA,OAAO,MAAM,CAAC;EAChB,CAAC;EAED;;;;;;EAMG;EACa,SAAA,SAAS,CAAC,OAAgB,EAAE,MAAe,EAAA;MACzD,IAAI,IAAI,GAAS,OAAO,CAAC;EAEzB,IAAA,OAAO,IAAI,EAAE;UACX,IAAI,IAAI,KAAK,MAAM,EAAE;EACnB,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;eAAM,IAAI,IAAI,CAAC,UAAU,EAAE;EAC1B,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;EACxB,SAAA;EAAM,aAAA;EACL,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EACF,KAAA;EAED,IAAA,OAAO,KAAK,CAAC;EACf,CAAC;AAEY,QAAA,MAAM,GAAG;EACpB;;;;;;EAMG;MACH,SAAS,CAAC,KAAc,EAAE,YAAsB,EAAA;EAC9C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;cAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;EACjB,SAAA;UAED,IAAI,KAAK,IAAI,IAAI,EAAE;cACjB,OAAO,KAAK,IAAI,KAAK,CAAC;EACvB,SAAA;UAED,OAAO,YAAY,IAAI,IAAI,CAAC;OAC7B;EAED;;;;;;EAMG;MACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;EAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;cAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;EACjB,SAAA;UAED,IAAI,KAAK,IAAI,IAAI,EAAE;cACjB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,IAAI,CAAC;EAC9C,SAAA;UAED,OAAO,YAAY,IAAI,IAAI,CAAC;OAC7B;EAED;;;;;;EAMG;MACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;EAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;cAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;EACjB,SAAA;UAED,IAAI,KAAK,IAAI,IAAI,EAAE;EACjB,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;EACtB,SAAA;UAED,OAAO,YAAY,IAAI,IAAI,CAAC;OAC7B;EAED;;;;;;EAMG;MACH,MAAM,CAAC,KAAc,EAAE,YAAqB,EAAA;EAC1C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;cAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;EACjB,SAAA;EAED,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;EACnB,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EAAM,aAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC1B,OAAO,KAAK,GAAG,IAAI,CAAC;EACrB,SAAA;EAAM,aAAA;cACL,OAAO,YAAY,IAAI,IAAI,CAAC;EAC7B,SAAA;OACF;EAED;;;;;;EAMG;MACH,SAAS,CACP,KAA4C,EAC5C,YAAe,EAAA;EAEf,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;cAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;EACjB,SAAA;EAED,QAAA,OAAO,KAAK,IAAI,YAAY,IAAI,IAAI,CAAC;OACtC;IACD;EAEF;;;;;;;EAOG;EACG,SAAU,QAAQ,CAAC,GAAW,EAAA;EAClC,IAAA,IAAI,MAAM,CAAC;MACX,QAAQ,GAAG,CAAC,MAAM;EAChB,QAAA,KAAK,CAAC,CAAC;EACP,QAAA,KAAK,CAAC;EACJ,YAAA,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9B,YAAA,OAAO,MAAM;EACX,kBAAE;EACE,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACvC,iBAAA;oBACD,IAAI,CAAC;EACX,QAAA,KAAK,CAAC,CAAC;EACP,QAAA,KAAK,CAAC;EACJ,YAAA,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC7B,YAAA,OAAO,MAAM;EACX,kBAAE;sBACE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;sBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;sBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC3B,iBAAA;oBACD,IAAI,CAAC;EACX,QAAA;EACE,YAAA,OAAO,IAAI,CAAC;EACf,KAAA;EACH,CAAC;EAED;;;;;;EAMG;EACa,SAAA,eAAe,CAAC,KAAa,EAAE,OAAe,EAAA;EAC5D,IAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;EAC1B,QAAA,OAAO,KAAK,CAAC;EACd,KAAA;EAAM,SAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;UAChC,MAAM,GAAG,GAAG,KAAK;eACd,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC9B,aAAA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;eAChB,KAAK,CAAC,GAAG,CAAC,CAAC;UACd,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EAC7E,KAAA;EAAM,SAAA;EACL,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;UAC5B,IAAI,GAAG,IAAI,IAAI,EAAE;EACf,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EAAM,aAAA;cACL,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;EAC1E,SAAA;EACF,KAAA;EACH,CAAC;EAED;;;;;;;EAOG;WACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;EAC/D,IAAA,QACE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAC3E;EACJ,CAAC;EAsCD;;;;;;EAMG;EACa,SAAA,UAAU,CACxB,UAAgC,EAChC,YAA8B,EAAA;EAE9B,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;UACxB,IAAI,QAAQ,GAAW,UAAU,CAAC;EAClC,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;cACxB,MAAM,GAAG,GAAG,QAAQ;mBACjB,MAAM,CAAC,CAAC,CAAC;mBACT,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;mBAC9B,KAAK,CAAC,GAAG,CAAC;mBACV,GAAG,CAAC,UAAU,KAAK,EAAA;EAClB,gBAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;EACzB,aAAC,CAAC,CAAC;EACL,YAAA,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,SAAA;EACD,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;EACjC,YAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC/B,YAAA,MAAM,eAAe,GAAG;kBACtB,CAAC,EAAE,GAAG,CAAC,CAAC;EACR,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;EACd,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;eAC7B,CAAC;EACF,YAAA,MAAM,cAAc,GAAG;kBACrB,CAAC,EAAE,GAAG,CAAC,CAAC;EACR,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EAC5B,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;eACf,CAAC;EACF,YAAA,MAAM,cAAc,GAAG,QAAQ,CAC7B,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,CACjB,CAAC;EACF,YAAA,MAAM,eAAe,GAAG,QAAQ,CAC9B,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,CAClB,CAAC;cACF,OAAO;EACL,gBAAA,UAAU,EAAE,QAAQ;EACpB,gBAAA,MAAM,EAAE,cAAc;EACtB,gBAAA,SAAS,EAAE;EACT,oBAAA,UAAU,EAAE,eAAe;EAC3B,oBAAA,MAAM,EAAE,cAAc;EACvB,iBAAA;EACD,gBAAA,KAAK,EAAE;EACL,oBAAA,UAAU,EAAE,eAAe;EAC3B,oBAAA,MAAM,EAAE,cAAc;EACvB,iBAAA;eACF,CAAC;EACH,SAAA;EAAM,aAAA;cACL,OAAO;EACL,gBAAA,UAAU,EAAE,QAAQ;EACpB,gBAAA,MAAM,EAAE,QAAQ;EAChB,gBAAA,SAAS,EAAE;EACT,oBAAA,UAAU,EAAE,QAAQ;EACpB,oBAAA,MAAM,EAAE,QAAQ;EACjB,iBAAA;EACD,gBAAA,KAAK,EAAE;EACL,oBAAA,UAAU,EAAE,QAAQ;EACpB,oBAAA,MAAM,EAAE,QAAQ;EACjB,iBAAA;eACF,CAAC;EACH,SAAA;EACF,KAAA;EAAM,SAAA;EACL,QAAA,IAAI,YAAY,EAAE;EAChB,YAAA,MAAM,KAAK,GAAoB;EAC7B,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU;EAC5D,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM;EAChD,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;EACvC,sBAAE;0BACE,MAAM,EAAE,UAAU,CAAC,SAAS;0BAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;EACjC,qBAAA;EACH,sBAAE;0BACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;8BACxD,YAAY,CAAC,SAAS,CAAC,UAAU;0BACnC,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;8BACpD,YAAY,CAAC,SAAS,CAAC,MAAM;EAChC,qBAAA;EACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;EAC/B,sBAAE;0BACE,MAAM,EAAE,UAAU,CAAC,KAAK;0BACxB,UAAU,EAAE,UAAU,CAAC,KAAK;EAC7B,qBAAA;EACH,sBAAE;0BACE,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM;8BAC5C,YAAY,CAAC,KAAK,CAAC,MAAM;0BAC3B,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU;8BAChD,YAAY,CAAC,KAAK,CAAC,UAAU;EAChC,qBAAA;eACN,CAAC;EACF,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EAAM,aAAA;EACL,YAAA,MAAM,KAAK,GAAgB;EACzB,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,SAAS;EAC9C,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,SAAS;EACtC,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;EACvC,sBAAE;0BACE,MAAM,EAAE,UAAU,CAAC,SAAS;0BAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;EACjC,qBAAA;EACH,sBAAE;0BACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;8BACxD,SAAS;0BACX,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;8BACpD,SAAS;EACZ,qBAAA;EACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;EAC/B,sBAAE;0BACE,MAAM,EAAE,UAAU,CAAC,KAAK;0BACxB,UAAU,EAAE,UAAU,CAAC,KAAK;EAC7B,qBAAA;EACH,sBAAE;EACE,wBAAA,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS;EAC5D,wBAAA,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;EACjE,qBAAA;eACN,CAAC;EACF,YAAA,OAAO,KAAK,CAAC;EACd,SAAA;EACF,KAAA;EACH,CAAC;EAED;;;;;;;;;EASG;WACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;EAC/D,IAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAChB,IAAA,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC;EACpB,IAAA,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;EAClB,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;EACpD,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;;MAGpD,IAAI,MAAM,KAAK,MAAM,EAAE;EACrB,QAAA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;EAClC,KAAA;;EAGD,IAAA,MAAM,CAAC,GACL,GAAG,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;MAC7E,MAAM,CAAC,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACvD,IAAA,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC;MACrD,MAAM,UAAU,GAAG,CAAC,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;MAC9C,MAAM,KAAK,GAAG,MAAM,CAAC;EACrB,IAAA,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;EAC7C,CAAC;EAMD;;;;;EAKG;EACH,SAAS,YAAY,CAAC,OAAe,EAAA;MACnC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;MAElD,MAAM,MAAM,GAAc,EAAE,CAAC;EAE7B,IAAA,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;EAEpC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;UACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAC/D,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CACrB,CAAC;EACH,KAAA;EAED,IAAA,OAAO,MAAM,CAAC;EAChB,CAAC;EAED;;;;;EAKG;EACa,SAAA,UAAU,CAAC,OAAoB,EAAE,OAAe,EAAA;EAC9D,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;EACvC,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;UACnD,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;EACvC,KAAA;EACH,CAAC;EAED;;;;;EAKG;EACa,SAAA,aAAa,CAAC,OAAoB,EAAE,OAAe,EAAA;EACjE,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;MACvC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;EACvC,QAAA,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;EACnC,KAAA;EACH,CAAC;EAED;;;;;;;;;EASG;WACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;EACtD,IAAA,IAAI,CAAqB,CAAC;EAC1B,IAAA,IAAI,CAAqB,CAAC;EAC1B,IAAA,IAAI,CAAqB,CAAC;MAE1B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5B,IAAA,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACpB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MACtB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;MAEhC,QAAQ,CAAC,GAAG,CAAC;EACX,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACR,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACR,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACR,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACR,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACR,QAAA,KAAK,CAAC;EACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cAC1B,MAAM;EACT,KAAA;MAED,OAAO;UACL,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;UAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;OACnC,CAAC;EACJ,CAAC;EAED;;;;;;;EAOG;WACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;MACtD,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9B,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EAED;;;;;EAKG;EACG,SAAU,QAAQ,CAAC,GAAW,EAAA;EAClC,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;MAC1B,IAAI,CAAC,GAAG,EAAE;EACR,QAAA,MAAM,IAAI,SAAS,CAAC,IAAI,GAAG,CAAA,uBAAA,CAAyB,CAAC,CAAC;EACvD,KAAA;EACD,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC;EAED;;;;;EAKG;EACG,SAAU,UAAU,CAAC,GAAW,EAAA;MACpC,MAAM,IAAI,GAAG,oCAAoC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5D,IAAA,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;EAKG;EACG,SAAU,UAAU,CAAC,GAAW,EAAA;EACpC,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACzB,CAAC;EAED;;;;;EAKG;EACG,SAAU,WAAW,CAAC,IAAY,EAAA;EACtC,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED;;;;;;;EAOG;EACa,SAAA,qBAAqB,CACnC,MAAW,EACX,eAA6B,EAAA;MAE7B,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;;UAEnE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;EAChD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACtC,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;kBACpE,IAAI,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;EACjD,oBAAA,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChE,iBAAA;EACF,aAAA;EACF,SAAA;EACD,QAAA,OAAO,QAAQ,CAAC;EACjB,KAAA;EAAM,SAAA;EACL,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;EACH,CAAC;EAID;;;;;;EAMG;EACG,SAAU,YAAY,CAC1B,eAAkB,EAAA;MAElB,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;EACnE,QAAA,OAAO,IAAI,CAAC;EACb,KAAA;MAED,IAAI,eAAe,YAAY,OAAO,EAAE;;EAEtC,QAAA,OAAO,eAAe,CAAC;EACxB,KAAA;MAED,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;EAChD,IAAA,KAAK,MAAM,CAAC,IAAI,eAAe,EAAE;EAC/B,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE;EAC5D,YAAA,IAAI,OAAQ,eAAuB,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;kBAClD,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAE,eAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,aAAA;EACF,SAAA;EACF,KAAA;EAED,IAAA,OAAO,QAAQ,CAAC;EAClB,CAAC;EAED;;;;;;EAMG;EACa,SAAA,UAAU,CAAI,CAAM,EAAE,OAA+B,EAAA;EACnE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACjC,QAAA,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,QAAA,IAAI,CAAC,CAAC;UACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;cAClD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACjB,SAAA;EACD,QAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACV,KAAA;EACD,IAAA,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;;;;;;;EAYG;EACG,SAAU,YAAY,CAC1B,WAAgB,EAChB,OAAY,EACZ,MAAc,EACd,aAAA,GAAqB,EAAE,EAAA;;MAGvB,MAAM,SAAS,GAAG,UAAU,GAAQ,EAAA;EAClC,QAAA,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,CAAC;EAC3C,KAAC,CAAC;MAEF,MAAM,QAAQ,GAAG,UAAU,GAAY,EAAA;UACrC,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;EACjD,KAAC,CAAC;;MAGF,MAAM,OAAO,GAAG,UAAU,GAAW,EAAA;EACnC,QAAA,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;EACnB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;EAChD,gBAAA,OAAO,KAAK,CAAC;EACd,aAAA;EACF,SAAA;EACD,QAAA,OAAO,IAAI,CAAC;EACd,KAAC,CAAC;;EAGF,IAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;EAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;EAC5D,KAAA;EAED,IAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;EACtB,QAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;EACxD,KAAA;EAED,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;EACtB,QAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;EACvD,KAAA;EAED,IAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;EAC5B,QAAA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;EAC9D,KAAA;;;;;EAMD,IAAA,MAAM,OAAO,GAAG,UAAU,MAAW,EAAE,OAAY,EAAE,MAAc,EAAA;UACjE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;EAC7B,YAAA,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;EACrB,SAAA;EAED,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;EAC5B,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;EAC3B,QAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;EACtB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;kBACnD,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;EACvB,aAAA;EACF,SAAA;EACH,KAAC,CAAC;;EAGF,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;EAClC,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;EACxE,IAAA,MAAM,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;EACtE,IAAA,MAAM,aAAa,GAAG,YAAY,GAAG,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;;;;MAKtE,IAAI,SAAS,KAAK,SAAS,EAAE;EAC3B,QAAA,OAAO;EACR,KAAA;EAED,IAAA,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;UAClC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;EAClC,YAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;EAC1B,SAAA;EAED,QAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC;UACxC,OAAO;EACR,KAAA;EAED,IAAA,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;;EAExD,QAAA,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;cAC3B,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACnD,SAAA;EAAM,aAAA;EACL,YAAA,OAAO;EACR,SAAA;EACF,KAAA;EAED,IAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;UACxB,OAAO;EACR,KAAA;;;;;EAMD,IAAA,IAAI,OAAO,GAAG,IAAI,CAAC;EAEnB,IAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;EACnC,QAAA,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;EAC7B,KAAA;EAAM,SAAA;;UAEL,IAAI,aAAa,KAAK,SAAS,EAAE;EAC/B,YAAA,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;EAChC,SAAA;EACF,KAAA;EAED,IAAA,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EACtC,IAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;EACxC,CAAC;EAiBD;;;;;;;;;EASG;EACG,SAAU,kBAAkB,CAChC,YAAmB,EACnB,UAAsC,EACtC,KAAa,EACb,MAAe,EAAA;MAEf,MAAM,aAAa,GAAG,KAAK,CAAC;MAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;MAClB,IAAI,GAAG,GAAG,CAAC,CAAC;EACZ,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;EAEnC,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;EAC/C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;EAE5C,QAAA,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;UAClC,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;EAEvE,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;UACvC,IAAI,YAAY,IAAI,CAAC,EAAE;;EAErB,YAAA,OAAO,MAAM,CAAC;EACf,SAAA;EAAM,aAAA,IAAI,YAAY,IAAI,CAAC,CAAC,EAAE;;EAE7B,YAAA,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC;EAClB,SAAA;EAAM,aAAA;;EAEL,YAAA,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;EACnB,SAAA;EAED,QAAA,SAAS,EAAE,CAAC;EACb,KAAA;MAED,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED;;;;;;;;;;;;EAYG;EACG,SAAU,iBAAiB,CAC/B,YAAoC,EACpC,MAAc,EACd,KAAQ,EACR,cAAkC,EAClC,UAAiD,EAAA;MAEjD,MAAM,aAAa,GAAG,KAAK,CAAC;MAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;MAClB,IAAI,GAAG,GAAG,CAAC,CAAC;EACZ,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;EACnC,IAAA,IAAI,SAAS,CAAC;EACd,IAAA,IAAI,KAAK,CAAC;EACV,IAAA,IAAI,SAAS,CAAC;EACd,IAAA,IAAI,MAAM,CAAC;MAEX,UAAU;EACR,QAAA,UAAU,IAAI,SAAS;EACrB,cAAE,UAAU;EACZ,cAAE,UAAU,CAAS,EAAE,CAAS,EAAA;kBAC5B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACrC,aAAC,CAAC;EAER,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;;EAE/C,QAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;EACxC,QAAA,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;UACzD,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;UACpC,SAAS;EACP,YAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;UAErE,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;;EAElC,YAAA,OAAO,MAAM,CAAC;EACf,SAAA;EAAM,aAAA,IACL,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC;EACjC,YAAA,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAC7B;;cAEA,OAAO,cAAc,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;EACtE,SAAA;EAAM,aAAA,IACL,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;EAC7B,YAAA,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EACjC;;cAEA,OAAO,cAAc,IAAI,QAAQ;EAC/B,kBAAE,MAAM;EACR,kBAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;EACnD,SAAA;EAAM,aAAA;;cAEL,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;;EAEjC,gBAAA,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC;EAClB,aAAA;EAAM,iBAAA;;EAEL,gBAAA,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;EACnB,aAAA;EACF,SAAA;EACD,QAAA,SAAS,EAAE,CAAC;EACb,KAAA;;MAGD,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED;;;;;;EAMG;AACU,QAAA,eAAe,GAAG;EAC7B;;;;;EAKG;EACH,IAAA,MAAM,CAAC,CAAS,EAAA;EACd,QAAA,OAAO,CAAC,CAAC;OACV;EAED;;;;;EAKG;EACH,IAAA,UAAU,CAAC,CAAS,EAAA;UAClB,OAAO,CAAC,GAAG,CAAC,CAAC;OACd;EAED;;;;;EAKG;EACH,IAAA,WAAW,CAAC,CAAS,EAAA;EACnB,QAAA,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;OACpB;EAED;;;;;EAKG;EACH,IAAA,aAAa,CAAC,CAAS,EAAA;UACrB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;OACnD;EAED;;;;;EAKG;EACH,IAAA,WAAW,CAAC,CAAS,EAAA;EACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OAClB;EAED;;;;;EAKG;EACH,IAAA,YAAY,CAAC,CAAS,EAAA;UACpB,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OACxB;EAED;;;;;EAKG;EACH,IAAA,cAAc,CAAC,CAAS,EAAA;EACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;OAC1E;EAED;;;;;EAKG;EACH,IAAA,WAAW,CAAC,CAAS,EAAA;EACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OACtB;EAED;;;;;EAKG;EACH,IAAA,YAAY,CAAC,CAAS,EAAA;UACpB,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OAC5B;EAED;;;;;EAKG;EACH,IAAA,cAAc,CAAC,CAAS,EAAA;EACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OAC9D;EAED;;;;;EAKG;EACH,IAAA,WAAW,CAAC,CAAS,EAAA;UACnB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OAC1B;EAED;;;;;EAKG;EACH,IAAA,YAAY,CAAC,CAAS,EAAA;EACpB,QAAA,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OAChC;EAED;;;;;EAKG;EACH,IAAA,cAAc,CAAC,CAAS,EAAA;EACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OACxE;IACD;EAEF;;;;EAIG;WACa,iBAAiB,GAAA;MAC/B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;EAC1C,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;EAC3B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;MAE7B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC5C,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;EAClC,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;EACxB,IAAA,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;EACzB,IAAA,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;EAClC,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;EAC5B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;EAC7B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;EAChC,IAAA,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EAEzB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;EACjC,IAAA,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;EAC7B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;EAChC,IAAA,IAAI,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;MAC3B,IAAI,EAAE,IAAI,EAAE,EAAE;EACZ,QAAA,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;EACxB,KAAA;EAED,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;MAEjC,OAAO,EAAE,GAAG,EAAE,CAAC;EACjB,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;;;;;EAOG;EACa,SAAA,OAAO,CAAC,IAAS,EAAE,SAAc,EAAA;EAC/C,IAAA,IAAI,SAAS,CAAC;EACd,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;EAC7B,QAAA,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;EACzB,KAAA;EACD,IAAA,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE;EACzB,QAAA,IAAI,MAAM,EAAE;cACV,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACzC,gBAAA,IAAI,SAAS,EAAE;sBACb,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,iBAAA;EACF,aAAA;EACD,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;kBACpC,MAAM;EACP,aAAA;EACF,SAAA;EACF,KAAA;EACD,IAAA,OAAO,SAAS,CAAC;EACnB;;ECxwDA,MAAM,UAAU,GAAG;EACnB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,iBAAiB,EAAE,SAAS;EAC9B,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,eAAe,EAAE,SAAS;EAC5B,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,gBAAgB,EAAE,SAAS;EAC7B,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,eAAe,EAAE,SAAS;EAC5B,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,eAAe,EAAE,SAAS;EAC5B,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,GAAG,EAAE,SAAS;EAChB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,oBAAoB,EAAE,SAAS;EACjC,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,GAAG,EAAE,SAAS;EAChB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,OAAO,EAAE,SAAS;EACpB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,SAAS,EAAE,SAAS;EACtB,EAAE,cAAc,EAAE,SAAS;EAC3B,EAAE,UAAU,EAAE,SAAS;EACvB,EAAE,aAAa,EAAE,SAAS;EAC1B,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,QAAQ,EAAE,SAAS;EACrB,EAAE,YAAY,EAAE,SAAS;EACzB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,MAAM,EAAE,SAAS;EACnB,EAAE,WAAW,EAAE,SAAS;EACxB,EAAE,KAAK,EAAE,SAAS;EAClB,EAAE,KAAK,EAAE,SAAS;EAClB,CAAC,CAAC;AACF;EACA;EACA;EACA;sBACO,MAAM,WAAW,CAAC;EACzB;EACA;EACA;EACA,EAAE,WAAW,CAAC,UAAU,GAAG,CAAC,EAAE;EAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;EACjC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;EAC3B,IAAI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;EACxD,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;EACxB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;EACpD,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC/B,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;EAC3D,IAAI,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;EACnC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB;EACA;EACA,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,EAAE,CAAC;EACnC,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC;AAClC;EACA;EACA,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;EACnB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,CAAC,SAAS,EAAE;EACtB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;EACnC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;EAC5B,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;EAC9B,KAAK;EACL,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB;EACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;EACpB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,iBAAiB,CAAC,QAAQ,EAAE;EAC9B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;EACxC,MAAM,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;EACrC,KAAK,MAAM;EACX,MAAM,MAAM,IAAI,KAAK;EACrB,QAAQ,6EAA6E;EACrF,OAAO,CAAC;EACR,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,gBAAgB,CAAC,QAAQ,EAAE;EAC7B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;EACxC,MAAM,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;EACpC,KAAK,MAAM;EACX,MAAM,MAAM,IAAI,KAAK;EACrB,QAAQ,8EAA8E;EACtF,OAAO,CAAC;EACR,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,KAAK,EAAE;EACxB,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;EACnC,MAAM,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;EAC/B,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI,EAAE;EACrC,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;EAC1B,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,IAAI,CAAC;AACb;EACA;EACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;EACjD,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;EACjC,MAAM,KAAK,GAAG,SAAS,CAAC;EACxB,KAAK;AACL;EACA;EACA,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;EAClC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;EACtC,QAAQ,MAAM,SAAS,GAAG,KAAK;EAC/B,WAAW,MAAM,CAAC,CAAC,CAAC;EACpB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EACtC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC;EACtB,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;EAC7E,OAAO,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;EAC9C,QAAQ,MAAM,SAAS,GAAG,KAAK;EAC/B,WAAW,MAAM,CAAC,CAAC,CAAC;EACpB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;EACtC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC;EACtB,QAAQ,IAAI,GAAG;EACf,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;EACzB,SAAS,CAAC;EACV,OAAO,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;EAC7C,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;EACvC,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;EACjE,OAAO;EACP,KAAK,MAAM;EACX,MAAM,IAAI,KAAK,YAAY,MAAM,EAAE;EACnC,QAAQ;EACR,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;EAC/B,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;EAC/B,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;EAC/B,UAAU;EACV,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;EAChE,UAAU,IAAI,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;EAClE,SAAS;EACT,OAAO;EACP,KAAK;AACL;EACA;EACA,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;EAC5B,MAAM,MAAM,IAAI,KAAK;EACrB,QAAQ,+HAA+H;EACvI,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;EAC/B,OAAO,CAAC;EACR,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;EACvC,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,GAAG;EACT,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;EAC1C,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;EAC3B,MAAM,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;EACrC,KAAK;AACL;EACA,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;EACzB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;EACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAC9B,GAAG;AACH;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,EAAE;EAC9B;EACA,IAAI,IAAI,aAAa,KAAK,IAAI,EAAE;EAChC,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EACzD,KAAK;AACL;EACA,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;EAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC7C,KAAK;AACL;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACtC;EACA;EACA;EACA,IAAI,UAAU,CAAC,MAAM;EACrB,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;EAC5C,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;EAC7B,QAAQ,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;EACvC,OAAO;EACP,KAAK,EAAE,CAAC,CAAC,CAAC;EACV,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACpC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;EACzB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACjB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,MAAM,GAAG;EACX,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACxB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACpC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACnC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,GAAG;EACd,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;EAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;EAC/C,KAAK,MAAM;EACX,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;EACjD,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE;EACrC;EACA,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;EAC7B,MAAM,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAClD,KAAK;AACL;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACtB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD;EACA,IAAI,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;EACrC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EAClC,IAAI,MAAM,CAAC;EACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACzE,IAAI,MAAM,CAAC;EACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE;EACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;EACvC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC;EAC5D,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;EACtC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7D;EACA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,KAAK,EAAE;EACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC;EAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACnC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,KAAK,EAAE;EACxB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACnE,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC;EACxB,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EACtB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;EACzB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE;EACnC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EACjD,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EACxD,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;EACxC,MAAM,IAAI,CAAC,UAAU;EACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;EACrC,SAAS,GAAG,CAAC,4BAA4B;EACzC,UAAU,GAAG,CAAC,yBAAyB;EACvC,UAAU,GAAG,CAAC,wBAAwB;EACtC,UAAU,GAAG,CAAC,uBAAuB;EACrC,UAAU,GAAG,CAAC,sBAAsB;EACpC,UAAU,CAAC,CAAC,CAAC;EACb,KAAK;EACL,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnE;EACA;EACA,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;EACjD,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;EAClD,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B;EACA,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3C,IAAI,GAAG,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EACtD,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3E,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;AACf;EACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;EAC7C,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAC3C;EACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;EAC9C,MAAM,OAAO;EACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG,CAAC;EACV,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;EAC1C,MAAM,OAAO;EACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG,CAAC;EACV,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,GAAG;EACb,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;EAChD,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACjD;EACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;EACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;EAC1D,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,kBAAkB,CAAC;AAC9C;EACA,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACxD,IAAI,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC7D,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC;EACxD,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC9D;EACA,IAAI,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC5D;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;EAC5C,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACrD,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;EACnC,MAAM,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;EACzC,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;EACtC,MAAM,QAAQ,CAAC,SAAS,GAAG,kDAAkD,CAAC;EAC9E,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACnD,KAAK,MAAM;EACX,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC1D,MAAM,IAAI,CAAC,UAAU;EACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;EACrC,SAAS,GAAG,CAAC,4BAA4B;EACzC,UAAU,GAAG,CAAC,yBAAyB;EACvC,UAAU,GAAG,CAAC,wBAAwB;EACtC,UAAU,GAAG,CAAC,uBAAuB;EACrC,UAAU,GAAG,CAAC,sBAAsB;EACpC,UAAU,CAAC,CAAC,CAAC;EACb,MAAM,IAAI,CAAC,iBAAiB;EAC5B,SAAS,UAAU,CAAC,IAAI,CAAC;EACzB,SAAS,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,KAAK;AACL;EACA,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,WAAW,CAAC;AAChD;EACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,aAAa,CAAC;AAC9C;EACA,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACvD,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,gBAAgB,CAAC;AACpD;EACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;AAC1C;EACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EACxD,IAAI,IAAI;EACR,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;EACvC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;EAClC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC;EACpC,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB;EACA,KAAK;EACL,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;EACpC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,WAAW,CAAC;AAC9C;EACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAC3D,IAAI,IAAI;EACR,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC;EAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;EACrC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC;EACvC,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB;EACA,KAAK;EACL,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;EACvC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,WAAW,CAAC;AACjD;EACA,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACnD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACzD;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;EACpB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,YAAY;EAC7C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACjC,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,YAAY;EAC5C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACjC,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,YAAY;EAChD,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACpC,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,YAAY;EAC/C,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACpC,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,0BAA0B,CAAC;EAChE,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,aAAa,CAAC;AACnD;EACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACtD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB,CAAC;EAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,UAAU,CAAC;AAC7C;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACrD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,eAAe,CAAC;EACjD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;AACvC;EACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,mBAAmB,CAAC;EACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/C;EACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACtD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB,CAAC;EAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ,CAAC;EAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7D;EACA,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACrD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,sBAAsB,CAAC;EACxD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC;EACzC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD;EACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB,CAAC;EACtD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC;EACvC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD;EACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB,CAAC;EACtD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC;EAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAChD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EACjD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACjD;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;EACnB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;EACpB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAID,QAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EACrD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACnD;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,KAAK,KAAK;EAC9C,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;EACzB,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAClC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;EACrC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK,KAAK;EAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;EACzC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK;EACxC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,kBAAkB,GAAG;EACvB,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;EAClC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC1D,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;EAC1C,QAAQ,IAAI,CAAC,UAAU;EACvB,UAAU,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;EACvC,WAAW,GAAG,CAAC,4BAA4B;EAC3C,YAAY,GAAG,CAAC,yBAAyB;EACzC,YAAY,GAAG,CAAC,wBAAwB;EACxC,YAAY,GAAG,CAAC,uBAAuB;EACvC,YAAY,GAAG,CAAC,sBAAsB;EACtC,YAAY,CAAC,CAAC,CAAC;EACf,OAAO;EACP,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE;EACA;EACA,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;EACnD,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;EACpD,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC;EACA;EACA,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;EACzB,MAAM,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;EAC1D,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;EACxB,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;EAC/C,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;EAC3B,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EAC9B,MAAM,IAAI,GAAG,CAAC;EACd,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;EACtC,QAAQ,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;EAC3C,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;EAC5E,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;EAC5E,UAAU,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;EACpD,UAAU,GAAG,CAAC,SAAS,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;EAC3E,UAAU,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,SAAS;EACT,OAAO;EACP,MAAM,GAAG,CAAC,WAAW,GAAG,eAAe,CAAC;EACxC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7E,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC;AACnB;EACA,MAAM,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,KAAK;EACL,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;EAC1B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,KAAK,EAAE;EACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;EAC7D,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;EAC5C,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC1C;EACA,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;EAC3D,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;AAC1D;EACA,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC;EAC7B,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;AAC5B;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnC,IAAI,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACtE;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;EACtD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;AACvD;EACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;EACtC,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC;EAClE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;EACvC,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC;AAClE;EACA;EACA,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;EAClC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1B,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;EAC9B,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACnE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACd,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACd,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB;EACA;EACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;EAC9C,MAAM,OAAO;EACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;EACzB,MAAM,GAAG,CAAC;EACV,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;EAC1C,MAAM,OAAO;EACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG;EACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;EAClB,MAAM,GAAG,CAAC;EACV,GAAG;EACH;;EC9xBA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE;EAC5B,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;EACvB,IAAI,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;EAC9C,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;EAChC,IAAI,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,GAAG,MAAM;EACT,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;uBACO,MAAM,YAAY,CAAC;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW;EACb,IAAI,YAAY;EAChB,IAAI,gBAAgB;EACpB,IAAI,gBAAgB;EACpB,IAAI,UAAU,GAAG,CAAC;EAClB,IAAI,UAAU,GAAG,MAAM,KAAK;EAC5B,IAAI;EACJ,IAAI,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;EAC/B,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;EAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;EACtC,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;EAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC;EACA,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;EACtB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;EAC7B,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;EAC1B,IAAI,IAAI,CAAC,cAAc,GAAG;EAC1B,MAAM,OAAO,EAAE,KAAK;EACpB,MAAM,MAAM,EAAE,IAAI;EAClB,MAAM,SAAS,EAAE,SAAS;EAC1B,MAAM,UAAU,EAAE,IAAI;EACtB,KAAK,CAAC;EACN,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AACrD;EACA,IAAI,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;EAC7C,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;EAC5B,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;EAC1B,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;EACvB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;EACxB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;EAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAIE,aAAW,CAAC,UAAU,CAAC,CAAC;EACnD,IAAI,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;EAC7B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,UAAU,CAAC,OAAO,EAAE;EACtB,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;EAC/B;EACA,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;EAC7B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B;EACA,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;EACzB,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;EACvC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;EACtC,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;EACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;EAC7C,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;EAC9C,QAAQ,IAAI,OAAO,IAAI,IAAI,EAAE;EAC7B,UAAU,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;EACxD,SAAS;EACT,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;EAC7C,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;EACrD,SAAS;EACT,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;EAC1C,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;EAC/C,SAAS;EACT,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE;EAC9C,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;EACvD,SAAS;EACT,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;EAC3C,UAAU,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;EACpC,SAAS;EACT,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;EAC/C,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;EACnC,QAAQ,OAAO,GAAG,OAAO,CAAC;EAC1B,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;EAChD,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;EACtC,QAAQ,OAAO,GAAG,IAAI,CAAC;EACvB,OAAO;EACP,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;EACzC,QAAQ,OAAO,GAAG,KAAK,CAAC;EACxB,OAAO;AACP;EACA,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;EACrC,KAAK;EACL,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;EAClB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,gBAAgB,CAAC,aAAa,EAAE;EAClC,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;EACvC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;EACvC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;EACpB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;EAChD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EAChD,OAAO;EACP,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;EACrB,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;EAClB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B;EACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;EACvC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;EACpB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;EACrB,IAAI,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;EAChD,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,EAAE;EAC/E,QAAQ,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;EACnC,QAAQ,IAAI,GAAG,KAAK,CAAC;EACrB,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;EAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;EACpC,UAAU,IAAI;EACd,YAAY,IAAI;EAChB,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;EAC9E,SAAS,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;EACrE,UAAU,IAAI,GAAG,IAAI,CAAC;EACtB,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;EAC5B,UAAU,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AACpC;EACA;EACA,UAAU,IAAI,OAAO,GAAG,CAAC,EAAE;EAC3B,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EAC/B,WAAW;EACX;EACA,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACnC;EACA;EACA,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;EACtE,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC;EAClB,OAAO;EACP,KAAK;EACL,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;EACvB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;EACjB;EACA,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,GAAG;EACV,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACjD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,2BAA2B,CAAC;EACzD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC7C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,KAAK;AACL;EACA,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;EAC9B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,MAAM,GAAG;EACX,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,KAAK;AACL;EACA,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;EACpC,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC/C,MAAM,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;EAC/B,KAAK;EACL,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B;EACA,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,CAAC,IAAI,EAAE;EAClB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;EAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;EACvC,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,OAAO,MAAM;EACb,QAAQ,IAAI,GAAG,SAAS,CAAC;EACzB,QAAQ,MAAM;EACd,OAAO;EACP,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,WAAW,EAAE;EAClC,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;EACrC,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EACjD,MAAM,IAAI,CAAC,SAAS;EACpB,QAAQ,gDAAgD,GAAG,IAAI,CAAC,MAAM,CAAC;EACvE,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;EACvC,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EAClC,OAAO,CAAC,CAAC;EACT,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClC,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;EACrC,KAAK;EACL,IAAI,OAAO,CAAC,CAAC;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,IAAI,EAAE;EACpB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC9C,IAAI,GAAG,CAAC,SAAS,GAAG,qCAAqC,CAAC;EAC1D,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;EACzB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;EAC5B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE;EAC9C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC9C,IAAI,GAAG,CAAC,SAAS;EACjB,MAAM,iDAAiD,GAAG,IAAI,CAAC,MAAM,CAAC;EACtE,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;EAC9B,MAAM,OAAO,GAAG,CAAC,UAAU,EAAE;EAC7B,QAAQ,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;EACxC,OAAO;EACP,MAAM,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EACjD,KAAK,MAAM;EACX,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,GAAG,CAAC;EACjC,KAAK;EACL,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;EAClC,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EACpD,IAAI,MAAM,CAAC,SAAS,GAAG,qCAAqC,CAAC;EAC7D,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;EAC1B,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EAC7B,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;EACrC,QAAQ,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC3C,OAAO;EACP,KAAK;AACL;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACzC,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EACtD,MAAM,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,IAAI,CAAC,KAAK,aAAa,EAAE;EAC/B,QAAQ,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;EACrC,OAAO;EACP,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;EACjC,KAAK;AACL;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;EACpB,IAAI,MAAM,CAAC,QAAQ,GAAG,YAAY;EAClC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;EACnC,KAAK,CAAC;AACN;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;EACxC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;EAC/B,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAClD,IAAI,KAAK,CAAC,SAAS,GAAG,oCAAoC,CAAC;EAC3D,IAAI,IAAI;EACR,MAAM,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;EAC3B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;EACtB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;EACtB,KAAK,CAAC,OAAO,GAAG,EAAE;EAClB;EACA,KAAK;EACL,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB;EACA;EACA,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;EACzB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB;EACA,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EAC7B,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC;EACzB,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;EAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;EAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;EAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;EACxC,OAAO,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;EACvC,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;EAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;EAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;EACxC,OAAO;EACP,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;EAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;EAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;EAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;EACxC,OAAO;EACP,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;EAC1B,KAAK,MAAM;EACX,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;EACjC,KAAK;AACL;EACA,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EAClD,IAAI,KAAK,CAAC,SAAS,GAAG,yCAAyC,CAAC;EAChE,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;EACpB,IAAI,KAAK,CAAC,QAAQ,GAAG,YAAY;EACjC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EAC/B,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;EAC3C,KAAK,CAAC;EACN,IAAI,KAAK,CAAC,OAAO,GAAG,YAAY;EAChC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EAC/B,KAAK,CAAC;AACN;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAChE;EACA;EACA,IAAI,IAAI,WAAW,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;EAC3E,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;EAChD,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;EAC/C,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,GAAG;EAChB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;EAC1C,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC3D,MAAM,cAAc,CAAC,SAAS,GAAG,qCAAqC,CAAC;EACvE,MAAM,cAAc,CAAC,SAAS,GAAG,kBAAkB,CAAC;EACpD,MAAM,cAAc,CAAC,OAAO,GAAG,MAAM;EACrC,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;EAC7B,OAAO,CAAC;EACR,MAAM,cAAc,CAAC,WAAW,GAAG,MAAM;EACzC,QAAQ,cAAc,CAAC,SAAS,GAAG,2CAA2C,CAAC;EAC/E,OAAO,CAAC;EACR,MAAM,cAAc,CAAC,UAAU,GAAG,MAAM;EACxC,QAAQ,cAAc,CAAC,SAAS,GAAG,qCAAqC,CAAC;EACzE,OAAO,CAAC;AACR;EACA,MAAM,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS;EACrC,QAAQ,+CAA+C,CAAC;AACxD;EACA,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;EACnD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAC5C,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE;EAC7B,IAAI;EACJ,MAAM,IAAI,CAAC,WAAW,KAAK,IAAI;EAC/B,MAAM,IAAI,CAAC,aAAa,KAAK,IAAI;EACjC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU;EACzC,MAAM;EACN,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAChD,MAAM,GAAG,CAAC,EAAE,GAAG,yBAAyB,CAAC;EACzC,MAAM,GAAG,CAAC,SAAS,GAAG,yBAAyB,CAAC;EAChD,MAAM,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;EAC7B,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;EAC1B,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;EAC5B,OAAO,CAAC;EACR,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;EAC7B,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;EAClD,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,GAAG;EACjB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;EAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACpE,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC9C,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;EAChD,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;EACzB,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,kBAAkB,GAAG;EACvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;EAC1C,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EACzE,MAAM,MAAM,IAAI,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;EAChE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;EACvD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1D,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACpD,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM;EACnD,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;EAC7C,OAAO,EAAE,IAAI,CAAC,CAAC;EACf,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM;EACrD,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;EAC5B,OAAO,EAAE,IAAI,CAAC,CAAC;EACf,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;EAC3C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EACrD,IAAI,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC;EAC/B,IAAI,QAAQ,CAAC,SAAS,GAAG,uCAAuC,CAAC;EACjE,IAAI,QAAQ,CAAC,OAAO,GAAG,YAAY,CAAC;EACpC,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;EAC7B,MAAM,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;EAC/B,MAAM,IAAI,KAAK,KAAK,YAAY,EAAE;EAClC,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;EAC9C,UAAU,IAAI,KAAK,KAAK,YAAY,CAAC,OAAO,EAAE;EAC9C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EACnE,WAAW;EACX,SAAS,MAAM;EACf,UAAU,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EACjE,SAAS;EACT,OAAO;EACP,KAAK;AACL;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;EACpB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;EACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACrC,KAAK,CAAC;AACN;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;EAC1C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;EAC5C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;EACrD,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;EAC3B,IAAI,QAAQ,CAAC,SAAS,GAAG,mCAAmC,CAAC;EAC7D,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;EAC3B,IAAI,IAAI,KAAK,KAAK,YAAY,EAAE;EAChC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EAC7D,KAAK;AACL;EACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;EACpB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;EACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;EACnC,KAAK,CAAC;AACN;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;EAC1C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;EACpC,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC9C,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC;AACvD;EACA,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;EAC1B,MAAM,GAAG,CAAC,SAAS,GAAG,yCAAyC,CAAC;EAChE,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;EACxC,KAAK,MAAM;EACX,MAAM,GAAG,CAAC,SAAS,GAAG,8CAA8C,CAAC;EACrE,KAAK;AACL;EACA,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC;EACvD,IAAI,GAAG,CAAC,OAAO,GAAG,MAAM;EACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;EAC9C,KAAK,CAAC;AACN;EACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;EACrC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EACrC;EACA,IAAI,GAAG,CAAC,OAAO,GAAG,YAAY,EAAE,CAAC;AACjC;EACA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;EACnC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC5B;EACA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EACrC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,KAAK,KAAK;EAClD,MAAM,MAAM,WAAW;EACvB,QAAQ,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;EAChF,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,WAAW,CAAC;EAC9C,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;EACtC,KAAK,CAAC,CAAC;AACP;EACA;EACA,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM;EAC5C,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;EAC1B,QAAQ,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;EAChD,OAAO,CAAC;EACR,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,GAAG,KAAK,EAAE;EACnD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;EACrB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;EACvC,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC;EAC7B,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,EAAE;EAC9B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;EAC7D,QAAQ,IAAI,GAAG,IAAI,CAAC;EACpB,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;EACjC,QAAQ,MAAM,OAAO,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EACzD,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;EAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACtC;EACA;EACA,UAAU,IAAI,IAAI,KAAK,KAAK,EAAE;EAC9B,YAAY;EACZ,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;EAClC,cAAc,OAAO,IAAI,KAAK,QAAQ;EACtC,cAAc,OAAO,IAAI,KAAK,SAAS;EACvC,cAAc,IAAI,YAAY,MAAM;EACpC,cAAc;EACd,cAAc,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;EACzC,cAAc,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EAC7D,cAAc,IAAI,CAAC,aAAa,GAAG,SAAS,KAAK,KAAK,CAAC;EACvD,aAAa;EACb,WAAW;EACX,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;EAC5B,UAAU,YAAY,GAAG,IAAI,CAAC;EAC9B,UAAU,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAChD;EACA,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;EACnC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;EACpD,WAAW,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;EAC/C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;EACtD,WAAW,MAAM,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;EAChD,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;EACrD,WAAW,MAAM,IAAI,IAAI,YAAY,MAAM,EAAE;EAC7C;EACA,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;EACpE;EACA,cAAc,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;EAC9C,gBAAgB,MAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;EAC3E,gBAAgB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;EACjE,gBAAgB,IAAI,YAAY,KAAK,IAAI,EAAE;EAC3C,kBAAkB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EACvE,kBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EACjD,kBAAkB,YAAY;EAC9B,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC;EACtE,iBAAiB,MAAM;EACvB,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;EAClE,iBAAiB;EACjB,eAAe,MAAM;EACrB,gBAAgB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;EACrE,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC/C,gBAAgB,YAAY;EAC5B,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC;EACpE,eAAe;EACf,aAAa;EACb,WAAW,MAAM;EACjB,YAAY,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EAC5E,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,OAAO,YAAY,CAAC;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;EACjC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;EAC1D,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC7C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;EAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EAC/D,OAAO;EACP,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC3C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;EAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;EAC/D,OAAO;EACP,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EACxC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;EAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;EACvE,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;EACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACxD;EACA,IAAI;EACJ,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;EACtB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;EAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;EACnC,MAAM;EACN,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;EAC7D,KAAK;EACL,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;EAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;EACpC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE;EAClD,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC;AAC7B;EACA;EACA,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;EAC5C,IAAI,KAAK,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C;EACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAChC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;EAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAChC,SAAS;EACT,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;EACnC,UAAU,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,SAAS,MAAM;EACf,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;EACnC,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,OAAO,UAAU,CAAC;EACtB,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,aAAa,GAAG;EAClB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACtC;EACA,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;EAC7C,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;EAC1E,KAAK;EACL,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW;EACrC,MAAM,SAAS,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC7E,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,UAAU,GAAG;EACf,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;EACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACzD,MAAM,IAAI,CAAC,iBAAiB;EAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK;EACpC,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI;EACnC,QAAQ,OAAO;EACf,OAAO,CAAC;EACR,KAAK;EACL,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;EACH;;EChyBA;EACA;EACA;gBACO,MAAM,KAAK,CAAC;EACnB;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,SAAS,EAAE,cAAc,EAAE;EACzC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;EAC/B,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,KAAK,CAAC;AAClD;EACA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACf,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;EACf,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;EACrB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB;EACA;EACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC;EACzC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3C,GAAG;AACH;EACA;EACA;EACA;EACA;EACA,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;EACpB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,CAAC,OAAO,EAAE;EACnB,IAAI,IAAI,OAAO,YAAY,OAAO,EAAE;EACpC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;EACpC,QAAQ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;EACtD,OAAO;EACP,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EACtC,KAAK,MAAM;EACX;EACA;EACA,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC;EACrC,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,MAAM,EAAE;EACf,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;EAC9B,MAAM,MAAM,GAAG,IAAI,CAAC;EACpB,KAAK;AACL;EACA,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;EACzB,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;EAC7C,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;EAC3C,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;EAC3D,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC;AACzD;EACA,MAAM,IAAI,IAAI,GAAG,CAAC;EAClB,QAAQ,GAAG,GAAG,CAAC,CAAC;AAChB;EACA,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,MAAM,EAAE;EACzC,QAAQ,IAAI,MAAM,GAAG,KAAK;EAC1B,UAAU,KAAK,GAAG,IAAI,CAAC;AACvB;EACA,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;EAC5C,UAAU,KAAK,GAAG,KAAK,CAAC;EACxB,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE;EACtD,UAAU,MAAM,GAAG,IAAI,CAAC;EACxB,SAAS;AACT;EACA,QAAQ,IAAI,MAAM,EAAE;EACpB,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;EAChC,SAAS,MAAM;EACf,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;EACxB,SAAS;AACT;EACA,QAAQ,IAAI,KAAK,EAAE;EACnB,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;EAChC,SAAS,MAAM;EACf,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;EACvB,SAAS;EACT,OAAO,MAAM;EACb,QAAQ,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;EAC9B,QAAQ,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE;EACrD,UAAU,GAAG,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;EAClD,SAAS;EACT,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;EAChC,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;EAC7B,SAAS;AACT;EACA,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;EACtB,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE;EACpD,UAAU,IAAI,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;EACjD,SAAS;EACT,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;EACjC,UAAU,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;EAC9B,SAAS;EACT,OAAO;AACP;EACA,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;EAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;EACxC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;EAC9C,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;EAC1B,KAAK,MAAM;EACX,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;EAClB,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,IAAI,GAAG;EACT,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACvB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;EAChC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;EAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;EAC3C,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,OAAO,GAAG;EACZ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAClD,GAAG;EACH;;ECrIA,IAAI,UAAU,GAAG,KAAK,CAAC;EACvB,IAAI,UAAU,CAAC;AACf;EACO,MAAMC,uBAAqB,GAAG,qCAAqC,CAAC;AAC3E;EACA;EACA;EACA;oBACO,MAAM,SAAS,CAAC;EACvB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,QAAQ,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE;EACxD,IAAI,UAAU,GAAG,KAAK,CAAC;EACvB,IAAI,UAAU,GAAG,gBAAgB,CAAC;EAClC,IAAI,IAAI,WAAW,GAAG,gBAAgB,CAAC;EACvC,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;EACjC,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;EAChD,KAAK;EACL,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;EAC9C,IAAI,OAAO,UAAU,CAAC;EACtB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;EAChD,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;EAClC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;EACjE,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;EACjE,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;EACxD,IAAI;EACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;EAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK,SAAS;EAC5C,MAAM;EACN,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;EAC9D,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,eAAe,GAAG,MAAM,CAAC;EACjC,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;AACzB;EACA,IAAI;EACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;EAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK,SAAS;EAC5C,MAAM;EACN;EACA;EACA;AACA;EACA;EACA,MAAM,eAAe,GAAG,SAAS,CAAC;AAClC;EACA;EACA;EACA,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,QAAQ,CAAC;EAClE,KAIK;AACL;EACA,IAAI,IAAI,YAAY,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;EACzD,IAAI,IAAI,SAAS,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE;EAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC;EAC3C,KAAK;AACL;EACA,IAAI,SAAS,CAAC,WAAW;EACzB,MAAM,MAAM;EACZ,MAAM,OAAO;EACb,MAAM,gBAAgB;EACtB,MAAM,eAAe;EACrB,MAAM,YAAY;EAClB,MAAM,IAAI;EACV,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,WAAW;EACpB,IAAI,MAAM;EACV,IAAI,OAAO;EACX,IAAI,gBAAgB;EACpB,IAAI,eAAe;EACnB,IAAI,YAAY;EAChB,IAAI,IAAI;EACR,IAAI;EACJ,IAAI,MAAM,GAAG,GAAG,UAAU,OAAO,EAAE;EACnC,MAAM,OAAO,CAAC,KAAK;EACnB,QAAQ,IAAI,GAAG,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;EAC9D,QAAQA,uBAAqB;EAC7B,OAAO,CAAC;EACR,KAAK,CAAC;AACN;EACA,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;EAC1D,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACnD;EACA,IAAI,IAAI,aAAa,KAAK,SAAS,EAAE;EACrC;EACA,MAAM;EACN,QAAQ,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,OAAO;EACpD,QAAQ,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;EACrD,QAAQ;EACR,QAAQ,GAAG;EACX,UAAU,8BAA8B;EACxC,YAAY,MAAM;EAClB,YAAY,IAAI;EAChB,YAAY,sBAAsB;EAClC,YAAY,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;EAC1C,YAAY,QAAQ;EACpB,YAAY,OAAO,CAAC,MAAM,CAAC;EAC3B,YAAY,KAAK;EACjB,SAAS,CAAC;EACV,QAAQ,UAAU,GAAG,IAAI,CAAC;EAC1B,OAAO,MAAM,IAAI,UAAU,KAAK,QAAQ,IAAI,eAAe,KAAK,SAAS,EAAE;EAC3E,QAAQ,IAAI,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EAChD,QAAQ,SAAS,CAAC,KAAK;EACvB,UAAU,OAAO,CAAC,MAAM,CAAC;EACzB,UAAU,gBAAgB,CAAC,eAAe,CAAC;EAC3C,UAAU,IAAI;EACd,SAAS,CAAC;EACV,OAAO;EACP,KAAK,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;EAClD;EACA,MAAM,GAAG;EACT,QAAQ,6BAA6B;EACrC,UAAU,MAAM;EAChB,UAAU,eAAe;EACzB,UAAU,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACpD,UAAU,cAAc;EACxB,UAAU,UAAU;EACpB,UAAU,KAAK;EACf,UAAU,OAAO,CAAC,MAAM,CAAC;EACzB,UAAU,GAAG;EACb,OAAO,CAAC;EACR,MAAM,UAAU,GAAG,IAAI,CAAC;EACxB,KAAK;EACL,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE;EACzB,IAAI,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;AAC/B;EACA,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;EAC3B,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;EAC3B,QAAQ,OAAO,MAAM,CAAC;EACtB,OAAO;EACP,MAAM,IAAI,MAAM,YAAY,OAAO,EAAE;EACrC,QAAQ,OAAO,SAAS,CAAC;EACzB,OAAO;EACP,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;EACpC,QAAQ,OAAO,QAAQ,CAAC;EACxB,OAAO;EACP,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;EACpC,QAAQ,OAAO,QAAQ,CAAC;EACxB,OAAO;EACP,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;EACjC,QAAQ,OAAO,OAAO,CAAC;EACvB,OAAO;EACP,MAAM,IAAI,MAAM,YAAY,IAAI,EAAE;EAClC,QAAQ,OAAO,MAAM,CAAC;EACtB,OAAO;EACP,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE;EACzC,QAAQ,OAAO,KAAK,CAAC;EACrB,OAAO;EACP,MAAM,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE;EAC5C,QAAQ,OAAO,QAAQ,CAAC;EACxB,OAAO;EACP,MAAM,OAAO,QAAQ,CAAC;EACtB,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;EAClC,MAAM,OAAO,QAAQ,CAAC;EACtB,KAAK,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;EACnC,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;EAClC,MAAM,OAAO,QAAQ,CAAC;EACtB,KAAK,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;EACnC,MAAM,OAAO,WAAW,CAAC;EACzB,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;EAC9C,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;EAC9E,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/E;EACA,IAAI,MAAM,oBAAoB,GAAG,CAAC,CAAC;EACnC,IAAI,MAAM,qBAAqB,GAAG,CAAC,CAAC;AACpC;EACA,IAAI,IAAI,GAAG,CAAC;EACZ,IAAI,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE;EAC9C,MAAM,GAAG;EACT,QAAQ,MAAM;EACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;EAC7D,QAAQ,4CAA4C;EACpD,QAAQ,WAAW,CAAC,UAAU;EAC9B,QAAQ,QAAQ,CAAC;EACjB,KAAK,MAAM;EACX,MAAM,YAAY,CAAC,QAAQ,IAAI,qBAAqB;EACpD,MAAM,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ;EAClD,MAAM;EACN,MAAM,GAAG;EACT,QAAQ,MAAM;EACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;EAC7D,QAAQ,sDAAsD;EAC9D,QAAQ,SAAS,CAAC,aAAa;EAC/B,UAAU,YAAY,CAAC,IAAI;EAC3B,UAAU,YAAY,CAAC,YAAY;EACnC,UAAU,EAAE;EACZ,SAAS,CAAC;EACV,KAAK,MAAM,IAAI,WAAW,CAAC,QAAQ,IAAI,oBAAoB,EAAE;EAC7D,MAAM,GAAG;EACT,QAAQ,kBAAkB;EAC1B,QAAQ,WAAW,CAAC,YAAY;EAChC,QAAQ,IAAI;EACZ,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EAC1D,KAAK,MAAM;EACX,MAAM,GAAG;EACT,QAAQ,+BAA+B;EACvC,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC7C,QAAQ,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EAC9C,KAAK;AACL;EACA,IAAI,OAAO,CAAC,KAAK;EACjB,MAAM,8BAA8B,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG;EACzD,MAAMA,uBAAqB;EAC3B,KAAK,CAAC;EACN,IAAI,UAAU,GAAG,IAAI,CAAC;EACtB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE;EACjE,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;EAClB,IAAI,IAAI,YAAY,GAAG,EAAE,CAAC;EAC1B,IAAI,IAAI,gBAAgB,GAAG,EAAE,CAAC;EAC9B,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;EACjD,IAAI,IAAI,UAAU,GAAG,SAAS,CAAC;EAC/B,IAAI,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE;EAC9B,MAAM,IAAI,QAAQ,CAAC;EACnB,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;EACpE,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa;EAC9C,UAAU,MAAM;EAChB,UAAU,OAAO,CAAC,EAAE,CAAC;EACrB,UAAU,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC;EACtC,SAAS,CAAC;EACV,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE;EACnC,UAAU,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;EAC7C,UAAU,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC;EACzC,UAAU,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC;EAChC,UAAU,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;EACzC,SAAS;EACT,OAAO,MAAM;EACb,QAAQ,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;EAC9D,UAAU,UAAU,GAAG,EAAE,CAAC;EAC1B,SAAS;EACT,QAAQ,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;EAC7D,QAAQ,IAAI,GAAG,GAAG,QAAQ,EAAE;EAC5B,UAAU,YAAY,GAAG,EAAE,CAAC;EAC5B,UAAU,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;EAC7C,UAAU,GAAG,GAAG,QAAQ,CAAC;EACzB,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI,OAAO;EACX,MAAM,YAAY,EAAE,YAAY;EAChC,MAAM,IAAI,EAAE,gBAAgB;EAC5B,MAAM,QAAQ,EAAE,GAAG;EACnB,MAAM,UAAU,EAAE,UAAU;EAC5B,KAAK,CAAC;EACN,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,4BAA4B,EAAE;EAC5E,IAAI,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,eAAe,CAAC;EAChD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC1C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EACtC,QAAQ,GAAG,IAAI,IAAI,CAAC;EACpB,OAAO;EACP,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;EAC/B,KAAK;EACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAC9C,MAAM,GAAG,IAAI,IAAI,CAAC;EAClB,KAAK;EACL,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC;EACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAC9C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAChD,QAAQ,GAAG,IAAI,IAAI,CAAC;EACpB,OAAO;EACP,MAAM,GAAG,IAAI,KAAK,CAAC;EACnB,KAAK;EACL,IAAI,OAAO,GAAG,GAAG,MAAM,CAAC;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE;EACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;EAClC,OAAO,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;EAClD,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;EAC7B,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;EACnC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC;EACxC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC;AACxC;EACA,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB;EACA;EACA,IAAI,IAAI,CAAC,CAAC;EACV,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACpC,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtB,KAAK;AACL;EACA;EACA,IAAI,IAAI,CAAC,CAAC;EACV,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACpC,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACvB,KAAK;AACL;EACA;EACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACpC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACtC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;EAChD,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,SAAS,MAAM;EACf,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;EACjC,YAAY,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACpC,YAAY,IAAI,CAAC,GAAG;EACpB,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAClC,cAAc,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAClC,aAAa;EACb,WAAW,CAAC;EACZ,SAAS;EACT,OAAO;EACP,KAAK;AACL;EACA,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;EACtC,GAAG;EACH;;ACzZO,QAAM,SAAS,GAAQC,YAAY;AACnC,QAAM,WAAW,GAAQC,cAAc;AACvC,QAAM,YAAY,GAAQC,eAAe;AACzC,QAAM,MAAM,GAAiBC,SAAS;AACtC,QAAM,KAAK,GAAQC,QAAQ;AAC3B,QAAM,qBAAqB,GAAWC,wBAAyB;AAC/D,QAAM,SAAS,GAAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}