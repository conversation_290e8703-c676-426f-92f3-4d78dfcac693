/**
 * vis-data
 * http://visjs.org/
 *
 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
 *
 * @version 7.1.9
 * @date    2023-11-24T17:53:34.179Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{})}(this,(function(t){function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var o={exports:{}},i=function(t){return t&&t.Math===Math&&t},a=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof r&&r)||function(){return this}()||r||Function("return this")(),u=function(t){try{return!!t()}catch(t){return!0}},s=!u((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),c=s,f=Function.prototype,l=f.apply,h=f.call,p="object"==typeof Reflect&&Reflect.apply||(c?h.bind(l):function(){return h.apply(l,arguments)}),v=s,d=Function.prototype,y=d.call,g=v&&d.bind.bind(y,y),m=v?g:function(t){return function(){return y.apply(t,arguments)}},b=m,w=b({}.toString),_=b("".slice),T=function(t){return _(w(t),8,-1)},E=T,O=m,S=function(t){if("Function"===E(t))return O(t)},x="object"==typeof document&&document.all,k={all:x,IS_HTMLDDA:void 0===x&&void 0!==x},j=k.all,A=k.IS_HTMLDDA?function(t){return"function"==typeof t||t===j}:function(t){return"function"==typeof t},P={},I=!u((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),D=s,L=Function.prototype.call,C=D?L.bind(L):function(){return L.apply(L,arguments)},R={},M={}.propertyIsEnumerable,N=Object.getOwnPropertyDescriptor,F=N&&!M.call({1:2},1);R.f=F?function(t){var e=N(this,t);return!!e&&e.enumerable}:M;var z,U,q=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},W=u,Y=T,G=Object,X=m("".split),V=W((function(){return!G("z").propertyIsEnumerable(0)}))?function(t){return"String"===Y(t)?X(t,""):G(t)}:G,B=function(t){return null==t},H=B,K=TypeError,J=function(t){if(H(t))throw new K("Can't call method on "+t);return t},$=V,Q=J,Z=function(t){return $(Q(t))},tt=A,et=k.all,rt=k.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:tt(t)||t===et}:function(t){return"object"==typeof t?null!==t:tt(t)},nt={},ot=nt,it=a,at=A,ut=function(t){return at(t)?t:void 0},st=function(t,e){return arguments.length<2?ut(ot[t])||ut(it[t]):ot[t]&&ot[t][e]||it[t]&&it[t][e]},ct=m({}.isPrototypeOf),ft="undefined"!=typeof navigator&&String(navigator.userAgent)||"",lt=a,ht=ft,pt=lt.process,vt=lt.Deno,dt=pt&&pt.versions||vt&&vt.version,yt=dt&&dt.v8;yt&&(U=(z=yt.split("."))[0]>0&&z[0]<4?1:+(z[0]+z[1])),!U&&ht&&(!(z=ht.match(/Edge\/(\d+)/))||z[1]>=74)&&(z=ht.match(/Chrome\/(\d+)/))&&(U=+z[1]);var gt=U,mt=gt,bt=u,wt=a.String,_t=!!Object.getOwnPropertySymbols&&!bt((function(){var t=Symbol("symbol detection");return!wt(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&mt&&mt<41})),Tt=_t&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Et=st,Ot=A,St=ct,xt=Object,kt=Tt?function(t){return"symbol"==typeof t}:function(t){var e=Et("Symbol");return Ot(e)&&St(e.prototype,xt(t))},jt=String,At=function(t){try{return jt(t)}catch(t){return"Object"}},Pt=A,It=At,Dt=TypeError,Lt=function(t){if(Pt(t))return t;throw new Dt(It(t)+" is not a function")},Ct=Lt,Rt=B,Mt=function(t,e){var r=t[e];return Rt(r)?void 0:Ct(r)},Nt=C,Ft=A,zt=rt,Ut=TypeError,qt={exports:{}},Wt=a,Yt=Object.defineProperty,Gt=function(t,e){try{Yt(Wt,t,{value:e,configurable:!0,writable:!0})}catch(r){Wt[t]=e}return e},Xt="__core-js_shared__",Vt=a[Xt]||Gt(Xt,{}),Bt=Vt;(qt.exports=function(t,e){return Bt[t]||(Bt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.33.2",mode:"pure",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.33.2/LICENSE",source:"https://github.com/zloirock/core-js"});var Ht=qt.exports,Kt=J,Jt=Object,$t=function(t){return Jt(Kt(t))},Qt=$t,Zt=m({}.hasOwnProperty),te=Object.hasOwn||function(t,e){return Zt(Qt(t),e)},ee=m,re=0,ne=Math.random(),oe=ee(1..toString),ie=function(t){return"Symbol("+(void 0===t?"":t)+")_"+oe(++re+ne,36)},ae=Ht,ue=te,se=ie,ce=_t,fe=Tt,le=a.Symbol,he=ae("wks"),pe=fe?le.for||le:le&&le.withoutSetter||se,ve=function(t){return ue(he,t)||(he[t]=ce&&ue(le,t)?le[t]:pe("Symbol."+t)),he[t]},de=C,ye=rt,ge=kt,me=Mt,be=function(t,e){var r,n;if("string"===e&&Ft(r=t.toString)&&!zt(n=Nt(r,t)))return n;if(Ft(r=t.valueOf)&&!zt(n=Nt(r,t)))return n;if("string"!==e&&Ft(r=t.toString)&&!zt(n=Nt(r,t)))return n;throw new Ut("Can't convert object to primitive value")},we=TypeError,_e=ve("toPrimitive"),Te=function(t,e){if(!ye(t)||ge(t))return t;var r,n=me(t,_e);if(n){if(void 0===e&&(e="default"),r=de(n,t,e),!ye(r)||ge(r))return r;throw new we("Can't convert object to primitive value")}return void 0===e&&(e="number"),be(t,e)},Ee=kt,Oe=function(t){var e=Te(t,"string");return Ee(e)?e:e+""},Se=rt,xe=a.document,ke=Se(xe)&&Se(xe.createElement),je=function(t){return ke?xe.createElement(t):{}},Ae=je,Pe=!I&&!u((function(){return 7!==Object.defineProperty(Ae("div"),"a",{get:function(){return 7}}).a})),Ie=I,De=C,Le=R,Ce=q,Re=Z,Me=Oe,Ne=te,Fe=Pe,ze=Object.getOwnPropertyDescriptor;P.f=Ie?ze:function(t,e){if(t=Re(t),e=Me(e),Fe)try{return ze(t,e)}catch(t){}if(Ne(t,e))return Ce(!De(Le.f,t,e),t[e])};var Ue=u,qe=A,We=/#|\.prototype\./,Ye=function(t,e){var r=Xe[Ge(t)];return r===Be||r!==Ve&&(qe(e)?Ue(e):!!e)},Ge=Ye.normalize=function(t){return String(t).replace(We,".").toLowerCase()},Xe=Ye.data={},Ve=Ye.NATIVE="N",Be=Ye.POLYFILL="P",He=Ye,Ke=Lt,Je=s,$e=S(S.bind),Qe=function(t,e){return Ke(t),void 0===e?t:Je?$e(t,e):function(){return t.apply(e,arguments)}},Ze={},tr=I&&u((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),er=rt,rr=String,nr=TypeError,or=function(t){if(er(t))return t;throw new nr(rr(t)+" is not an object")},ir=I,ar=Pe,ur=tr,sr=or,cr=Oe,fr=TypeError,lr=Object.defineProperty,hr=Object.getOwnPropertyDescriptor,pr="enumerable",vr="configurable",dr="writable";Ze.f=ir?ur?function(t,e,r){if(sr(t),e=cr(e),sr(r),"function"==typeof t&&"prototype"===e&&"value"in r&&dr in r&&!r[dr]){var n=hr(t,e);n&&n[dr]&&(t[e]=r.value,r={configurable:vr in r?r[vr]:n[vr],enumerable:pr in r?r[pr]:n[pr],writable:!1})}return lr(t,e,r)}:lr:function(t,e,r){if(sr(t),e=cr(e),sr(r),ar)try{return lr(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new fr("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var yr=Ze,gr=q,mr=I?function(t,e,r){return yr.f(t,e,gr(1,r))}:function(t,e,r){return t[e]=r,t},br=a,wr=p,_r=S,Tr=A,Er=P.f,Or=He,Sr=nt,xr=Qe,kr=mr,jr=te,Ar=function(t){var e=function(r,n,o){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,n)}return new t(r,n,o)}return wr(t,this,arguments)};return e.prototype=t.prototype,e},Pr=function(t,e){var r,n,o,i,a,u,s,c,f,l=t.target,h=t.global,p=t.stat,v=t.proto,d=h?br:p?br[l]:(br[l]||{}).prototype,y=h?Sr:Sr[l]||kr(Sr,l,{})[l],g=y.prototype;for(i in e)n=!(r=Or(h?i:l+(p?".":"#")+i,t.forced))&&d&&jr(d,i),u=y[i],n&&(s=t.dontCallGetSet?(f=Er(d,i))&&f.value:d[i]),a=n&&s?s:e[i],n&&typeof u==typeof a||(c=t.bind&&n?xr(a,br):t.wrap&&n?Ar(a):v&&Tr(a)?_r(a):a,(t.sham||a&&a.sham||u&&u.sham)&&kr(c,"sham",!0),kr(y,i,c),v&&(jr(Sr,o=l+"Prototype")||kr(Sr,o,{}),kr(Sr[o],i,a),t.real&&g&&(r||!g[i])&&kr(g,i,a)))},Ir=Pr,Dr=I,Lr=Ze.f;Ir({target:"Object",stat:!0,forced:Object.defineProperty!==Lr,sham:!Dr},{defineProperty:Lr});var Cr=nt.Object,Rr=o.exports=function(t,e,r){return Cr.defineProperty(t,e,r)};Cr.defineProperty.sham&&(Rr.sham=!0);var Mr=o.exports,Nr=Mr,Fr=n(Nr),zr=T,Ur=Array.isArray||function(t){return"Array"===zr(t)},qr=Math.ceil,Wr=Math.floor,Yr=Math.trunc||function(t){var e=+t;return(e>0?Wr:qr)(e)},Gr=function(t){var e=+t;return e!=e||0===e?0:Yr(e)},Xr=Gr,Vr=Math.min,Br=function(t){return t>0?Vr(Xr(t),9007199254740991):0},Hr=function(t){return Br(t.length)},Kr=TypeError,Jr=function(t){if(t>9007199254740991)throw Kr("Maximum allowed index exceeded");return t},$r=Oe,Qr=Ze,Zr=q,tn=function(t,e,r){var n=$r(e);n in t?Qr.f(t,n,Zr(0,r)):t[n]=r},en={};en[ve("toStringTag")]="z";var rn="[object z]"===String(en),nn=rn,on=A,an=T,un=ve("toStringTag"),sn=Object,cn="Arguments"===an(function(){return arguments}()),fn=nn?an:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=sn(t),un))?r:cn?an(e):"Object"===(n=an(e))&&on(e.callee)?"Arguments":n},ln=A,hn=Vt,pn=m(Function.toString);ln(hn.inspectSource)||(hn.inspectSource=function(t){return pn(t)});var vn=hn.inspectSource,dn=m,yn=u,gn=A,mn=fn,bn=vn,wn=function(){},_n=[],Tn=st("Reflect","construct"),En=/^\s*(?:class|function)\b/,On=dn(En.exec),Sn=!En.test(wn),xn=function(t){if(!gn(t))return!1;try{return Tn(wn,_n,t),!0}catch(t){return!1}},kn=function(t){if(!gn(t))return!1;switch(mn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Sn||!!On(En,bn(t))}catch(t){return!0}};kn.sham=!0;var jn=!Tn||yn((function(){var t;return xn(xn.call)||!xn(Object)||!xn((function(){t=!0}))||t}))?kn:xn,An=Ur,Pn=jn,In=rt,Dn=ve("species"),Ln=Array,Cn=function(t){var e;return An(t)&&(e=t.constructor,(Pn(e)&&(e===Ln||An(e.prototype))||In(e)&&null===(e=e[Dn]))&&(e=void 0)),void 0===e?Ln:e},Rn=function(t,e){return new(Cn(t))(0===e?0:e)},Mn=u,Nn=gt,Fn=ve("species"),zn=function(t){return Nn>=51||!Mn((function(){var e=[];return(e.constructor={})[Fn]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Un=Pr,qn=u,Wn=Ur,Yn=rt,Gn=$t,Xn=Hr,Vn=Jr,Bn=tn,Hn=Rn,Kn=zn,Jn=gt,$n=ve("isConcatSpreadable"),Qn=Jn>=51||!qn((function(){var t=[];return t[$n]=!1,t.concat()[0]!==t})),Zn=function(t){if(!Yn(t))return!1;var e=t[$n];return void 0!==e?!!e:Wn(t)};Un({target:"Array",proto:!0,arity:1,forced:!Qn||!Kn("concat")},{concat:function(t){var e,r,n,o,i,a=Gn(this),u=Hn(a,0),s=0;for(e=-1,n=arguments.length;e<n;e++)if(Zn(i=-1===e?a:arguments[e]))for(o=Xn(i),Vn(s+o),r=0;r<o;r++,s++)r in i&&Bn(u,s,i[r]);else Vn(s+1),Bn(u,s++,i);return u.length=s,u}});var to=fn,eo=String,ro=function(t){if("Symbol"===to(t))throw new TypeError("Cannot convert a Symbol value to a string");return eo(t)},no={},oo=Gr,io=Math.max,ao=Math.min,uo=function(t,e){var r=oo(t);return r<0?io(r+e,0):ao(r,e)},so=Z,co=uo,fo=Hr,lo=function(t){return function(e,r,n){var o,i=so(e),a=fo(i),u=co(n,a);if(t&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===r)return t||u||0;return!t&&-1}},ho={includes:lo(!0),indexOf:lo(!1)},po={},vo=te,yo=Z,go=ho.indexOf,mo=po,bo=m([].push),wo=function(t,e){var r,n=yo(t),o=0,i=[];for(r in n)!vo(mo,r)&&vo(n,r)&&bo(i,r);for(;e.length>o;)vo(n,r=e[o++])&&(~go(i,r)||bo(i,r));return i},_o=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],To=wo,Eo=_o,Oo=Object.keys||function(t){return To(t,Eo)},So=I,xo=tr,ko=Ze,jo=or,Ao=Z,Po=Oo;no.f=So&&!xo?Object.defineProperties:function(t,e){jo(t);for(var r,n=Ao(e),o=Po(e),i=o.length,a=0;i>a;)ko.f(t,r=o[a++],n[r]);return t};var Io,Do=st("document","documentElement"),Lo=ie,Co=Ht("keys"),Ro=function(t){return Co[t]||(Co[t]=Lo(t))},Mo=or,No=no,Fo=_o,zo=po,Uo=Do,qo=je,Wo="prototype",Yo="script",Go=Ro("IE_PROTO"),Xo=function(){},Vo=function(t){return"<"+Yo+">"+t+"</"+Yo+">"},Bo=function(t){t.write(Vo("")),t.close();var e=t.parentWindow.Object;return t=null,e},Ho=function(){try{Io=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;Ho="undefined"!=typeof document?document.domain&&Io?Bo(Io):(e=qo("iframe"),r="java"+Yo+":",e.style.display="none",Uo.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(Vo("document.F=Object")),t.close(),t.F):Bo(Io);for(var n=Fo.length;n--;)delete Ho[Wo][Fo[n]];return Ho()};zo[Go]=!0;var Ko=Object.create||function(t,e){var r;return null!==t?(Xo[Wo]=Mo(t),r=new Xo,Xo[Wo]=null,r[Go]=t):r=Ho(),void 0===e?r:No.f(r,e)},Jo={},$o=wo,Qo=_o.concat("length","prototype");Jo.f=Object.getOwnPropertyNames||function(t){return $o(t,Qo)};var Zo={},ti=uo,ei=Hr,ri=tn,ni=Array,oi=Math.max,ii=function(t,e,r){for(var n=ei(t),o=ti(e,n),i=ti(void 0===r?n:r,n),a=ni(oi(i-o,0)),u=0;o<i;o++,u++)ri(a,u,t[o]);return a.length=u,a},ai=T,ui=Z,si=Jo.f,ci=ii,fi="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];Zo.f=function(t){return fi&&"Window"===ai(t)?function(t){try{return si(t)}catch(t){return ci(fi)}}(t):si(ui(t))};var li={};li.f=Object.getOwnPropertySymbols;var hi=mr,pi=function(t,e,r,n){return n&&n.enumerable?t[e]=r:hi(t,e,r),t},vi=Ze,di=function(t,e,r){return vi.f(t,e,r)},yi={},gi=ve;yi.f=gi;var mi,bi,wi,_i=nt,Ti=te,Ei=yi,Oi=Ze.f,Si=function(t){var e=_i.Symbol||(_i.Symbol={});Ti(e,t)||Oi(e,t,{value:Ei.f(t)})},xi=C,ki=st,ji=ve,Ai=pi,Pi=function(){var t=ki("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,n=ji("toPrimitive");e&&!e[n]&&Ai(e,n,(function(t){return xi(r,this)}),{arity:1})},Ii=fn,Di=rn?{}.toString:function(){return"[object "+Ii(this)+"]"},Li=rn,Ci=Ze.f,Ri=mr,Mi=te,Ni=Di,Fi=ve("toStringTag"),zi=function(t,e,r,n){if(t){var o=r?t:t.prototype;Mi(o,Fi)||Ci(o,Fi,{configurable:!0,value:e}),n&&!Li&&Ri(o,"toString",Ni)}},Ui=A,qi=a.WeakMap,Wi=Ui(qi)&&/native code/.test(String(qi)),Yi=a,Gi=rt,Xi=mr,Vi=te,Bi=Vt,Hi=Ro,Ki=po,Ji="Object already initialized",$i=Yi.TypeError,Qi=Yi.WeakMap;if(Wi||Bi.state){var Zi=Bi.state||(Bi.state=new Qi);Zi.get=Zi.get,Zi.has=Zi.has,Zi.set=Zi.set,mi=function(t,e){if(Zi.has(t))throw new $i(Ji);return e.facade=t,Zi.set(t,e),e},bi=function(t){return Zi.get(t)||{}},wi=function(t){return Zi.has(t)}}else{var ta=Hi("state");Ki[ta]=!0,mi=function(t,e){if(Vi(t,ta))throw new $i(Ji);return e.facade=t,Xi(t,ta,e),e},bi=function(t){return Vi(t,ta)?t[ta]:{}},wi=function(t){return Vi(t,ta)}}var ea={set:mi,get:bi,has:wi,enforce:function(t){return wi(t)?bi(t):mi(t,{})},getterFor:function(t){return function(e){var r;if(!Gi(e)||(r=bi(e)).type!==t)throw new $i("Incompatible receiver, "+t+" required");return r}}},ra=Qe,na=V,oa=$t,ia=Hr,aa=Rn,ua=m([].push),sa=function(t){var e=1===t,r=2===t,n=3===t,o=4===t,i=6===t,a=7===t,u=5===t||i;return function(s,c,f,l){for(var h,p,v=oa(s),d=na(v),y=ra(c,f),g=ia(d),m=0,b=l||aa,w=e?b(s,g):r||a?b(s,0):void 0;g>m;m++)if((u||m in d)&&(p=y(h=d[m],m,v),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return h;case 6:return m;case 2:ua(w,h)}else switch(t){case 4:return!1;case 7:ua(w,h)}return i?-1:n||o?o:w}},ca={forEach:sa(0),map:sa(1),filter:sa(2),some:sa(3),every:sa(4),find:sa(5),findIndex:sa(6),filterReject:sa(7)},fa=Pr,la=a,ha=C,pa=m,va=I,da=_t,ya=u,ga=te,ma=ct,ba=or,wa=Z,_a=Oe,Ta=ro,Ea=q,Oa=Ko,Sa=Oo,xa=Jo,ka=Zo,ja=li,Aa=P,Pa=Ze,Ia=no,Da=R,La=pi,Ca=di,Ra=Ht,Ma=po,Na=ie,Fa=ve,za=yi,Ua=Si,qa=Pi,Wa=zi,Ya=ea,Ga=ca.forEach,Xa=Ro("hidden"),Va="Symbol",Ba="prototype",Ha=Ya.set,Ka=Ya.getterFor(Va),Ja=Object[Ba],$a=la.Symbol,Qa=$a&&$a[Ba],Za=la.RangeError,tu=la.TypeError,eu=la.QObject,ru=Aa.f,nu=Pa.f,ou=ka.f,iu=Da.f,au=pa([].push),uu=Ra("symbols"),su=Ra("op-symbols"),cu=Ra("wks"),fu=!eu||!eu[Ba]||!eu[Ba].findChild,lu=function(t,e,r){var n=ru(Ja,e);n&&delete Ja[e],nu(t,e,r),n&&t!==Ja&&nu(Ja,e,n)},hu=va&&ya((function(){return 7!==Oa(nu({},"a",{get:function(){return nu(this,"a",{value:7}).a}})).a}))?lu:nu,pu=function(t,e){var r=uu[t]=Oa(Qa);return Ha(r,{type:Va,tag:t,description:e}),va||(r.description=e),r},vu=function(t,e,r){t===Ja&&vu(su,e,r),ba(t);var n=_a(e);return ba(r),ga(uu,n)?(r.enumerable?(ga(t,Xa)&&t[Xa][n]&&(t[Xa][n]=!1),r=Oa(r,{enumerable:Ea(0,!1)})):(ga(t,Xa)||nu(t,Xa,Ea(1,{})),t[Xa][n]=!0),hu(t,n,r)):nu(t,n,r)},du=function(t,e){ba(t);var r=wa(e),n=Sa(r).concat(bu(r));return Ga(n,(function(e){va&&!ha(yu,r,e)||vu(t,e,r[e])})),t},yu=function(t){var e=_a(t),r=ha(iu,this,e);return!(this===Ja&&ga(uu,e)&&!ga(su,e))&&(!(r||!ga(this,e)||!ga(uu,e)||ga(this,Xa)&&this[Xa][e])||r)},gu=function(t,e){var r=wa(t),n=_a(e);if(r!==Ja||!ga(uu,n)||ga(su,n)){var o=ru(r,n);return!o||!ga(uu,n)||ga(r,Xa)&&r[Xa][n]||(o.enumerable=!0),o}},mu=function(t){var e=ou(wa(t)),r=[];return Ga(e,(function(t){ga(uu,t)||ga(Ma,t)||au(r,t)})),r},bu=function(t){var e=t===Ja,r=ou(e?su:wa(t)),n=[];return Ga(r,(function(t){!ga(uu,t)||e&&!ga(Ja,t)||au(n,uu[t])})),n};da||($a=function(){if(ma(Qa,this))throw new tu("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?Ta(arguments[0]):void 0,e=Na(t),r=function(t){var n=void 0===this?la:this;n===Ja&&ha(r,su,t),ga(n,Xa)&&ga(n[Xa],e)&&(n[Xa][e]=!1);var o=Ea(1,t);try{hu(n,e,o)}catch(t){if(!(t instanceof Za))throw t;lu(n,e,o)}};return va&&fu&&hu(Ja,e,{configurable:!0,set:r}),pu(e,t)},La(Qa=$a[Ba],"toString",(function(){return Ka(this).tag})),La($a,"withoutSetter",(function(t){return pu(Na(t),t)})),Da.f=yu,Pa.f=vu,Ia.f=du,Aa.f=gu,xa.f=ka.f=mu,ja.f=bu,za.f=function(t){return pu(Fa(t),t)},va&&Ca(Qa,"description",{configurable:!0,get:function(){return Ka(this).description}})),fa({global:!0,constructor:!0,wrap:!0,forced:!da,sham:!da},{Symbol:$a}),Ga(Sa(cu),(function(t){Ua(t)})),fa({target:Va,stat:!0,forced:!da},{useSetter:function(){fu=!0},useSimple:function(){fu=!1}}),fa({target:"Object",stat:!0,forced:!da,sham:!va},{create:function(t,e){return void 0===e?Oa(t):du(Oa(t),e)},defineProperty:vu,defineProperties:du,getOwnPropertyDescriptor:gu}),fa({target:"Object",stat:!0,forced:!da},{getOwnPropertyNames:mu}),qa(),Wa($a,Va),Ma[Xa]=!0;var wu=_t&&!!Symbol.for&&!!Symbol.keyFor,_u=Pr,Tu=st,Eu=te,Ou=ro,Su=Ht,xu=wu,ku=Su("string-to-symbol-registry"),ju=Su("symbol-to-string-registry");_u({target:"Symbol",stat:!0,forced:!xu},{for:function(t){var e=Ou(t);if(Eu(ku,e))return ku[e];var r=Tu("Symbol")(e);return ku[e]=r,ju[r]=e,r}});var Au=Pr,Pu=te,Iu=kt,Du=At,Lu=wu,Cu=Ht("symbol-to-string-registry");Au({target:"Symbol",stat:!0,forced:!Lu},{keyFor:function(t){if(!Iu(t))throw new TypeError(Du(t)+" is not a symbol");if(Pu(Cu,t))return Cu[t]}});var Ru=m([].slice),Mu=Ur,Nu=A,Fu=T,zu=ro,Uu=m([].push),qu=Pr,Wu=st,Yu=p,Gu=C,Xu=m,Vu=u,Bu=A,Hu=kt,Ku=Ru,Ju=function(t){if(Nu(t))return t;if(Mu(t)){for(var e=t.length,r=[],n=0;n<e;n++){var o=t[n];"string"==typeof o?Uu(r,o):"number"!=typeof o&&"Number"!==Fu(o)&&"String"!==Fu(o)||Uu(r,zu(o))}var i=r.length,a=!0;return function(t,e){if(a)return a=!1,e;if(Mu(this))return e;for(var n=0;n<i;n++)if(r[n]===t)return e}}},$u=_t,Qu=String,Zu=Wu("JSON","stringify"),ts=Xu(/./.exec),es=Xu("".charAt),rs=Xu("".charCodeAt),ns=Xu("".replace),os=Xu(1..toString),is=/[\uD800-\uDFFF]/g,as=/^[\uD800-\uDBFF]$/,us=/^[\uDC00-\uDFFF]$/,ss=!$u||Vu((function(){var t=Wu("Symbol")("stringify detection");return"[null]"!==Zu([t])||"{}"!==Zu({a:t})||"{}"!==Zu(Object(t))})),cs=Vu((function(){return'"\\udf06\\ud834"'!==Zu("\udf06\ud834")||'"\\udead"'!==Zu("\udead")})),fs=function(t,e){var r=Ku(arguments),n=Ju(e);if(Bu(n)||void 0!==t&&!Hu(t))return r[1]=function(t,e){if(Bu(n)&&(e=Gu(n,this,Qu(t),e)),!Hu(e))return e},Yu(Zu,null,r)},ls=function(t,e,r){var n=es(r,e-1),o=es(r,e+1);return ts(as,t)&&!ts(us,o)||ts(us,t)&&!ts(as,n)?"\\u"+os(rs(t,0),16):t};Zu&&qu({target:"JSON",stat:!0,arity:3,forced:ss||cs},{stringify:function(t,e,r){var n=Ku(arguments),o=Yu(ss?fs:Zu,null,n);return cs&&"string"==typeof o?ns(o,is,ls):o}});var hs=li,ps=$t;Pr({target:"Object",stat:!0,forced:!_t||u((function(){hs.f(1)}))},{getOwnPropertySymbols:function(t){var e=hs.f;return e?e(ps(t)):[]}}),Si("asyncIterator"),Si("hasInstance"),Si("isConcatSpreadable"),Si("iterator"),Si("match"),Si("matchAll"),Si("replace"),Si("search"),Si("species"),Si("split");var vs=Pi;Si("toPrimitive"),vs();var ds=st,ys=zi;Si("toStringTag"),ys(ds("Symbol"),"Symbol"),Si("unscopables"),zi(a.JSON,"JSON",!0);var gs,ms,bs,ws=nt.Symbol,_s={},Ts=I,Es=te,Os=Function.prototype,Ss=Ts&&Object.getOwnPropertyDescriptor,xs=Es(Os,"name"),ks={EXISTS:xs,PROPER:xs&&"something"===function(){}.name,CONFIGURABLE:xs&&(!Ts||Ts&&Ss(Os,"name").configurable)},js=!u((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),As=te,Ps=A,Is=$t,Ds=js,Ls=Ro("IE_PROTO"),Cs=Object,Rs=Cs.prototype,Ms=Ds?Cs.getPrototypeOf:function(t){var e=Is(t);if(As(e,Ls))return e[Ls];var r=e.constructor;return Ps(r)&&e instanceof r?r.prototype:e instanceof Cs?Rs:null},Ns=u,Fs=A,zs=rt,Us=Ko,qs=Ms,Ws=pi,Ys=ve("iterator"),Gs=!1;[].keys&&("next"in(bs=[].keys())?(ms=qs(qs(bs)))!==Object.prototype&&(gs=ms):Gs=!0);var Xs=!zs(gs)||Ns((function(){var t={};return gs[Ys].call(t)!==t}));Fs((gs=Xs?{}:Us(gs))[Ys])||Ws(gs,Ys,(function(){return this}));var Vs={IteratorPrototype:gs,BUGGY_SAFARI_ITERATORS:Gs},Bs=Vs.IteratorPrototype,Hs=Ko,Ks=q,Js=zi,$s=_s,Qs=function(){return this},Zs=m,tc=Lt,ec=A,rc=String,nc=TypeError,oc=function(t,e,r){try{return Zs(tc(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}},ic=or,ac=function(t){if("object"==typeof t||ec(t))return t;throw new nc("Can't set "+rc(t)+" as a prototype")},uc=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=oc(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return ic(r),ac(n),e?t(r,n):r.__proto__=n,r}}():void 0),sc=Pr,cc=C,fc=ks,lc=function(t,e,r,n){var o=e+" Iterator";return t.prototype=Hs(Bs,{next:Ks(+!n,r)}),Js(t,o,!1,!0),$s[o]=Qs,t},hc=Ms,pc=zi,vc=pi,dc=_s,yc=Vs,gc=fc.PROPER,mc=yc.BUGGY_SAFARI_ITERATORS,bc=ve("iterator"),wc="keys",_c="values",Tc="entries",Ec=function(){return this},Oc=function(t,e,r,n,o,i,a){lc(r,e,n);var u,s,c,f=function(t){if(t===o&&d)return d;if(!mc&&t&&t in p)return p[t];switch(t){case wc:case _c:case Tc:return function(){return new r(this,t)}}return function(){return new r(this)}},l=e+" Iterator",h=!1,p=t.prototype,v=p[bc]||p["@@iterator"]||o&&p[o],d=!mc&&v||f(o),y="Array"===e&&p.entries||v;if(y&&(u=hc(y.call(new t)))!==Object.prototype&&u.next&&(pc(u,l,!0,!0),dc[l]=Ec),gc&&o===_c&&v&&v.name!==_c&&(h=!0,d=function(){return cc(v,this)}),o)if(s={values:f(_c),keys:i?d:f(wc),entries:f(Tc)},a)for(c in s)(mc||h||!(c in p))&&vc(p,c,s[c]);else sc({target:e,proto:!0,forced:mc||h},s);return a&&p[bc]!==d&&vc(p,bc,d,{name:o}),dc[e]=d,s},Sc=function(t,e){return{value:t,done:e}},xc=Z,kc=_s,jc=ea;Ze.f;var Ac=Oc,Pc=Sc,Ic="Array Iterator",Dc=jc.set,Lc=jc.getterFor(Ic);Ac(Array,"Array",(function(t,e){Dc(this,{type:Ic,target:xc(t),index:0,kind:e})}),(function(){var t=Lc(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=void 0,Pc(void 0,!0);switch(t.kind){case"keys":return Pc(r,!1);case"values":return Pc(e[r],!1)}return Pc([r,e[r]],!1)}),"values"),kc.Arguments=kc.Array;var Cc={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Rc=a,Mc=fn,Nc=mr,Fc=_s,zc=ve("toStringTag");for(var Uc in Cc){var qc=Rc[Uc],Wc=qc&&qc.prototype;Wc&&Mc(Wc)!==zc&&Nc(Wc,zc,Uc),Fc[Uc]=Fc.Array}var Yc=ws,Gc=ve,Xc=Ze.f,Vc=Gc("metadata"),Bc=Function.prototype;void 0===Bc[Vc]&&Xc(Bc,Vc,{value:null}),Si("asyncDispose"),Si("dispose"),Si("metadata");var Hc=Yc,Kc=m,Jc=st("Symbol"),$c=Jc.keyFor,Qc=Kc(Jc.prototype.valueOf),Zc=Jc.isRegisteredSymbol||function(t){try{return void 0!==$c(Qc(t))}catch(t){return!1}};Pr({target:"Symbol",stat:!0},{isRegisteredSymbol:Zc});for(var tf=Ht,ef=st,rf=m,nf=kt,of=ve,af=ef("Symbol"),uf=af.isWellKnownSymbol,sf=ef("Object","getOwnPropertyNames"),cf=rf(af.prototype.valueOf),ff=tf("wks"),lf=0,hf=sf(af),pf=hf.length;lf<pf;lf++)try{var vf=hf[lf];nf(af[vf])&&of(vf)}catch(t){}var df=function(t){if(uf&&uf(t))return!0;try{for(var e=cf(t),r=0,n=sf(ff),o=n.length;r<o;r++)if(ff[n[r]]==e)return!0}catch(t){}return!1};Pr({target:"Symbol",stat:!0,forced:!0},{isWellKnownSymbol:df}),Si("matcher"),Si("observable"),Pr({target:"Symbol",stat:!0,name:"isRegisteredSymbol"},{isRegistered:Zc}),Pr({target:"Symbol",stat:!0,name:"isWellKnownSymbol",forced:!0},{isWellKnown:df}),Si("metadataKey"),Si("patternMatch"),Si("replaceAll");var yf=Hc,gf=n(yf),mf=m,bf=Gr,wf=ro,_f=J,Tf=mf("".charAt),Ef=mf("".charCodeAt),Of=mf("".slice),Sf=function(t){return function(e,r){var n,o,i=wf(_f(e)),a=bf(r),u=i.length;return a<0||a>=u?t?"":void 0:(n=Ef(i,a))<55296||n>56319||a+1===u||(o=Ef(i,a+1))<56320||o>57343?t?Tf(i,a):n:t?Of(i,a,a+2):o-56320+(n-55296<<10)+65536}},xf={codeAt:Sf(!1),charAt:Sf(!0)}.charAt,kf=ro,jf=ea,Af=Oc,Pf=Sc,If="String Iterator",Df=jf.set,Lf=jf.getterFor(If);Af(String,"String",(function(t){Df(this,{type:If,string:kf(t),index:0})}),(function(){var t,e=Lf(this),r=e.string,n=e.index;return n>=r.length?Pf(void 0,!0):(t=xf(r,n),e.index+=t.length,Pf(t,!1))}));var Cf=yi.f("iterator"),Rf=Cf,Mf=n(Rf);function Nf(t){return Nf="function"==typeof gf&&"symbol"==typeof Mf?function(t){return typeof t}:function(t){return t&&"function"==typeof gf&&t.constructor===gf&&t!==gf.prototype?"symbol":typeof t},Nf(t)}var Ff=n(yi.f("toPrimitive"));function zf(t){var e=function(t,e){if("object"!==Nf(t)||null===t)return t;var r=t[Ff];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Nf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Nf(e)?e:String(e)}function Uf(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Fr(t,zf(n.key),n)}}function qf(t,e,r){return e&&Uf(t.prototype,e),r&&Uf(t,r),Fr(t,"prototype",{writable:!1}),t}function Wf(t,e,r){return(e=zf(e))in t?Fr(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Yf=m,Gf=Lt,Xf=rt,Vf=te,Bf=Ru,Hf=s,Kf=Function,Jf=Yf([].concat),$f=Yf([].join),Qf={},Zf=Hf?Kf.bind:function(t){var e=Gf(this),r=e.prototype,n=Bf(arguments,1),o=function(){var r=Jf(n,Bf(arguments));return this instanceof o?function(t,e,r){if(!Vf(Qf,e)){for(var n=[],o=0;o<e;o++)n[o]="a["+o+"]";Qf[e]=Kf("C,a","return new C("+$f(n,",")+")")}return Qf[e](t,r)}(e,r.length,r):e.apply(t,r)};return Xf(r)&&(o.prototype=r),o},tl=Zf;Pr({target:"Function",proto:!0,forced:Function.bind!==tl},{bind:tl});var el=a,rl=nt,nl=function(t,e){var r=rl[t+"Prototype"],n=r&&r[e];if(n)return n;var o=el[t],i=o&&o.prototype;return i&&i[e]},ol=nl("Function","bind"),il=ct,al=ol,ul=Function.prototype,sl=function(t){var e=t.bind;return t===ul||il(ul,t)&&e===ul.bind?al:e},cl=n(sl),fl=Lt,ll=$t,hl=V,pl=Hr,vl=TypeError,dl=function(t){return function(e,r,n,o){fl(r);var i=ll(e),a=hl(i),u=pl(i),s=t?u-1:0,c=t?-1:1;if(n<2)for(;;){if(s in a){o=a[s],s+=c;break}if(s+=c,t?s<0:u<=s)throw new vl("Reduce of empty array with no initial value")}for(;t?s>=0:u>s;s+=c)s in a&&(o=r(o,a[s],s,i));return o}},yl={left:dl(!1),right:dl(!0)},gl=u,ml=function(t,e){var r=[][t];return!!r&&gl((function(){r.call(null,e||function(){return 1},1)}))},bl="process"===T(a.process),wl=yl.left;Pr({target:"Array",proto:!0,forced:!bl&&gt>79&&gt<83||!ml("reduce")},{reduce:function(t){var e=arguments.length;return wl(this,t,e,e>1?arguments[1]:void 0)}});var _l=nl("Array","reduce"),Tl=ct,El=_l,Ol=Array.prototype,Sl=n((function(t){var e=t.reduce;return t===Ol||Tl(Ol,t)&&e===Ol.reduce?El:e})),xl=ca.filter;Pr({target:"Array",proto:!0,forced:!zn("filter")},{filter:function(t){return xl(this,t,arguments.length>1?arguments[1]:void 0)}});var kl=nl("Array","filter"),jl=ct,Al=kl,Pl=Array.prototype,Il=n((function(t){var e=t.filter;return t===Pl||jl(Pl,t)&&e===Pl.filter?Al:e})),Dl=ca.map;Pr({target:"Array",proto:!0,forced:!zn("map")},{map:function(t){return Dl(this,t,arguments.length>1?arguments[1]:void 0)}});var Ll=nl("Array","map"),Cl=ct,Rl=Ll,Ml=Array.prototype,Nl=n((function(t){var e=t.map;return t===Ml||Cl(Ml,t)&&e===Ml.map?Rl:e})),Fl=Ur,zl=Hr,Ul=Jr,ql=Qe,Wl=function(t,e,r,n,o,i,a,u){for(var s,c,f=o,l=0,h=!!a&&ql(a,u);l<n;)l in r&&(s=h?h(r[l],l,e):r[l],i>0&&Fl(s)?(c=zl(s),f=Wl(t,e,s,c,f,i-1)-1):(Ul(f+1),t[f]=s),f++),l++;return f},Yl=Wl,Gl=Lt,Xl=$t,Vl=Hr,Bl=Rn;Pr({target:"Array",proto:!0},{flatMap:function(t){var e,r=Xl(this),n=Vl(r);return Gl(t),(e=Bl(r,0)).length=Yl(e,r,r,n,0,1,t,arguments.length>1?arguments[1]:void 0),e}});var Hl=nl("Array","flatMap"),Kl=ct,Jl=Hl,$l=Array.prototype,Ql=n((function(t){var e=t.flatMap;return t===$l||Kl($l,t)&&e===$l.flatMap?Jl:e}));var Zl=function(){function t(r,n,o){var i,a,u;e(this,t),Wf(this,"_listeners",{add:cl(i=this._add).call(i,this),remove:cl(a=this._remove).call(a,this),update:cl(u=this._update).call(u,this)}),this._source=r,this._transformers=n,this._target=o}return qf(t,[{key:"all",value:function(){return this._target.update(this._transformItems(this._source.get())),this}},{key:"start",value:function(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}},{key:"stop",value:function(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}},{key:"_transformItems",value:function(t){var e;return Sl(e=this._transformers).call(e,(function(t,e){return e(t)}),t)}},{key:"_add",value:function(t,e){null!=e&&this._target.add(this._transformItems(this._source.get(e.items)))}},{key:"_update",value:function(t,e){null!=e&&this._target.update(this._transformItems(this._source.get(e.items)))}},{key:"_remove",value:function(t,e){null!=e&&this._target.remove(this._transformItems(e.oldData))}}]),t}(),th=function(){function t(r){e(this,t),Wf(this,"_transformers",[]),this._source=r}return qf(t,[{key:"filter",value:function(t){return this._transformers.push((function(e){return Il(e).call(e,t)})),this}},{key:"map",value:function(t){return this._transformers.push((function(e){return Nl(e).call(e,t)})),this}},{key:"flatMap",value:function(t){return this._transformers.push((function(e){return Ql(e).call(e,t)})),this}},{key:"to",value:function(t){return new Zl(this._source,this._transformers,t)}}]),t}(),eh=C,rh=or,nh=Mt,oh=function(t,e,r){var n,o;rh(t);try{if(!(n=nh(t,"return"))){if("throw"===e)throw r;return r}n=eh(n,t)}catch(t){o=!0,n=t}if("throw"===e)throw r;if(o)throw n;return rh(n),r},ih=or,ah=oh,uh=_s,sh=ve("iterator"),ch=Array.prototype,fh=function(t){return void 0!==t&&(uh.Array===t||ch[sh]===t)},lh=fn,hh=Mt,ph=B,vh=_s,dh=ve("iterator"),yh=function(t){if(!ph(t))return hh(t,dh)||hh(t,"@@iterator")||vh[lh(t)]},gh=C,mh=Lt,bh=or,wh=At,_h=yh,Th=TypeError,Eh=function(t,e){var r=arguments.length<2?_h(t):e;if(mh(r))return bh(gh(r,t));throw new Th(wh(t)+" is not iterable")},Oh=Qe,Sh=C,xh=$t,kh=function(t,e,r,n){try{return n?e(ih(r)[0],r[1]):e(r)}catch(e){ah(t,"throw",e)}},jh=fh,Ah=jn,Ph=Hr,Ih=tn,Dh=Eh,Lh=yh,Ch=Array,Rh=ve("iterator"),Mh=!1;try{var Nh=0,Fh={next:function(){return{done:!!Nh++}},return:function(){Mh=!0}};Fh[Rh]=function(){return this},Array.from(Fh,(function(){throw 2}))}catch(t){}var zh=function(t,e){try{if(!e&&!Mh)return!1}catch(t){return!1}var r=!1;try{var n={};n[Rh]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r},Uh=function(t){var e=xh(t),r=Ah(this),n=arguments.length,o=n>1?arguments[1]:void 0,i=void 0!==o;i&&(o=Oh(o,n>2?arguments[2]:void 0));var a,u,s,c,f,l,h=Lh(e),p=0;if(!h||this===Ch&&jh(h))for(a=Ph(e),u=r?new this(a):Ch(a);a>p;p++)l=i?o(e[p],p):e[p],Ih(u,p,l);else for(f=(c=Dh(e,h)).next,u=r?new this:[];!(s=Sh(f,c)).done;p++)l=i?kh(c,o,[s.value,p],!0):s.value,Ih(u,p,l);return u.length=p,u};Pr({target:"Array",stat:!0,forced:!zh((function(t){Array.from(t)}))},{from:Uh});var qh=nt.Array.from,Wh=n(qh),Yh=yh,Gh=n(Yh),Xh=n(Yh);Pr({target:"Array",stat:!0},{isArray:Ur});var Vh=nt.Array.isArray,Bh=n(Vh);var Hh=I,Kh=Ur,Jh=TypeError,$h=Object.getOwnPropertyDescriptor,Qh=Hh&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,e){if(Kh(t)&&!$h(t,"length").writable)throw new Jh("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},Zh=$t,tp=Hr,ep=Qh,rp=Jr;Pr({target:"Array",proto:!0,arity:1,forced:u((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function(t){var e=Zh(this),r=tp(e),n=arguments.length;rp(r+n);for(var o=0;o<n;o++)e[r]=arguments[o],r++;return ep(e,r),r}});var np=nl("Array","push"),op=ct,ip=np,ap=Array.prototype,up=function(t){var e=t.push;return t===ap||op(ap,t)&&e===ap.push?ip:e},sp=n(up);var cp=Pr,fp=Ur,lp=jn,hp=rt,pp=uo,vp=Hr,dp=Z,yp=tn,gp=ve,mp=Ru,bp=zn("slice"),wp=gp("species"),_p=Array,Tp=Math.max;cp({target:"Array",proto:!0,forced:!bp},{slice:function(t,e){var r,n,o,i=dp(this),a=vp(i),u=pp(t,a),s=pp(void 0===e?a:e,a);if(fp(i)&&(r=i.constructor,(lp(r)&&(r===_p||fp(r.prototype))||hp(r)&&null===(r=r[wp]))&&(r=void 0),r===_p||void 0===r))return mp(i,u,s);for(n=new(void 0===r?_p:r)(Tp(s-u,0)),o=0;u<s;u++,o++)u in i&&yp(n,o,i[u]);return n.length=o,n}});var Ep=nl("Array","slice"),Op=ct,Sp=Ep,xp=Array.prototype,kp=function(t){var e=t.slice;return t===xp||Op(xp,t)&&e===xp.slice?Sp:e},jp=kp,Ap=n(jp),Pp=n(qh);function Ip(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Dp(t,e){var r;if(t){if("string"==typeof t)return Ip(t,e);var n=Ap(r=Object.prototype.toString.call(t)).call(r,8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Pp(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ip(t,e):void 0}}function Lp(t,e){return function(t){if(Bh(t))return t}(t)||function(t,e){var r=null==t?null:void 0!==gf&&Gh(t)||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],s=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(sp(u).call(u,n.value),u.length!==e);s=!0);}catch(t){c=!0,o=t}finally{try{if(!s&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||Dp(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Cp(t){return function(t){if(Bh(t))return Ip(t)}(t)||function(t){if(void 0!==gf&&null!=Gh(t)||null!=t["@@iterator"])return Pp(t)}(t)||Dp(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var Rp=n(Yc),Mp=nl("Array","concat"),Np=ct,Fp=Mp,zp=Array.prototype,Up=n((function(t){var e=t.concat;return t===zp||Np(zp,t)&&e===zp.concat?Fp:e})),qp=n(kp),Wp=st,Yp=Jo,Gp=li,Xp=or,Vp=m([].concat),Bp=Wp("Reflect","ownKeys")||function(t){var e=Yp.f(Xp(t)),r=Gp.f;return r?Vp(e,r(t)):e};Pr({target:"Reflect",stat:!0},{ownKeys:Bp});var Hp=n(nt.Reflect.ownKeys),Kp=n(Vh),Jp=$t,$p=Oo;Pr({target:"Object",stat:!0,forced:u((function(){$p(1)}))},{keys:function(t){return $p(Jp(t))}});var Qp=n(nt.Object.keys),Zp=ca.forEach,tv=ml("forEach")?[].forEach:function(t){return Zp(this,t,arguments.length>1?arguments[1]:void 0)};Pr({target:"Array",proto:!0,forced:[].forEach!==tv},{forEach:tv});var ev=nl("Array","forEach"),rv=fn,nv=te,ov=ct,iv=ev,av=Array.prototype,uv={DOMTokenList:!0,NodeList:!0},sv=function(t){var e=t.forEach;return t===av||ov(av,t)&&e===av.forEach||nv(uv,rv(t))?iv:e},cv=n(sv),fv=Pr,lv=Ur,hv=m([].reverse),pv=[1,2];fv({target:"Array",proto:!0,forced:String(pv)===String(pv.reverse())},{reverse:function(){return lv(this)&&(this.length=this.length),hv(this)}});var vv=nl("Array","reverse"),dv=ct,yv=vv,gv=Array.prototype,mv=function(t){var e=t.reverse;return t===gv||dv(gv,t)&&e===gv.reverse?yv:e},bv=n(mv),wv=At,_v=TypeError,Tv=function(t,e){if(!delete t[e])throw new _v("Cannot delete property "+wv(e)+" of "+wv(t))},Ev=Pr,Ov=$t,Sv=uo,xv=Gr,kv=Hr,jv=Qh,Av=Jr,Pv=Rn,Iv=tn,Dv=Tv,Lv=zn("splice"),Cv=Math.max,Rv=Math.min;Ev({target:"Array",proto:!0,forced:!Lv},{splice:function(t,e){var r,n,o,i,a,u,s=Ov(this),c=kv(s),f=Sv(t,c),l=arguments.length;for(0===l?r=n=0:1===l?(r=0,n=c-f):(r=l-2,n=Rv(Cv(xv(e),0),c-f)),Av(c+r-n),o=Pv(s,n),i=0;i<n;i++)(a=f+i)in s&&Iv(o,i,s[a]);if(o.length=n,r<n){for(i=f;i<c-n;i++)u=i+r,(a=i+n)in s?s[u]=s[a]:Dv(s,u);for(i=c;i>c-n+r;i--)Dv(s,i-1)}else if(r>n)for(i=c-n;i>f;i--)u=i+r-1,(a=i+n-1)in s?s[u]=s[a]:Dv(s,u);for(i=0;i<r;i++)s[i+f]=arguments[i+2];return jv(s,c-n+r),o}});var Mv=nl("Array","splice"),Nv=ct,Fv=Mv,zv=Array.prototype,Uv=n((function(t){var e=t.splice;return t===zv||Nv(zv,t)&&e===zv.splice?Fv:e})),qv=I,Wv=m,Yv=C,Gv=u,Xv=Oo,Vv=li,Bv=R,Hv=$t,Kv=V,Jv=Object.assign,$v=Object.defineProperty,Qv=Wv([].concat),Zv=!Jv||Gv((function(){if(qv&&1!==Jv({b:1},Jv($v({},"a",{enumerable:!0,get:function(){$v(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol("assign detection"),n="abcdefghijklmnopqrst";return t[r]=7,n.split("").forEach((function(t){e[t]=t})),7!==Jv({},t)[r]||Xv(Jv({},e)).join("")!==n}))?function(t,e){for(var r=Hv(t),n=arguments.length,o=1,i=Vv.f,a=Bv.f;n>o;)for(var u,s=Kv(arguments[o++]),c=i?Qv(Xv(s),i(s)):Xv(s),f=c.length,l=0;f>l;)u=c[l++],qv&&!Yv(a,s,u)||(r[u]=s[u]);return r}:Jv,td=Zv;Pr({target:"Object",stat:!0,arity:2,forced:Object.assign!==td},{assign:td});var ed=n(nt.Object.assign),rd=$t,nd=Ms,od=js;Pr({target:"Object",stat:!0,forced:u((function(){nd(1)})),sham:!od},{getPrototypeOf:function(t){return nd(rd(t))}});var id=nt.Object.getPrototypeOf;Pr({target:"Object",stat:!0,sham:!I},{create:Ko});var ad=nt.Object,ud=function(t,e){return ad.create(t,e)},sd=n(ud),cd=nt,fd=p;cd.JSON||(cd.JSON={stringify:JSON.stringify});var ld=function(t,e,r){return fd(cd.JSON.stringify,null,arguments)},hd=n(ld),pd="function"==typeof Bun&&Bun&&"string"==typeof Bun.version,vd=TypeError,dd=function(t,e){if(t<e)throw new vd("Not enough arguments");return t},yd=a,gd=p,md=A,bd=pd,wd=ft,_d=Ru,Td=dd,Ed=yd.Function,Od=/MSIE .\./.test(wd)||bd&&function(){var t=yd.Bun.version.split(".");return t.length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2])}(),Sd=function(t,e){var r=e?2:1;return Od?function(n,o){var i=Td(arguments.length,1)>r,a=md(n)?n:Ed(n),u=i?_d(arguments,r):[],s=i?function(){gd(a,this,u)}:a;return e?t(s,o):t(s)}:t},xd=Pr,kd=a,jd=Sd(kd.setInterval,!0);xd({global:!0,bind:!0,forced:kd.setInterval!==jd},{setInterval:jd});var Ad=Pr,Pd=a,Id=Sd(Pd.setTimeout,!0);Ad({global:!0,bind:!0,forced:Pd.setTimeout!==Id},{setTimeout:Id});var Dd=n(nt.setTimeout),Ld={exports:{}};!function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const r=this._callbacks.get(t)??[];return r.push(e),this._callbacks.set(t,r),this},e.prototype.once=function(t,e){const r=(...n)=>{this.off(t,r),e.apply(this,n)};return r.fn=e,this.on(t,r),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const r=this._callbacks.get(t);if(r){for(const[t,n]of r.entries())if(n===e||n.fn===e){r.splice(t,1);break}0===r.length?this._callbacks.delete(t):this._callbacks.set(t,r)}return this},e.prototype.emit=function(t,...e){const r=this._callbacks.get(t);if(r){const t=[...r];for(const r of t)r.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(Ld);var Cd,Rd=n(Ld.exports);
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
   * http://naver.github.io/egjs
   *
   * Forked By Naver egjs
   * Copyright (c) hammerjs
   * Licensed under the MIT license */
function Md(){return Md=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Md.apply(this,arguments)}function Nd(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function Fd(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}Cd="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),r=1;r<arguments.length;r++){var n=arguments[r];if(null!=n)for(var o in n)n.hasOwnProperty(o)&&(e[o]=n[o])}return e}:Object.assign;var zd,Ud=Cd,qd=["","webkit","Moz","MS","ms","o"],Wd="undefined"==typeof document?{style:{}}:document.createElement("div"),Yd=Math.round,Gd=Math.abs,Xd=Date.now;function Vd(t,e){for(var r,n,o=e[0].toUpperCase()+e.slice(1),i=0;i<qd.length;){if((n=(r=qd[i])?r+o:e)in t)return n;i++}}zd="undefined"==typeof window?{}:window;var Bd=Vd(Wd.style,"touchAction"),Hd=void 0!==Bd;var Kd="compute",Jd="auto",$d="manipulation",Qd="none",Zd="pan-x",ty="pan-y",ey=function(){if(!Hd)return!1;var t={},e=zd.CSS&&zd.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(r){return t[r]=!e||zd.CSS.supports("touch-action",r)})),t}(),ry="ontouchstart"in zd,ny=void 0!==Vd(zd,"PointerEvent"),oy=ry&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),iy="touch",ay="mouse",uy=25,sy=1,cy=4,fy=8,ly=1,hy=2,py=4,vy=8,dy=16,yy=hy|py,gy=vy|dy,my=yy|gy,by=["x","y"],wy=["clientX","clientY"];function _y(t,e,r){var n;if(t)if(t.forEach)t.forEach(e,r);else if(void 0!==t.length)for(n=0;n<t.length;)e.call(r,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&e.call(r,t[n],n,t)}function Ty(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function Ey(t,e){return t.indexOf(e)>-1}var Oy=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===Kd&&(t=this.compute()),Hd&&this.manager.element.style&&ey[t]&&(this.manager.element.style[Bd]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return _y(this.manager.recognizers,(function(e){Ty(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(Ey(t,Qd))return Qd;var e=Ey(t,Zd),r=Ey(t,ty);return e&&r?Qd:e||r?e?Zd:ty:Ey(t,$d)?$d:Jd}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,r=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var n=this.actions,o=Ey(n,Qd)&&!ey[Qd],i=Ey(n,ty)&&!ey[ty],a=Ey(n,Zd)&&!ey[Zd];if(o){var u=1===t.pointers.length,s=t.distance<2,c=t.deltaTime<250;if(u&&s&&c)return}if(!a||!i)return o||i&&r&yy||a&&r&gy?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function Sy(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function xy(t){var e=t.length;if(1===e)return{x:Yd(t[0].clientX),y:Yd(t[0].clientY)};for(var r=0,n=0,o=0;o<e;)r+=t[o].clientX,n+=t[o].clientY,o++;return{x:Yd(r/e),y:Yd(n/e)}}function ky(t){for(var e=[],r=0;r<t.pointers.length;)e[r]={clientX:Yd(t.pointers[r].clientX),clientY:Yd(t.pointers[r].clientY)},r++;return{timeStamp:Xd(),pointers:e,center:xy(e),deltaX:t.deltaX,deltaY:t.deltaY}}function jy(t,e,r){r||(r=by);var n=e[r[0]]-t[r[0]],o=e[r[1]]-t[r[1]];return Math.sqrt(n*n+o*o)}function Ay(t,e,r){r||(r=by);var n=e[r[0]]-t[r[0]],o=e[r[1]]-t[r[1]];return 180*Math.atan2(o,n)/Math.PI}function Py(t,e){return t===e?ly:Gd(t)>=Gd(e)?t<0?hy:py:e<0?vy:dy}function Iy(t,e,r){return{x:e/t||0,y:r/t||0}}function Dy(t,e){var r=t.session,n=e.pointers,o=n.length;r.firstInput||(r.firstInput=ky(e)),o>1&&!r.firstMultiple?r.firstMultiple=ky(e):1===o&&(r.firstMultiple=!1);var i=r.firstInput,a=r.firstMultiple,u=a?a.center:i.center,s=e.center=xy(n);e.timeStamp=Xd(),e.deltaTime=e.timeStamp-i.timeStamp,e.angle=Ay(u,s),e.distance=jy(u,s),function(t,e){var r=e.center,n=t.offsetDelta||{},o=t.prevDelta||{},i=t.prevInput||{};e.eventType!==sy&&i.eventType!==cy||(o=t.prevDelta={x:i.deltaX||0,y:i.deltaY||0},n=t.offsetDelta={x:r.x,y:r.y}),e.deltaX=o.x+(r.x-n.x),e.deltaY=o.y+(r.y-n.y)}(r,e),e.offsetDirection=Py(e.deltaX,e.deltaY);var c,f,l=Iy(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=l.x,e.overallVelocityY=l.y,e.overallVelocity=Gd(l.x)>Gd(l.y)?l.x:l.y,e.scale=a?(c=a.pointers,jy((f=n)[0],f[1],wy)/jy(c[0],c[1],wy)):1,e.rotation=a?function(t,e){return Ay(e[1],e[0],wy)+Ay(t[1],t[0],wy)}(a.pointers,n):0,e.maxPointers=r.prevInput?e.pointers.length>r.prevInput.maxPointers?e.pointers.length:r.prevInput.maxPointers:e.pointers.length,function(t,e){var r,n,o,i,a=t.lastInterval||e,u=e.timeStamp-a.timeStamp;if(e.eventType!==fy&&(u>uy||void 0===a.velocity)){var s=e.deltaX-a.deltaX,c=e.deltaY-a.deltaY,f=Iy(u,s,c);n=f.x,o=f.y,r=Gd(f.x)>Gd(f.y)?f.x:f.y,i=Py(s,c),t.lastInterval=e}else r=a.velocity,n=a.velocityX,o=a.velocityY,i=a.direction;e.velocity=r,e.velocityX=n,e.velocityY=o,e.direction=i}(r,e);var h,p=t.element,v=e.srcEvent;Sy(h=v.composedPath?v.composedPath()[0]:v.path?v.path[0]:v.target,p)&&(p=h),e.target=p}function Ly(t,e,r){var n=r.pointers.length,o=r.changedPointers.length,i=e&sy&&n-o==0,a=e&(cy|fy)&&n-o==0;r.isFirst=!!i,r.isFinal=!!a,i&&(t.session={}),r.eventType=e,Dy(t,r),t.emit("hammer.input",r),t.recognize(r),t.session.prevInput=r}function Cy(t){return t.trim().split(/\s+/g)}function Ry(t,e,r){_y(Cy(e),(function(e){t.addEventListener(e,r,!1)}))}function My(t,e,r){_y(Cy(e),(function(e){t.removeEventListener(e,r,!1)}))}function Ny(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var Fy=function(){function t(t,e){var r=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){Ty(t.options.enable,[t])&&r.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&Ry(this.element,this.evEl,this.domHandler),this.evTarget&&Ry(this.target,this.evTarget,this.domHandler),this.evWin&&Ry(Ny(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&My(this.element,this.evEl,this.domHandler),this.evTarget&&My(this.target,this.evTarget,this.domHandler),this.evWin&&My(Ny(this.element),this.evWin,this.domHandler)},t}();function zy(t,e,r){if(t.indexOf&&!r)return t.indexOf(e);for(var n=0;n<t.length;){if(r&&t[n][r]==e||!r&&t[n]===e)return n;n++}return-1}var Uy={pointerdown:sy,pointermove:2,pointerup:cy,pointercancel:fy,pointerout:fy},qy={2:iy,3:"pen",4:ay,5:"kinect"},Wy="pointerdown",Yy="pointermove pointerup pointercancel";zd.MSPointerEvent&&!zd.PointerEvent&&(Wy="MSPointerDown",Yy="MSPointerMove MSPointerUp MSPointerCancel");var Gy=function(t){function e(){var r,n=e.prototype;return n.evEl=Wy,n.evWin=Yy,(r=t.apply(this,arguments)||this).store=r.manager.session.pointerEvents=[],r}return Nd(e,t),e.prototype.handler=function(t){var e=this.store,r=!1,n=t.type.toLowerCase().replace("ms",""),o=Uy[n],i=qy[t.pointerType]||t.pointerType,a=i===iy,u=zy(e,t.pointerId,"pointerId");o&sy&&(0===t.button||a)?u<0&&(e.push(t),u=e.length-1):o&(cy|fy)&&(r=!0),u<0||(e[u]=t,this.callback(this.manager,o,{pointers:e,changedPointers:[t],pointerType:i,srcEvent:t}),r&&e.splice(u,1))},e}(Fy);function Xy(t){return Array.prototype.slice.call(t,0)}function Vy(t,e,r){for(var n=[],o=[],i=0;i<t.length;){var a=e?t[i][e]:t[i];zy(o,a)<0&&n.push(t[i]),o[i]=a,i++}return r&&(n=e?n.sort((function(t,r){return t[e]>r[e]})):n.sort()),n}var By={touchstart:sy,touchmove:2,touchend:cy,touchcancel:fy},Hy=function(t){function e(){var r;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(r=t.apply(this,arguments)||this).targetIds={},r}return Nd(e,t),e.prototype.handler=function(t){var e=By[t.type],r=Ky.call(this,t,e);r&&this.callback(this.manager,e,{pointers:r[0],changedPointers:r[1],pointerType:iy,srcEvent:t})},e}(Fy);function Ky(t,e){var r,n,o=Xy(t.touches),i=this.targetIds;if(e&(2|sy)&&1===o.length)return i[o[0].identifier]=!0,[o,o];var a=Xy(t.changedTouches),u=[],s=this.target;if(n=o.filter((function(t){return Sy(t.target,s)})),e===sy)for(r=0;r<n.length;)i[n[r].identifier]=!0,r++;for(r=0;r<a.length;)i[a[r].identifier]&&u.push(a[r]),e&(cy|fy)&&delete i[a[r].identifier],r++;return u.length?[Vy(n.concat(u),"identifier",!0),u]:void 0}var Jy={mousedown:sy,mousemove:2,mouseup:cy},$y=function(t){function e(){var r,n=e.prototype;return n.evEl="mousedown",n.evWin="mousemove mouseup",(r=t.apply(this,arguments)||this).pressed=!1,r}return Nd(e,t),e.prototype.handler=function(t){var e=Jy[t.type];e&sy&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=cy),this.pressed&&(e&cy&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:ay,srcEvent:t}))},e}(Fy),Qy=2500;function Zy(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var r={x:e.clientX,y:e.clientY},n=this.lastTouches;this.lastTouches.push(r);setTimeout((function(){var t=n.indexOf(r);t>-1&&n.splice(t,1)}),Qy)}}function tg(t,e){t&sy?(this.primaryTouch=e.changedPointers[0].identifier,Zy.call(this,e)):t&(cy|fy)&&Zy.call(this,e)}function eg(t){for(var e=t.srcEvent.clientX,r=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var o=this.lastTouches[n],i=Math.abs(e-o.x),a=Math.abs(r-o.y);if(i<=25&&a<=25)return!0}return!1}var rg=function(){return function(t){function e(e,r){var n;return(n=t.call(this,e,r)||this).handler=function(t,e,r){var o=r.pointerType===iy,i=r.pointerType===ay;if(!(i&&r.sourceCapabilities&&r.sourceCapabilities.firesTouchEvents)){if(o)tg.call(Fd(Fd(n)),e,r);else if(i&&eg.call(Fd(Fd(n)),r))return;n.callback(t,e,r)}},n.touch=new Hy(n.manager,n.handler),n.mouse=new $y(n.manager,n.handler),n.primaryTouch=null,n.lastTouches=[],n}return Nd(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(Fy)}();function ng(t,e,r){return!!Array.isArray(t)&&(_y(t,r[e],r),!0)}var og=32,ig=1;function ag(t,e){var r=e.manager;return r?r.get(t):t}function ug(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var sg=function(){function t(t){void 0===t&&(t={}),this.options=Md({enable:!0},t),this.id=ig++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Ud(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(ng(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=ag(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return ng(t,"dropRecognizeWith",this)||(t=ag(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(ng(t,"requireFailure",this))return this;var e=this.requireFail;return-1===zy(e,t=ag(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(ng(t,"dropRequireFailure",this))return this;t=ag(t,this);var e=zy(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,r=this.state;function n(r){e.manager.emit(r,t)}r<8&&n(e.options.event+ug(r)),n(e.options.event),t.additionalEvent&&n(t.additionalEvent),r>=8&&n(e.options.event+ug(r))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=og},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Ud({},t);if(!Ty(this.options.enable,[this,e]))return this.reset(),void(this.state=og);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),cg=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Md({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,r.pCenter=!1,r._timer=null,r._input=null,r.count=0,r}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[$d]},r.process=function(t){var e=this,r=this.options,n=t.pointers.length===r.pointers,o=t.distance<r.threshold,i=t.deltaTime<r.time;if(this.reset(),t.eventType&sy&&0===this.count)return this.failTimeout();if(o&&i&&n){if(t.eventType!==cy)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<r.interval,u=!this.pCenter||jy(this.pCenter,t.center)<r.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,u&&a?this.count+=1:this.count=1,this._input=t,0===this.count%r.taps)return this.hasRequireFailures()?(this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),r.interval),2):8}return og},r.failTimeout=function(){var t=this;return this._timer=setTimeout((function(){t.state=og}),this.options.interval),og},r.reset=function(){clearTimeout(this._timer)},r.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(sg),fg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Md({pointers:1},e))||this}Nd(e,t);var r=e.prototype;return r.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},r.process=function(t){var e=this.state,r=t.eventType,n=6&e,o=this.attrTest(t);return n&&(r&fy||!o)?16|e:n||o?r&cy?8|e:2&e?4|e:2:og},e}(sg);function lg(t){return t===dy?"down":t===vy?"up":t===hy?"left":t===py?"right":""}var hg=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Md({event:"pan",threshold:10,pointers:1,direction:my},e))||this).pX=null,r.pY=null,r}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){var t=this.options.direction,e=[];return t&yy&&e.push(ty),t&gy&&e.push(Zd),e},r.directionTest=function(t){var e=this.options,r=!0,n=t.distance,o=t.direction,i=t.deltaX,a=t.deltaY;return o&e.direction||(e.direction&yy?(o=0===i?ly:i<0?hy:py,r=i!==this.pX,n=Math.abs(t.deltaX)):(o=0===a?ly:a<0?vy:dy,r=a!==this.pY,n=Math.abs(t.deltaY))),t.direction=o,r&&n>e.threshold&&o&e.direction},r.attrTest=function(t){return fg.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},r.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var r=lg(e.direction);r&&(e.additionalEvent=this.options.event+r),t.prototype.emit.call(this,e)},e}(fg),pg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Md({event:"swipe",threshold:10,velocity:.3,direction:yy|gy,pointers:1},e))||this}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){return hg.prototype.getTouchAction.call(this)},r.attrTest=function(e){var r,n=this.options.direction;return n&(yy|gy)?r=e.overallVelocity:n&yy?r=e.overallVelocityX:n&gy&&(r=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&n&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Gd(r)>this.options.velocity&&e.eventType&cy},r.emit=function(t){var e=lg(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(fg),vg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Md({event:"pinch",threshold:0,pointers:2},e))||this}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Qd]},r.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},r.emit=function(e){if(1!==e.scale){var r=e.scale<1?"in":"out";e.additionalEvent=this.options.event+r}t.prototype.emit.call(this,e)},e}(fg),dg=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Md({event:"rotate",threshold:0,pointers:2},e))||this}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Qd]},r.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(fg),yg=function(t){function e(e){var r;return void 0===e&&(e={}),(r=t.call(this,Md({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,r._input=null,r}Nd(e,t);var r=e.prototype;return r.getTouchAction=function(){return[Jd]},r.process=function(t){var e=this,r=this.options,n=t.pointers.length===r.pointers,o=t.distance<r.threshold,i=t.deltaTime>r.time;if(this._input=t,!o||!n||t.eventType&(cy|fy)&&!i)this.reset();else if(t.eventType&sy)this.reset(),this._timer=setTimeout((function(){e.state=8,e.tryEmit()}),r.time);else if(t.eventType&cy)return 8;return og},r.reset=function(){clearTimeout(this._timer)},r.emit=function(t){8===this.state&&(t&&t.eventType&cy?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Xd(),this.manager.emit(this.options.event,this._input)))},e}(sg),gg={domEvents:!1,touchAction:Kd,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},mg=[[dg,{enable:!1}],[vg,{enable:!1},["rotate"]],[pg,{direction:yy}],[hg,{direction:yy},["swipe"]],[cg],[cg,{event:"doubletap",taps:2},["tap"]],[yg]];function bg(t,e){var r,n=t.element;n.style&&(_y(t.options.cssProps,(function(o,i){r=Vd(n.style,i),e?(t.oldCssProps[r]=n.style[r],n.style[r]=o):n.style[r]=t.oldCssProps[r]||""})),e||(t.oldCssProps={}))}var wg=function(){function t(t,e){var r,n=this;this.options=Ud({},gg,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((r=this).options.inputClass||(ny?Gy:oy?Hy:ry?rg:$y))(r,Ly),this.touchAction=new Oy(this,this.options.touchAction),bg(this,!0),_y(this.options.recognizers,(function(t){var e=n.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}var e=t.prototype;return e.set=function(t){return Ud(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var r;this.touchAction.preventDefaults(t);var n=this.recognizers,o=e.curRecognizer;(!o||o&&8&o.state)&&(e.curRecognizer=null,o=null);for(var i=0;i<n.length;)r=n[i],2===e.stopped||o&&r!==o&&!r.canRecognizeWith(o)?r.reset():r.recognize(t),!o&&14&r.state&&(e.curRecognizer=r,o=r),i++}},e.get=function(t){if(t instanceof sg)return t;for(var e=this.recognizers,r=0;r<e.length;r++)if(e[r].options.event===t)return e[r];return null},e.add=function(t){if(ng(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(ng(t,"remove",this))return this;var e=this.get(t);if(t){var r=this.recognizers,n=zy(r,e);-1!==n&&(r.splice(n,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var r=this.handlers;return _y(Cy(t),(function(t){r[t]=r[t]||[],r[t].push(e)})),this},e.off=function(t,e){if(void 0===t)return this;var r=this.handlers;return _y(Cy(t),(function(t){e?r[t]&&r[t].splice(zy(r[t],e),1):delete r[t]})),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var r=document.createEvent("Event");r.initEvent(t,!0,!0),r.gesture=e,e.target.dispatchEvent(r)}(t,e);var r=this.handlers[t]&&this.handlers[t].slice();if(r&&r.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var n=0;n<r.length;)r[n](e),n++}},e.destroy=function(){this.element&&bg(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),_g={touchstart:sy,touchmove:2,touchend:cy,touchcancel:fy},Tg=function(t){function e(){var r,n=e.prototype;return n.evTarget="touchstart",n.evWin="touchstart touchmove touchend touchcancel",(r=t.apply(this,arguments)||this).started=!1,r}return Nd(e,t),e.prototype.handler=function(t){var e=_g[t.type];if(e===sy&&(this.started=!0),this.started){var r=Eg.call(this,t,e);e&(cy|fy)&&r[0].length-r[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:r[0],changedPointers:r[1],pointerType:iy,srcEvent:t})}},e}(Fy);function Eg(t,e){var r=Xy(t.touches),n=Xy(t.changedTouches);return e&(cy|fy)&&(r=Vy(r.concat(n),"identifier",!0)),[r,n]}function Og(t,e,r){var n="DEPRECATED METHOD: "+e+"\n"+r+" AT \n";return function(){var e=new Error("get-stack-trace"),r=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",o=window.console&&(window.console.warn||window.console.log);return o&&o.call(window.console,n,r),t.apply(this,arguments)}}var Sg=Og((function(t,e,r){for(var n=Object.keys(e),o=0;o<n.length;)(!r||r&&void 0===t[n[o]])&&(t[n[o]]=e[n[o]]),o++;return t}),"extend","Use `assign`."),xg=Og((function(t,e){return Sg(t,e,!0)}),"merge","Use `assign`.");function kg(t,e,r){var n,o=e.prototype;(n=t.prototype=Object.create(o)).constructor=t,n._super=o,r&&Ud(n,r)}function jg(t,e){return function(){return t.apply(e,arguments)}}var Ag=function(){var t=function(t,e){return void 0===e&&(e={}),new wg(t,Md({recognizers:mg.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=my,t.DIRECTION_DOWN=dy,t.DIRECTION_LEFT=hy,t.DIRECTION_RIGHT=py,t.DIRECTION_UP=vy,t.DIRECTION_HORIZONTAL=yy,t.DIRECTION_VERTICAL=gy,t.DIRECTION_NONE=ly,t.DIRECTION_DOWN=dy,t.INPUT_START=sy,t.INPUT_MOVE=2,t.INPUT_END=cy,t.INPUT_CANCEL=fy,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=og,t.Manager=wg,t.Input=Fy,t.TouchAction=Oy,t.TouchInput=Hy,t.MouseInput=$y,t.PointerEventInput=Gy,t.TouchMouseInput=rg,t.SingleTouchInput=Tg,t.Recognizer=sg,t.AttrRecognizer=fg,t.Tap=cg,t.Pan=hg,t.Swipe=pg,t.Pinch=vg,t.Rotate=dg,t.Press=yg,t.on=Ry,t.off=My,t.each=_y,t.merge=xg,t.extend=Sg,t.bindFn=jg,t.assign=Ud,t.inherit=kg,t.bindFn=jg,t.prefixed=Vd,t.toArray=Xy,t.inArray=zy,t.uniqueArray=Vy,t.splitStr=Cy,t.boolOrFn=Ty,t.hasParent=Sy,t.addEventListeners=Ry,t.removeEventListeners=My,t.defaults=Ud({},gg,{preset:mg}),t}();function Pg(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return Ig(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ig(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function Ig(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Dg=Rp("DELETE");function Lg(){var t=Cg.apply(void 0,arguments);return Mg(t),t}function Cg(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(e.length<2)return e[0];var n;if(e.length>2)return Cg.apply(void 0,Up(n=[Lg(e[0],e[1])]).call(n,Cp(qp(e).call(e,2))));var o=e[0],i=e[1];if(o instanceof Date&&i instanceof Date)return o.setTime(i.getTime()),o;var a,u=Pg(Hp(i));try{for(u.s();!(a=u.n()).done;){var s=a.value;Object.prototype.propertyIsEnumerable.call(i,s)&&(i[s]===Dg?delete o[s]:null===o[s]||null===i[s]||"object"!=typeof o[s]||"object"!=typeof i[s]||Kp(o[s])||Kp(i[s])?o[s]=Rg(i[s]):o[s]=Cg(o[s],i[s]))}}catch(t){u.e(t)}finally{u.f()}return o}function Rg(t){return Kp(t)?Nl(t).call(t,(function(t){return Rg(t)})):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):Cg({},t):t}function Mg(t){for(var e=0,r=Qp(t);e<r.length;e++){var n=r[e];t[n]===Dg?delete t[n]:"object"==typeof t[n]&&null!==t[n]&&Mg(t[n])}}var Ng="undefined"!=typeof window?window.Hammer||Ag:function(){return function(){var t=function(){};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function Fg(t){var e,r=this;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push((function(){r._dom.overlay.parentNode.removeChild(r._dom.overlay)}));var n=Ng(this._dom.overlay);n.on("tap",cl(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push((function(){n.destroy()}));var o=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];cv(o).call(o,(function(t){n.on(t,(function(t){t.srcEvent.stopPropagation()}))})),document&&document.body&&(this._onClick=function(e){(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||r.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push((function(){document.body.removeEventListener("click",r._onClick)}))),this._escListener=function(t){("key"in t?"Escape"===t.key:27===t.keyCode)&&r.deactivate()}}Rd(Fg.prototype),Fg.current=null,Fg.prototype.destroy=function(){var t,e;this.deactivate();var r,n=Pg(bv(t=Uv(e=this._cleanupQueue).call(e,0)).call(t));try{for(n.s();!(r=n.n()).done;){(0,r.value)()}}catch(t){n.e(t)}finally{n.f()}},Fg.prototype.activate=function(){Fg.current&&Fg.current.deactivate(),Fg.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},Fg.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},Fg.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var zg=jn,Ug=At,qg=TypeError,Wg=function(t){if(zg(t))return t;throw new qg(Ug(t)+" is not a constructor")},Yg=Pr,Gg=p,Xg=Zf,Vg=Wg,Bg=or,Hg=rt,Kg=Ko,Jg=u,$g=st("Reflect","construct"),Qg=Object.prototype,Zg=[].push,tm=Jg((function(){function t(){}return!($g((function(){}),[],t)instanceof t)})),em=!Jg((function(){$g((function(){}))})),rm=tm||em;Yg({target:"Reflect",stat:!0,forced:rm,sham:rm},{construct:function(t,e){Vg(t),Bg(e);var r=arguments.length<3?t:Vg(arguments[2]);if(em&&!tm)return $g(t,e,r);if(t===r){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var n=[null];return Gg(Zg,n,e),new(Gg(Xg,t,n))}var o=r.prototype,i=Kg(Hg(o)?o:Qg),a=Gg(t,i,e);return Hg(a)?a:i}});var nm=n(nt.Reflect.construct),om=n(nt.Object.getOwnPropertySymbols),im={exports:{}},am=Pr,um=u,sm=Z,cm=P.f,fm=I;am({target:"Object",stat:!0,forced:!fm||um((function(){cm(1)})),sham:!fm},{getOwnPropertyDescriptor:function(t,e){return cm(sm(t),e)}});var lm=nt.Object,hm=im.exports=function(t,e){return lm.getOwnPropertyDescriptor(t,e)};lm.getOwnPropertyDescriptor.sham&&(hm.sham=!0);var pm=n(im.exports),vm=Bp,dm=Z,ym=P,gm=tn;Pr({target:"Object",stat:!0,sham:!I},{getOwnPropertyDescriptors:function(t){for(var e,r,n=dm(t),o=ym.f,i=vm(n),a={},u=0;i.length>u;)void 0!==(r=o(n,e=i[u++]))&&gm(a,e,r);return a}});var mm=n(nt.Object.getOwnPropertyDescriptors),bm={exports:{}},wm=Pr,_m=I,Tm=no.f;wm({target:"Object",stat:!0,forced:Object.defineProperties!==Tm,sham:!_m},{defineProperties:Tm});var Em=nt.Object,Om=bm.exports=function(t,e){return Em.defineProperties(t,e)};Em.defineProperties.sham&&(Om.sham=!0);var Sm=n(bm.exports),xm=n(Mr);function km(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var jm=ud,Am=n(jm);Pr({target:"Object",stat:!0},{setPrototypeOf:uc});var Pm=nt.Object.setPrototypeOf,Im=n(Pm),Dm=n(sl);function Lm(t,e){var r;return Lm=Im?Dm(r=Im).call(r):function(t,e){return t.__proto__=e,t},Lm(t,e)}function Cm(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Am(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Fr(t,"prototype",{writable:!1}),e&&Lm(t,e)}function Rm(t,e){if(e&&("object"===Nf(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return km(t)}var Mm=id,Nm=n(Mm);function Fm(t){var e;return Fm=Im?Dm(e=Nm).call(e):function(t){return t.__proto__||Nm(t)},Fm(t)}var zm={exports:{}},Um={exports:{}};!function(t){var e=yf,r=Rf;function n(o){return t.exports=n="function"==typeof e&&"symbol"==typeof r?function(t){return typeof t}:function(t){return t&&"function"==typeof e&&t.constructor===e&&t!==e.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,n(o)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}(Um);var qm=Um.exports,Wm=sv,Ym=te,Gm=Bp,Xm=P,Vm=Ze,Bm=rt,Hm=mr,Km=Error,Jm=m("".replace),$m=String(new Km("zxcasd").stack),Qm=/\n\s*at [^:]*:[^\n]*/,Zm=Qm.test($m),tb=q,eb=!u((function(){var t=new Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",tb(1,7)),7!==t.stack)})),rb=mr,nb=function(t,e){if(Zm&&"string"==typeof t&&!Km.prepareStackTrace)for(;e--;)t=Jm(t,Qm,"");return t},ob=eb,ib=Error.captureStackTrace,ab=Qe,ub=C,sb=or,cb=At,fb=fh,lb=Hr,hb=ct,pb=Eh,vb=yh,db=oh,yb=TypeError,gb=function(t,e){this.stopped=t,this.result=e},mb=gb.prototype,bb=function(t,e,r){var n,o,i,a,u,s,c,f=r&&r.that,l=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_RECORD),p=!(!r||!r.IS_ITERATOR),v=!(!r||!r.INTERRUPTED),d=ab(e,f),y=function(t){return n&&db(n,"normal",t),new gb(!0,t)},g=function(t){return l?(sb(t),v?d(t[0],t[1],y):d(t[0],t[1])):v?d(t,y):d(t)};if(h)n=t.iterator;else if(p)n=t;else{if(!(o=vb(t)))throw new yb(cb(t)+" is not iterable");if(fb(o)){for(i=0,a=lb(t);a>i;i++)if((u=g(t[i]))&&hb(mb,u))return u;return new gb(!1)}n=pb(t,o)}for(s=h?t.next:n.next;!(c=ub(s,n)).done;){try{u=g(c.value)}catch(t){db(n,"throw",t)}if("object"==typeof u&&u&&hb(mb,u))return u}return new gb(!1)},wb=ro,_b=Pr,Tb=ct,Eb=Ms,Ob=uc,Sb=function(t,e,r){for(var n=Gm(e),o=Vm.f,i=Xm.f,a=0;a<n.length;a++){var u=n[a];Ym(t,u)||r&&Ym(r,u)||o(t,u,i(e,u))}},xb=Ko,kb=mr,jb=q,Ab=function(t,e){Bm(e)&&"cause"in e&&Hm(t,"cause",e.cause)},Pb=function(t,e,r,n){ob&&(ib?ib(t,e):rb(t,"stack",nb(r,n)))},Ib=bb,Db=function(t,e){return void 0===t?arguments.length<2?"":e:wb(t)},Lb=ve("toStringTag"),Cb=Error,Rb=[].push,Mb=function(t,e){var r,n=Tb(Nb,this);Ob?r=Ob(new Cb,n?Eb(this):Nb):(r=n?this:xb(Nb),kb(r,Lb,"Error")),void 0!==e&&kb(r,"message",Db(e)),Pb(r,Mb,r.stack,1),arguments.length>2&&Ab(r,arguments[2]);var o=[];return Ib(t,Rb,{that:o}),kb(r,"errors",o),r};Ob?Ob(Mb,Cb):Sb(Mb,Cb,{name:!0});var Nb=Mb.prototype=xb(Cb.prototype,{constructor:jb(1,Mb),message:jb(1,""),name:jb(1,"AggregateError")});_b({global:!0,constructor:!0,arity:2},{AggregateError:Mb});var Fb,zb,Ub,qb,Wb=st,Yb=di,Gb=I,Xb=ve("species"),Vb=function(t){var e=Wb(t);Gb&&e&&!e[Xb]&&Yb(e,Xb,{configurable:!0,get:function(){return this}})},Bb=ct,Hb=TypeError,Kb=function(t,e){if(Bb(e,t))return t;throw new Hb("Incorrect invocation")},Jb=or,$b=Wg,Qb=B,Zb=ve("species"),tw=function(t,e){var r,n=Jb(t).constructor;return void 0===n||Qb(r=Jb(n)[Zb])?e:$b(r)},ew=/(?:ipad|iphone|ipod).*applewebkit/i.test(ft),rw=a,nw=p,ow=Qe,iw=A,aw=te,uw=u,sw=Do,cw=Ru,fw=je,lw=dd,hw=ew,pw=bl,vw=rw.setImmediate,dw=rw.clearImmediate,yw=rw.process,gw=rw.Dispatch,mw=rw.Function,bw=rw.MessageChannel,ww=rw.String,_w=0,Tw={},Ew="onreadystatechange";uw((function(){Fb=rw.location}));var Ow=function(t){if(aw(Tw,t)){var e=Tw[t];delete Tw[t],e()}},Sw=function(t){return function(){Ow(t)}},xw=function(t){Ow(t.data)},kw=function(t){rw.postMessage(ww(t),Fb.protocol+"//"+Fb.host)};vw&&dw||(vw=function(t){lw(arguments.length,1);var e=iw(t)?t:mw(t),r=cw(arguments,1);return Tw[++_w]=function(){nw(e,void 0,r)},zb(_w),_w},dw=function(t){delete Tw[t]},pw?zb=function(t){yw.nextTick(Sw(t))}:gw&&gw.now?zb=function(t){gw.now(Sw(t))}:bw&&!hw?(qb=(Ub=new bw).port2,Ub.port1.onmessage=xw,zb=ow(qb.postMessage,qb)):rw.addEventListener&&iw(rw.postMessage)&&!rw.importScripts&&Fb&&"file:"!==Fb.protocol&&!uw(kw)?(zb=kw,rw.addEventListener("message",xw,!1)):zb=Ew in fw("script")?function(t){sw.appendChild(fw("script"))[Ew]=function(){sw.removeChild(this),Ow(t)}}:function(t){setTimeout(Sw(t),0)});var jw={set:vw,clear:dw},Aw=function(){this.head=null,this.tail=null};Aw.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}};var Pw,Iw,Dw,Lw,Cw,Rw=Aw,Mw=/ipad|iphone|ipod/i.test(ft)&&"undefined"!=typeof Pebble,Nw=/web0s(?!.*chrome)/i.test(ft),Fw=a,zw=Qe,Uw=P.f,qw=jw.set,Ww=Rw,Yw=ew,Gw=Mw,Xw=Nw,Vw=bl,Bw=Fw.MutationObserver||Fw.WebKitMutationObserver,Hw=Fw.document,Kw=Fw.process,Jw=Fw.Promise,$w=Uw(Fw,"queueMicrotask"),Qw=$w&&$w.value;if(!Qw){var Zw=new Ww,t_=function(){var t,e;for(Vw&&(t=Kw.domain)&&t.exit();e=Zw.get();)try{e()}catch(t){throw Zw.head&&Pw(),t}t&&t.enter()};Yw||Vw||Xw||!Bw||!Hw?!Gw&&Jw&&Jw.resolve?((Lw=Jw.resolve(void 0)).constructor=Jw,Cw=zw(Lw.then,Lw),Pw=function(){Cw(t_)}):Vw?Pw=function(){Kw.nextTick(t_)}:(qw=zw(qw,Fw),Pw=function(){qw(t_)}):(Iw=!0,Dw=Hw.createTextNode(""),new Bw(t_).observe(Dw,{characterData:!0}),Pw=function(){Dw.data=Iw=!Iw}),Qw=function(t){Zw.head||Pw(),Zw.add(t)}}var e_=Qw,r_=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},n_=a.Promise,o_="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,i_=!o_&&!bl&&"object"==typeof window&&"object"==typeof document,a_=a,u_=n_,s_=A,c_=He,f_=vn,l_=ve,h_=i_,p_=o_,v_=gt,d_=u_&&u_.prototype,y_=l_("species"),g_=!1,m_=s_(a_.PromiseRejectionEvent),b_=c_("Promise",(function(){var t=f_(u_),e=t!==String(u_);if(!e&&66===v_)return!0;if(!d_.catch||!d_.finally)return!0;if(!v_||v_<51||!/native code/.test(t)){var r=new u_((function(t){t(1)})),n=function(t){t((function(){}),(function(){}))};if((r.constructor={})[y_]=n,!(g_=r.then((function(){}))instanceof n))return!0}return!e&&(h_||p_)&&!m_})),w_={CONSTRUCTOR:b_,REJECTION_EVENT:m_,SUBCLASSING:g_},__={},T_=Lt,E_=TypeError,O_=function(t){var e,r;this.promise=new t((function(t,n){if(void 0!==e||void 0!==r)throw new E_("Bad Promise constructor");e=t,r=n})),this.resolve=T_(e),this.reject=T_(r)};__.f=function(t){return new O_(t)};var S_,x_,k_=Pr,j_=bl,A_=a,P_=C,I_=pi,D_=zi,L_=Vb,C_=Lt,R_=A,M_=rt,N_=Kb,F_=tw,z_=jw.set,U_=e_,q_=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}},W_=r_,Y_=Rw,G_=ea,X_=n_,V_=w_,B_=__,H_="Promise",K_=V_.CONSTRUCTOR,J_=V_.REJECTION_EVENT,$_=G_.getterFor(H_),Q_=G_.set,Z_=X_&&X_.prototype,tT=X_,eT=Z_,rT=A_.TypeError,nT=A_.document,oT=A_.process,iT=B_.f,aT=iT,uT=!!(nT&&nT.createEvent&&A_.dispatchEvent),sT="unhandledrejection",cT=function(t){var e;return!(!M_(t)||!R_(e=t.then))&&e},fT=function(t,e){var r,n,o,i=e.value,a=1===e.state,u=a?t.ok:t.fail,s=t.resolve,c=t.reject,f=t.domain;try{u?(a||(2===e.rejection&&dT(e),e.rejection=1),!0===u?r=i:(f&&f.enter(),r=u(i),f&&(f.exit(),o=!0)),r===t.promise?c(new rT("Promise-chain cycle")):(n=cT(r))?P_(n,r,s,c):s(r)):c(i)}catch(t){f&&!o&&f.exit(),c(t)}},lT=function(t,e){t.notified||(t.notified=!0,U_((function(){for(var r,n=t.reactions;r=n.get();)fT(r,t);t.notified=!1,e&&!t.rejection&&pT(t)})))},hT=function(t,e,r){var n,o;uT?((n=nT.createEvent("Event")).promise=e,n.reason=r,n.initEvent(t,!1,!0),A_.dispatchEvent(n)):n={promise:e,reason:r},!J_&&(o=A_["on"+t])?o(n):t===sT&&q_("Unhandled promise rejection",r)},pT=function(t){P_(z_,A_,(function(){var e,r=t.facade,n=t.value;if(vT(t)&&(e=W_((function(){j_?oT.emit("unhandledRejection",n,r):hT(sT,r,n)})),t.rejection=j_||vT(t)?2:1,e.error))throw e.value}))},vT=function(t){return 1!==t.rejection&&!t.parent},dT=function(t){P_(z_,A_,(function(){var e=t.facade;j_?oT.emit("rejectionHandled",e):hT("rejectionhandled",e,t.value)}))},yT=function(t,e,r){return function(n){t(e,n,r)}},gT=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,lT(t,!0))},mT=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new rT("Promise can't be resolved itself");var n=cT(e);n?U_((function(){var r={done:!1};try{P_(n,e,yT(mT,r,t),yT(gT,r,t))}catch(e){gT(r,e,t)}})):(t.value=e,t.state=1,lT(t,!1))}catch(e){gT({done:!1},e,t)}}};K_&&(eT=(tT=function(t){N_(this,eT),C_(t),P_(S_,this);var e=$_(this);try{t(yT(mT,e),yT(gT,e))}catch(t){gT(e,t)}}).prototype,(S_=function(t){Q_(this,{type:H_,done:!1,notified:!1,parent:!1,reactions:new Y_,rejection:!1,state:0,value:void 0})}).prototype=I_(eT,"then",(function(t,e){var r=$_(this),n=iT(F_(this,tT));return r.parent=!0,n.ok=!R_(t)||t,n.fail=R_(e)&&e,n.domain=j_?oT.domain:void 0,0===r.state?r.reactions.add(n):U_((function(){fT(n,r)})),n.promise})),x_=function(){var t=new S_,e=$_(t);this.promise=t,this.resolve=yT(mT,e),this.reject=yT(gT,e)},B_.f=iT=function(t){return t===tT||undefined===t?new x_(t):aT(t)}),k_({global:!0,constructor:!0,wrap:!0,forced:K_},{Promise:tT}),D_(tT,H_,!1,!0),L_(H_);var bT=n_,wT=w_.CONSTRUCTOR||!zh((function(t){bT.all(t).then(void 0,(function(){}))})),_T=C,TT=Lt,ET=__,OT=r_,ST=bb;Pr({target:"Promise",stat:!0,forced:wT},{all:function(t){var e=this,r=ET.f(e),n=r.resolve,o=r.reject,i=OT((function(){var r=TT(e.resolve),i=[],a=0,u=1;ST(t,(function(t){var s=a++,c=!1;u++,_T(r,e,t).then((function(t){c||(c=!0,i[s]=t,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),r.promise}});var xT=Pr,kT=w_.CONSTRUCTOR;n_&&n_.prototype,xT({target:"Promise",proto:!0,forced:kT,real:!0},{catch:function(t){return this.then(void 0,t)}});var jT=C,AT=Lt,PT=__,IT=r_,DT=bb;Pr({target:"Promise",stat:!0,forced:wT},{race:function(t){var e=this,r=PT.f(e),n=r.reject,o=IT((function(){var o=AT(e.resolve);DT(t,(function(t){jT(o,e,t).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var LT=C,CT=__;Pr({target:"Promise",stat:!0,forced:w_.CONSTRUCTOR},{reject:function(t){var e=CT.f(this);return LT(e.reject,void 0,t),e.promise}});var RT=or,MT=rt,NT=__,FT=function(t,e){if(RT(t),MT(e)&&e.constructor===t)return e;var r=NT.f(t);return(0,r.resolve)(e),r.promise},zT=Pr,UT=n_,qT=w_.CONSTRUCTOR,WT=FT,YT=st("Promise"),GT=!qT;zT({target:"Promise",stat:!0,forced:true},{resolve:function(t){return WT(GT&&this===YT?UT:this,t)}});var XT=C,VT=Lt,BT=__,HT=r_,KT=bb;Pr({target:"Promise",stat:!0,forced:wT},{allSettled:function(t){var e=this,r=BT.f(e),n=r.resolve,o=r.reject,i=HT((function(){var r=VT(e.resolve),o=[],i=0,a=1;KT(t,(function(t){var u=i++,s=!1;a++,XT(r,e,t).then((function(t){s||(s=!0,o[u]={status:"fulfilled",value:t},--a||n(o))}),(function(t){s||(s=!0,o[u]={status:"rejected",reason:t},--a||n(o))}))})),--a||n(o)}));return i.error&&o(i.value),r.promise}});var JT=C,$T=Lt,QT=st,ZT=__,tE=r_,eE=bb,rE="No one promise resolved";Pr({target:"Promise",stat:!0,forced:wT},{any:function(t){var e=this,r=QT("AggregateError"),n=ZT.f(e),o=n.resolve,i=n.reject,a=tE((function(){var n=$T(e.resolve),a=[],u=0,s=1,c=!1;eE(t,(function(t){var f=u++,l=!1;s++,JT(n,e,t).then((function(t){l||c||(c=!0,o(t))}),(function(t){l||c||(l=!0,a[f]=t,--s||i(new r(a,rE)))}))})),--s||i(new r(a,rE))}));return a.error&&i(a.value),n.promise}});var nE=Pr,oE=n_,iE=u,aE=st,uE=A,sE=tw,cE=FT,fE=oE&&oE.prototype;nE({target:"Promise",proto:!0,real:!0,forced:!!oE&&iE((function(){fE.finally.call({then:function(){}},(function(){}))}))},{finally:function(t){var e=sE(this,aE("Promise")),r=uE(t);return this.then(r?function(r){return cE(e,t()).then((function(){return r}))}:t,r?function(r){return cE(e,t()).then((function(){throw r}))}:t)}});var lE=nt.Promise,hE=__;Pr({target:"Promise",stat:!0},{withResolvers:function(){var t=hE.f(this);return{promise:t.promise,resolve:t.resolve,reject:t.reject}}});var pE=lE,vE=__,dE=r_;Pr({target:"Promise",stat:!0,forced:!0},{try:function(t){var e=vE.f(this),r=dE(t);return(r.error?e.reject:e.resolve)(r.value),e.promise}});var yE=pE,gE=mv;!function(t){var e=qm.default,r=Nr,n=yf,o=jm,i=Mm,a=Wm,u=up,s=Pm,c=yE,f=gE,l=jp;function h(){t.exports=h=function(){return v},t.exports.__esModule=!0,t.exports.default=t.exports;var p,v={},d=Object.prototype,y=d.hasOwnProperty,g=r||function(t,e,r){t[e]=r.value},m="function"==typeof n?n:{},b=m.iterator||"@@iterator",w=m.asyncIterator||"@@asyncIterator",_=m.toStringTag||"@@toStringTag";function T(t,e,n){return r(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{T({},"")}catch(p){T=function(t,e,r){return t[e]=r}}function E(t,e,r,n){var i=e&&e.prototype instanceof P?e:P,a=o(i.prototype),u=new W(n||[]);return g(a,"_invoke",{value:F(t,r,u)}),a}function O(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}v.wrap=E;var S="suspendedStart",x="suspendedYield",k="executing",j="completed",A={};function P(){}function I(){}function D(){}var L={};T(L,b,(function(){return this}));var C=i&&i(i(Y([])));C&&C!==d&&y.call(C,b)&&(L=C);var R=D.prototype=P.prototype=o(L);function M(t){var e;a(e=["next","throw","return"]).call(e,(function(e){T(t,e,(function(t){return this._invoke(e,t)}))}))}function N(t,r){function n(o,i,a,u){var s=O(t[o],t,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==e(f)&&y.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):r.resolve(f).then((function(t){c.value=t,a(c)}),(function(t){return n("throw",t,a,u)}))}u(s.arg)}var o;g(this,"_invoke",{value:function(t,e){function i(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(i,i):i()}})}function F(t,e,r){var n=S;return function(o,i){if(n===k)throw new Error("Generator is already running");if(n===j){if("throw"===o)throw i;return{value:p,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=z(a,r);if(u){if(u===A)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===S)throw n=j,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=k;var s=O(t,e,r);if("normal"===s.type){if(n=r.done?j:x,s.arg===A)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n=j,r.method="throw",r.arg=s.arg)}}}function z(t,e){var r=e.method,n=t.iterator[r];if(n===p)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=p,z(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),A;var o=O(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,A;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=p),e.delegate=null,A):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,A)}function U(t){var e,r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),u(e=this.tryEntries).call(e,r)}function q(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function W(t){this.tryEntries=[{tryLoc:"root"}],a(t).call(t,U,this),this.reset(!0)}function Y(t){if(t||""===t){var r=t[b];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(y.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=p,e.done=!0,e};return o.next=o}}throw new TypeError(e(t)+" is not iterable")}return I.prototype=D,g(R,"constructor",{value:D,configurable:!0}),g(D,"constructor",{value:I,configurable:!0}),I.displayName=T(D,_,"GeneratorFunction"),v.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===I||"GeneratorFunction"===(e.displayName||e.name))},v.mark=function(t){return s?s(t,D):(t.__proto__=D,T(t,_,"GeneratorFunction")),t.prototype=o(R),t},v.awrap=function(t){return{__await:t}},M(N.prototype),T(N.prototype,w,(function(){return this})),v.AsyncIterator=N,v.async=function(t,e,r,n,o){void 0===o&&(o=c);var i=new N(E(t,e,r,n),o);return v.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},M(R),T(R,_,"Generator"),T(R,b,(function(){return this})),T(R,"toString",(function(){return"[object Generator]"})),v.keys=function(t){var e=Object(t),r=[];for(var n in e)u(r).call(r,n);return f(r).call(r),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},v.values=Y,W.prototype={constructor:W,reset:function(t){var e;if(this.prev=0,this.next=0,this.sent=this._sent=p,this.done=!1,this.delegate=null,this.method="next",this.arg=p,a(e=this.tryEntries).call(e,q),!t)for(var r in this)"t"===r.charAt(0)&&y.call(this,r)&&!isNaN(+l(r).call(r,1))&&(this[r]=p)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,n){return i.type="throw",i.arg=t,e.next=r,n&&(e.method="next",e.arg=p),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],i=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var a=y.call(o,"catchLoc"),u=y.call(o,"finallyLoc");if(a&&u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&y.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,A):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),A},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),q(r),A}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;q(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:Y(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=p),A}},v}t.exports=h,t.exports.__esModule=!0,t.exports.default=t.exports}(zm);var mE=(0,zm.exports)(),bE=mE;try{regeneratorRuntime=mE}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=mE:Function("r","regeneratorRuntime = r")(mE)}var wE=n(bE),_E={exports:{}},TE=u((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),EE=u,OE=rt,SE=T,xE=TE,kE=Object.isExtensible,jE=EE((function(){kE(1)}))||xE?function(t){return!!OE(t)&&((!xE||"ArrayBuffer"!==SE(t))&&(!kE||kE(t)))}:kE,AE=!u((function(){return Object.isExtensible(Object.preventExtensions({}))})),PE=Pr,IE=m,DE=po,LE=rt,CE=te,RE=Ze.f,ME=Jo,NE=Zo,FE=jE,zE=AE,UE=!1,qE=ie("meta"),WE=0,YE=function(t){RE(t,qE,{value:{objectID:"O"+WE++,weakData:{}}})},GE=_E.exports={enable:function(){GE.enable=function(){},UE=!0;var t=ME.f,e=IE([].splice),r={};r[qE]=1,t(r).length&&(ME.f=function(r){for(var n=t(r),o=0,i=n.length;o<i;o++)if(n[o]===qE){e(n,o,1);break}return n},PE({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:NE.f}))},fastKey:function(t,e){if(!LE(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!CE(t,qE)){if(!FE(t))return"F";if(!e)return"E";YE(t)}return t[qE].objectID},getWeakData:function(t,e){if(!CE(t,qE)){if(!FE(t))return!0;if(!e)return!1;YE(t)}return t[qE].weakData},onFreeze:function(t){return zE&&UE&&FE(t)&&!CE(t,qE)&&YE(t),t}};DE[qE]=!0;var XE=_E.exports,VE=Pr,BE=a,HE=XE,KE=u,JE=mr,$E=bb,QE=Kb,ZE=A,tO=rt,eO=B,rO=zi,nO=Ze.f,oO=ca.forEach,iO=I,aO=ea.set,uO=ea.getterFor,sO=function(t,e,r){var n,o=-1!==t.indexOf("Map"),i=-1!==t.indexOf("Weak"),a=o?"set":"add",u=BE[t],s=u&&u.prototype,c={};if(iO&&ZE(u)&&(i||s.forEach&&!KE((function(){(new u).entries().next()})))){var f=(n=e((function(e,r){aO(QE(e,f),{type:t,collection:new u}),eO(r)||$E(r,e[a],{that:e,AS_ENTRIES:o})}))).prototype,l=uO(t);oO(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var e="add"===t||"set"===t;!(t in s)||i&&"clear"===t||JE(f,t,(function(r,n){var o=l(this).collection;if(!e&&i&&!tO(r))return"get"===t&&void 0;var a=o[t](0===r?0:r,n);return e?this:a}))})),i||nO(f,"size",{configurable:!0,get:function(){return l(this).collection.size}})}else n=r.getConstructor(e,t,o,a),HE.enable();return rO(n,t,!1,!0),c[t]=n,VE({global:!0,forced:!0},c),i||r.setStrong(n,t,o),n},cO=pi,fO=Ko,lO=di,hO=function(t,e,r){for(var n in e)r&&r.unsafe&&t[n]?t[n]=e[n]:cO(t,n,e[n],r);return t},pO=Qe,vO=Kb,dO=B,yO=bb,gO=Oc,mO=Sc,bO=Vb,wO=I,_O=XE.fastKey,TO=ea.set,EO=ea.getterFor,OO={getConstructor:function(t,e,r,n){var o=t((function(t,o){vO(t,i),TO(t,{type:e,index:fO(null),first:void 0,last:void 0,size:0}),wO||(t.size=0),dO(o)||yO(o,t[n],{that:t,AS_ENTRIES:r})})),i=o.prototype,a=EO(e),u=function(t,e,r){var n,o,i=a(t),u=s(t,e);return u?u.value=r:(i.last=u={index:o=_O(e,!0),key:e,value:r,previous:n=i.last,next:void 0,removed:!1},i.first||(i.first=u),n&&(n.next=u),wO?i.size++:t.size++,"F"!==o&&(i.index[o]=u)),t},s=function(t,e){var r,n=a(t),o=_O(e);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key===e)return r};return hO(i,{clear:function(){for(var t=a(this),e=t.index,r=t.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete e[r.index],r=r.next;t.first=t.last=void 0,wO?t.size=0:this.size=0},delete:function(t){var e=this,r=a(e),n=s(e,t);if(n){var o=n.next,i=n.previous;delete r.index[n.index],n.removed=!0,i&&(i.next=o),o&&(o.previous=i),r.first===n&&(r.first=o),r.last===n&&(r.last=i),wO?r.size--:e.size--}return!!n},forEach:function(t){for(var e,r=a(this),n=pO(t,arguments.length>1?arguments[1]:void 0);e=e?e.next:r.first;)for(n(e.value,e.key,this);e&&e.removed;)e=e.previous},has:function(t){return!!s(this,t)}}),hO(i,r?{get:function(t){var e=s(this,t);return e&&e.value},set:function(t,e){return u(this,0===t?0:t,e)}}:{add:function(t){return u(this,t=0===t?0:t,t)}}),wO&&lO(i,"size",{configurable:!0,get:function(){return a(this).size}}),o},setStrong:function(t,e,r){var n=e+" Iterator",o=EO(e),i=EO(n);gO(t,e,(function(t,e){TO(this,{type:n,target:t,state:o(t),kind:e,last:void 0})}),(function(){for(var t=i(this),e=t.kind,r=t.last;r&&r.removed;)r=r.previous;return t.target&&(t.last=r=r?r.next:t.state.first)?mO("keys"===e?r.key:"values"===e?r.value:[r.key,r.value],!1):(t.target=void 0,mO(void 0,!0))}),r?"entries":"values",!r,!0),bO(e)}};sO("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),OO);var SO=n(nt.Map),xO=ca.some;Pr({target:"Array",proto:!0,forced:!ml("some")},{some:function(t){return xO(this,t,arguments.length>1?arguments[1]:void 0)}});var kO=nl("Array","some"),jO=ct,AO=kO,PO=Array.prototype,IO=n((function(t){var e=t.some;return t===PO||jO(PO,t)&&e===PO.some?AO:e})),DO=nl("Array","keys"),LO=fn,CO=te,RO=ct,MO=DO,NO=Array.prototype,FO={DOMTokenList:!0,NodeList:!0},zO=n((function(t){var e=t.keys;return t===NO||RO(NO,t)&&e===NO.keys||CO(FO,LO(t))?MO:e})),UO=ii,qO=Math.floor,WO=function(t,e){var r=t.length,n=qO(r/2);return r<8?YO(t,e):GO(t,WO(UO(t,0,n),e),WO(UO(t,n),e),e)},YO=function(t,e){for(var r,n,o=t.length,i=1;i<o;){for(n=i,r=t[i];n&&e(t[n-1],r)>0;)t[n]=t[--n];n!==i++&&(t[n]=r)}return t},GO=function(t,e,r,n){for(var o=e.length,i=r.length,a=0,u=0;a<o||u<i;)t[a+u]=a<o&&u<i?n(e[a],r[u])<=0?e[a++]:r[u++]:a<o?e[a++]:r[u++];return t},XO=WO,VO=ft.match(/firefox\/(\d+)/i),BO=!!VO&&+VO[1],HO=/MSIE|Trident/.test(ft),KO=ft.match(/AppleWebKit\/(\d+)\./),JO=!!KO&&+KO[1],$O=Pr,QO=m,ZO=Lt,tS=$t,eS=Hr,rS=Tv,nS=ro,oS=u,iS=XO,aS=ml,uS=BO,sS=HO,cS=gt,fS=JO,lS=[],hS=QO(lS.sort),pS=QO(lS.push),vS=oS((function(){lS.sort(void 0)})),dS=oS((function(){lS.sort(null)})),yS=aS("sort"),gS=!oS((function(){if(cS)return cS<70;if(!(uS&&uS>3)){if(sS)return!0;if(fS)return fS<603;var t,e,r,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(n=0;n<47;n++)lS.push({k:e+n,v:r})}for(lS.sort((function(t,e){return e.v-t.v})),n=0;n<lS.length;n++)e=lS[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));$O({target:"Array",proto:!0,forced:vS||!dS||!yS||!gS},{sort:function(t){void 0!==t&&ZO(t);var e=tS(this);if(gS)return void 0===t?hS(e):hS(e,t);var r,n,o=[],i=eS(e);for(n=0;n<i;n++)n in e&&pS(o,e[n]);for(iS(o,function(t){return function(e,r){return void 0===r?-1:void 0===e?1:void 0!==t?+t(e,r)||0:nS(e)>nS(r)?1:-1}}(t)),r=eS(o),n=0;n<r;)e[n]=o[n++];for(;n<i;)rS(e,n++);return e}});var mS=nl("Array","sort"),bS=ct,wS=mS,_S=Array.prototype,TS=n((function(t){var e=t.sort;return t===_S||bS(_S,t)&&e===_S.sort?wS:e})),ES=nl("Array","values"),OS=fn,SS=te,xS=ct,kS=ES,jS=Array.prototype,AS={DOMTokenList:!0,NodeList:!0},PS=n((function(t){var e=t.values;return t===jS||xS(jS,t)&&e===jS.values||SS(AS,OS(t))?kS:e})),IS=n(Cf),DS=nl("Array","entries"),LS=fn,CS=te,RS=ct,MS=DS,NS=Array.prototype,FS={DOMTokenList:!0,NodeList:!0},zS=n((function(t){var e=t.entries;return t===NS||RS(NS,t)&&e===NS.entries||CS(FS,LS(t))?MS:e}));let US;const qS=new Uint8Array(16);function WS(){if(!US&&(US="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!US))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return US(qS)}const YS=[];for(let t=0;t<256;++t)YS.push((t+256).toString(16).slice(1));var GS={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function XS(t,e,r){if(GS.randomUUID&&!e&&!t)return GS.randomUUID();const n=(t=t||{}).random||(t.rng||WS)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,e){r=r||0;for(let t=0;t<16;++t)e[r+t]=n[t];return e}return function(t,e=0){return YS[t[e+0]]+YS[t[e+1]]+YS[t[e+2]]+YS[t[e+3]]+"-"+YS[t[e+4]]+YS[t[e+5]]+"-"+YS[t[e+6]]+YS[t[e+7]]+"-"+YS[t[e+8]]+YS[t[e+9]]+"-"+YS[t[e+10]]+YS[t[e+11]]+YS[t[e+12]]+YS[t[e+13]]+YS[t[e+14]]+YS[t[e+15]]}(n)}function VS(t){return"string"==typeof t||"number"==typeof t}var BS=function(){function t(r){e(this,t),Wf(this,"_queue",[]),Wf(this,"_timeout",null),Wf(this,"_extended",null),this.delay=null,this.max=1/0,this.setOptions(r)}return qf(t,[{key:"setOptions",value:function(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}},{key:"destroy",value:function(){if(this.flush(),this._extended){for(var t=this._extended.object,e=this._extended.methods,r=0;r<e.length;r++){var n=e[r];n.original?t[n.name]=n.original:delete t[n.name]}this._extended=null}}},{key:"replace",value:function(t,e){var r=this,n=t[e];if(!n)throw new Error("Method "+e+" undefined");t[e]=function(){for(var t=arguments.length,e=new Array(t),o=0;o<t;o++)e[o]=arguments[o];r.queue({args:e,fn:n,context:this})}}},{key:"queue",value:function(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}},{key:"_flushIfNeeded",value:function(){var t=this;this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=Dd((function(){t.flush()}),this.delay))}},{key:"flush",value:function(){var t,e;cv(t=Uv(e=this._queue).call(e,0)).call(t,(function(t){t.fn.apply(t.context||t.fn,t.args||[])}))}}],[{key:"extend",value:function(e,r){var n=new t(r);if(void 0!==e.flush)throw new Error("Target object already has a property flush");e.flush=function(){n.flush()};var o=[{name:"flush",original:void 0}];if(r&&r.replace)for(var i=0;i<r.replace.length;i++){var a=r.replace[i];o.push({name:a,original:e[a]}),n.replace(e,a)}return n._extended={object:e,methods:o},n}}]),t}(),HS=function(){function t(){e(this,t),Wf(this,"_subscribers",{"*":[],add:[],remove:[],update:[]}),Wf(this,"subscribe",t.prototype.on),Wf(this,"unsubscribe",t.prototype.off)}return qf(t,[{key:"_trigger",value:function(t,e,r){var n,o;if("*"===t)throw new Error("Cannot trigger event *");cv(n=Up(o=[]).call(o,Cp(this._subscribers[t]),Cp(this._subscribers["*"]))).call(n,(function(n){n(t,e,null!=r?r:null)}))}},{key:"on",value:function(t,e){"function"==typeof e&&this._subscribers[t].push(e)}},{key:"off",value:function(t,e){var r;this._subscribers[t]=Il(r=this._subscribers[t]).call(r,(function(t){return t!==e}))}}]),t}();sO("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),OO);var KS=n(nt.Set),JS=n(Eh);function $S(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return QS(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return QS(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function QS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ZS=function(t){function r(t){e(this,r),this._pairs=t}return qf(r,[{key:t,value:wE.mark((function t(){var e,r,n,o,i;return wE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=$S(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Lp(r.value,2),o=n[0],i=n[1],t.next=7,[o,i];case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"entries",value:wE.mark((function t(){var e,r,n,o,i;return wE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=$S(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Lp(r.value,2),o=n[0],i=n[1],t.next=7,[o,i];case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"keys",value:wE.mark((function t(){var e,r,n,o;return wE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=$S(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Lp(r.value,1),o=n[0],t.next=7,o;case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"values",value:wE.mark((function t(){var e,r,n,o;return wE.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=$S(this._pairs),t.prev=1,e.s();case 3:if((r=e.n()).done){t.next=9;break}return n=Lp(r.value,2),o=n[1],t.next=7,o;case 7:t.next=3;break;case 9:t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.e(t.t0);case 14:return t.prev=14,e.f(),t.finish(14);case 17:case"end":return t.stop()}}),t,this,[[1,11,14,17]])}))},{key:"toIdArray",value:function(){var t;return Nl(t=Cp(this._pairs)).call(t,(function(t){return t[0]}))}},{key:"toItemArray",value:function(){var t;return Nl(t=Cp(this._pairs)).call(t,(function(t){return t[1]}))}},{key:"toEntryArray",value:function(){return Cp(this._pairs)}},{key:"toObjectMap",value:function(){var t,e=sd(null),r=$S(this._pairs);try{for(r.s();!(t=r.n()).done;){var n=Lp(t.value,2),o=n[0],i=n[1];e[o]=i}}catch(t){r.e(t)}finally{r.f()}return e}},{key:"toMap",value:function(){return new SO(this._pairs)}},{key:"toIdSet",value:function(){return new KS(this.toIdArray())}},{key:"toItemSet",value:function(){return new KS(this.toItemArray())}},{key:"cache",value:function(){return new r(Cp(this._pairs))}},{key:"distinct",value:function(t){var e,r=new KS,n=$S(this._pairs);try{for(n.s();!(e=n.n()).done;){var o=Lp(e.value,2),i=o[0],a=o[1];r.add(t(a,i))}}catch(t){n.e(t)}finally{n.f()}return r}},{key:"filter",value:function(t){var e=this._pairs;return new r({[IS]:()=>wE.mark((function r(){var n,o,i,a,u;return wE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=$S(e),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=10;break}if(i=Lp(o.value,2),a=i[0],u=i[1],!t(u,a)){r.next=8;break}return r.next=8,[a,u];case 8:r.next=3;break;case 10:r.next=15;break;case 12:r.prev=12,r.t0=r.catch(1),n.e(r.t0);case 15:return r.prev=15,n.f(),r.finish(15);case 18:case"end":return r.stop()}}),r,null,[[1,12,15,18]])}))()})}},{key:"forEach",value:function(t){var e,r=$S(this._pairs);try{for(r.s();!(e=r.n()).done;){var n=Lp(e.value,2),o=n[0];t(n[1],o)}}catch(t){r.e(t)}finally{r.f()}}},{key:"map",value:function(t){var e=this._pairs;return new r({[IS]:()=>wE.mark((function r(){var n,o,i,a,u;return wE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=$S(e),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=9;break}return i=Lp(o.value,2),a=i[0],u=i[1],r.next=7,[a,t(u,a)];case 7:r.next=3;break;case 9:r.next=14;break;case 11:r.prev=11,r.t0=r.catch(1),n.e(r.t0);case 14:return r.prev=14,n.f(),r.finish(14);case 17:case"end":return r.stop()}}),r,null,[[1,11,14,17]])}))()})}},{key:"max",value:function(t){var e=JS(this._pairs),r=e.next();if(r.done)return null;for(var n=r.value[1],o=t(r.value[1],r.value[0]);!(r=e.next()).done;){var i=Lp(r.value,2),a=i[0],u=i[1],s=t(u,a);s>o&&(o=s,n=u)}return n}},{key:"min",value:function(t){var e=JS(this._pairs),r=e.next();if(r.done)return null;for(var n=r.value[1],o=t(r.value[1],r.value[0]);!(r=e.next()).done;){var i=Lp(r.value,2),a=i[0],u=i[1],s=t(u,a);s<o&&(o=s,n=u)}return n}},{key:"reduce",value:function(t,e){var r,n=$S(this._pairs);try{for(n.s();!(r=n.n()).done;){var o=Lp(r.value,2),i=o[0];e=t(e,o[1],i)}}catch(t){n.e(t)}finally{n.f()}return e}},{key:"sort",value:function(t){var e=this;return new r({[IS]:function(){var r;return JS(TS(r=Cp(e._pairs)).call(r,(function(e,r){var n=Lp(e,2),o=n[0],i=n[1],a=Lp(r,2),u=a[0],s=a[1];return t(i,s,o,u)})))}})}}]),r}(IS);function tx(t,e){var r=Qp(t);if(om){var n=om(t);e&&(n=Il(n).call(n,(function(e){return pm(t,e).enumerable}))),r.push.apply(r,n)}return r}function ex(t){for(var e=1;e<arguments.length;e++){var r,n,o=null!=arguments[e]?arguments[e]:{};e%2?cv(r=tx(Object(o),!0)).call(r,(function(e){Wf(t,e,o[e])})):mm?Sm(t,mm(o)):cv(n=tx(Object(o))).call(n,(function(e){xm(t,e,pm(o,e))}))}return t}function rx(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return nx(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nx(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function nx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ox(t){var e=function(){if("undefined"==typeof Reflect||!nm)return!1;if(nm.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(nm(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Fm(t);if(e){var o=Fm(this).constructor;r=nm(n,arguments,o)}else r=n.apply(this,arguments);return Rm(this,r)}}var ix=function(t){Cm(n,t);var r=ox(n);function n(t,o){var i;return e(this,n),Wf(km(i=r.call(this)),"_queue",null),t&&!Kp(t)&&(o=t,t=[]),i._options=o||{},i._data=new SO,i.length=0,i._idProp=i._options.fieldId||"id",t&&t.length&&i.add(t),i.setOptions(o),i}return qf(n,[{key:"idProp",get:function(){return this._idProp}},{key:"setOptions",value:function(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=BS.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}},{key:"add",value:function(t,e){var r,n=this,o=[];if(Kp(t)){var i=Nl(t).call(t,(function(t){return t[n._idProp]}));if(IO(i).call(i,(function(t){return n._data.has(t)})))throw new Error("A duplicate id was found in the parameter array.");for(var a=0,u=t.length;a<u;a++)r=this._addItem(t[a]),o.push(r)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");r=this._addItem(t),o.push(r)}return o.length&&this._trigger("add",{items:o},e),o}},{key:"update",value:function(t,e){var r=this,n=[],o=[],i=[],a=[],u=this._idProp,s=function(t){var e=t[u];if(null!=e&&r._data.has(e)){var s=t,c=ed({},r._data.get(e)),f=r._updateItem(s);o.push(f),a.push(s),i.push(c)}else{var l=r._addItem(t);n.push(l)}};if(Kp(t))for(var c=0,f=t.length;c<f;c++)t[c]&&"object"==typeof t[c]?s(t[c]):console.warn("Ignoring input item, which is not an object at index "+c);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");s(t)}if(n.length&&this._trigger("add",{items:n},e),o.length){var l={items:o,oldData:i,data:a};this._trigger("update",l,e)}return Up(n).call(n,o)}},{key:"updateOnly",value:function(t,e){var r,n=this;Kp(t)||(t=[t]);var o=Nl(r=Nl(t).call(t,(function(t){var e=n._data.get(t[n._idProp]);if(null==e)throw new Error("Updating non-existent items is not allowed.");return{oldData:e,update:t}}))).call(r,(function(t){var e=t.oldData,r=t.update,o=e[n._idProp],i=function(t){for(var e,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return Lg.apply(void 0,Up(e=[{},t]).call(e,n))}(e,r);return n._data.set(o,i),{id:o,oldData:e,updatedData:i}}));if(o.length){var i={items:Nl(o).call(o,(function(t){return t.id})),oldData:Nl(o).call(o,(function(t){return t.oldData})),data:Nl(o).call(o,(function(t){return t.updatedData}))};return this._trigger("update",i,e),i.items}return[]}},{key:"get",value:function(t,e){var r=void 0,n=void 0,o=void 0;VS(t)?(r=t,o=e):Kp(t)?(n=t,o=e):o=t;var i,a=o&&"Object"===o.returnType?"Object":"Array",u=o&&Il(o),s=[],c=void 0,f=void 0,l=void 0;if(null!=r)(c=this._data.get(r))&&u&&!u(c)&&(c=void 0);else if(null!=n)for(var h=0,p=n.length;h<p;h++)null==(c=this._data.get(n[h]))||u&&!u(c)||s.push(c);else for(var v,d=0,y=(f=Cp(zO(v=this._data).call(v))).length;d<y;d++)l=f[d],null==(c=this._data.get(l))||u&&!u(c)||s.push(c);if(o&&o.order&&null==r&&this._sort(s,o.order),o&&o.fields){var g=o.fields;if(null!=r&&null!=c)c=this._filterFields(c,g);else for(var m=0,b=s.length;m<b;m++)s[m]=this._filterFields(s[m],g)}if("Object"==a){for(var w={},_=0,T=s.length;_<T;_++){var E=s[_];w[E[this._idProp]]=E}return w}return null!=r?null!==(i=c)&&void 0!==i?i:null:s}},{key:"getIds",value:function(t){var e=this._data,r=t&&Il(t),n=t&&t.order,o=Cp(zO(e).call(e)),i=[];if(r)if(n){for(var a=[],u=0,s=o.length;u<s;u++){var c=o[u],f=this._data.get(c);null!=f&&r(f)&&a.push(f)}this._sort(a,n);for(var l=0,h=a.length;l<h;l++)i.push(a[l][this._idProp])}else for(var p=0,v=o.length;p<v;p++){var d=o[p],y=this._data.get(d);null!=y&&r(y)&&i.push(y[this._idProp])}else if(n){for(var g=[],m=0,b=o.length;m<b;m++){var w=o[m];g.push(e.get(w))}this._sort(g,n);for(var _=0,T=g.length;_<T;_++)i.push(g[_][this._idProp])}else for(var E=0,O=o.length;E<O;E++){var S=o[E],x=e.get(S);null!=x&&i.push(x[this._idProp])}return i}},{key:"getDataSet",value:function(){return this}},{key:"forEach",value:function(t,e){var r=e&&Il(e),n=this._data,o=Cp(zO(n).call(n));if(e&&e.order)for(var i=this.get(e),a=0,u=i.length;a<u;a++){var s=i[a];t(s,s[this._idProp])}else for(var c=0,f=o.length;c<f;c++){var l=o[c],h=this._data.get(l);null==h||r&&!r(h)||t(h,l)}}},{key:"map",value:function(t,e){for(var r=e&&Il(e),n=[],o=this._data,i=Cp(zO(o).call(o)),a=0,u=i.length;a<u;a++){var s=i[a],c=this._data.get(s);null==c||r&&!r(c)||n.push(t(c,s))}return e&&e.order&&this._sort(n,e.order),n}},{key:"_filterFields",value:function(t,e){var r;return t?Sl(r=Kp(e)?e:Qp(e)).call(r,(function(e,r){return e[r]=t[r],e}),{}):t}},{key:"_sort",value:function(t,e){if("string"==typeof e){var r=e;TS(t).call(t,(function(t,e){var n=t[r],o=e[r];return n>o?1:n<o?-1:0}))}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");TS(t).call(t,e)}}},{key:"remove",value:function(t,e){for(var r=[],n=[],o=Kp(t)?t:[t],i=0,a=o.length;i<a;i++){var u=this._remove(o[i]);if(u){var s=u[this._idProp];null!=s&&(r.push(s),n.push(u))}}return r.length&&this._trigger("remove",{items:r,oldData:n},e),r}},{key:"_remove",value:function(t){var e;if(VS(t)?e=t:t&&"object"==typeof t&&(e=t[this._idProp]),null!=e&&this._data.has(e)){var r=this._data.get(e)||null;return this._data.delete(e),--this.length,r}return null}},{key:"clear",value:function(t){for(var e,r=Cp(zO(e=this._data).call(e)),n=[],o=0,i=r.length;o<i;o++)n.push(this._data.get(r[o]));return this._data.clear(),this.length=0,this._trigger("remove",{items:r,oldData:n},t),r}},{key:"max",value:function(t){var e,r,n=null,o=null,i=rx(PS(e=this._data).call(e));try{for(i.s();!(r=i.n()).done;){var a=r.value,u=a[t];"number"==typeof u&&(null==o||u>o)&&(n=a,o=u)}}catch(t){i.e(t)}finally{i.f()}return n||null}},{key:"min",value:function(t){var e,r,n=null,o=null,i=rx(PS(e=this._data).call(e));try{for(i.s();!(r=i.n()).done;){var a=r.value,u=a[t];"number"==typeof u&&(null==o||u<o)&&(n=a,o=u)}}catch(t){i.e(t)}finally{i.f()}return n||null}},{key:"distinct",value:function(t){for(var e=this._data,r=Cp(zO(e).call(e)),n=[],o=0,i=0,a=r.length;i<a;i++){for(var u=r[i],s=e.get(u)[t],c=!1,f=0;f<o;f++)if(n[f]==s){c=!0;break}c||void 0===s||(n[o]=s,o++)}return n}},{key:"_addItem",value:function(t){var e=function(t,e){return null==t[e]&&(t[e]=XS()),t}(t,this._idProp),r=e[this._idProp];if(this._data.has(r))throw new Error("Cannot add item: item with id "+r+" already exists");return this._data.set(r,e),++this.length,r}},{key:"_updateItem",value:function(t){var e=t[this._idProp];if(null==e)throw new Error("Cannot update item: item has no id (item: "+hd(t)+")");var r=this._data.get(e);if(!r)throw new Error("Cannot update item: no item with id "+e+" found");return this._data.set(e,ex(ex({},r),t)),e}},{key:"stream",value:function(t){if(t){var e=this._data;return new ZS({[IS]:()=>wE.mark((function r(){var n,o,i,a;return wE.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:n=rx(t),r.prev=1,n.s();case 3:if((o=n.n()).done){r.next=11;break}if(i=o.value,null==(a=e.get(i))){r.next=9;break}return r.next=9,[i,a];case 9:r.next=3;break;case 11:r.next=16;break;case 13:r.prev=13,r.t0=r.catch(1),n.e(r.t0);case 16:return r.prev=16,n.f(),r.finish(16);case 19:case"end":return r.stop()}}),r,null,[[1,13,16,19]])}))()})}var r;return new ZS({[IS]:cl(r=zS(this._data)).call(r,this._data)})}}]),n}(HS);function ax(t,e){var r=void 0!==Rp&&Xh(t)||t["@@iterator"];if(!r){if(Kp(t)||(r=function(t,e){var r;if(!t)return;if("string"==typeof t)return ux(t,e);var n=qp(r=Object.prototype.toString.call(t)).call(r,8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Wh(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ux(t,e)}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}function ux(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sx(t){var e=function(){if("undefined"==typeof Reflect||!nm)return!1;if(nm.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(nm(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Fm(t);if(e){var o=Fm(this).constructor;r=nm(n,arguments,o)}else r=n.apply(this,arguments);return Rm(this,r)}}var cx=function(t){Cm(n,t);var r=sx(n);function n(t,o){var i,a;return e(this,n),Wf(km(a=r.call(this)),"length",0),Wf(km(a),"_ids",new KS),a._options=o||{},a._listener=cl(i=a._onEvent).call(i,km(a)),a.setData(t),a}return qf(n,[{key:"idProp",get:function(){return this.getDataSet().idProp}},{key:"setData",value:function(t){if(this._data){this._data.off&&this._data.off("*",this._listener);var e=this._data.getIds({filter:Il(this._options)}),r=this._data.get(e);this._ids.clear(),this.length=0,this._trigger("remove",{items:e,oldData:r})}if(null!=t){this._data=t;for(var n=this._data.getIds({filter:Il(this._options)}),o=0,i=n.length;o<i;o++){var a=n[o];this._ids.add(a)}this.length=n.length,this._trigger("add",{items:n})}else this._data=new ix;this._data.on&&this._data.on("*",this._listener)}},{key:"refresh",value:function(){for(var t=this._data.getIds({filter:Il(this._options)}),e=Cp(this._ids),r={},n=[],o=[],i=[],a=0,u=t.length;a<u;a++){var s=t[a];r[s]=!0,this._ids.has(s)||(n.push(s),this._ids.add(s))}for(var c=0,f=e.length;c<f;c++){var l=e[c],h=this._data.get(l);null==h?console.error("If you see this, report it please."):r[l]||(o.push(l),i.push(h),this._ids.delete(l))}this.length+=n.length-o.length,n.length&&this._trigger("add",{items:n}),o.length&&this._trigger("remove",{items:o,oldData:i})}},{key:"get",value:function(t,e){if(null==this._data)return null;var r,n=null;VS(t)||Kp(t)?(n=t,r=e):r=t;var o=ed({},this._options,r),i=Il(this._options),a=r&&Il(r);return i&&a&&(o.filter=function(t){return i(t)&&a(t)}),null==n?this._data.get(o):this._data.get(n,o)}},{key:"getIds",value:function(t){if(this._data.length){var e,r=Il(this._options),n=null!=t?Il(t):null;return e=n?r?function(t){return r(t)&&n(t)}:n:r,this._data.getIds({filter:e,order:t&&t.order})}return[]}},{key:"forEach",value:function(t,e){if(this._data){var r,n,o=Il(this._options),i=e&&Il(e);n=i?o?function(t){return o(t)&&i(t)}:i:o,cv(r=this._data).call(r,t,{filter:n,order:e&&e.order})}}},{key:"map",value:function(t,e){if(this._data){var r,n,o=Il(this._options),i=e&&Il(e);return n=i?o?function(t){return o(t)&&i(t)}:i:o,Nl(r=this._data).call(r,t,{filter:n,order:e&&e.order})}return[]}},{key:"getDataSet",value:function(){return this._data.getDataSet()}},{key:"stream",value:function(t){var e;return this._data.stream(t||{[IS]:cl(e=zO(this._ids)).call(e,this._ids)})}},{key:"dispose",value:function(){var t;null!==(t=this._data)&&void 0!==t&&t.off&&this._data.off("*",this._listener);var e,r="This data view has already been disposed of.",o={get:function(){throw new Error(r)},set:function(){throw new Error(r)},configurable:!1},i=ax(Hp(n.prototype));try{for(i.s();!(e=i.n()).done;){var a=e.value;xm(this,a,o)}}catch(t){i.e(t)}finally{i.f()}}},{key:"_onEvent",value:function(t,e,r){if(e&&e.items&&this._data){var n=e.items,o=[],i=[],a=[],u=[],s=[],c=[];switch(t){case"add":for(var f=0,l=n.length;f<l;f++){var h=n[f];this.get(h)&&(this._ids.add(h),o.push(h))}break;case"update":for(var p=0,v=n.length;p<v;p++){var d=n[p];this.get(d)?this._ids.has(d)?(i.push(d),s.push(e.data[p]),u.push(e.oldData[p])):(this._ids.add(d),o.push(d)):this._ids.has(d)&&(this._ids.delete(d),a.push(d),c.push(e.oldData[p]))}break;case"remove":for(var y=0,g=n.length;y<g;y++){var m=n[y];this._ids.has(m)&&(this._ids.delete(m),a.push(m),c.push(e.oldData[y]))}}this.length+=o.length-a.length,o.length&&this._trigger("add",{items:o},r),i.length&&this._trigger("update",{items:i,oldData:u,data:s},r),a.length&&this._trigger("remove",{items:a,oldData:c},r)}}}]),n}(HS);function fx(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.add&&"function"==typeof e.clear&&"function"==typeof e.distinct&&"function"==typeof cv(e)&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof Nl(e)&&"function"==typeof e.max&&"function"==typeof e.min&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.remove&&"function"==typeof e.setOptions&&"function"==typeof e.stream&&"function"==typeof e.update&&"function"==typeof e.updateOnly}t.DELETE=Dg,t.DataSet=ix,t.DataStream=ZS,t.DataView=cx,t.Queue=BS,t.createNewDataPipeFrom=function(t){return new th(t)},t.isDataSetLike=fx,t.isDataViewLike=function(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof cv(e)&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof Nl(e)&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.stream&&fx(t,e.getDataSet())}}));
//# sourceMappingURL=vis-data.min.js.map
