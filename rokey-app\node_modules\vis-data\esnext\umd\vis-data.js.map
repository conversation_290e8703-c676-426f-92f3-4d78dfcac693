{"version": 3, "file": "vis-data.js", "sources": ["../../src/data-pipe.ts", "../../src/data-interface.ts", "../../src/queue.ts", "../../src/data-set-part.ts", "../../src/data-stream.ts", "../../src/data-set.ts", "../../src/data-view.ts", "../../src/data-set-check.ts", "../../src/data-view-check.ts"], "sourcesContent": ["import { DataInterface, EventCallbacks, PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * This interface is used to control the pipe.\n */\nexport interface DataPipe {\n  /**\n   * Take all items from the source data set or data view, transform them as\n   * configured and update the target data set.\n   */\n  all(): this;\n\n  /**\n   * Start observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   *\n   * @remarks\n   * The current content of the source data set will be ignored. If you for\n   * example want to process all the items that are already there use:\n   * `pipe.all().start()`.\n   */\n  start(): this;\n\n  /**\n   * Stop observing the source data set or data view, transforming the items\n   * and updating the target data set.\n   */\n  stop(): this;\n}\n\n/**\n * This interface is used to construct the pipe.\n */\nexport type DataPipeFactory = InstanceType<typeof DataPipeUnderConstruction>;\n\n/**\n * Create new data pipe.\n *\n * @param from - The source data set or data view.\n * @remarks\n * Example usage:\n * ```typescript\n * interface AppItem {\n *   whoami: string;\n *   appData: unknown;\n *   visData: VisItem;\n * }\n * interface VisItem {\n *   id: number;\n *   label: string;\n *   color: string;\n *   x: number;\n *   y: number;\n * }\n *\n * const ds1 = new DataSet<AppItem, \"whoami\">([], { fieldId: \"whoami\" });\n * const ds2 = new DataSet<VisItem, \"id\">();\n *\n * const pipe = createNewDataPipeFrom(ds1)\n *   .filter((item): boolean => item.enabled === true)\n *   .map<VisItem, \"id\">((item): VisItem => item.visData)\n *   .to(ds2);\n *\n * pipe.start();\n * ```\n * @returns A factory whose methods can be used to configure the pipe.\n */\nexport function createNewDataPipeFrom<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n>(from: DataInterface<SI, SP>): DataPipeUnderConstruction<SI, SP> {\n  return new DataPipeUnderConstruction(from);\n}\n\ntype Transformer<T> = (input: T[]) => T[];\n\n/**\n * Internal implementation of the pipe. This should be accessible only through\n * `createNewDataPipeFrom` from the outside.\n *\n * @typeParam SI - Source item type.\n * @typeParam SP - Source item type's id property name.\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass SimpleDataPipe<\n  SI extends PartItem<SP>,\n  SP extends string,\n  TI extends PartItem<TP>,\n  TP extends string\n> implements DataPipe\n{\n  /**\n   * Bound listeners for use with `DataInterface['on' | 'off']`.\n   */\n  private readonly _listeners: EventCallbacks<SI, SP> = {\n    add: this._add.bind(this),\n    remove: this._remove.bind(this),\n    update: this._update.bind(this),\n  };\n\n  /**\n   * Create a new data pipe.\n   *\n   * @param _source - The data set or data view that will be observed.\n   * @param _transformers - An array of transforming functions to be used to\n   * filter or transform the items in the pipe.\n   * @param _target - The data set or data view that will receive the items.\n   */\n  public constructor(\n    private readonly _source: DataInterface<SI, SP>,\n    private readonly _transformers: readonly Transformer<unknown>[],\n    private readonly _target: DataSet<TI, TP>\n  ) {}\n\n  /** @inheritDoc */\n  public all(): this {\n    this._target.update(this._transformItems(this._source.get()));\n    return this;\n  }\n\n  /** @inheritDoc */\n  public start(): this {\n    this._source.on(\"add\", this._listeners.add);\n    this._source.on(\"remove\", this._listeners.remove);\n    this._source.on(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /** @inheritDoc */\n  public stop(): this {\n    this._source.off(\"add\", this._listeners.add);\n    this._source.off(\"remove\", this._listeners.remove);\n    this._source.off(\"update\", this._listeners.update);\n\n    return this;\n  }\n\n  /**\n   * Apply the transformers to the items.\n   *\n   * @param items - The items to be transformed.\n   * @returns The transformed items.\n   */\n  private _transformItems(items: unknown[]): any[] {\n    return this._transformers.reduce((items, transform): unknown[] => {\n      return transform(items);\n    }, items);\n  }\n\n  /**\n   * Handle an add event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the added items.\n   */\n  private _add(\n    _name: Parameters<EventCallbacks<SI, SP>[\"add\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"add\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.add(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle an update event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the ids of the updated items.\n   */\n  private _update(\n    _name: Parameters<EventCallbacks<SI, SP>[\"update\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"update\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.update(this._transformItems(this._source.get(payload.items)));\n  }\n\n  /**\n   * Handle a remove event.\n   *\n   * @param _name - Ignored.\n   * @param payload - The payload containing the data of the removed items.\n   */\n  private _remove(\n    _name: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[0],\n    payload: Parameters<EventCallbacks<SI, SP>[\"remove\"]>[1]\n  ): void {\n    if (payload == null) {\n      return;\n    }\n\n    this._target.remove(this._transformItems(payload.oldData));\n  }\n}\n\n/**\n * Internal implementation of the pipe factory. This should be accessible\n * only through `createNewDataPipeFrom` from the outside.\n *\n * @typeParam TI - Target item type.\n * @typeParam TP - Target item type's id property name.\n */\nclass DataPipeUnderConstruction<\n  SI extends PartItem<SP>,\n  SP extends string = \"id\"\n> {\n  /**\n   * Array transformers used to transform items within the pipe. This is typed\n   * as any for the sake of simplicity.\n   */\n  private readonly _transformers: Transformer<any>[] = [];\n\n  /**\n   * Create a new data pipe factory. This is an internal constructor that\n   * should never be called from outside of this file.\n   *\n   * @param _source - The source data set or data view for this pipe.\n   */\n  public constructor(private readonly _source: DataInterface<SI, SP>) {}\n\n  /**\n   * Filter the items.\n   *\n   * @param callback - A filtering function that returns true if given item\n   * should be piped and false if not.\n   * @returns This factory for further configuration.\n   */\n  public filter(\n    callback: (item: SI) => boolean\n  ): DataPipeUnderConstruction<SI, SP> {\n    this._transformers.push((input): unknown[] => input.filter(callback));\n    return this;\n  }\n\n  /**\n   * Map each source item to a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * corresponding mapped item.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public map<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.map(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Map each source item to zero or more items of a new type.\n   *\n   * @param callback - A mapping function that takes a source item and returns\n   * an array of corresponding mapped items.\n   * @typeParam TI - Target item type.\n   * @typeParam TP - Target item type's id property name.\n   * @returns This factory for further configuration.\n   */\n  public flatMap<TI extends PartItem<TP>, TP extends string = \"id\">(\n    callback: (item: SI) => TI[]\n  ): DataPipeUnderConstruction<TI, TP> {\n    this._transformers.push((input): unknown[] => input.flatMap(callback));\n    return this as unknown as DataPipeUnderConstruction<TI, TP>;\n  }\n\n  /**\n   * Connect this pipe to given data set.\n   *\n   * @param target - The data set that will receive the items from this pipe.\n   * @returns The pipe connected between given data sets and performing\n   * configured transformation on the processed items.\n   */\n  public to(target: DataSet<SI, SP>): DataPipe {\n    return new SimpleDataPipe(this._source, this._transformers, target);\n  }\n}\n", "import { Assignable } from \"vis-util/esnext\";\nimport { DataSet } from \"./data-set\";\nimport { DataStream } from \"./data-stream\";\n\ntype ValueOf<T> = T[keyof T];\n\n/** Valid id type. */\nexport type Id = number | string;\n/** Nullable id type. */\nexport type OptId = undefined | null | Id;\n/**\n * Determine whether a value can be used as an id.\n *\n * @param value - Input value of unknown type.\n * @returns True if the value is valid id, false otherwise.\n */\nexport function isId(value: unknown): value is Id {\n  return typeof value === \"string\" || typeof value === \"number\";\n}\n\n/**\n * Make an object deeply partial.\n */\nexport type DeepPartial<T> = T extends any[] | Function | Node\n  ? T\n  : T extends object\n  ? { [key in keyof T]?: DeepPartial<T[key]> }\n  : T;\n\n/**\n * An item that may ({@link Id}) or may not (absent, undefined or null) have an id property.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type PartItem<IdProp extends string> = Partial<Record<IdProp, OptId>>;\n/**\n * An item that has a property containing an id and all other required properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type FullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Item & Record<IdProp, Id>;\n/**\n * An item that has a property containing an id and optionally other properties of given item type.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport type UpdateItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> = Assignable<FullItem<Item, IdProp>> & Record<IdProp, Id>;\n\n/**\n * Test whether an item has an id (is a {@link FullItem}).\n *\n * @param item - The item to be tested.\n * @param idProp - Name of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns True if this value is a {@link FullItem}, false otherwise.\n */\nexport function isFullItem<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n>(item: Item, idProp: IdProp): item is FullItem<Item, IdProp> {\n  return item[idProp] != null;\n}\n\n/** Add event payload. */\nexport interface AddEventPayload {\n  /** Ids of added items. */\n  items: Id[];\n}\n/** Update event payload. */\nexport interface UpdateEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of updated items. */\n  items: Id[];\n  /** Items as they were before this update. */\n  oldData: FullItem<Item, IdProp>[];\n  /**\n   * Items as they are now.\n   *\n   * @deprecated Just get the data from the data set or data view.\n   */\n  data: FullItem<Item, IdProp>[];\n}\n/** Remove event payload. */\nexport interface RemoveEventPayload<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /** Ids of removed items. */\n  items: Id[];\n  /** Items as they were before their removal. */\n  oldData: FullItem<Item, IdProp>[];\n}\n\n/**\n * Map of event payload types (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloads<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  add: AddEventPayload;\n  update: UpdateEventPayload<Item, IdProp>;\n  remove: RemoveEventPayload<Item, IdProp>;\n}\n/**\n * Map of event payload types including any event (event name → payload).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventPayloadsWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventPayloads<Item, IdProp> {\n  \"*\": ValueOf<EventPayloads<Item, IdProp>>;\n}\n\n/**\n * Map of event callback types (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacks<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  add(name: \"add\", payload: AddEventPayload | null, senderId?: Id | null): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  update(\n    name: \"update\",\n    payload: UpdateEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  remove(\n    name: \"remove\",\n    payload: RemoveEventPayload<Item, IdProp> | null,\n    senderId?: Id | null\n  ): void;\n}\n/**\n * Map of event callback types including any event (event name → callback).\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface EventCallbacksWithAny<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> extends EventCallbacks<Item, IdProp> {\n  /**\n   * @param name - The name of the event ({@link EventName}).\n   * @param payload - Data about the items affected by this event.\n   * @param senderId - A senderId, optionally provided by the application code which triggered the event. If senderId is not provided, the argument will be `null`.\n   */\n  \"*\"<N extends keyof EventCallbacks<Item, IdProp>>(\n    name: N,\n    payload: EventPayloads<Item, IdProp>[N],\n    senderId?: Id | null\n  ): void;\n}\n\n/** Available event names. */\nexport type EventName = keyof EventPayloads<never, \"\">;\n/** Available event names and '*' to listen for all. */\nexport type EventNameWithAny = keyof EventPayloadsWithAny<never, \"\">;\n\n/**\n * Data interface order parameter.\n * - A string value determines which property will be used for sorting (using < and > operators for numeric comparison).\n * - A function will be used the same way as in Array.sort.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceOrder<Item> =\n  | keyof Item\n  | ((a: Item, b: Item) => number);\n\n/**\n * Data interface get options (return type independent).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsBase<Item> {\n  /**\n   * An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items.\n   *\n   * @remarks\n   * Warning**: There is no TypeScript support for this.\n   */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface get options (returns a single item or an array).\n *\n * @remarks\n * Whether an item or and array of items is returned is determined by the type of the id(s) argument.\n * If an array of ids is requested an array of items will be returned.\n * If a single id is requested a single item (or null if the id doesn't correspond to any item) will be returned.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsArray<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as a single item (if invoked with an id) or an array of items (if invoked with an array of ids). */\n  returnType?: undefined | \"Array\";\n}\n/**\n * Data interface get options (returns an object).\n *\n * @remarks\n * The returned object has ids as keys and items as values of corresponding ids.\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetOptionsObject<Item>\n  extends DataInterfaceGetOptionsBase<Item> {\n  /** Items will be returned as an object map (id → item). */\n  returnType: \"Object\";\n}\n/**\n * Data interface get options (returns single item, an array or object).\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport type DataInterfaceGetOptions<Item> =\n  | DataInterfaceGetOptionsArray<Item>\n  | DataInterfaceGetOptionsObject<Item>;\n\n/**\n * Data interface get ids options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceGetIdsOptions<Item> {\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface for each options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n */\nexport interface DataInterfaceForEachOptions<Item> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Item>;\n}\n\n/**\n * Data interface map oprions.\n *\n * @typeParam Original - The original item type in the data.\n * @typeParam Mapped - The type after mapping.\n */\nexport interface DataInterfaceMapOptions<Original, Mapped> {\n  /** An array with field names, or an object with current field name and new field name that the field is returned as. By default, all properties of the items are emitted. When fields is defined, only the properties whose name is specified in fields will be included in the returned items. */\n  fields?: string[] | Record<string, string>;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Original) => boolean;\n  /** Order the items by a field name or custom sort function. */\n  order?: DataInterfaceOrder<Mapped>;\n}\n\n/**\n * Common interface for data sets and data view.\n *\n * @typeParam Item - Item type that may or may not have an id (missing ids will be generated upon insertion).\n * @typeParam IdProp - Name of the property on the Item type that contains the id.\n */\nexport interface DataInterface<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n> {\n  /** The number of items. */\n  length: number;\n\n  /** The key of id property. */\n  idProp: IdProp;\n\n  /**\n   * Add a universal event listener.\n   *\n   * @remarks The `*` event is triggered when any of the events `add`, `update`, and `remove` occurs.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Add an `add` event listener.\n   *\n   * @remarks The `add` event is triggered when an item or a set of items is added, or when an item is updated while not yet existing.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Add a `remove` event listener.\n   *\n   * @remarks The `remove` event is triggered when an item or a set of items is removed.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Add an `update` event listener.\n   *\n   * @remarks The `update` event is triggered when an existing item or a set of existing items is updated.\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Remove a universal event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"*\", callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]): void;\n  /**\n   * Remove an `add` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(event: \"add\", callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]): void;\n  /**\n   * Remove a `remove` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /**\n   * Remove an `update` event listener.\n   *\n   * @param event - Event name.\n   * @param callback - Callback function.\n   */\n  off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n\n  /**\n   * Get all the items.\n   *\n   * @returns An array containing all the items.\n   */\n  get(): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items.\n   */\n  get(options: DataInterfaceGetOptionsArray<Item>): FullItem<Item, IdProp>[];\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get all the items.\n   *\n   * @param options - Additional options.\n   * @returns An array containing requested items or if requested an object map of items (may be an empty object if there are no items).\n   */\n  get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(id: Id): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item or null if the id doesn't correspond to any item.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get one item.\n   *\n   * @param id - The id of the item.\n   * @param options - Additional options.\n   * @returns The item if found or null otherwise. If requested an object map with 0 to 1 items.\n   */\n  get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(ids: Id[]): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get multiple items.\n   *\n   * @param ids - An array of requested ids.\n   * @param options - Additional options.\n   * @returns An array of found items (ids that do not correspond to any item are omitted).\n   * If requested an object map of items (may be an empty object if no item was found).\n   */\n  get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /**\n   * Get items.\n   *\n   * @param ids - Id or ids to be returned.\n   * @param options - Options to specify iteration details.\n   * @returns The items (format is determined by ids (single or array) and the options.\n   */\n  get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /**\n   * Get the DataSet to which the instance implementing this interface is connected.\n   * In case there is a chain of multiple DataViews, the root DataSet of this chain is returned.\n   *\n   * @returns The data set that actually contains the data.\n   */\n  getDataSet(): DataSet<Item, IdProp>;\n\n  /**\n   * Get ids of items.\n   *\n   * @remarks\n   * No guarantee is given about the order of returned ids unless an ordering function is supplied.\n   * @param options - Additional configuration.\n   * @returns An array of requested ids.\n   */\n  getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[];\n\n  /**\n   * Execute a callback function for each item.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration unless an ordering function is supplied.\n   * @param callback - Executed in similar fashion to Array.forEach callback, but instead of item, index, array receives item, id.\n   * @param options - Options to specify iteration details.\n   */\n  forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void;\n\n  /**\n   * Map each item into different item and return them as an array.\n   *\n   * @remarks\n   * No guarantee is given about the order of iteration even if ordering function is supplied (the items are sorted after the mapping).\n   * @param callback - Array.map-like callback, but only with the first two params.\n   * @param options - Options to specify iteration details.\n   * @returns The mapped items.\n   */\n  map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[];\n\n  /**\n   * Stream.\n   *\n   * @param ids - Ids of the items to be included in this stream (missing are ignored), all if omitted.\n   * @returns The data stream for this data set.\n   */\n  stream(ids?: Iterable<Id>): DataStream<Item>;\n}\n", "/** Queue configuration object. */\nexport interface QueueOptions {\n  /** The queue will be flushed automatically after an inactivity of this delay in milliseconds. By default there is no automatic flushing (`null`). */\n  delay?: null | number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value is `Infinity`. */\n  max?: number;\n}\n/**\n * Queue extending options.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport interface QueueExtendOptions<T> {\n  /** A list with method names of the methods on the object to be replaced with queued ones. */\n  replace: T[];\n  /** When provided, the queue will be flushed automatically after an inactivity of this delay in milliseconds. Default value is null. */\n  delay?: number;\n  /** When the queue exceeds the given maximum number of entries, the queue is flushed automatically. Default value of max is Infinity. */\n  max?: number;\n}\n/**\n * Queue call entry.\n * - A function to be executed.\n * - An object with function, args, context (like function.bind(context, ...args)).\n */\ntype QueueCallEntry =\n  | Function\n  | {\n      fn: Function;\n      args: unknown[];\n    }\n  | {\n      fn: Function;\n      args: unknown[];\n      context: unknown;\n    };\n\ninterface QueueExtended<O> {\n  object: O;\n  methods: {\n    name: string;\n    original: unknown;\n  }[];\n}\n\n/**\n * A queue.\n *\n * @typeParam T - The type of method names to be replaced by queued versions.\n */\nexport class Queue<T = never> {\n  /** Delay in milliseconds. If defined the queue will be periodically flushed. */\n  public delay: null | number;\n  /** Maximum number of entries in the queue before it will be flushed. */\n  public max: number;\n\n  private readonly _queue: {\n    fn: Function;\n    args?: unknown[];\n    context?: unknown;\n  }[] = [];\n\n  private _timeout: ReturnType<typeof setTimeout> | null = null;\n  private _extended: null | QueueExtended<T> = null;\n\n  /**\n   * Construct a new Queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public constructor(options?: QueueOptions) {\n    // options\n    this.delay = null;\n    this.max = Infinity;\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Update the configuration of the queue.\n   *\n   * @param options - Queue configuration.\n   */\n  public setOptions(options?: QueueOptions): void {\n    if (options && typeof options.delay !== \"undefined\") {\n      this.delay = options.delay;\n    }\n    if (options && typeof options.max !== \"undefined\") {\n      this.max = options.max;\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Extend an object with queuing functionality.\n   * The object will be extended with a function flush, and the methods provided in options.replace will be replaced with queued ones.\n   *\n   * @param object - The object to be extended.\n   * @param options - Additional options.\n   * @returns The created queue.\n   */\n  public static extend<O extends { flush?: () => void }, K extends string>(\n    object: O,\n    options: QueueExtendOptions<K>\n  ): Queue<O> {\n    const queue = new Queue<O>(options);\n\n    if (object.flush !== undefined) {\n      throw new Error(\"Target object already has a property flush\");\n    }\n    object.flush = (): void => {\n      queue.flush();\n    };\n\n    const methods: QueueExtended<O>[\"methods\"] = [\n      {\n        name: \"flush\",\n        original: undefined,\n      },\n    ];\n\n    if (options && options.replace) {\n      for (let i = 0; i < options.replace.length; i++) {\n        const name = options.replace[i];\n        methods.push({\n          name: name,\n          // @TODO: better solution?\n          original: (object as unknown as Record<K, () => void>)[name],\n        });\n        // @TODO: better solution?\n        queue.replace(object as unknown as Record<K, () => void>, name);\n      }\n    }\n\n    queue._extended = {\n      object: object,\n      methods: methods,\n    };\n\n    return queue;\n  }\n\n  /**\n   * Destroy the queue. The queue will first flush all queued actions, and in case it has extended an object, will restore the original object.\n   */\n  public destroy(): void {\n    this.flush();\n\n    if (this._extended) {\n      const object = this._extended.object;\n      const methods = this._extended.methods;\n      for (let i = 0; i < methods.length; i++) {\n        const method = methods[i];\n        if (method.original) {\n          // @TODO: better solution?\n          (object as any)[method.name] = method.original;\n        } else {\n          // @TODO: better solution?\n          delete (object as any)[method.name];\n        }\n      }\n      this._extended = null;\n    }\n  }\n\n  /**\n   * Replace a method on an object with a queued version.\n   *\n   * @param object - Object having the method.\n   * @param method - The method name.\n   */\n  public replace<M extends string>(\n    object: Record<M, () => void>,\n    method: M\n  ): void {\n    /* eslint-disable-next-line @typescript-eslint/no-this-alias -- Function this is necessary in the function bellow, so class this has to be saved into a variable here. */\n    const me = this;\n    const original = object[method];\n    if (!original) {\n      throw new Error(\"Method \" + method + \" undefined\");\n    }\n\n    object[method] = function (...args: unknown[]): void {\n      // add this call to the queue\n      me.queue({\n        args: args,\n        fn: original,\n        context: this,\n      });\n    };\n  }\n\n  /**\n   * Queue a call.\n   *\n   * @param entry - The function or entry to be queued.\n   */\n  public queue(entry: QueueCallEntry): void {\n    if (typeof entry === \"function\") {\n      this._queue.push({ fn: entry });\n    } else {\n      this._queue.push(entry);\n    }\n\n    this._flushIfNeeded();\n  }\n\n  /**\n   * Check whether the queue needs to be flushed.\n   */\n  private _flushIfNeeded(): void {\n    // flush when the maximum is exceeded.\n    if (this._queue.length > this.max) {\n      this.flush();\n    }\n\n    // flush after a period of inactivity when a delay is configured\n    if (this._timeout != null) {\n      clearTimeout(this._timeout);\n      this._timeout = null;\n    }\n    if (this.queue.length > 0 && typeof this.delay === \"number\") {\n      this._timeout = setTimeout((): void => {\n        this.flush();\n      }, this.delay);\n    }\n  }\n\n  /**\n   * Flush all queued calls\n   */\n  public flush(): void {\n    this._queue.splice(0).forEach((entry): void => {\n      entry.fn.apply(entry.context || entry.fn, entry.args || []);\n    });\n  }\n}\n", "import {\n  DataInterface,\n  EventCallbacksWithAny,\n  EventName,\n  EventNameWithAny,\n  EventPayloads,\n  Id,\n  PartItem,\n} from \"./data-interface\";\n\ntype EventSubscribers<Item extends PartItem<IdProp>, IdProp extends string> = {\n  [Name in keyof EventCallbacksWithAny<Item, IdProp>]: (...args: any[]) => void;\n};\n\n/**\n * {@link DataSet} code that can be reused in {@link DataView} or other similar implementations of {@link DataInterface}.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport abstract class DataSetPart<\n  Item extends PartItem<IdProp>,\n  IdProp extends string\n> implements Pick<DataInterface<Item, IdProp>, \"on\" | \"off\">\n{\n  private readonly _subscribers: {\n    [Name in EventNameWithAny]: EventSubscribers<Item, IdProp>[Name][];\n  } = {\n    \"*\": [],\n    add: [],\n    remove: [],\n    update: [],\n  };\n\n  protected _trigger(\n    event: \"add\",\n    payload: EventPayloads<Item, IdProp>[\"add\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"update\",\n    payload: EventPayloads<Item, IdProp>[\"update\"],\n    senderId?: Id | null\n  ): void;\n  protected _trigger(\n    event: \"remove\",\n    payload: EventPayloads<Item, IdProp>[\"remove\"],\n    senderId?: Id | null\n  ): void;\n  /**\n   * Trigger an event\n   *\n   * @param event - Event name.\n   * @param payload - Event payload.\n   * @param senderId - Id of the sender.\n   */\n  protected _trigger<Name extends EventName>(\n    event: Name,\n    payload: EventPayloads<Item, IdProp>[Name],\n    senderId?: Id | null\n  ): void {\n    if ((event as string) === \"*\") {\n      throw new Error(\"Cannot trigger event *\");\n    }\n\n    [...this._subscribers[event], ...this._subscribers[\"*\"]].forEach(\n      (subscriber): void => {\n        subscriber(event, payload, senderId != null ? senderId : null);\n      }\n    );\n  }\n\n  /** @inheritDoc */\n  public on(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public on(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Subscribe to an event, add an event listener.\n   *\n   * @remarks Non-function callbacks are ignored.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public on<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    if (typeof callback === \"function\") {\n      this._subscribers[event].push(callback);\n    }\n    // @TODO: Maybe throw for invalid callbacks?\n  }\n\n  /** @inheritDoc */\n  public off(\n    event: \"*\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"*\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"add\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"add\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"remove\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"remove\"]\n  ): void;\n  /** @inheritDoc */\n  public off(\n    event: \"update\",\n    callback: EventCallbacksWithAny<Item, IdProp>[\"update\"]\n  ): void;\n  /**\n   * Unsubscribe from an event, remove an event listener.\n   *\n   * @remarks If the same callback was subscribed more than once **all** occurences will be removed.\n   * @param event - Event name.\n   * @param callback - Callback method.\n   */\n  public off<Name extends EventNameWithAny>(\n    event: Name,\n    callback: EventCallbacksWithAny<Item, IdProp>[Name]\n  ): void {\n    this._subscribers[event] = this._subscribers[event].filter(\n      (subscriber): boolean => subscriber !== callback\n    );\n  }\n\n  /**\n   * @deprecated Use on instead (PS: DataView.subscribe === DataView.on).\n   */\n  public subscribe: DataSetPart<Item, IdProp>[\"on\"] = DataSetPart.prototype.on;\n  /**\n   * @deprecated Use off instead (PS: DataView.unsubscribe === DataView.off).\n   */\n  public unsubscribe: DataSetPart<Item, IdProp>[\"off\"] =\n    DataSetPart.prototype.off;\n\n  /* develblock:start */\n  public get testLeakSubscribers(): any {\n    return this._subscribers;\n  }\n  /* develblock:end */\n}\n", "import { Id } from \"./data-interface\";\n\n/**\n * Data stream\n *\n * @remarks\n * {@link DataStream} offers an always up to date stream of items from a {@link DataSet} or {@link DataView}.\n * That means that the stream is evaluated at the time of iteration, conversion to another data type or when {@link cache} is called, not when the {@link DataStream} was created.\n * Multiple invocations of for example {@link toItemArray} may yield different results (if the data source like for example {@link DataSet} gets modified).\n * @typeParam Item - The item type this stream is going to work with.\n */\nexport class DataStream<Item> implements Iterable<[Id, Item]> {\n  private readonly _pairs: Iterable<[Id, Item]>;\n\n  /**\n   * Create a new data stream.\n   *\n   * @param pairs - The id, item pairs.\n   */\n  public constructor(pairs: Iterable<[Id, Item]>) {\n    this._pairs = pairs;\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of key, value pairs for every entry in the stream.\n   */\n  public *entries(): IterableIterator<[Id, Item]> {\n    for (const [id, item] of this._pairs) {\n      yield [id, item];\n    }\n  }\n\n  /**\n   * Return an iterable of keys in the stream.\n   */\n  public *keys(): IterableIterator<Id> {\n    for (const [id] of this._pairs) {\n      yield id;\n    }\n  }\n\n  /**\n   * Return an iterable of values in the stream.\n   */\n  public *values(): IterableIterator<Item> {\n    for (const [, item] of this._pairs) {\n      yield item;\n    }\n  }\n\n  /**\n   * Return an array containing all the ids in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all ids from this stream.\n   */\n  public toIdArray(): Id[] {\n    return [...this._pairs].map((pair): Id => pair[0]);\n  }\n\n  /**\n   * Return an array containing all the items in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all items from this stream.\n   */\n  public toItemArray(): Item[] {\n    return [...this._pairs].map((pair): Item => pair[1]);\n  }\n\n  /**\n   * Return an array containing all the entries in this stream.\n   *\n   * @remarks\n   * The array may contain duplicities.\n   * @returns The array with all entries from this stream.\n   */\n  public toEntryArray(): [Id, Item][] {\n    return [...this._pairs];\n  }\n\n  /**\n   * Return an object map containing all the items in this stream accessible by ids.\n   *\n   * @remarks\n   * In case of duplicate ids (coerced to string so `7 == '7'`) the last encoutered appears in the returned object.\n   * @returns The object map of all id → item pairs from this stream.\n   */\n  public toObjectMap(): Record<Id, Item> {\n    const map: Record<Id, Item> = Object.create(null);\n    for (const [id, item] of this._pairs) {\n      map[id] = item;\n    }\n    return map;\n  }\n\n  /**\n   * Return a map containing all the items in this stream accessible by ids.\n   *\n   * @returns The map of all id → item pairs from this stream.\n   */\n  public toMap(): Map<Id, Item> {\n    return new Map(this._pairs);\n  }\n\n  /**\n   * Return a set containing all the (unique) ids in this stream.\n   *\n   * @returns The set of all ids from this stream.\n   */\n  public toIdSet(): Set<Id> {\n    return new Set(this.toIdArray());\n  }\n\n  /**\n   * Return a set containing all the (unique) items in this stream.\n   *\n   * @returns The set of all items from this stream.\n   */\n  public toItemSet(): Set<Item> {\n    return new Set(this.toItemArray());\n  }\n\n  /**\n   * Cache the items from this stream.\n   *\n   * @remarks\n   * This method allows for items to be fetched immediatelly and used (possibly multiple times) later.\n   * It can also be used to optimize performance as {@link DataStream} would otherwise reevaluate everything upon each iteration.\n   *\n   * ## Example\n   * ```javascript\n   * const ds = new DataSet([…])\n   *\n   * const cachedStream = ds.stream()\n   *   .filter(…)\n   *   .sort(…)\n   *   .map(…)\n   *   .cached(…) // Data are fetched, processed and cached here.\n   *\n   * ds.clear()\n   * chachedStream // Still has all the items.\n   * ```\n   * @returns A new {@link DataStream} with cached items (detached from the original {@link DataSet}).\n   */\n  public cache(): DataStream<Item> {\n    return new DataStream([...this._pairs]);\n  }\n\n  /**\n   * Get the distinct values of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @typeParam T - The type of the distinct value.\n   * @returns A set of all distinct properties.\n   */\n  public distinct<T>(callback: (item: Item, id: Id) => T): Set<T> {\n    const set = new Set<T>();\n\n    for (const [id, item] of this._pairs) {\n      set.add(callback(item, id));\n    }\n\n    return set;\n  }\n\n  /**\n   * Filter the items of the stream.\n   *\n   * @param callback - The function that decides whether an item will be included.\n   * @returns A new data stream with the filtered items.\n   */\n  public filter(callback: (item: Item, id: Id) => boolean): DataStream<Item> {\n    const pairs = this._pairs;\n    return new DataStream<Item>({\n      *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n        for (const [id, item] of pairs) {\n          if (callback(item, id)) {\n            yield [id, item];\n          }\n        }\n      },\n    });\n  }\n\n  /**\n   * Execute a callback for each item of the stream.\n   *\n   * @param callback - The function that will be invoked for each item.\n   */\n  public forEach(callback: (item: Item, id: Id) => boolean): void {\n    for (const [id, item] of this._pairs) {\n      callback(item, id);\n    }\n  }\n\n  /**\n   * Map the items into a different type.\n   *\n   * @param callback - The function that does the conversion.\n   * @typeParam Mapped - The type of the item after mapping.\n   * @returns A new data stream with the mapped items.\n   */\n  public map<Mapped>(\n    callback: (item: Item, id: Id) => Mapped\n  ): DataStream<Mapped> {\n    const pairs = this._pairs;\n    return new DataStream<Mapped>({\n      *[Symbol.iterator](): IterableIterator<[Id, Mapped]> {\n        for (const [id, item] of pairs) {\n          yield [id, callback(item, id)];\n        }\n      },\n    });\n  }\n\n  /**\n   * Get the item with the maximum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the maximum if found otherwise null.\n   */\n  public max(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let maxItem: Item = curr.value[1];\n    let maxValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value > maxValue) {\n        maxValue = value;\n        maxItem = item;\n      }\n    }\n\n    return maxItem;\n  }\n\n  /**\n   * Get the item with the minimum value of given property.\n   *\n   * @param callback - The function that picks and possibly converts the property.\n   * @returns The item with the minimum if found otherwise null.\n   */\n  public min(callback: (item: Item, id: Id) => number): Item | null {\n    const iter = this._pairs[Symbol.iterator]();\n    let curr = iter.next();\n    if (curr.done) {\n      return null;\n    }\n\n    let minItem: Item = curr.value[1];\n    let minValue: number = callback(curr.value[1], curr.value[0]);\n    while (!(curr = iter.next()).done) {\n      const [id, item] = curr.value;\n      const value = callback(item, id);\n      if (value < minValue) {\n        minValue = value;\n        minItem = item;\n      }\n    }\n\n    return minItem;\n  }\n\n  /**\n   * Reduce the items into a single value.\n   *\n   * @param callback - The function that does the reduction.\n   * @param accumulator - The initial value of the accumulator.\n   * @typeParam T - The type of the accumulated value.\n   * @returns The reduced value.\n   */\n  public reduce<T>(\n    callback: (accumulator: T, item: Item, id: Id) => T,\n    accumulator: T\n  ): T {\n    for (const [id, item] of this._pairs) {\n      accumulator = callback(accumulator, item, id);\n    }\n    return accumulator;\n  }\n\n  /**\n   * Sort the items.\n   *\n   * @param callback - Item comparator.\n   * @returns A new stream with sorted items.\n   */\n  public sort(\n    callback: (itemA: Item, itemB: Item, idA: Id, idB: Id) => number\n  ): DataStream<Item> {\n    return new DataStream({\n      [Symbol.iterator]: (): IterableIterator<[Id, Item]> =>\n        [...this._pairs]\n          .sort(([idA, itemA], [idB, itemB]): number =>\n            callback(itemA, itemB, idA, idB)\n          )\n          [Symbol.iterator](),\n    });\n  }\n}\n", "import { v4 as uuid4 } from \"uuid\";\nimport { pureDeepObjectAssign } from \"vis-util/esnext\";\n\nimport {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  DataInterfaceOrder,\n  DeepPartial,\n  EventPayloads,\n  FullItem,\n  Id,\n  OptId,\n  PartItem,\n  UpdateItem,\n  isId,\n} from \"./data-interface\";\n\nimport { Queue, QueueOptions } from \"./queue\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Initial DataSet configuration object.\n *\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataSetInitialOptions<IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /**\n   * Queue data changes ('add', 'update', 'remove') and flush them at once. The queue can be flushed manually by calling `DataSet.flush()`, or can be flushed after a configured delay or maximum number of entries.\n   *\n   * When queue is true, a queue is created with default options. Options can be specified by providing an object.\n   */\n  queue?: QueueOptions | false;\n}\n/** DataSet configuration object. */\nexport interface DataSetOptions {\n  /**\n   * Queue configuration object or false if no queue should be used.\n   *\n   * - If false and there was a queue before it will be flushed and then removed.\n   * - If {@link QueueOptions} the existing queue will be reconfigured or a new queue will be created.\n   */\n  queue?: Queue | QueueOptions | false;\n}\n\n/**\n * Add an id to given item if it doesn't have one already.\n *\n * @remarks\n * The item will be modified.\n * @param item - The item that will have an id after a call to this function.\n * @param idProp - The key of the id property.\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n * @returns true\n */\nfunction ensureFullItem<Item extends PartItem<IdProp>, IdProp extends string>(\n  item: Item,\n  idProp: IdProp\n): FullItem<Item, IdProp> {\n  if (item[idProp] == null) {\n    // generate an id\n    item[idProp] = uuid4() as any;\n  }\n\n  return item as FullItem<Item, IdProp>;\n}\n\n/**\n * # DataSet\n *\n * Vis.js comes with a flexible DataSet, which can be used to hold and\n * manipulate unstructured data and listen for changes in the data. The DataSet\n * is key/value based. Data items can be added, updated and removed from the\n * DataSet, and one can subscribe to changes in the DataSet. The data in the\n * DataSet can be filtered and ordered. Data can be normalized when appending it\n * to the DataSet as well.\n *\n * ## Example\n *\n * The following example shows how to use a DataSet.\n *\n * ```javascript\n * // create a DataSet\n * var options = {};\n * var data = new vis.DataSet(options);\n *\n * // add items\n * // note that the data items can contain different properties and data formats\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // subscribe to any change in the DataSet\n * data.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an existing item\n * data.update({id: 2, group: 1});\n *\n * // remove an item\n * data.remove(4);\n *\n * // get all ids\n * var ids = data.getIds();\n * console.log('ids', ids);\n *\n * // get a specific item\n * var item1 = data.get(1);\n * console.log('item1', item1);\n *\n * // retrieve a filtered subset of the data\n * var items = data.get({\n *   filter: function (item) {\n *     return item.group == 1;\n *   }\n * });\n * console.log('filtered items', items);\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataSet<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** Flush all queued calls. */\n  public flush?: () => void;\n  /** @inheritDoc */\n  public length: number;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this._idProp;\n  }\n\n  private readonly _options: DataSetInitialOptions<IdProp>;\n  private readonly _data: Map<Id, FullItem<Item, IdProp>>;\n  private readonly _idProp: IdProp;\n  private _queue: Queue<this> | null = null;\n\n  /**\n   * @param options - DataSet configuration.\n   */\n  public constructor(options?: DataSetInitialOptions<IdProp>);\n  /**\n   * @param data - An initial set of items for the new instance.\n   * @param options - DataSet configuration.\n   */\n  public constructor(data: Item[], options?: DataSetInitialOptions<IdProp>);\n  /**\n   * Construct a new DataSet.\n   *\n   * @param data - Initial data or options.\n   * @param options - Options (type error if data is also options).\n   */\n  public constructor(\n    data?: Item[] | DataSetInitialOptions<IdProp>,\n    options?: DataSetInitialOptions<IdProp>\n  ) {\n    super();\n\n    // correctly read optional arguments\n    if (data && !Array.isArray(data)) {\n      options = data;\n      data = [];\n    }\n\n    this._options = options || {};\n    this._data = new Map(); // map with data indexed by id\n    this.length = 0; // number of items in the DataSet\n    this._idProp = this._options.fieldId || (\"id\" as IdProp); // name of the field containing id\n\n    // add initial data when provided\n    if (data && data.length) {\n      this.add(data);\n    }\n\n    this.setOptions(options);\n  }\n\n  /**\n   * Set new options.\n   *\n   * @param options - The new options.\n   */\n  public setOptions(options?: DataSetOptions): void {\n    if (options && options.queue !== undefined) {\n      if (options.queue === false) {\n        // delete queue if loaded\n        if (this._queue) {\n          this._queue.destroy();\n          this._queue = null;\n        }\n      } else {\n        // create queue and update its options\n        if (!this._queue) {\n          this._queue = Queue.extend(this, {\n            replace: [\"add\", \"update\", \"remove\"],\n          });\n        }\n\n        if (options.queue && typeof options.queue === \"object\") {\n          this._queue.setOptions(options.queue);\n        }\n      }\n    }\n  }\n\n  /**\n   * Add a data item or an array with items.\n   *\n   * After the items are added to the DataSet, the DataSet will trigger an event `add`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet()\n   *\n   * // add items\n   * const ids = data.add([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { text: 'item without an id' }\n   * ])\n   *\n   * console.log(ids) // [1, 2, '<UUIDv4>']\n   * ```\n   *\n   * @param data - Items to be added (ids will be generated if missing).\n   * @param senderId - Sender id.\n   * @returns addedIds - Array with the ids (generated if not present) of the added items.\n   * @throws When an item with the same id as any of the added items already exists.\n   */\n  public add(data: Item | Item[], senderId?: Id | null): (string | number)[] {\n    const addedIds: Id[] = [];\n    let id: Id;\n\n    if (Array.isArray(data)) {\n      // Array\n      const idsToAdd: Id[] = data.map((d) => d[this._idProp] as Id);\n      if (idsToAdd.some((id) => this._data.has(id))) {\n        throw new Error(\"A duplicate id was found in the parameter array.\");\n      }\n      for (let i = 0, len = data.length; i < len; i++) {\n        id = this._addItem(data[i]);\n        addedIds.push(id);\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      id = this._addItem(data);\n      addedIds.push(id);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n\n    return addedIds;\n  }\n\n  /**\n   * Update existing items. When an item does not exist, it will be created.\n   *\n   * @remarks\n   * The provided properties will be merged in the existing item. When an item does not exist, it will be created.\n   *\n   * After the items are updated, the DataSet will trigger an event `add` for the added items, and an event `update`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' },\n   *   { id: 4, text: 'item 4 (new)' }\n   * ])\n   *\n   * console.log(ids) // [2, 4]\n   * ```\n   *\n   * ## Warning for TypeScript users\n   * This method may introduce partial items into the data set. Use add or updateOnly instead for better type safety.\n   * @param data - Items to be updated (if the id is already present) or added (if the id is missing).\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the added (these may be newly generated if there was no id in the item from the data) or updated items.\n   * @throws When the supplied data is neither an item nor an array of items.\n   */\n  public update(\n    data: DeepPartial<Item> | DeepPartial<Item>[],\n    senderId?: Id | null\n  ): Id[] {\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const oldData: FullItem<Item, IdProp>[] = [];\n    const updatedData: FullItem<Item, IdProp>[] = [];\n    const idProp = this._idProp;\n\n    const addOrUpdate = (item: DeepPartial<Item>): void => {\n      const origId: OptId = item[idProp];\n      if (origId != null && this._data.has(origId)) {\n        const fullItem = item as FullItem<Item, IdProp>; // it has an id, therefore it is a fullitem\n        const oldItem = Object.assign({}, this._data.get(origId));\n        // update item\n        const id = this._updateItem(fullItem);\n        updatedIds.push(id);\n        updatedData.push(fullItem);\n        oldData.push(oldItem);\n      } else {\n        // add new item\n        const id = this._addItem(item as any);\n        addedIds.push(id);\n      }\n    };\n\n    if (Array.isArray(data)) {\n      // Array\n      for (let i = 0, len = data.length; i < len; i++) {\n        if (data[i] && typeof data[i] === \"object\") {\n          addOrUpdate(data[i]);\n        } else {\n          console.warn(\n            \"Ignoring input item, which is not an object at index \" + i\n          );\n        }\n      }\n    } else if (data && typeof data === \"object\") {\n      // Single item\n      addOrUpdate(data);\n    } else {\n      throw new Error(\"Unknown dataType\");\n    }\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      const props = { items: updatedIds, oldData: oldData, data: updatedData };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n    }\n\n    return addedIds.concat(updatedIds);\n  }\n\n  /**\n   * Update existing items. When an item does not exist, an error will be thrown.\n   *\n   * @remarks\n   * The provided properties will be deeply merged into the existing item.\n   * When an item does not exist (id not present in the data set or absent), an error will be thrown and nothing will be changed.\n   *\n   * After the items are updated, the DataSet will trigger an event `update`.\n   * When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   *\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' },\n   * ])\n   *\n   * // update items\n   * const ids = data.update([\n   *   { id: 2, text: 'item 2 (updated)' }, // works\n   *   // { id: 4, text: 'item 4 (new)' }, // would throw\n   *   // { text: 'item 4 (new)' }, // would also throw\n   * ])\n   *\n   * console.log(ids) // [2]\n   * ```\n   * @param data - Updates (the id and optionally other props) to the items in this data set.\n   * @param senderId - Sender id.\n   * @returns updatedIds - The ids of the updated items.\n   * @throws When the supplied data is neither an item nor an array of items, when the ids are missing.\n   */\n  public updateOnly(\n    data: UpdateItem<Item, IdProp> | UpdateItem<Item, IdProp>[],\n    senderId?: Id | null\n  ): Id[] {\n    if (!Array.isArray(data)) {\n      data = [data];\n    }\n\n    const updateEventData = data\n      .map(\n        (\n          update\n        ): {\n          oldData: FullItem<Item, IdProp>;\n          update: UpdateItem<Item, IdProp>;\n        } => {\n          const oldData = this._data.get(update[this._idProp]);\n          if (oldData == null) {\n            throw new Error(\"Updating non-existent items is not allowed.\");\n          }\n          return { oldData, update };\n        }\n      )\n      .map(\n        ({\n          oldData,\n          update,\n        }): {\n          id: Id;\n          oldData: FullItem<Item, IdProp>;\n          updatedData: FullItem<Item, IdProp>;\n        } => {\n          const id = oldData[this._idProp];\n          const updatedData = pureDeepObjectAssign(oldData, update);\n\n          this._data.set(id, updatedData);\n\n          return {\n            id,\n            oldData: oldData,\n            updatedData,\n          };\n        }\n      );\n\n    if (updateEventData.length) {\n      const props: EventPayloads<Item, IdProp>[\"update\"] = {\n        items: updateEventData.map((value): Id => value.id),\n        oldData: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.oldData\n        ),\n        data: updateEventData.map(\n          (value): FullItem<Item, IdProp> => value.updatedData\n        ),\n      };\n      // TODO: remove deprecated property 'data' some day\n      //Object.defineProperty(props, 'data', {\n      //  'get': (function() {\n      //    console.warn('Property data is deprecated. Use DataSet.get(ids) to retrieve the new data, use the oldData property on this object to get the old data');\n      //    return updatedData;\n      //  }).bind(this)\n      //});\n      this._trigger(\"update\", props, senderId);\n\n      return props.items;\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>> {\n    // @TODO: Woudn't it be better to split this into multiple methods?\n\n    // parse the arguments\n    let id: Id | undefined = undefined;\n    let ids: Id[] | undefined = undefined;\n    let options: DataInterfaceGetOptions<Item> | undefined = undefined;\n    if (isId(first)) {\n      // get(id [, options])\n      id = first;\n      options = second;\n    } else if (Array.isArray(first)) {\n      // get(ids [, options])\n      ids = first;\n      options = second;\n    } else {\n      // get([, options])\n      options = first;\n    }\n\n    // determine the return type\n    const returnType =\n      options && options.returnType === \"Object\" ? \"Object\" : \"Array\";\n    // @TODO: WTF is this? Or am I missing something?\n    // var returnType\n    // if (options && options.returnType) {\n    //   var allowedValues = ['Array', 'Object']\n    //   returnType =\n    //     allowedValues.indexOf(options.returnType) == -1\n    //       ? 'Array'\n    //       : options.returnType\n    // } else {\n    //   returnType = 'Array'\n    // }\n\n    // build options\n    const filter = options && options.filter;\n    const items: FullItem<Item, IdProp>[] = [];\n    let item: undefined | FullItem<Item, IdProp> = undefined;\n    let itemIds: undefined | Id[] = undefined;\n    let itemId: undefined | Id = undefined;\n\n    // convert items\n    if (id != null) {\n      // return a single item\n      item = this._data.get(id);\n      if (item && filter && !filter(item)) {\n        item = undefined;\n      }\n    } else if (ids != null) {\n      // return a subset of items\n      for (let i = 0, len = ids.length; i < len; i++) {\n        item = this._data.get(ids[i]);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    } else {\n      // return all items\n      itemIds = [...this._data.keys()];\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        itemId = itemIds[i];\n        item = this._data.get(itemId);\n        if (item != null && (!filter || filter(item))) {\n          items.push(item);\n        }\n      }\n    }\n\n    // order the results\n    if (options && options.order && id == undefined) {\n      this._sort(items, options.order);\n    }\n\n    // filter fields of the items\n    if (options && options.fields) {\n      const fields = options.fields;\n      if (id != undefined && item != null) {\n        item = this._filterFields(item, fields) as FullItem<Item, IdProp>;\n      } else {\n        for (let i = 0, len = items.length; i < len; i++) {\n          items[i] = this._filterFields(items[i], fields) as FullItem<\n            Item,\n            IdProp\n          >;\n        }\n      }\n    }\n\n    // return the results\n    if (returnType == \"Object\") {\n      const result: Record<string, FullItem<Item, IdProp>> = {};\n      for (let i = 0, len = items.length; i < len; i++) {\n        const resultant = items[i];\n        // @TODO: Shoudn't this be this._fieldId?\n        // result[resultant.id] = resultant\n        const id: Id = resultant[this._idProp];\n        result[id] = resultant;\n      }\n      return result;\n    } else {\n      if (id != null) {\n        // a single item\n        return item ?? null;\n      } else {\n        // just return our array\n        return items;\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    const data = this._data;\n    const filter = options && options.filter;\n    const order = options && options.order;\n    const itemIds = [...data.keys()];\n    const ids: Id[] = [];\n\n    if (filter) {\n      // get filtered items\n      if (order) {\n        // create ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            items.push(item);\n          }\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = this._data.get(id);\n          if (item != null && filter(item)) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    } else {\n      // get all items\n      if (order) {\n        // create an ordered list\n        const items = [];\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          items.push(data.get(id)!);\n        }\n\n        this._sort(items, order);\n\n        for (let i = 0, len = items.length; i < len; i++) {\n          ids.push(items[i][this._idProp]);\n        }\n      } else {\n        // create unordered list\n        for (let i = 0, len = itemIds.length; i < len; i++) {\n          const id = itemIds[i];\n          const item = data.get(id);\n          if (item != null) {\n            ids.push(item[this._idProp]);\n          }\n        }\n      }\n    }\n\n    return ids;\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this;\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    const filter = options && options.filter;\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    if (options && options.order) {\n      // execute forEach on ordered list\n      const items: FullItem<Item, IdProp>[] = this.get(options);\n\n      for (let i = 0, len = items.length; i < len; i++) {\n        const item = items[i];\n        const id = item[this._idProp];\n        callback(item, id);\n      }\n    } else {\n      // unordered\n      for (let i = 0, len = itemIds.length; i < len; i++) {\n        const id = itemIds[i];\n        const item = this._data.get(id);\n        if (item != null && (!filter || filter(item))) {\n          callback(item, id);\n        }\n      }\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    const filter = options && options.filter;\n    const mappedItems: T[] = [];\n    const data = this._data;\n    const itemIds = [...data.keys()];\n\n    // convert and filter items\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = this._data.get(id);\n      if (item != null && (!filter || filter(item))) {\n        mappedItems.push(callback(item, id));\n      }\n    }\n\n    // order items\n    if (options && options.order) {\n      this._sort(mappedItems, options.order);\n    }\n\n    return mappedItems;\n  }\n\n  private _filterFields<K extends string>(item: null, fields: K[]): null;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[]\n  ): Record<K, unknown>;\n  private _filterFields<K extends string>(\n    item: Record<K, unknown>,\n    fields: K[] | Record<K, string>\n  ): any;\n  /**\n   * Filter the fields of an item.\n   *\n   * @param item - The item whose fields should be filtered.\n   * @param fields - The names of the fields that will be kept.\n   * @typeParam K - Field name type.\n   * @returns The item without any additional fields.\n   */\n  private _filterFields<K extends string>(\n    item: Record<K, unknown> | null,\n    fields: K[] | Record<K, unknown>\n  ): Record<K, unknown> | null {\n    if (!item) {\n      // item is null\n      return item;\n    }\n\n    return (\n      Array.isArray(fields)\n        ? // Use the supplied array\n          fields\n        : // Use the keys of the supplied object\n          (Object.keys(fields) as K[])\n    ).reduce<Record<string, unknown>>(\n      (filteredItem, field): Record<string, unknown> => {\n        filteredItem[field] = item[field];\n        return filteredItem;\n      },\n      {}\n    );\n  }\n\n  /**\n   * Sort the provided array with items.\n   *\n   * @param items - Items to be sorted in place.\n   * @param order - A field name or custom sort function.\n   * @typeParam T - The type of the items in the items array.\n   */\n  private _sort<T>(items: T[], order: DataInterfaceOrder<T>): void {\n    if (typeof order === \"string\") {\n      // order by provided field name\n      const name = order; // field name\n      items.sort((a, b): -1 | 0 | 1 => {\n        // @TODO: How to treat missing properties?\n        const av = (a as any)[name];\n        const bv = (b as any)[name];\n        return av > bv ? 1 : av < bv ? -1 : 0;\n      });\n    } else if (typeof order === \"function\") {\n      // order by sort function\n      items.sort(order);\n    } else {\n      // TODO: extend order by an Object {field:string, direction:string}\n      //       where direction can be 'asc' or 'desc'\n      throw new TypeError(\"Order must be a function or a string\");\n    }\n  }\n\n  /**\n   * Remove an item or multiple items by “reference” (only the id is used) or by id.\n   *\n   * The method ignores removal of non-existing items, and returns an array containing the ids of the items which are actually removed from the DataSet.\n   *\n   * After the items are removed, the DataSet will trigger an event `remove` for the removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * ## Example\n   * ```javascript\n   * // create a DataSet\n   * const data = new vis.DataSet([\n   *   { id: 1, text: 'item 1' },\n   *   { id: 2, text: 'item 2' },\n   *   { id: 3, text: 'item 3' }\n   * ])\n   *\n   * // remove items\n   * const ids = data.remove([2, { id: 3 }, 4])\n   *\n   * console.log(ids) // [2, 3]\n   * ```\n   *\n   * @param id - One or more items or ids of items to be removed.\n   * @param senderId - Sender id.\n   * @returns The ids of the removed items.\n   */\n  public remove(id: Id | Item | (Id | Item)[], senderId?: Id | null): Id[] {\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // force everything to be an array for simplicity\n    const ids = Array.isArray(id) ? id : [id];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const item = this._remove(ids[i]);\n      if (item) {\n        const itemId: OptId = item[this._idProp];\n        if (itemId != null) {\n          removedIds.push(itemId);\n          removedItems.push(item);\n        }\n      }\n    }\n\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n\n    return removedIds;\n  }\n\n  /**\n   * Remove an item by its id or reference.\n   *\n   * @param id - Id of an item or the item itself.\n   * @returns The removed item if removed, null otherwise.\n   */\n  private _remove(id: Id | Item): FullItem<Item, IdProp> | null {\n    // @TODO: It origianlly returned the item although the docs say id.\n    // The code expects the item, so probably an error in the docs.\n    let ident: OptId;\n\n    // confirm the id to use based on the args type\n    if (isId(id)) {\n      ident = id;\n    } else if (id && typeof id === \"object\") {\n      ident = id[this._idProp]; // look for the identifier field using ._idProp\n    }\n\n    // do the removing if the item is found\n    if (ident != null && this._data.has(ident)) {\n      const item = this._data.get(ident) || null;\n      this._data.delete(ident);\n      --this.length;\n      return item;\n    }\n\n    return null;\n  }\n\n  /**\n   * Clear the entire data set.\n   *\n   * After the items are removed, the {@link DataSet} will trigger an event `remove` for all removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.\n   *\n   * @param senderId - Sender id.\n   * @returns removedIds - The ids of all removed items.\n   */\n  public clear(senderId?: Id | null): Id[] {\n    const ids = [...this._data.keys()];\n    const items: FullItem<Item, IdProp>[] = [];\n\n    for (let i = 0, len = ids.length; i < len; i++) {\n      items.push(this._data.get(ids[i])!);\n    }\n\n    this._data.clear();\n    this.length = 0;\n\n    this._trigger(\"remove\", { items: ids, oldData: items }, senderId);\n\n    return ids;\n  }\n\n  /**\n   * Find the item with maximum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for max value.\n   * @returns Item containing max value, or null if no items.\n   */\n  public max(field: keyof Item): Item | null {\n    let max = null;\n    let maxField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (maxField == null || itemField > maxField)\n      ) {\n        max = item;\n        maxField = itemField;\n      }\n    }\n\n    return max || null;\n  }\n\n  /**\n   * Find the item with minimum value of a specified field.\n   *\n   * @param field - Name of the property that should be searched for min value.\n   * @returns Item containing min value, or null if no items.\n   */\n  public min(field: keyof Item): Item | null {\n    let min = null;\n    let minField = null;\n\n    for (const item of this._data.values()) {\n      const itemField = item[field];\n      if (\n        typeof itemField === \"number\" &&\n        (minField == null || itemField < minField)\n      ) {\n        min = item;\n        minField = itemField;\n      }\n    }\n\n    return min || null;\n  }\n\n  public distinct<T extends keyof Item>(prop: T): Item[T][];\n  public distinct(prop: string): unknown[];\n  /**\n   * Find all distinct values of a specified field\n   *\n   * @param prop - The property name whose distinct values should be returned.\n   * @returns Unordered array containing all distinct values. Items without specified property are ignored.\n   */\n  public distinct<T extends string>(prop: T): unknown[] {\n    const data = this._data;\n    const itemIds = [...data.keys()];\n    const values: unknown[] = [];\n    let count = 0;\n\n    for (let i = 0, len = itemIds.length; i < len; i++) {\n      const id = itemIds[i];\n      const item = data.get(id);\n      const value = (item as any)[prop];\n      let exists = false;\n      for (let j = 0; j < count; j++) {\n        if (values[j] == value) {\n          exists = true;\n          break;\n        }\n      }\n      if (!exists && value !== undefined) {\n        values[count] = value;\n        count++;\n      }\n    }\n\n    return values;\n  }\n\n  /**\n   * Add a single item. Will fail when an item with the same id already exists.\n   *\n   * @param item - A new item to be added.\n   * @returns Added item's id. An id is generated when it is not present in the item.\n   */\n  private _addItem(item: Item): Id {\n    const fullItem = ensureFullItem(item, this._idProp);\n    const id = fullItem[this._idProp];\n\n    // check whether this id is already taken\n    if (this._data.has(id)) {\n      // item already exists\n      throw new Error(\n        \"Cannot add item: item with id \" + id + \" already exists\"\n      );\n    }\n\n    this._data.set(id, fullItem);\n    ++this.length;\n\n    return id;\n  }\n\n  /**\n   * Update a single item: merge with existing item.\n   * Will fail when the item has no id, or when there does not exist an item with the same id.\n   *\n   * @param update - The new item\n   * @returns The id of the updated item.\n   */\n  private _updateItem(update: FullItem<Item, IdProp>): Id {\n    const id: OptId = update[this._idProp];\n    if (id == null) {\n      throw new Error(\n        \"Cannot update item: item has no id (item: \" +\n          JSON.stringify(update) +\n          \")\"\n      );\n    }\n    const item = this._data.get(id);\n    if (!item) {\n      // item doesn't exist\n      throw new Error(\"Cannot update item: no item with id \" + id + \" found\");\n    }\n\n    this._data.set(id, { ...item, ...update });\n\n    return id;\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    if (ids) {\n      const data = this._data;\n\n      return new DataStream<Item>({\n        *[Symbol.iterator](): IterableIterator<[Id, Item]> {\n          for (const id of ids) {\n            const item = data.get(id);\n            if (item != null) {\n              yield [id, item];\n            }\n          }\n        },\n      });\n    } else {\n      return new DataStream({\n        [Symbol.iterator]: this._data.entries.bind(this._data),\n      });\n    }\n  }\n\n  /* develblock:start */\n  public get testLeakData(): Map<Id, FullItem<Item, IdProp>> {\n    return this._data;\n  }\n  public get testLeakIdProp(): IdProp {\n    return this._idProp;\n  }\n  public get testLeakOptions(): DataSetInitialOptions<IdProp> {\n    return this._options;\n  }\n  public get testLeakQueue(): Queue<this> | null {\n    return this._queue;\n  }\n  public set testLeakQueue(v: Queue<this> | null) {\n    this._queue = v;\n  }\n  /* develblock:end */\n}\n", "import {\n  DataInterface,\n  DataInterfaceForEachOptions,\n  DataInterfaceGetIdsOptions,\n  DataInterfaceGetOptions,\n  DataInterfaceGetOptionsArray,\n  DataInterfaceGetOptionsObject,\n  DataInterfaceMapOptions,\n  EventCallbacksWithAny,\n  EventName,\n  EventPayloads,\n  FullItem,\n  Id,\n  PartItem,\n  RemoveEventPayload,\n  UpdateEventPayload,\n  isId,\n} from \"./data-interface\";\n\nimport { DataSet } from \"./data-set\";\nimport { DataSetPart } from \"./data-set-part\";\nimport { DataStream } from \"./data-stream\";\n\n/**\n * Data view options.\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport interface DataViewOptions<Item, IdProp extends string> {\n  /**\n   * The name of the field containing the id of the items. When data is fetched from a server which uses some specific field to identify items, this field name can be specified in the DataSet using the option `fieldId`. For example [CouchDB](http://couchdb.apache.org/) uses the field `'_id'` to identify documents.\n   */\n  fieldId?: IdProp;\n  /** Items can be filtered on specific properties by providing a filter function. A filter function is executed for each of the items in the DataSet, and is called with the item as parameter. The function must return a boolean. All items for which the filter function returns true will be emitted. */\n  filter?: (item: Item) => boolean;\n}\n\n/**\n * DataView\n *\n * A DataView offers a filtered and/or formatted view on a DataSet. One can subscribe to changes in a DataView, and easily get filtered or formatted data without having to specify filters and field types all the time.\n *\n * ## Example\n * ```javascript\n * // create a DataSet\n * var data = new vis.DataSet();\n * data.add([\n *   {id: 1, text: 'item 1', date: new Date(2013, 6, 20), group: 1, first: true},\n *   {id: 2, text: 'item 2', date: '2013-06-23', group: 2},\n *   {id: 3, text: 'item 3', date: '2013-06-25', group: 2},\n *   {id: 4, text: 'item 4'}\n * ]);\n *\n * // create a DataView\n * // the view will only contain items having a property group with value 1,\n * // and will only output fields id, text, and date.\n * var view = new vis.DataView(data, {\n *   filter: function (item) {\n *     return (item.group == 1);\n *   },\n *   fields: ['id', 'text', 'date']\n * });\n *\n * // subscribe to any change in the DataView\n * view.on('*', function (event, properties, senderId) {\n *   console.log('event', event, properties);\n * });\n *\n * // update an item in the data set\n * data.update({id: 2, group: 1});\n *\n * // get all ids in the view\n * var ids = view.getIds();\n * console.log('ids', ids); // will output [1, 2]\n *\n * // get all items in the view\n * var items = view.get();\n * ```\n *\n * @typeParam Item - Item type that may or may not have an id.\n * @typeParam IdProp - Name of the property that contains the id.\n */\nexport class DataView<\n    Item extends PartItem<IdProp>,\n    IdProp extends string = \"id\"\n  >\n  extends DataSetPart<Item, IdProp>\n  implements DataInterface<Item, IdProp>\n{\n  /** @inheritDoc */\n  public length = 0;\n  /** @inheritDoc */\n  public get idProp(): IdProp {\n    return this.getDataSet().idProp;\n  }\n\n  private readonly _listener: EventCallbacksWithAny<Item, IdProp>[\"*\"];\n  private _data!: DataInterface<Item, IdProp>; // constructor → setData\n  private readonly _ids: Set<Id> = new Set(); // ids of the items currently in memory (just contains a boolean true)\n  private readonly _options: DataViewOptions<Item, IdProp>;\n\n  /**\n   * Create a DataView.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @param options - Options to configure this data view.\n   */\n  public constructor(\n    data: DataInterface<Item, IdProp>,\n    options?: DataViewOptions<Item, IdProp>\n  ) {\n    super();\n\n    this._options = options || {};\n\n    this._listener = this._onEvent.bind(this);\n\n    this.setData(data);\n  }\n\n  // TODO: implement a function .config() to dynamically update things like configured filter\n  // and trigger changes accordingly\n\n  /**\n   * Set a data source for the view.\n   *\n   * @param data - The instance containing data (directly or indirectly).\n   * @remarks\n   * Note that when the data view is bound to a data set it won't be garbage\n   * collected unless the data set is too. Use `dataView.setData(null)` or\n   * `dataView.dispose()` to enable garbage collection before you lose the last\n   * reference.\n   */\n  public setData(data: DataInterface<Item, IdProp>): void {\n    if (this._data) {\n      // unsubscribe from current dataset\n      if (this._data.off) {\n        this._data.off(\"*\", this._listener);\n      }\n\n      // trigger a remove of all items in memory\n      const ids = this._data.getIds({ filter: this._options.filter });\n      const items = this._data.get(ids);\n\n      this._ids.clear();\n      this.length = 0;\n      this._trigger(\"remove\", { items: ids, oldData: items });\n    }\n\n    if (data != null) {\n      this._data = data;\n\n      // trigger an add of all added items\n      const ids = this._data.getIds({ filter: this._options.filter });\n      for (let i = 0, len = ids.length; i < len; i++) {\n        const id = ids[i];\n        this._ids.add(id);\n      }\n      this.length = ids.length;\n      this._trigger(\"add\", { items: ids });\n    } else {\n      this._data = new DataSet<Item, IdProp>();\n    }\n\n    // subscribe to new dataset\n    if (this._data.on) {\n      this._data.on(\"*\", this._listener);\n    }\n  }\n\n  /**\n   * Refresh the DataView.\n   * Useful when the DataView has a filter function containing a variable parameter.\n   */\n  public refresh(): void {\n    const ids = this._data.getIds({\n      filter: this._options.filter,\n    });\n    const oldIds = [...this._ids];\n    const newIds: Record<Id, boolean> = {};\n    const addedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    // check for additions\n    for (let i = 0, len = ids.length; i < len; i++) {\n      const id = ids[i];\n      newIds[id] = true;\n      if (!this._ids.has(id)) {\n        addedIds.push(id);\n        this._ids.add(id);\n      }\n    }\n\n    // check for removals\n    for (let i = 0, len = oldIds.length; i < len; i++) {\n      const id = oldIds[i];\n      const item = this._data.get(id);\n      if (item == null) {\n        // @TODO: Investigate.\n        // Doesn't happen during tests or examples.\n        // Is it really impossible or could it eventually happen?\n        // How to handle it if it does? The types guarantee non-nullable items.\n        console.error(\"If you see this, report it please.\");\n      } else if (!newIds[id]) {\n        removedIds.push(id);\n        removedItems.push(item);\n        this._ids.delete(id);\n      }\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    // trigger events\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds });\n    }\n    if (removedIds.length) {\n      this._trigger(\"remove\", { items: removedIds, oldData: removedItems });\n    }\n  }\n\n  /** @inheritDoc */\n  public get(): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(id: Id): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsArray<Item>\n  ): null | FullItem<Item, IdProp>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    id: Id,\n    options: DataInterfaceGetOptions<Item>\n  ): null | FullItem<Item, IdProp> | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(ids: Id[]): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsArray<Item>\n  ): FullItem<Item, IdProp>[];\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptionsObject<Item>\n  ): Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id[],\n    options: DataInterfaceGetOptions<Item>\n  ): FullItem<Item, IdProp>[] | Record<Id, FullItem<Item, IdProp>>;\n  /** @inheritDoc */\n  public get(\n    ids: Id | Id[],\n    options?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<Id, FullItem<Item, IdProp>>;\n\n  /** @inheritDoc */\n  public get(\n    first?: DataInterfaceGetOptions<Item> | Id | Id[],\n    second?: DataInterfaceGetOptions<Item>\n  ):\n    | null\n    | FullItem<Item, IdProp>\n    | FullItem<Item, IdProp>[]\n    | Record<string, FullItem<Item, IdProp>> {\n    if (this._data == null) {\n      return null;\n    }\n\n    // parse the arguments\n    let ids: Id | Id[] | null = null;\n    let options: any;\n    if (isId(first) || Array.isArray(first)) {\n      ids = first;\n      options = second;\n    } else {\n      options = first;\n    }\n\n    // extend the options with the default options and provided options\n    const viewOptions: DataInterfaceGetOptions<Item> = Object.assign(\n      {},\n      this._options,\n      options\n    );\n\n    // create a combined filter method when needed\n    const thisFilter = this._options.filter;\n    const optionsFilter = options && options.filter;\n    if (thisFilter && optionsFilter) {\n      viewOptions.filter = (item): boolean => {\n        return thisFilter(item) && optionsFilter(item);\n      };\n    }\n\n    if (ids == null) {\n      return this._data.get(viewOptions);\n    } else {\n      return this._data.get(ids, viewOptions);\n    }\n  }\n\n  /** @inheritDoc */\n  public getIds(options?: DataInterfaceGetIdsOptions<Item>): Id[] {\n    if (this._data.length) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options != null ? options.filter : null;\n      let filter: DataInterfaceGetIdsOptions<Item>[\"filter\"];\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): boolean => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.getIds({\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public forEach(\n    callback: (item: Item, id: Id) => void,\n    options?: DataInterfaceForEachOptions<Item>\n  ): void {\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | ((item: Item) => boolean);\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = function (item: Item): boolean {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      this._data.forEach(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    }\n  }\n\n  /** @inheritDoc */\n  public map<T>(\n    callback: (item: Item, id: Id) => T,\n    options?: DataInterfaceMapOptions<Item, T>\n  ): T[] {\n    type Filter = NonNullable<DataInterfaceMapOptions<Item, T>[\"filter\"]>;\n\n    if (this._data) {\n      const defaultFilter = this._options.filter;\n      const optionsFilter = options && options.filter;\n      let filter: undefined | Filter;\n\n      if (optionsFilter) {\n        if (defaultFilter) {\n          filter = (item): ReturnType<Filter> => {\n            return defaultFilter(item) && optionsFilter(item);\n          };\n        } else {\n          filter = optionsFilter;\n        }\n      } else {\n        filter = defaultFilter;\n      }\n\n      return this._data.map(callback, {\n        filter: filter,\n        order: options && options.order,\n      });\n    } else {\n      return [];\n    }\n  }\n\n  /** @inheritDoc */\n  public getDataSet(): DataSet<Item, IdProp> {\n    return this._data.getDataSet();\n  }\n\n  /** @inheritDoc */\n  public stream(ids?: Iterable<Id>): DataStream<Item> {\n    return this._data.stream(\n      ids || {\n        [Symbol.iterator]: this._ids.keys.bind(this._ids),\n      }\n    );\n  }\n\n  /**\n   * Render the instance unusable prior to garbage collection.\n   *\n   * @remarks\n   * The intention of this method is to help discover scenarios where the data\n   * view is being used when the programmer thinks it has been garbage collected\n   * already. It's stricter version of `dataView.setData(null)`.\n   */\n  public dispose(): void {\n    if (this._data?.off) {\n      this._data.off(\"*\", this._listener);\n    }\n\n    const message = \"This data view has already been disposed of.\";\n    const replacement = {\n      get: (): void => {\n        throw new Error(message);\n      },\n      set: (): void => {\n        throw new Error(message);\n      },\n\n      configurable: false,\n    };\n    for (const key of Reflect.ownKeys(DataView.prototype)) {\n      Object.defineProperty(this, key, replacement);\n    }\n  }\n\n  /**\n   * Event listener. Will propagate all events from the connected data set to the subscribers of the DataView, but will filter the items and only trigger when there are changes in the filtered data set.\n   *\n   * @param event - The name of the event.\n   * @param params - Parameters of the event.\n   * @param senderId - Id supplied by the sender.\n   */\n  private _onEvent<EN extends EventName>(\n    event: EN,\n    params: EventPayloads<Item, IdProp>[EN],\n    senderId?: Id | null\n  ): void {\n    if (!params || !params.items || !this._data) {\n      return;\n    }\n\n    const ids = params.items;\n    const addedIds: Id[] = [];\n    const updatedIds: Id[] = [];\n    const removedIds: Id[] = [];\n    const oldItems: FullItem<Item, IdProp>[] = [];\n    const updatedItems: FullItem<Item, IdProp>[] = [];\n    const removedItems: FullItem<Item, IdProp>[] = [];\n\n    switch (event) {\n      case \"add\":\n        // filter the ids of the added items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n          if (item) {\n            this._ids.add(id);\n            addedIds.push(id);\n          }\n        }\n\n        break;\n\n      case \"update\":\n        // determine the event from the views viewpoint: an updated\n        // item can be added, updated, or removed from this view.\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          const item = this.get(id);\n\n          if (item) {\n            if (this._ids.has(id)) {\n              updatedIds.push(id);\n              updatedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).data[i]\n              );\n              oldItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              this._ids.add(id);\n              addedIds.push(id);\n            }\n          } else {\n            if (this._ids.has(id)) {\n              this._ids.delete(id);\n              removedIds.push(id);\n              removedItems.push(\n                (params as UpdateEventPayload<Item, IdProp>).oldData[i]\n              );\n            } else {\n              // nothing interesting for me :-(\n            }\n          }\n        }\n\n        break;\n\n      case \"remove\":\n        // filter the ids of the removed items\n        for (let i = 0, len = ids.length; i < len; i++) {\n          const id = ids[i];\n          if (this._ids.has(id)) {\n            this._ids.delete(id);\n            removedIds.push(id);\n            removedItems.push(\n              (params as RemoveEventPayload<Item, IdProp>).oldData[i]\n            );\n          }\n        }\n\n        break;\n    }\n\n    this.length += addedIds.length - removedIds.length;\n\n    if (addedIds.length) {\n      this._trigger(\"add\", { items: addedIds }, senderId);\n    }\n    if (updatedIds.length) {\n      this._trigger(\n        \"update\",\n        { items: updatedIds, oldData: oldItems, data: updatedItems },\n        senderId\n      );\n    }\n    if (removedIds.length) {\n      this._trigger(\n        \"remove\",\n        { items: removedIds, oldData: removedItems },\n        senderId\n      );\n    }\n  }\n}\n", "import { PartItem } from \"./data-interface\";\nimport { DataSet } from \"./data-set\";\n\n/**\n * Check that given value is compatible with Vis Data Set interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataSetLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataSet<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.add === \"function\" &&\n    typeof v.clear === \"function\" &&\n    typeof v.distinct === \"function\" &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.max === \"function\" &&\n    typeof v.min === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.remove === \"function\" &&\n    typeof v.setOptions === \"function\" &&\n    typeof v.stream === \"function\" &&\n    typeof v.update === \"function\" &&\n    typeof v.updateOnly === \"function\"\n  );\n}\n", "import { DataView } from \"./data-view\";\nimport { PartItem } from \"./data-interface\";\nimport { isDataSetLike } from \"./data-set-check\";\n\n/**\n * Check that given value is compatible with Vis Data View interface.\n *\n * @param idProp - The expected property to contain item id.\n * @param v - The value to be tested.\n * @returns True if all expected values and methods match, false otherwise.\n */\nexport function isDataViewLike<\n  Item extends PartItem<IdProp>,\n  IdProp extends string = \"id\"\n>(idProp: IdProp, v: any): v is DataView<Item, IdProp> {\n  return (\n    typeof v === \"object\" &&\n    v !== null &&\n    idProp === v.idProp &&\n    typeof v.forEach === \"function\" &&\n    typeof v.get === \"function\" &&\n    typeof v.getDataSet === \"function\" &&\n    typeof v.getIds === \"function\" &&\n    typeof v.length === \"number\" &&\n    typeof v.map === \"function\" &&\n    typeof v.off === \"function\" &&\n    typeof v.on === \"function\" &&\n    typeof v.stream === \"function\" &&\n    isDataSetLike(idProp, v.getDataSet())\n  );\n}\n"], "names": ["uuid4", "pureDeepObjectAssign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAoCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BG;EACG,SAAU,qBAAqB,CAGnC,IAA2B,EAAA;EAC3B,IAAA,OAAO,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;EAC7C,CAAC;EAID;;;;;;;;EAQG;EACH,MAAM,cAAc,CAAA;EAyBC,IAAA,OAAA,CAAA;EACA,IAAA,aAAA,CAAA;EACA,IAAA,OAAA,CAAA;EApBnB;;EAEG;EACc,IAAA,UAAU,GAA2B;UACpD,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;UACzB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;UAC/B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;OAChC,CAAC;EAEF;;;;;;;EAOG;EACH,IAAA,WAAA,CACmB,OAA8B,EAC9B,aAA8C,EAC9C,OAAwB,EAAA;UAFxB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAuB;UAC9B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAiC;UAC9C,IAAO,CAAA,OAAA,GAAP,OAAO,CAAiB;OACvC;;MAGG,GAAG,GAAA;EACR,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EAC9D,QAAA,OAAO,IAAI,CAAC;OACb;;MAGM,KAAK,GAAA;EACV,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;EAC5C,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;EAClD,QAAA,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;EAElD,QAAA,OAAO,IAAI,CAAC;OACb;;MAGM,IAAI,GAAA;EACT,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;EAC7C,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;EACnD,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;EAEnD,QAAA,OAAO,IAAI,CAAC;OACb;EAED;;;;;EAKG;EACK,IAAA,eAAe,CAAC,KAAgB,EAAA;UACtC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,SAAS,KAAe;EAC/D,YAAA,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;WACzB,EAAE,KAAK,CAAC,CAAC;OACX;EAED;;;;;EAKG;MACK,IAAI,CACV,KAAmD,EACnD,OAAqD,EAAA;UAErD,IAAI,OAAO,IAAI,IAAI,EAAE;cACnB,OAAO;EACR,SAAA;UAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;OACzE;EAED;;;;;EAKG;MACK,OAAO,CACb,KAAsD,EACtD,OAAwD,EAAA;UAExD,IAAI,OAAO,IAAI,IAAI,EAAE;cACnB,OAAO;EACR,SAAA;UAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;OAC5E;EAED;;;;;EAKG;MACK,OAAO,CACb,KAAsD,EACtD,OAAwD,EAAA;UAExD,IAAI,OAAO,IAAI,IAAI,EAAE;cACnB,OAAO;EACR,SAAA;EAED,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;OAC5D;EACF,CAAA;EAED;;;;;;EAMG;EACH,MAAM,yBAAyB,CAAA;EAgBO,IAAA,OAAA,CAAA;EAZpC;;;EAGG;MACc,aAAa,GAAuB,EAAE,CAAC;EAExD;;;;;EAKG;EACH,IAAA,WAAA,CAAoC,OAA8B,EAAA;UAA9B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAuB;OAAI;EAEtE;;;;;;EAMG;EACI,IAAA,MAAM,CACX,QAA+B,EAAA;EAE/B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtE,QAAA,OAAO,IAAI,CAAC;OACb;EAED;;;;;;;;EAQG;EACI,IAAA,GAAG,CACR,QAA0B,EAAA;EAE1B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;EACnE,QAAA,OAAO,IAAoD,CAAC;OAC7D;EAED;;;;;;;;EAQG;EACI,IAAA,OAAO,CACZ,QAA4B,EAAA;EAE5B,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAgB,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;EACvE,QAAA,OAAO,IAAoD,CAAC;OAC7D;EAED;;;;;;EAMG;EACI,IAAA,EAAE,CAAC,MAAuB,EAAA;EAC/B,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;OACrE;EACF;;ECnRD;;;;;EAKG;EACG,SAAU,IAAI,CAAC,KAAc,EAAA;MACjC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;EAChE;;EC2BA;;;;EAIG;QACU,KAAK,CAAA;;EAET,IAAA,KAAK,CAAgB;;EAErB,IAAA,GAAG,CAAS;MAEF,MAAM,GAIjB,EAAE,CAAC;MAED,QAAQ,GAAyC,IAAI,CAAC;MACtD,SAAS,GAA4B,IAAI,CAAC;EAElD;;;;EAIG;EACH,IAAA,WAAA,CAAmB,OAAsB,EAAA;;EAEvC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;EAClB,QAAA,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;EAEpB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;OAC1B;EAED;;;;EAIG;EACI,IAAA,UAAU,CAAC,OAAsB,EAAA;UACtC,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW,EAAE;EACnD,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;EAC5B,SAAA;UACD,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,WAAW,EAAE;EACjD,YAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;EACxB,SAAA;UAED,IAAI,CAAC,cAAc,EAAE,CAAC;OACvB;EAED;;;;;;;EAOG;EACI,IAAA,OAAO,MAAM,CAClB,MAAS,EACT,OAA8B,EAAA;EAE9B,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAI,OAAO,CAAC,CAAC;EAEpC,QAAA,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;EAC9B,YAAA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;EAC/D,SAAA;EACD,QAAA,MAAM,CAAC,KAAK,GAAG,MAAW;cACxB,KAAK,CAAC,KAAK,EAAE,CAAC;EAChB,SAAC,CAAC;EAEF,QAAA,MAAM,OAAO,GAAgC;EAC3C,YAAA;EACE,gBAAA,IAAI,EAAE,OAAO;EACb,gBAAA,QAAQ,EAAE,SAAS;EACpB,aAAA;WACF,CAAC;EAEF,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;EAC9B,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;kBAC/C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;kBAChC,OAAO,CAAC,IAAI,CAAC;EACX,oBAAA,IAAI,EAAE,IAAI;;EAEV,oBAAA,QAAQ,EAAG,MAA2C,CAAC,IAAI,CAAC;EAC7D,iBAAA,CAAC,CAAC;;EAEH,gBAAA,KAAK,CAAC,OAAO,CAAC,MAA0C,EAAE,IAAI,CAAC,CAAC;EACjE,aAAA;EACF,SAAA;UAED,KAAK,CAAC,SAAS,GAAG;EAChB,YAAA,MAAM,EAAE,MAAM;EACd,YAAA,OAAO,EAAE,OAAO;WACjB,CAAC;EAEF,QAAA,OAAO,KAAK,CAAC;OACd;EAED;;EAEG;MACI,OAAO,GAAA;UACZ,IAAI,CAAC,KAAK,EAAE,CAAC;UAEb,IAAI,IAAI,CAAC,SAAS,EAAE;EAClB,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;EACrC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;EACvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EACvC,gBAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;kBAC1B,IAAI,MAAM,CAAC,QAAQ,EAAE;;sBAElB,MAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;EAChD,iBAAA;EAAM,qBAAA;;EAEL,oBAAA,OAAQ,MAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACrC,iBAAA;EACF,aAAA;EACD,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;EACvB,SAAA;OACF;EAED;;;;;EAKG;MACI,OAAO,CACZ,MAA6B,EAC7B,MAAS,EAAA;;UAGT,MAAM,EAAE,GAAG,IAAI,CAAC;EAChB,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;UAChC,IAAI,CAAC,QAAQ,EAAE;cACb,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,MAAM,GAAG,YAAY,CAAC,CAAC;EACpD,SAAA;EAED,QAAA,MAAM,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,IAAe,EAAA;;cAE3C,EAAE,CAAC,KAAK,CAAC;EACP,gBAAA,IAAI,EAAE,IAAI;EACV,gBAAA,EAAE,EAAE,QAAQ;EACZ,gBAAA,OAAO,EAAE,IAAI;EACd,aAAA,CAAC,CAAC;EACL,SAAC,CAAC;OACH;EAED;;;;EAIG;EACI,IAAA,KAAK,CAAC,KAAqB,EAAA;EAChC,QAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;cAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;EACjC,SAAA;EAAM,aAAA;EACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACzB,SAAA;UAED,IAAI,CAAC,cAAc,EAAE,CAAC;OACvB;EAED;;EAEG;MACK,cAAc,GAAA;;UAEpB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE;cACjC,IAAI,CAAC,KAAK,EAAE,CAAC;EACd,SAAA;;EAGD,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;EACzB,YAAA,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC5B,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;EACtB,SAAA;EACD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;EAC3D,YAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAW;kBACpC,IAAI,CAAC,KAAK,EAAE,CAAC;EACf,aAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EAChB,SAAA;OACF;EAED;;EAEG;MACI,KAAK,GAAA;EACV,QAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAU;EAC5C,YAAA,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;EAC9D,SAAC,CAAC,CAAC;OACJ;EACF;;EC/ND;;;;;EAKG;QACmB,WAAW,CAAA;EAKd,IAAA,YAAY,GAEzB;EACF,QAAA,GAAG,EAAE,EAAE;EACP,QAAA,GAAG,EAAE,EAAE;EACP,QAAA,MAAM,EAAE,EAAE;EACV,QAAA,MAAM,EAAE,EAAE;OACX,CAAC;EAiBF;;;;;;EAMG;EACO,IAAA,QAAQ,CAChB,KAAW,EACX,OAA0C,EAC1C,QAAoB,EAAA;UAEpB,IAAK,KAAgB,KAAK,GAAG,EAAE;EAC7B,YAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;EAC3C,SAAA;UAED,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAC9D,CAAC,UAAU,KAAU;EACnB,YAAA,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAC;EACjE,SAAC,CACF,CAAC;OACH;EAsBD;;;;;;EAMG;MACI,EAAE,CACP,KAAW,EACX,QAAmD,EAAA;EAEnD,QAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;cAClC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACzC,SAAA;;OAEF;EAsBD;;;;;;EAMG;MACI,GAAG,CACR,KAAW,EACX,QAAmD,EAAA;UAEnD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CACxD,CAAC,UAAU,KAAc,UAAU,KAAK,QAAQ,CACjD,CAAC;OACH;EAED;;EAEG;EACI,IAAA,SAAS,GAAoC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;EAC7E;;EAEG;EACI,IAAA,WAAW,GAChB,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC;EAE7B;;ECzJD;;;;;;;;EAQG;QACU,UAAU,CAAA;EACJ,IAAA,MAAM,CAAuB;EAE9C;;;;EAIG;EACH,IAAA,WAAA,CAAmB,KAA2B,EAAA;EAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;OACrB;EAED;;EAEG;EACI,IAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;UACvB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EACpC,YAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAClB,SAAA;OACF;EAED;;EAEG;EACI,IAAA,CAAC,OAAO,GAAA;UACb,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EACpC,YAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAClB,SAAA;OACF;EAED;;EAEG;EACI,IAAA,CAAC,IAAI,GAAA;UACV,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EAC9B,YAAA,MAAM,EAAE,CAAC;EACV,SAAA;OACF;EAED;;EAEG;EACI,IAAA,CAAC,MAAM,GAAA;UACZ,KAAK,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EAClC,YAAA,MAAM,IAAI,CAAC;EACZ,SAAA;OACF;EAED;;;;;;EAMG;MACI,SAAS,GAAA;EACd,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;OACpD;EAED;;;;;;EAMG;MACI,WAAW,GAAA;EAChB,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAW,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;OACtD;EAED;;;;;;EAMG;MACI,YAAY,GAAA;EACjB,QAAA,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;OACzB;EAED;;;;;;EAMG;MACI,WAAW,GAAA;UAChB,MAAM,GAAG,GAAqB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;UAClD,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EACpC,YAAA,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;EAChB,SAAA;EACD,QAAA,OAAO,GAAG,CAAC;OACZ;EAED;;;;EAIG;MACI,KAAK,GAAA;EACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OAC7B;EAED;;;;EAIG;MACI,OAAO,GAAA;UACZ,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;OAClC;EAED;;;;EAIG;MACI,SAAS,GAAA;UACd,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;OACpC;EAED;;;;;;;;;;;;;;;;;;;;;EAqBG;MACI,KAAK,GAAA;UACV,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;OACzC;EAED;;;;;;EAMG;EACI,IAAA,QAAQ,CAAI,QAAmC,EAAA;EACpD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,EAAK,CAAC;UAEzB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;cACpC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;EAC7B,SAAA;EAED,QAAA,OAAO,GAAG,CAAC;OACZ;EAED;;;;;EAKG;EACI,IAAA,MAAM,CAAC,QAAyC,EAAA;EACrD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;UAC1B,OAAO,IAAI,UAAU,CAAO;EAC1B,YAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;kBAChB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE;EAC9B,oBAAA,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;EACtB,wBAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAClB,qBAAA;EACF,iBAAA;eACF;EACF,SAAA,CAAC,CAAC;OACJ;EAED;;;;EAIG;EACI,IAAA,OAAO,CAAC,QAAyC,EAAA;UACtD,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;EACpC,YAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;EACpB,SAAA;OACF;EAED;;;;;;EAMG;EACI,IAAA,GAAG,CACR,QAAwC,EAAA;EAExC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;UAC1B,OAAO,IAAI,UAAU,CAAS;EAC5B,YAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;kBAChB,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE;sBAC9B,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;EAChC,iBAAA;eACF;EACF,SAAA,CAAC,CAAC;OACJ;EAED;;;;;EAKG;EACI,IAAA,GAAG,CAAC,QAAwC,EAAA;UACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;EAC5C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;UACvB,IAAI,IAAI,CAAC,IAAI,EAAE;EACb,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;UAED,IAAI,OAAO,GAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAClC,QAAA,IAAI,QAAQ,GAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,OAAO,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;cACjC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;cAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;cACjC,IAAI,KAAK,GAAG,QAAQ,EAAE;kBACpB,QAAQ,GAAG,KAAK,CAAC;kBACjB,OAAO,GAAG,IAAI,CAAC;EAChB,aAAA;EACF,SAAA;EAED,QAAA,OAAO,OAAO,CAAC;OAChB;EAED;;;;;EAKG;EACI,IAAA,GAAG,CAAC,QAAwC,EAAA;UACjD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;EAC5C,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;UACvB,IAAI,IAAI,CAAC,IAAI,EAAE;EACb,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;UAED,IAAI,OAAO,GAAS,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAClC,QAAA,IAAI,QAAQ,GAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,OAAO,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;cACjC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;cAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;cACjC,IAAI,KAAK,GAAG,QAAQ,EAAE;kBACpB,QAAQ,GAAG,KAAK,CAAC;kBACjB,OAAO,GAAG,IAAI,CAAC;EAChB,aAAA;EACF,SAAA;EAED,QAAA,OAAO,OAAO,CAAC;OAChB;EAED;;;;;;;EAOG;MACI,MAAM,CACX,QAAmD,EACnD,WAAc,EAAA;UAEd,KAAK,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;cACpC,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;EAC/C,SAAA;EACD,QAAA,OAAO,WAAW,CAAC;OACpB;EAED;;;;;EAKG;EACI,IAAA,IAAI,CACT,QAAgE,EAAA;UAEhE,OAAO,IAAI,UAAU,CAAC;EACpB,YAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,MACjB,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;EACb,iBAAA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAC/B,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CACjC,CACA,MAAM,CAAC,QAAQ,CAAC,EAAE;EACxB,SAAA,CAAC,CAAC;OACJ;EACF;;ECvQD;;;;;;;;;;EAUG;EACH,SAAS,cAAc,CACrB,IAAU,EACV,MAAc,EAAA;EAEd,IAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;;EAExB,QAAA,IAAI,CAAC,MAAM,CAAC,GAAGA,OAAK,EAAS,CAAC;EAC/B,KAAA;EAED,IAAA,OAAO,IAA8B,CAAC;EACxC,CAAC;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA0DG;EACG,MAAO,OAIX,SAAQ,WAAyB,CAAA;;EAI1B,IAAA,KAAK,CAAc;;EAEnB,IAAA,MAAM,CAAS;;EAEtB,IAAA,IAAW,MAAM,GAAA;UACf,OAAO,IAAI,CAAC,OAAO,CAAC;OACrB;EAEgB,IAAA,QAAQ,CAAgC;EACxC,IAAA,KAAK,CAAkC;EACvC,IAAA,OAAO,CAAS;MACzB,MAAM,GAAuB,IAAI,CAAC;EAW1C;;;;;EAKG;MACH,WACE,CAAA,IAA6C,EAC7C,OAAuC,EAAA;EAEvC,QAAA,KAAK,EAAE,CAAC;;UAGR,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;cAChC,OAAO,GAAG,IAAI,CAAC;cACf,IAAI,GAAG,EAAE,CAAC;EACX,SAAA;EAED,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;UAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;EACvB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAChB,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAK,IAAe,CAAC;;EAGzD,QAAA,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;EACvB,YAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;EAChB,SAAA;EAED,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;OAC1B;EAED;;;;EAIG;EACI,IAAA,UAAU,CAAC,OAAwB,EAAA;EACxC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;EAC1C,YAAA,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;;kBAE3B,IAAI,IAAI,CAAC,MAAM,EAAE;EACf,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;EACtB,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACpB,iBAAA;EACF,aAAA;EAAM,iBAAA;;EAEL,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;sBAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;EAC/B,wBAAA,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrC,qBAAA,CAAC,CAAC;EACJ,iBAAA;kBAED,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;sBACtD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EACvC,iBAAA;EACF,aAAA;EACF,SAAA;OACF;EAED;;;;;;;;;;;;;;;;;;;;;;;;;EAyBG;MACI,GAAG,CAAC,IAAmB,EAAE,QAAoB,EAAA;UAClD,MAAM,QAAQ,GAAS,EAAE,CAAC;EAC1B,QAAA,IAAI,EAAM,CAAC;EAEX,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;EAEvB,YAAA,MAAM,QAAQ,GAAS,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAO,CAAC,CAAC;EAC9D,YAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;EAC7C,gBAAA,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;EACrE,aAAA;EACD,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;kBAC/C,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnB,aAAA;EACF,SAAA;EAAM,aAAA,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;EAE3C,YAAA,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzB,YAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnB,SAAA;EAAM,aAAA;EACL,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;EACrC,SAAA;UAED,IAAI,QAAQ,CAAC,MAAM,EAAE;EACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;EACrD,SAAA;EAED,QAAA,OAAO,QAAQ,CAAC;OACjB;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCG;MACI,MAAM,CACX,IAA6C,EAC7C,QAAoB,EAAA;UAEpB,MAAM,QAAQ,GAAS,EAAE,CAAC;UAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;UAC5B,MAAM,OAAO,GAA6B,EAAE,CAAC;UAC7C,MAAM,WAAW,GAA6B,EAAE,CAAC;EACjD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;EAE5B,QAAA,MAAM,WAAW,GAAG,CAAC,IAAuB,KAAU;EACpD,YAAA,MAAM,MAAM,GAAU,IAAI,CAAC,MAAM,CAAC,CAAC;EACnC,YAAA,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;EAC5C,gBAAA,MAAM,QAAQ,GAAG,IAA8B,CAAC;EAChD,gBAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;kBAE1D,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;EACtC,gBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACpB,gBAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAC3B,gBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACvB,aAAA;EAAM,iBAAA;;kBAEL,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAW,CAAC,CAAC;EACtC,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnB,aAAA;EACH,SAAC,CAAC;EAEF,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;;EAEvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC/C,gBAAA,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;EAC1C,oBAAA,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,iBAAA;EAAM,qBAAA;EACL,oBAAA,OAAO,CAAC,IAAI,CACV,uDAAuD,GAAG,CAAC,CAC5D,CAAC;EACH,iBAAA;EACF,aAAA;EACF,SAAA;EAAM,aAAA,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;cAE3C,WAAW,CAAC,IAAI,CAAC,CAAC;EACnB,SAAA;EAAM,aAAA;EACL,YAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;EACrC,SAAA;UAED,IAAI,QAAQ,CAAC,MAAM,EAAE;EACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;EACrD,SAAA;UACD,IAAI,UAAU,CAAC,MAAM,EAAE;EACrB,YAAA,MAAM,KAAK,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;;;;;;;;cAQzE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;EAC1C,SAAA;EAED,QAAA,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;OACpC;EAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiCG;MACI,UAAU,CACf,IAA2D,EAC3D,QAAoB,EAAA;EAEpB,QAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;EACxB,YAAA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;EACf,SAAA;UAED,MAAM,eAAe,GAAG,IAAI;EACzB,aAAA,GAAG,CACF,CACE,MAAM,KAIJ;EACF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;cACrD,IAAI,OAAO,IAAI,IAAI,EAAE;EACnB,gBAAA,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;EAChE,aAAA;EACD,YAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;EAC7B,SAAC,CACF;eACA,GAAG,CACF,CAAC,EACC,OAAO,EACP,MAAM,GACP,KAIG;cACF,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;cACjC,MAAM,WAAW,GAAGC,2BAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;cAE1D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;cAEhC,OAAO;kBACL,EAAE;EACF,gBAAA,OAAO,EAAE,OAAO;kBAChB,WAAW;eACZ,CAAC;EACJ,SAAC,CACF,CAAC;UAEJ,IAAI,eAAe,CAAC,MAAM,EAAE;EAC1B,YAAA,MAAM,KAAK,GAA0C;EACnD,gBAAA,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,KAAS,KAAK,CAAC,EAAE,CAAC;EACnD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,CAC1B,CAAC,KAAK,KAA6B,KAAK,CAAC,OAAO,CACjD;EACD,gBAAA,IAAI,EAAE,eAAe,CAAC,GAAG,CACvB,CAAC,KAAK,KAA6B,KAAK,CAAC,WAAW,CACrD;eACF,CAAC;;;;;;;;cAQF,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;cAEzC,OAAO,KAAK,CAAC,KAAK,CAAC;EACpB,SAAA;EAAM,aAAA;EACL,YAAA,OAAO,EAAE,CAAC;EACX,SAAA;OACF;;MA6DM,GAAG,CACR,KAAiD,EACjD,MAAsC,EAAA;;;UAStC,IAAI,EAAE,GAAmB,SAAS,CAAC;UACnC,IAAI,GAAG,GAAqB,SAAS,CAAC;UACtC,IAAI,OAAO,GAA8C,SAAS,CAAC;EACnE,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;;cAEf,EAAE,GAAG,KAAK,CAAC;cACX,OAAO,GAAG,MAAM,CAAC;EAClB,SAAA;EAAM,aAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;cAE/B,GAAG,GAAG,KAAK,CAAC;cACZ,OAAO,GAAG,MAAM,CAAC;EAClB,SAAA;EAAM,aAAA;;cAEL,OAAO,GAAG,KAAK,CAAC;EACjB,SAAA;;EAGD,QAAA,MAAM,UAAU,GACd,OAAO,IAAI,OAAO,CAAC,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;;;;;;;;;;;;;EAclE,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;UACzC,MAAM,KAAK,GAA6B,EAAE,CAAC;UAC3C,IAAI,IAAI,GAAuC,SAAS,CAAC;UACzD,IAAI,OAAO,GAAqB,SAAS,CAAC;UAC1C,IAAI,MAAM,GAAmB,SAAS,CAAC;;UAGvC,IAAI,EAAE,IAAI,IAAI,EAAE;;cAEd,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cAC1B,IAAI,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;kBACnC,IAAI,GAAG,SAAS,CAAC;EAClB,aAAA;EACF,SAAA;eAAM,IAAI,GAAG,IAAI,IAAI,EAAE;;EAEtB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,gBAAA,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;EAC7C,oBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClB,iBAAA;EACF,aAAA;EACF,SAAA;EAAM,aAAA;;cAEL,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;EACjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,gBAAA,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;kBACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;EAC9B,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;EAC7C,oBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClB,iBAAA;EACF,aAAA;EACF,SAAA;;UAGD,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,EAAE,IAAI,SAAS,EAAE;cAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;EAClC,SAAA;;EAGD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;EAC7B,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;EAC9B,YAAA,IAAI,EAAE,IAAI,SAAS,IAAI,IAAI,IAAI,IAAI,EAAE;kBACnC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAA2B,CAAC;EACnE,aAAA;EAAM,iBAAA;EACL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAChD,oBAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAG7C,CAAC;EACH,iBAAA;EACF,aAAA;EACF,SAAA;;UAGD,IAAI,UAAU,IAAI,QAAQ,EAAE;cAC1B,MAAM,MAAM,GAA2C,EAAE,CAAC;EAC1D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAChD,gBAAA,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;;kBAG3B,MAAM,EAAE,GAAO,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACvC,gBAAA,MAAM,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;EACxB,aAAA;EACD,YAAA,OAAO,MAAM,CAAC;EACf,SAAA;EAAM,aAAA;cACL,IAAI,EAAE,IAAI,IAAI,EAAE;;kBAEd,OAAO,IAAI,IAAI,IAAI,CAAC;EACrB,aAAA;EAAM,iBAAA;;EAEL,gBAAA,OAAO,KAAK,CAAC;EACd,aAAA;EACF,SAAA;OACF;;EAGM,IAAA,MAAM,CAAC,OAA0C,EAAA;EACtD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;EACxB,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;EACzC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC;UACvC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;UACjC,MAAM,GAAG,GAAS,EAAE,CAAC;EAErB,QAAA,IAAI,MAAM,EAAE;;EAEV,YAAA,IAAI,KAAK,EAAE;;kBAET,MAAM,KAAK,GAAG,EAAE,CAAC;EACjB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;sBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;sBAChC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;EAChC,wBAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAClB,qBAAA;EACF,iBAAA;EAED,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAEzB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAChD,oBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;EAClC,iBAAA;EACF,aAAA;EAAM,iBAAA;;EAEL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;sBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;sBAChC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;0BAChC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;EAC9B,qBAAA;EACF,iBAAA;EACF,aAAA;EACF,SAAA;EAAM,aAAA;;EAEL,YAAA,IAAI,KAAK,EAAE;;kBAET,MAAM,KAAK,GAAG,EAAE,CAAC;EACjB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;sBACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAE,CAAC,CAAC;EAC3B,iBAAA;EAED,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAEzB,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAChD,oBAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;EAClC,iBAAA;EACF,aAAA;EAAM,iBAAA;;EAEL,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,oBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;sBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;sBAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;0BAChB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;EAC9B,qBAAA;EACF,iBAAA;EACF,aAAA;EACF,SAAA;EAED,QAAA,OAAO,GAAG,CAAC;OACZ;;MAGM,UAAU,GAAA;EACf,QAAA,OAAO,IAAI,CAAC;OACb;;MAGM,OAAO,CACZ,QAAsC,EACtC,OAA2C,EAAA;EAE3C,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;EACzC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;UACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;EAEjC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;;cAE5B,MAAM,KAAK,GAA6B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;EAE1D,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAChD,gBAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;kBACtB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC9B,gBAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;EACpB,aAAA;EACF,SAAA;EAAM,aAAA;;EAEL,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,gBAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;kBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAChC,gBAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;EAC7C,oBAAA,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;EACpB,iBAAA;EACF,aAAA;EACF,SAAA;OACF;;MAGM,GAAG,CACR,QAAmC,EACnC,OAA0C,EAAA;EAE1C,QAAA,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;UACzC,MAAM,WAAW,GAAQ,EAAE,CAAC;EAC5B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;UACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;EAGjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;cACtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAChC,YAAA,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;kBAC7C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;EACtC,aAAA;EACF,SAAA;;EAGD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;cAC5B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;EACxC,SAAA;EAED,QAAA,OAAO,WAAW,CAAC;OACpB;EAWD;;;;;;;EAOG;MACK,aAAa,CACnB,IAA+B,EAC/B,MAAgC,EAAA;UAEhC,IAAI,CAAC,IAAI,EAAE;;EAET,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;EAED,QAAA,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;EACnB;kBACE,MAAM;EACR;EACG,gBAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAS,EAChC,MAAM,CACN,CAAC,YAAY,EAAE,KAAK,KAA6B;cAC/C,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;EAClC,YAAA,OAAO,YAAY,CAAC;WACrB,EACD,EAAE,CACH,CAAC;OACH;EAED;;;;;;EAMG;MACK,KAAK,CAAI,KAAU,EAAE,KAA4B,EAAA;EACvD,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;;EAE7B,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC;cACnB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAgB;;EAE9B,gBAAA,MAAM,EAAE,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;EAC5B,gBAAA,MAAM,EAAE,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;kBAC5B,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACxC,aAAC,CAAC,CAAC;EACJ,SAAA;EAAM,aAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;;EAEtC,YAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACnB,SAAA;EAAM,aAAA;;;EAGL,YAAA,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;EAC7D,SAAA;OACF;EAED;;;;;;;;;;;;;;;;;;;;;;;;;EAyBG;MACI,MAAM,CAAC,EAA6B,EAAE,QAAoB,EAAA;UAC/D,MAAM,UAAU,GAAS,EAAE,CAAC;UAC5B,MAAM,YAAY,GAA6B,EAAE,CAAC;;EAGlD,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;EAE1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;cAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC,YAAA,IAAI,IAAI,EAAE;kBACR,MAAM,MAAM,GAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;kBACzC,IAAI,MAAM,IAAI,IAAI,EAAE;EAClB,oBAAA,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACxB,oBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACzB,iBAAA;EACF,aAAA;EACF,SAAA;UAED,IAAI,UAAU,CAAC,MAAM,EAAE;EACrB,YAAA,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,EAC5C,QAAQ,CACT,CAAC;EACH,SAAA;EAED,QAAA,OAAO,UAAU,CAAC;OACnB;EAED;;;;;EAKG;EACK,IAAA,OAAO,CAAC,EAAa,EAAA;;;EAG3B,QAAA,IAAI,KAAY,CAAC;;EAGjB,QAAA,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE;cACZ,KAAK,GAAG,EAAE,CAAC;EACZ,SAAA;EAAM,aAAA,IAAI,EAAE,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;cACvC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC1B,SAAA;;EAGD,QAAA,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;EAC1C,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;EAC3C,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;cACzB,EAAE,IAAI,CAAC,MAAM,CAAC;EACd,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;EAED,QAAA,OAAO,IAAI,CAAC;OACb;EAED;;;;;;;EAOG;EACI,IAAA,KAAK,CAAC,QAAoB,EAAA;UAC/B,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;UACnC,MAAM,KAAK,GAA6B,EAAE,CAAC;EAE3C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;EACrC,SAAA;EAED,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;EACnB,QAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAEhB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;EAElE,QAAA,OAAO,GAAG,CAAC;OACZ;EAED;;;;;EAKG;EACI,IAAA,GAAG,CAAC,KAAiB,EAAA;UAC1B,IAAI,GAAG,GAAG,IAAI,CAAC;UACf,IAAI,QAAQ,GAAG,IAAI,CAAC;UAEpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;EACtC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;cAC9B,IACE,OAAO,SAAS,KAAK,QAAQ;mBAC5B,QAAQ,IAAI,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,EAC1C;kBACA,GAAG,GAAG,IAAI,CAAC;kBACX,QAAQ,GAAG,SAAS,CAAC;EACtB,aAAA;EACF,SAAA;UAED,OAAO,GAAG,IAAI,IAAI,CAAC;OACpB;EAED;;;;;EAKG;EACI,IAAA,GAAG,CAAC,KAAiB,EAAA;UAC1B,IAAI,GAAG,GAAG,IAAI,CAAC;UACf,IAAI,QAAQ,GAAG,IAAI,CAAC;UAEpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE;EACtC,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;cAC9B,IACE,OAAO,SAAS,KAAK,QAAQ;mBAC5B,QAAQ,IAAI,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,EAC1C;kBACA,GAAG,GAAG,IAAI,CAAC;kBACX,QAAQ,GAAG,SAAS,CAAC;EACtB,aAAA;EACF,SAAA;UAED,OAAO,GAAG,IAAI,IAAI,CAAC;OACpB;EAID;;;;;EAKG;EACI,IAAA,QAAQ,CAAmB,IAAO,EAAA;EACvC,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;UACxB,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;UACjC,MAAM,MAAM,GAAc,EAAE,CAAC;UAC7B,IAAI,KAAK,GAAG,CAAC,CAAC;EAEd,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAClD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;cACtB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC1B,YAAA,MAAM,KAAK,GAAI,IAAY,CAAC,IAAI,CAAC,CAAC;cAClC,IAAI,MAAM,GAAG,KAAK,CAAC;cACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;EAC9B,gBAAA,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;sBACtB,MAAM,GAAG,IAAI,CAAC;sBACd,MAAM;EACP,iBAAA;EACF,aAAA;EACD,YAAA,IAAI,CAAC,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE;EAClC,gBAAA,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EACtB,gBAAA,KAAK,EAAE,CAAC;EACT,aAAA;EACF,SAAA;EAED,QAAA,OAAO,MAAM,CAAC;OACf;EAED;;;;;EAKG;EACK,IAAA,QAAQ,CAAC,IAAU,EAAA;UACzB,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;UACpD,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;UAGlC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;;cAEtB,MAAM,IAAI,KAAK,CACb,gCAAgC,GAAG,EAAE,GAAG,iBAAiB,CAC1D,CAAC;EACH,SAAA;UAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;UAC7B,EAAE,IAAI,CAAC,MAAM,CAAC;EAEd,QAAA,OAAO,EAAE,CAAC;OACX;EAED;;;;;;EAMG;EACK,IAAA,WAAW,CAAC,MAA8B,EAAA;UAChD,MAAM,EAAE,GAAU,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACvC,IAAI,EAAE,IAAI,IAAI,EAAE;cACd,MAAM,IAAI,KAAK,CACb,4CAA4C;EAC1C,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;EACtB,gBAAA,GAAG,CACN,CAAC;EACH,SAAA;UACD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;UAChC,IAAI,CAAC,IAAI,EAAE;;cAET,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC;EACzE,SAAA;EAED,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC;EAE3C,QAAA,OAAO,EAAE,CAAC;OACX;;EAGM,IAAA,MAAM,CAAC,GAAkB,EAAA;EAC9B,QAAA,IAAI,GAAG,EAAE;EACP,YAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;cAExB,OAAO,IAAI,UAAU,CAAO;EAC1B,gBAAA,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAA;EAChB,oBAAA,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE;0BACpB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;0BAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;EAChB,4BAAA,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;EAClB,yBAAA;EACF,qBAAA;mBACF;EACF,aAAA,CAAC,CAAC;EACJ,SAAA;EAAM,aAAA;cACL,OAAO,IAAI,UAAU,CAAC;EACpB,gBAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;EACvD,aAAA,CAAC,CAAC;EACJ,SAAA;OACF;EAEF;;ECvjCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4CG;EACG,MAAO,QAIX,SAAQ,WAAyB,CAAA;;MAI1B,MAAM,GAAG,CAAC,CAAC;;EAElB,IAAA,IAAW,MAAM,GAAA;EACf,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC;OACjC;EAEgB,IAAA,SAAS,CAA2C;MAC7D,KAAK,CAA+B;EAC3B,IAAA,IAAI,GAAY,IAAI,GAAG,EAAE,CAAC;EAC1B,IAAA,QAAQ,CAAgC;EAEzD;;;;;EAKG;MACH,WACE,CAAA,IAAiC,EACjC,OAAuC,EAAA;EAEvC,QAAA,KAAK,EAAE,CAAC;EAER,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,EAAE,CAAC;UAE9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE1C,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;OACpB;;;EAKD;;;;;;;;;EASG;EACI,IAAA,OAAO,CAAC,IAAiC,EAAA;UAC9C,IAAI,IAAI,CAAC,KAAK,EAAE;;EAEd,YAAA,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;kBAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EACrC,aAAA;;EAGD,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;cAChE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAElC,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;EAClB,YAAA,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;EAChB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;EACzD,SAAA;UAED,IAAI,IAAI,IAAI,IAAI,EAAE;EAChB,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;EAGlB,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;EAChE,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,gBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAClB,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACnB,aAAA;EACD,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;cACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;EACtC,SAAA;EAAM,aAAA;EACL,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,OAAO,EAAgB,CAAC;EAC1C,SAAA;;EAGD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;cACjB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EACpC,SAAA;OACF;EAED;;;EAGG;MACI,OAAO,GAAA;EACZ,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EAC5B,YAAA,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;EAC7B,SAAA,CAAC,CAAC;UACH,MAAM,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;UAC9B,MAAM,MAAM,GAAwB,EAAE,CAAC;UACvC,MAAM,QAAQ,GAAS,EAAE,CAAC;UAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;UAC5B,MAAM,YAAY,GAA6B,EAAE,CAAC;;EAGlD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,YAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAClB,YAAA,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;cAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;EACtB,gBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EAClB,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACnB,aAAA;EACF,SAAA;;EAGD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EACjD,YAAA,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;cACrB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cAChC,IAAI,IAAI,IAAI,IAAI,EAAE;;;;;EAKhB,gBAAA,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;EACrD,aAAA;EAAM,iBAAA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;EACtB,gBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACpB,gBAAA,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxB,gBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EACtB,aAAA;EACF,SAAA;UAED,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;;UAGnD,IAAI,QAAQ,CAAC,MAAM,EAAE;cACnB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;EAC3C,SAAA;UACD,IAAI,UAAU,CAAC,MAAM,EAAE;EACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;EACvE,SAAA;OACF;;MA6DM,GAAG,CACR,KAAiD,EACjD,MAAsC,EAAA;EAMtC,QAAA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;EACtB,YAAA,OAAO,IAAI,CAAC;EACb,SAAA;;UAGD,IAAI,GAAG,GAAqB,IAAI,CAAC;EACjC,QAAA,IAAI,OAAY,CAAC;UACjB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;cACvC,GAAG,GAAG,KAAK,CAAC;cACZ,OAAO,GAAG,MAAM,CAAC;EAClB,SAAA;EAAM,aAAA;cACL,OAAO,GAAG,KAAK,CAAC;EACjB,SAAA;;EAGD,QAAA,MAAM,WAAW,GAAkC,MAAM,CAAC,MAAM,CAC9D,EAAE,EACF,IAAI,CAAC,QAAQ,EACb,OAAO,CACR,CAAC;;EAGF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EACxC,QAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;UAChD,IAAI,UAAU,IAAI,aAAa,EAAE;EAC/B,YAAA,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,KAAa;kBACrC,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;EACjD,aAAC,CAAC;EACH,SAAA;UAED,IAAI,GAAG,IAAI,IAAI,EAAE;cACf,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;EACpC,SAAA;EAAM,aAAA;cACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;EACzC,SAAA;OACF;;EAGM,IAAA,MAAM,CAAC,OAA0C,EAAA;EACtD,QAAA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;EACrB,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;EAC9D,YAAA,IAAI,MAAkD,CAAC;EAEvD,YAAA,IAAI,aAAa,EAAE;EACjB,gBAAA,IAAI,aAAa,EAAE;EACjB,oBAAA,MAAM,GAAG,CAAC,IAAI,KAAa;0BACzB,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;EACpD,qBAAC,CAAC;EACH,iBAAA;EAAM,qBAAA;sBACL,MAAM,GAAG,aAAa,CAAC;EACxB,iBAAA;EACF,aAAA;EAAM,iBAAA;kBACL,MAAM,GAAG,aAAa,CAAC;EACxB,aAAA;EAED,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EACvB,gBAAA,MAAM,EAAE,MAAM;EACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;EAChC,aAAA,CAAC,CAAC;EACJ,SAAA;EAAM,aAAA;EACL,YAAA,OAAO,EAAE,CAAC;EACX,SAAA;OACF;;MAGM,OAAO,CACZ,QAAsC,EACtC,OAA2C,EAAA;UAE3C,IAAI,IAAI,CAAC,KAAK,EAAE;EACd,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;EAChD,YAAA,IAAI,MAA6C,CAAC;EAElD,YAAA,IAAI,aAAa,EAAE;EACjB,gBAAA,IAAI,aAAa,EAAE;sBACjB,MAAM,GAAG,UAAU,IAAU,EAAA;0BAC3B,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;EACpD,qBAAC,CAAC;EACH,iBAAA;EAAM,qBAAA;sBACL,MAAM,GAAG,aAAa,CAAC;EACxB,iBAAA;EACF,aAAA;EAAM,iBAAA;kBACL,MAAM,GAAG,aAAa,CAAC;EACxB,aAAA;EAED,YAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;EAC3B,gBAAA,MAAM,EAAE,MAAM;EACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;EAChC,aAAA,CAAC,CAAC;EACJ,SAAA;OACF;;MAGM,GAAG,CACR,QAAmC,EACnC,OAA0C,EAAA;UAI1C,IAAI,IAAI,CAAC,KAAK,EAAE;EACd,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;EAC3C,YAAA,MAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;EAChD,YAAA,IAAI,MAA0B,CAAC;EAE/B,YAAA,IAAI,aAAa,EAAE;EACjB,gBAAA,IAAI,aAAa,EAAE;EACjB,oBAAA,MAAM,GAAG,CAAC,IAAI,KAAwB;0BACpC,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;EACpD,qBAAC,CAAC;EACH,iBAAA;EAAM,qBAAA;sBACL,MAAM,GAAG,aAAa,CAAC;EACxB,iBAAA;EACF,aAAA;EAAM,iBAAA;kBACL,MAAM,GAAG,aAAa,CAAC;EACxB,aAAA;EAED,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;EAC9B,gBAAA,MAAM,EAAE,MAAM;EACd,gBAAA,KAAK,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK;EAChC,aAAA,CAAC,CAAC;EACJ,SAAA;EAAM,aAAA;EACL,YAAA,OAAO,EAAE,CAAC;EACX,SAAA;OACF;;MAGM,UAAU,GAAA;EACf,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;OAChC;;EAGM,IAAA,MAAM,CAAC,GAAkB,EAAA;EAC9B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CACtB,GAAG,IAAI;EACL,YAAA,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;EAClD,SAAA,CACF,CAAC;OACH;EAED;;;;;;;EAOG;MACI,OAAO,GAAA;EACZ,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;cACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EACrC,SAAA;UAED,MAAM,OAAO,GAAG,8CAA8C,CAAC;EAC/D,QAAA,MAAM,WAAW,GAAG;cAClB,GAAG,EAAE,MAAW;EACd,gBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;eAC1B;cACD,GAAG,EAAE,MAAW;EACd,gBAAA,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;eAC1B;EAED,YAAA,YAAY,EAAE,KAAK;WACpB,CAAC;UACF,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;cACrD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;EAC/C,SAAA;OACF;EAED;;;;;;EAMG;EACK,IAAA,QAAQ,CACd,KAAS,EACT,MAAuC,EACvC,QAAoB,EAAA;EAEpB,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;cAC3C,OAAO;EACR,SAAA;EAED,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC;UACzB,MAAM,QAAQ,GAAS,EAAE,CAAC;UAC1B,MAAM,UAAU,GAAS,EAAE,CAAC;UAC5B,MAAM,UAAU,GAAS,EAAE,CAAC;UAC5B,MAAM,QAAQ,GAA6B,EAAE,CAAC;UAC9C,MAAM,YAAY,GAA6B,EAAE,CAAC;UAClD,MAAM,YAAY,GAA6B,EAAE,CAAC;EAElD,QAAA,QAAQ,KAAK;EACX,YAAA,KAAK,KAAK;;EAER,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAC1B,oBAAA,IAAI,IAAI,EAAE;EACR,wBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAClB,wBAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnB,qBAAA;EACF,iBAAA;kBAED,MAAM;EAER,YAAA,KAAK,QAAQ;;;EAGX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAE1B,oBAAA,IAAI,IAAI,EAAE;0BACR,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;EACrB,4BAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;8BACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,IAAI,CAAC,CAAC,CAAC,CACrD,CAAC;8BACF,QAAQ,CAAC,IAAI,CACV,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;EACH,yBAAA;EAAM,6BAAA;EACL,4BAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAClB,4BAAA,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnB,yBAAA;EACF,qBAAA;EAAM,yBAAA;0BACL,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;EACrB,4BAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EACrB,4BAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;8BACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;EACH,yBAEA;EACF,qBAAA;EACF,iBAAA;kBAED,MAAM;EAER,YAAA,KAAK,QAAQ;;EAEX,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;EAC9C,oBAAA,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClB,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;EACrB,wBAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EACrB,wBAAA,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;0BACpB,YAAY,CAAC,IAAI,CACd,MAA2C,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAC;EACH,qBAAA;EACF,iBAAA;kBAED,MAAM;EACT,SAAA;UAED,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;UAEnD,IAAI,QAAQ,CAAC,MAAM,EAAE;EACnB,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;EACrD,SAAA;UACD,IAAI,UAAU,CAAC,MAAM,EAAE;cACrB,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,EAC5D,QAAQ,CACT,CAAC;EACH,SAAA;UACD,IAAI,UAAU,CAAC,MAAM,EAAE;EACrB,YAAA,IAAI,CAAC,QAAQ,CACX,QAAQ,EACR,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,EAC5C,QAAQ,CACT,CAAC;EACH,SAAA;OACF;EACF;;ECtjBD;;;;;;EAMG;EACa,SAAA,aAAa,CAG3B,MAAc,EAAE,CAAM,EAAA;EACtB,IAAA,QACE,OAAO,CAAC,KAAK,QAAQ;EACrB,QAAA,CAAC,KAAK,IAAI;UACV,MAAM,KAAK,CAAC,CAAC,MAAM;EACnB,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,KAAK,KAAK,UAAU;EAC7B,QAAA,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU;EAChC,QAAA,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU;EAC/B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;EAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;EAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;EAC5B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU;EAC1B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;EAC9B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;EAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;EAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;EAC9B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU,EAClC;EACJ;;ECjCA;;;;;;EAMG;EACa,SAAA,cAAc,CAG5B,MAAc,EAAE,CAAM,EAAA;EACtB,IAAA,QACE,OAAO,CAAC,KAAK,QAAQ;EACrB,QAAA,CAAC,KAAK,IAAI;UACV,MAAM,KAAK,CAAC,CAAC,MAAM;EACnB,QAAA,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU;EAC/B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU;EAClC,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;EAC9B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ;EAC5B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,GAAG,KAAK,UAAU;EAC3B,QAAA,OAAO,CAAC,CAAC,EAAE,KAAK,UAAU;EAC1B,QAAA,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;UAC9B,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,EACrC;EACJ;;;;;;;;;;;;;;;;;;"}