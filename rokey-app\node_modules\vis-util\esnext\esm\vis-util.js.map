{"version": 3, "file": "vis-util.js", "sources": ["../../src/deep-object-assign.ts", "../../src/random/alea.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/popup.js", "../../src/shared/validator.js", "../../src/shared/index.ts"], "sourcesContent": ["/**\n * Use this symbol to delete properies in deepObjectAssign.\n */\nexport const DELETE = Symbol(\"DELETE\");\n\n/**\n * Turns `undefined` into `undefined | typeof DELETE` and makes everything\n * partial. Intended to be used with `deepObjectAssign`.\n */\nexport type Assignable<T> = T extends undefined\n  ?\n      | (T extends Function\n          ? T\n          : T extends object\n          ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n          : T)\n      | typeof DELETE\n  : T extends Function\n  ? T | undefined\n  : T extends object\n  ? { [Key in keyof T]?: Assignable<T[Key]> | undefined }\n  : T | undefined;\n\n/**\n * Pure version of deepObjectAssign, it doesn't modify any of it's arguments.\n *\n * @param base - The base object that fullfils the whole interface T.\n * @param updates - Updates that may change or delete props.\n * @returns A brand new instance with all the supplied objects deeply merged.\n */\nexport function pureDeepObjectAssign<T>(\n  base: T,\n  ...updates: Assignable<T>[]\n): T {\n  return deepObjectAssign({} as any, base, ...updates);\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param target - The object that will be augmented using the sources.\n * @param sources - Objects to be deeply merged into the target.\n * @returns The target (same instance).\n */\nexport function deepObjectAssign<T>(target: T, ...sources: Assignable<T>[]): T;\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nexport function deepObjectAssign(...values: readonly any[]): any {\n  const merged = deepObjectAssignNonentry(...values);\n  stripDelete(merged);\n  return merged;\n}\n\n/**\n * Deep version of object assign with additional deleting by the DELETE symbol.\n *\n * @remarks\n * This doesn't strip the DELETE symbols so they may end up in the final object.\n * @param values - Objects to be deeply merged.\n * @returns The first object from values.\n */\nfunction deepObjectAssignNonentry(...values: readonly any[]): any {\n  if (values.length < 2) {\n    return values[0];\n  } else if (values.length > 2) {\n    return deepObjectAssignNonentry(\n      deepObjectAssign(values[0], values[1]),\n      ...values.slice(2)\n    );\n  }\n\n  const a = values[0];\n  const b = values[1];\n\n  if (a instanceof Date && b instanceof Date) {\n    a.setTime(b.getTime());\n    return a;\n  }\n\n  for (const prop of Reflect.ownKeys(b)) {\n    if (!Object.prototype.propertyIsEnumerable.call(b, prop)) {\n      // Ignore nonenumerable props, Object.assign() would do the same.\n    } else if (b[prop] === DELETE) {\n      delete a[prop];\n    } else if (\n      a[prop] !== null &&\n      b[prop] !== null &&\n      typeof a[prop] === \"object\" &&\n      typeof b[prop] === \"object\" &&\n      !Array.isArray(a[prop]) &&\n      !Array.isArray(b[prop])\n    ) {\n      a[prop] = deepObjectAssignNonentry(a[prop], b[prop]);\n    } else {\n      a[prop] = clone(b[prop]);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep clone given object or array. In case of primitive simply return.\n *\n * @param a - Anything.\n * @returns Deep cloned object/array or unchanged a.\n */\nfunction clone(a: any): any {\n  if (Array.isArray(a)) {\n    return a.map((value: any): any => clone(value));\n  } else if (typeof a === \"object\" && a !== null) {\n    if (a instanceof Date) {\n      return new Date(a.getTime());\n    }\n    return deepObjectAssignNonentry({}, a);\n  } else {\n    return a;\n  }\n}\n\n/**\n * Strip DELETE from given object.\n *\n * @param a - Object which may contain DELETE but won't after this is executed.\n */\nfunction stripDelete(a: any): void {\n  for (const prop of Object.keys(a)) {\n    if (a[prop] === DELETE) {\n      delete a[prop];\n    } else if (typeof a[prop] === \"object\" && a[prop] !== null) {\n      stripDelete(a[prop]);\n    }\n  }\n}\n", "/**\n * Seedable, fast and reasonably good (not crypto but more than okay for our\n * needs) random number generator.\n *\n * @remarks\n * Adapted from {@link https://web.archive.org/web/20110429100736/http://baagoe.com:80/en/RandomMusings/javascript}.\n * Original algorithm created by <PERSON> \\<baagoe\\@baagoe.com\\> in 2010.\n */\n\n/**\n * Random number generator.\n */\nexport interface RNG {\n  /** Returns \\<0, 1). Faster than [[fract53]]. */\n  (): number;\n  /** Returns \\<0, 1). Provides more precise data. */\n  fract53(): number;\n  /** Returns \\<0, 2^32). */\n  uint32(): number;\n\n  /** The algorithm gehind this instance. */\n  algorithm: string;\n  /** The seed used to seed this instance. */\n  seed: Mashable[];\n  /** The version of this instance. */\n  version: string;\n}\n\n/**\n * Create a seeded pseudo random generator based on <PERSON><PERSON> by <PERSON>.\n *\n * @param seed - All supplied arguments will be used as a seed. In case nothing\n * is supplied the current time will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nexport function Alea(...seed: Mashable[]): RNG {\n  return AleaImplementation(seed.length ? seed : [Date.now()]);\n}\n\n/**\n * An implementation of [[Alea]] without user input validation.\n *\n * @param seed - The data that will be used to seed the generator.\n * @returns A ready to use seeded generator.\n */\nfunction AleaImplementation(seed: Mashable[]): RNG {\n  let [s0, s1, s2] = mashSeed(seed);\n  let c = 1;\n\n  const random: RNG = (): number => {\n    const t = 2091639 * s0 + c * 2.3283064365386963e-10; // 2^-32\n    s0 = s1;\n    s1 = s2;\n    return (s2 = t - (c = t | 0));\n  };\n\n  random.uint32 = (): number => random() * 0x100000000; // 2^32\n\n  random.fract53 = (): number =>\n    random() + ((random() * 0x200000) | 0) * 1.1102230246251565e-16; // 2^-53\n\n  random.algorithm = \"Alea\";\n  random.seed = seed;\n  random.version = \"0.9\";\n\n  return random;\n}\n\n/**\n * Turn arbitrary data into values [[AleaImplementation]] can use to generate\n * random numbers.\n *\n * @param seed - Arbitrary data that will be used as the seed.\n * @returns Three numbers to use as initial values for [[AleaImplementation]].\n */\nfunction mashSeed(...seed: Mashable[]): [number, number, number] {\n  const mash = Mash();\n\n  let s0 = mash(\" \");\n  let s1 = mash(\" \");\n  let s2 = mash(\" \");\n\n  for (let i = 0; i < seed.length; i++) {\n    s0 -= mash(seed[i]);\n    if (s0 < 0) {\n      s0 += 1;\n    }\n    s1 -= mash(seed[i]);\n    if (s1 < 0) {\n      s1 += 1;\n    }\n    s2 -= mash(seed[i]);\n    if (s2 < 0) {\n      s2 += 1;\n    }\n  }\n\n  return [s0, s1, s2];\n}\n\n/**\n * Values of these types can be used as a seed.\n */\nexport type Mashable = number | string | boolean | object | bigint;\n\n/**\n * Create a new mash function.\n *\n * @returns A nonpure function that takes arbitrary [[Mashable]] data and turns\n * them into numbers.\n */\nfunction Mash(): (data: Mashable) => number {\n  let n = 0xefc8249d;\n\n  return function (data): number {\n    const string = data.toString();\n    for (let i = 0; i < string.length; i++) {\n      n += string.charCodeAt(i);\n      let h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n}\n", "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n *\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n *\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n *\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n *\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", "// utility functions\n\n// parse ASP.Net Date pattern,\n// for example '/Date(1198908717056)/' or '/Date(1198908717056-0700)/'\n// code from http://momentjs.com/\nconst ASPDateRegex = /^\\/?Date\\((-?\\d+)/i;\n\n// Color REs\nconst fullHexRE = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i;\nconst shortHexRE = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\nconst rgbRE =\n  /^rgb\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *\\)$/i;\nconst rgbaRE =\n  /^rgba\\( *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *(1?\\d{1,2}|2[0-4]\\d|25[0-5]) *, *([01]|0?\\.\\d+) *\\)$/i;\n\n/**\n * Hue, Saturation, Value.\n */\nexport interface HSV {\n  /**\n   * Hue \\<0, 1\\>.\n   */\n  h: number;\n  /**\n   * Saturation \\<0, 1\\>.\n   */\n  s: number;\n  /**\n   * Value \\<0, 1\\>.\n   */\n  v: number;\n}\n\n/**\n * Red, Green, Blue.\n */\nexport interface RGB {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n}\n\n/**\n * Red, Green, Blue, Alpha.\n */\nexport interface RGBA {\n  /**\n   * Red \\<0, 255\\> integer.\n   */\n  r: number;\n  /**\n   * Green \\<0, 255\\> integer.\n   */\n  g: number;\n  /**\n   * Blue \\<0, 255\\> integer.\n   */\n  b: number;\n  /**\n   * Alpha \\<0, 1\\>.\n   */\n  a: number;\n}\n\n/**\n * Test whether given object is a number.\n *\n * @param value - Input value of unknown type.\n * @returns True if number, false otherwise.\n */\nexport function isNumber(value: unknown): value is number {\n  return value instanceof Number || typeof value === \"number\";\n}\n\n/**\n * Remove everything in the DOM object.\n *\n * @param DOMobject - Node whose child nodes will be recursively deleted.\n */\nexport function recursiveDOMDelete(DOMobject: Node | null | undefined): void {\n  if (DOMobject) {\n    while (DOMobject.hasChildNodes() === true) {\n      const child = DOMobject.firstChild;\n      if (child) {\n        recursiveDOMDelete(child);\n        DOMobject.removeChild(child);\n      }\n    }\n  }\n}\n\n/**\n * Test whether given object is a string.\n *\n * @param value - Input value of unknown type.\n * @returns True if string, false otherwise.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === \"string\";\n}\n\n/**\n * Test whether given object is a object (not primitive or null).\n *\n * @param value - Input value of unknown type.\n * @returns True if not null object, false otherwise.\n */\nexport function isObject(value: unknown): value is object {\n  return typeof value === \"object\" && value !== null;\n}\n\n/**\n * Test whether given object is a Date, or a String containing a Date.\n *\n * @param value - Input value of unknown type.\n * @returns True if Date instance or string date representation, false otherwise.\n */\nexport function isDate(value: unknown): value is Date | string {\n  if (value instanceof Date) {\n    return true;\n  } else if (isString(value)) {\n    // test whether this string contains a date\n    const match = ASPDateRegex.exec(value);\n    if (match) {\n      return true;\n    } else if (!isNaN(Date.parse(value))) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Copy property from b to a if property present in a.\n * If property in b explicitly set to null, delete it if `allowDeletion` set.\n *\n * Internal helper routine, should not be exported. Not added to `exports` for that reason.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param prop - Name of property to copy from b to a.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n */\nfunction copyOrDelete(\n  a: any,\n  b: any,\n  prop: string,\n  allowDeletion: boolean\n): void {\n  let doDeletion = false;\n  if (allowDeletion === true) {\n    doDeletion = b[prop] === null && a[prop] !== undefined;\n  }\n\n  if (doDeletion) {\n    delete a[prop];\n  } else {\n    a[prop] = b[prop]; // Remember, this is a reference copy!\n  }\n}\n\n/**\n * Fill an object with a possibly partially defined other object.\n *\n * Only copies values for the properties already present in a.\n * That means an object is not created on a property if only the b object has it.\n *\n * @param a - The object that will have it's properties updated.\n * @param b - The object with property updates.\n * @param allowDeletion - If true, delete properties in a that are explicitly set to null in b.\n */\nexport function fillIfDefined<T extends object>(\n  a: T,\n  b: Partial<T>,\n  allowDeletion = false\n): void {\n  // NOTE: iteration of properties of a\n  // NOTE: prototype properties iterated over as well\n  for (const prop in a) {\n    if (b[prop] !== undefined) {\n      if (b[prop] === null || typeof b[prop] !== \"object\") {\n        // Note: typeof null === 'object'\n        copyOrDelete(a, b, prop, allowDeletion);\n      } else {\n        const aProp = a[prop];\n        const bProp = b[prop];\n        if (isObject(aProp) && isObject(bProp)) {\n          fillIfDefined(aProp, bProp, allowDeletion);\n        }\n      }\n    }\n  }\n}\n\n/**\n * Copy the values of all of the enumerable own properties from one or more source objects to a\n * target object. Returns the target object.\n *\n * @param target - The target object to copy to.\n * @param source - The source object from which to copy properties.\n * @returns The target object.\n */\nexport const extend = Object.assign;\n\n/**\n * Extend object a with selected properties of object b or a series of objects.\n *\n * @remarks\n * Only properties with defined values are copied.\n * @param props - Properties to be copied to a.\n * @param a - The target.\n * @param others - The sources.\n * @returns Argument a.\n */\nexport function selectiveExtend(\n  props: string[],\n  a: any,\n  ...others: any[]\n): any {\n  if (!Array.isArray(props)) {\n    throw new Error(\"Array with property names expected as first argument\");\n  }\n\n  for (const other of others) {\n    for (let p = 0; p < props.length; p++) {\n      const prop = props[p];\n      if (other && Object.prototype.hasOwnProperty.call(other, prop)) {\n        a[prop] = other[prop];\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object a with selected properties of object b.\n * Only properties with defined values are copied.\n *\n * @remarks\n * Previous version of this routine implied that multiple source objects could\n * be used; however, the implementation was **wrong**. Since multiple (\\>1)\n * sources weren't used anywhere in the `vis.js` code, this has been removed\n * @param props - Names of first-level properties to copy over.\n * @param a - Target object.\n * @param b - Source object.\n * @param allowDeletion - If true, delete property in a if explicitly set to null in b.\n * @returns Argument a.\n */\nexport function selectiveDeepExtend(\n  props: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (let p = 0; p < props.length; p++) {\n    const prop = props[p];\n    if (Object.prototype.hasOwnProperty.call(b, prop)) {\n      if (b[prop] && b[prop].constructor === Object) {\n        if (a[prop] === undefined) {\n          a[prop] = {};\n        }\n        if (a[prop].constructor === Object) {\n          deepExtend(a[prop], b[prop], false, allowDeletion);\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        throw new TypeError(\"Arrays are not supported by deepExtend\");\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Extend object `a` with properties of object `b`, ignoring properties which\n * are explicitly specified to be excluded.\n *\n * @remarks\n * The properties of `b` are considered for copying. Properties which are\n * themselves objects are are also extended. Only properties with defined\n * values are copied.\n * @param propsToExclude - Names of properties which should *not* be copied.\n * @param a - Object to extend.\n * @param b - Object to take properties from for extension.\n * @param allowDeletion - If true, delete properties in a that are explicitly\n * set to null in b.\n * @returns Argument a.\n */\nexport function selectiveNotDeepExtend(\n  propsToExclude: string[],\n  a: any,\n  b: any,\n  allowDeletion = false\n): any {\n  // TODO: add support for Arrays to deepExtend\n  // NOTE: array properties have an else-below; apparently, there is a problem here.\n  if (Array.isArray(b)) {\n    throw new TypeError(\"Arrays are not supported by deepExtend\");\n  }\n\n  for (const prop in b) {\n    if (!Object.prototype.hasOwnProperty.call(b, prop)) {\n      continue;\n    } // Handle local properties only\n    if (propsToExclude.includes(prop)) {\n      continue;\n    } // In exclusion list, skip\n\n    if (b[prop] && b[prop].constructor === Object) {\n      if (a[prop] === undefined) {\n        a[prop] = {};\n      }\n      if (a[prop].constructor === Object) {\n        deepExtend(a[prop], b[prop]); // NOTE: allowDeletion not propagated!\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    } else if (Array.isArray(b[prop])) {\n      a[prop] = [];\n      for (let i = 0; i < b[prop].length; i++) {\n        a[prop].push(b[prop][i]);\n      }\n    } else {\n      copyOrDelete(a, b, prop, allowDeletion);\n    }\n  }\n\n  return a;\n}\n\n/**\n * Deep extend an object a with the properties of object b.\n *\n * @param a - Target object.\n * @param b - Source object.\n * @param protoExtend - If true, the prototype values will also be extended.\n * (That is the options objects that inherit from others will also get the\n * inherited options).\n * @param allowDeletion - If true, the values of fields that are null will be deleted.\n * @returns Argument a.\n */\nexport function deepExtend(\n  a: any,\n  b: any,\n  protoExtend = false,\n  allowDeletion = false\n): any {\n  for (const prop in b) {\n    if (Object.prototype.hasOwnProperty.call(b, prop) || protoExtend === true) {\n      if (\n        typeof b[prop] === \"object\" &&\n        b[prop] !== null &&\n        Object.getPrototypeOf(b[prop]) === Object.prototype\n      ) {\n        if (a[prop] === undefined) {\n          a[prop] = deepExtend({}, b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else if (\n          typeof a[prop] === \"object\" &&\n          a[prop] !== null &&\n          Object.getPrototypeOf(a[prop]) === Object.prototype\n        ) {\n          deepExtend(a[prop], b[prop], protoExtend); // NOTE: allowDeletion not propagated!\n        } else {\n          copyOrDelete(a, b, prop, allowDeletion);\n        }\n      } else if (Array.isArray(b[prop])) {\n        a[prop] = b[prop].slice();\n      } else {\n        copyOrDelete(a, b, prop, allowDeletion);\n      }\n    }\n  }\n  return a;\n}\n\n/**\n * Test whether all elements in two arrays are equal.\n *\n * @param a - First array.\n * @param b - Second array.\n * @returns True if both arrays have the same length and same elements (1 = '1').\n */\nexport function equalArray(a: unknown[], b: unknown[]): boolean {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0, len = a.length; i < len; i++) {\n    if (a[i] != b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Get the type of an object, for example exports.getType([]) returns 'Array'.\n *\n * @param object - Input value of unknown type.\n * @returns Detected type.\n */\nexport function getType(object: unknown): string {\n  const type = typeof object;\n\n  if (type === \"object\") {\n    if (object === null) {\n      return \"null\";\n    }\n    if (object instanceof Boolean) {\n      return \"Boolean\";\n    }\n    if (object instanceof Number) {\n      return \"Number\";\n    }\n    if (object instanceof String) {\n      return \"String\";\n    }\n    if (Array.isArray(object)) {\n      return \"Array\";\n    }\n    if (object instanceof Date) {\n      return \"Date\";\n    }\n\n    return \"Object\";\n  }\n  if (type === \"number\") {\n    return \"Number\";\n  }\n  if (type === \"boolean\") {\n    return \"Boolean\";\n  }\n  if (type === \"string\") {\n    return \"String\";\n  }\n  if (type === undefined) {\n    return \"undefined\";\n  }\n\n  return type;\n}\n\nexport function copyAndExtendArray<T>(arr: ReadonlyArray<T>, newValue: T): T[];\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[];\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - First part.\n * @param newValue - The value to be aadded into the array.\n * @returns A new array with all items from arr and newValue (which is last).\n */\nexport function copyAndExtendArray<A, V>(\n  arr: ReadonlyArray<A>,\n  newValue: V\n): (A | V)[] {\n  return [...arr, newValue];\n}\n\n/**\n * Used to extend an array and copy it. This is used to propagate paths recursively.\n *\n * @param arr - The array to be copied.\n * @returns Shallow copy of arr.\n */\nexport function copyArray<T>(arr: ReadonlyArray<T>): T[] {\n  return arr.slice();\n}\n\n/**\n * Retrieve the absolute left value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute left position of this element in the browser page.\n */\nexport function getAbsoluteLeft(elem: Element): number {\n  return elem.getBoundingClientRect().left;\n}\n\n/**\n * Retrieve the absolute right value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute right position of this element in the browser page.\n */\nexport function getAbsoluteRight(elem: Element): number {\n  return elem.getBoundingClientRect().right;\n}\n\n/**\n * Retrieve the absolute top value of a DOM element.\n *\n * @param elem - A dom element, for example a div.\n * @returns The absolute top position of this element in the browser page.\n */\nexport function getAbsoluteTop(elem: Element): number {\n  return elem.getBoundingClientRect().top;\n}\n\n/**\n * Add a className to the given elements style.\n *\n * @param elem - The element to which the classes will be added.\n * @param classNames - Space separated list of classes.\n */\nexport function addClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const newClasses = classNames.split(\" \");\n  classes = classes.concat(\n    newClasses.filter(function (className): boolean {\n      return !classes.includes(className);\n    })\n  );\n  elem.className = classes.join(\" \");\n}\n\n/**\n * Remove a className from the given elements style.\n *\n * @param elem - The element from which the classes will be removed.\n * @param classNames - Space separated list of classes.\n */\nexport function removeClassName(elem: Element, classNames: string): void {\n  let classes = elem.className.split(\" \");\n  const oldClasses = classNames.split(\" \");\n  classes = classes.filter(function (className): boolean {\n    return !oldClasses.includes(className);\n  });\n  elem.className = classes.join(\" \");\n}\n\nexport function forEach<V>(\n  array: undefined | null | V[],\n  callback: (value: V, index: number, object: V[]) => void\n): void;\nexport function forEach<O extends object>(\n  object: undefined | null | O,\n  callback: <Key extends keyof O>(value: O[Key], key: Key, object: O) => void\n): void;\n/**\n * For each method for both arrays and objects.\n * In case of an array, the built-in Array.forEach() is applied (**No, it's not!**).\n * In case of an Object, the method loops over all properties of the object.\n *\n * @param object - An Object or Array to be iterated over.\n * @param callback - Array.forEach-like callback.\n */\nexport function forEach(object: any, callback: any): void {\n  if (Array.isArray(object)) {\n    // array\n    const len = object.length;\n    for (let i = 0; i < len; i++) {\n      callback(object[i], i, object);\n    }\n  } else {\n    // object\n    for (const key in object) {\n      if (Object.prototype.hasOwnProperty.call(object, key)) {\n        callback(object[key], key, object);\n      }\n    }\n  }\n}\n\n/**\n * Convert an object into an array: all objects properties are put into the array. The resulting array is unordered.\n *\n * @param o - Object that contains the properties and methods.\n * @returns An array of unordered values.\n */\nexport const toArray = Object.values;\n\n/**\n * Update a property in an object.\n *\n * @param object - The object whose property will be updated.\n * @param key - Name of the property to be updated.\n * @param value - The new value to be assigned.\n * @returns Whether the value was updated (true) or already strictly the same in the original object (false).\n */\nexport function updateProperty<K extends string, V>(\n  object: Record<K, V>,\n  key: K,\n  value: V\n): boolean {\n  if (object[key] !== value) {\n    object[key] = value;\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Throttle the given function to be only executed once per animation frame.\n *\n * @param fn - The original function.\n * @returns The throttled function.\n */\nexport function throttle(fn: () => void): () => void {\n  let scheduled = false;\n\n  return (): void => {\n    if (!scheduled) {\n      scheduled = true;\n      requestAnimationFrame((): void => {\n        scheduled = false;\n        fn();\n      });\n    }\n  };\n}\n\n/**\n * Cancels the event's default action if it is cancelable, without stopping further propagation of the event.\n *\n * @param event - The event whose default action should be prevented.\n */\nexport function preventDefault(event: Event | undefined): void {\n  if (!event) {\n    event = window.event;\n  }\n\n  if (!event) {\n    // No event, no work.\n  } else if (event.preventDefault) {\n    event.preventDefault(); // non-IE browsers\n  } else {\n    // @TODO: IE types? Does anyone care?\n    (event as any).returnValue = false; // IE browsers\n  }\n}\n\n/**\n * Get HTML element which is the target of the event.\n *\n * @param event - The event.\n * @returns The element or null if not obtainable.\n */\nexport function getTarget(\n  event: Event | undefined = window.event\n): Element | null {\n  // code from http://www.quirksmode.org/js/events_properties.html\n  // @TODO: EventTarget can be almost anything, is it okay to return only Elements?\n\n  let target: null | EventTarget = null;\n  if (!event) {\n    // No event, no target.\n  } else if (event.target) {\n    target = event.target;\n  } else if (event.srcElement) {\n    target = event.srcElement;\n  }\n\n  if (!(target instanceof Element)) {\n    return null;\n  }\n\n  if (target.nodeType != null && target.nodeType == 3) {\n    // defeat Safari bug\n    target = target.parentNode;\n    if (!(target instanceof Element)) {\n      return null;\n    }\n  }\n\n  return target;\n}\n\n/**\n * Check if given element contains given parent somewhere in the DOM tree.\n *\n * @param element - The element to be tested.\n * @param parent - The ancestor (not necessarily parent) of the element.\n * @returns True if parent is an ancestor of the element, false otherwise.\n */\nexport function hasParent(element: Element, parent: Element): boolean {\n  let elem: Node = element;\n\n  while (elem) {\n    if (elem === parent) {\n      return true;\n    } else if (elem.parentNode) {\n      elem = elem.parentNode;\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n\nexport const option = {\n  /**\n   * Convert a value into a boolean.\n   *\n   * @param value - Value to be converted intoboolean, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding boolean value, if none then the default value, if none then null.\n   */\n  asBoolean(value: unknown, defaultValue?: boolean): boolean | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return value != false;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a number.\n   *\n   * @param value - Value to be converted intonumber, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** number value, if none then the default value, if none then null.\n   */\n  asNumber(value: unknown, defaultValue?: number): number | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return Number(value) || defaultValue || null;\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a string.\n   *\n   * @param value - Value to be converted intostring, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding **boxed** string value, if none then the default value, if none then null.\n   */\n  asString(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (value != null) {\n      return String(value);\n    }\n\n    return defaultValue || null;\n  },\n\n  /**\n   * Convert a value into a size.\n   *\n   * @param value - Value to be converted intosize, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns Corresponding string value (number + 'px'), if none then the default value, if none then null.\n   */\n  asSize(value: unknown, defaultValue?: string): string | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    if (isString(value)) {\n      return value;\n    } else if (isNumber(value)) {\n      return value + \"px\";\n    } else {\n      return defaultValue || null;\n    }\n  },\n\n  /**\n   * Convert a value into a DOM Element.\n   *\n   * @param value - Value to be converted into DOM Element, a function will be executed as `(() => unknown)`.\n   * @param defaultValue - If the value or the return value of the function == null then this will be returned.\n   * @returns The DOM Element, if none then the default value, if none then null.\n   */\n  asElement<T extends Node>(\n    value: T | (() => T | undefined) | undefined,\n    defaultValue: T\n  ): T | null {\n    if (typeof value == \"function\") {\n      value = value();\n    }\n\n    return value || defaultValue || null;\n  },\n};\n\n/**\n * Convert hex color string into RGB color object.\n *\n * @remarks\n * {@link http://stackoverflow.com/questions/5623838/rgb-to-hex-and-hex-to-rgb}\n * @param hex - Hex color string (3 or 6 digits, with or without #).\n * @returns RGB color object.\n */\nexport function hexToRGB(hex: string): RGB | null {\n  let result;\n  switch (hex.length) {\n    case 3:\n    case 4:\n      result = shortHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1] + result[1], 16),\n            g: parseInt(result[2] + result[2], 16),\n            b: parseInt(result[3] + result[3], 16),\n          }\n        : null;\n    case 6:\n    case 7:\n      result = fullHexRE.exec(hex);\n      return result\n        ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16),\n          }\n        : null;\n    default:\n      return null;\n  }\n}\n\n/**\n * This function takes string color in hex or RGB format and adds the opacity, RGBA is passed through unchanged.\n *\n * @param color - The color string (hex, RGB, RGBA).\n * @param opacity - The new opacity.\n * @returns RGBA string, for example 'rgba(255, 0, 127, 0.3)'.\n */\nexport function overrideOpacity(color: string, opacity: number): string {\n  if (color.includes(\"rgba\")) {\n    return color;\n  } else if (color.includes(\"rgb\")) {\n    const rgb = color\n      .substr(color.indexOf(\"(\") + 1)\n      .replace(\")\", \"\")\n      .split(\",\");\n    return \"rgba(\" + rgb[0] + \",\" + rgb[1] + \",\" + rgb[2] + \",\" + opacity + \")\";\n  } else {\n    const rgb = hexToRGB(color);\n    if (rgb == null) {\n      return color;\n    } else {\n      return \"rgba(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \",\" + opacity + \")\";\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into hex color string.\n *\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns Hex color string (for example: '#0acdc0').\n */\nexport function RGBToHex(red: number, green: number, blue: number): string {\n  return (\n    \"#\" + ((1 << 24) + (red << 16) + (green << 8) + blue).toString(16).slice(1)\n  );\n}\n\nexport interface ColorObject {\n  background?: string;\n  border?: string;\n  hover?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n  highlight?:\n    | string\n    | {\n        border?: string;\n        background?: string;\n      };\n}\nexport interface FullColorObject {\n  background: string;\n  border: string;\n  hover: {\n    border: string;\n    background: string;\n  };\n  highlight: {\n    border: string;\n    background: string;\n  };\n}\n\nexport function parseColor(inputColor: string): FullColorObject;\nexport function parseColor(inputColor: FullColorObject): FullColorObject;\nexport function parseColor(inputColor: ColorObject): ColorObject;\nexport function parseColor(\n  inputColor: ColorObject,\n  defaultColor: FullColorObject\n): FullColorObject;\n/**\n * Parse a color property into an object with border, background, and highlight colors.\n *\n * @param inputColor - Shorthand color string or input color object.\n * @param defaultColor - Full color object to fill in missing values in inputColor.\n * @returns Color object.\n */\nexport function parseColor(\n  inputColor: ColorObject | string,\n  defaultColor?: FullColorObject\n): ColorObject | FullColorObject {\n  if (isString(inputColor)) {\n    let colorStr: string = inputColor;\n    if (isValidRGB(colorStr)) {\n      const rgb = colorStr\n        .substr(4)\n        .substr(0, colorStr.length - 5)\n        .split(\",\")\n        .map(function (value): number {\n          return parseInt(value);\n        });\n      colorStr = RGBToHex(rgb[0], rgb[1], rgb[2]);\n    }\n    if (isValidHex(colorStr) === true) {\n      const hsv = hexToHSV(colorStr);\n      const lighterColorHSV = {\n        h: hsv.h,\n        s: hsv.s * 0.8,\n        v: Math.min(1, hsv.v * 1.02),\n      };\n      const darkerColorHSV = {\n        h: hsv.h,\n        s: Math.min(1, hsv.s * 1.25),\n        v: hsv.v * 0.8,\n      };\n      const darkerColorHex = HSVToHex(\n        darkerColorHSV.h,\n        darkerColorHSV.s,\n        darkerColorHSV.v\n      );\n      const lighterColorHex = HSVToHex(\n        lighterColorHSV.h,\n        lighterColorHSV.s,\n        lighterColorHSV.v\n      );\n      return {\n        background: colorStr,\n        border: darkerColorHex,\n        highlight: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n        hover: {\n          background: lighterColorHex,\n          border: darkerColorHex,\n        },\n      };\n    } else {\n      return {\n        background: colorStr,\n        border: colorStr,\n        highlight: {\n          background: colorStr,\n          border: colorStr,\n        },\n        hover: {\n          background: colorStr,\n          border: colorStr,\n        },\n      };\n    }\n  } else {\n    if (defaultColor) {\n      const color: FullColorObject = {\n        background: inputColor.background || defaultColor.background,\n        border: inputColor.border || defaultColor.border,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                defaultColor.highlight.background,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                defaultColor.highlight.border,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) ||\n                defaultColor.hover.border,\n              background:\n                (inputColor.hover && inputColor.hover.background) ||\n                defaultColor.hover.background,\n            },\n      };\n      return color;\n    } else {\n      const color: ColorObject = {\n        background: inputColor.background || undefined,\n        border: inputColor.border || undefined,\n        highlight: isString(inputColor.highlight)\n          ? {\n              border: inputColor.highlight,\n              background: inputColor.highlight,\n            }\n          : {\n              background:\n                (inputColor.highlight && inputColor.highlight.background) ||\n                undefined,\n              border:\n                (inputColor.highlight && inputColor.highlight.border) ||\n                undefined,\n            },\n        hover: isString(inputColor.hover)\n          ? {\n              border: inputColor.hover,\n              background: inputColor.hover,\n            }\n          : {\n              border:\n                (inputColor.hover && inputColor.hover.border) || undefined,\n              background:\n                (inputColor.hover && inputColor.hover.background) || undefined,\n            },\n      };\n      return color;\n    }\n  }\n}\n\n/**\n * Convert RGB \\<0, 255\\> into HSV object.\n *\n * @remarks\n * {@link http://www.javascripter.net/faq/rgb2hsv.htm}\n * @param red - Red channel.\n * @param green - Green channel.\n * @param blue - Blue channel.\n * @returns HSV color object.\n */\nexport function RGBToHSV(red: number, green: number, blue: number): HSV {\n  red = red / 255;\n  green = green / 255;\n  blue = blue / 255;\n  const minRGB = Math.min(red, Math.min(green, blue));\n  const maxRGB = Math.max(red, Math.max(green, blue));\n\n  // Black-gray-white\n  if (minRGB === maxRGB) {\n    return { h: 0, s: 0, v: minRGB };\n  }\n\n  // Colors other than black-gray-white:\n  const d =\n    red === minRGB ? green - blue : blue === minRGB ? red - green : blue - red;\n  const h = red === minRGB ? 3 : blue === minRGB ? 1 : 5;\n  const hue = (60 * (h - d / (maxRGB - minRGB))) / 360;\n  const saturation = (maxRGB - minRGB) / maxRGB;\n  const value = maxRGB;\n  return { h: hue, s: saturation, v: value };\n}\n\ninterface CSSStyles {\n  [key: string]: string;\n}\n\n/**\n * Split a string with css styles into an object with key/values.\n *\n * @param cssText - CSS source code to split into key/value object.\n * @returns Key/value object corresponding to {@link cssText}.\n */\nfunction splitCSSText(cssText: string): CSSStyles {\n  const tmpEllement = document.createElement(\"div\");\n\n  const styles: CSSStyles = {};\n\n  tmpEllement.style.cssText = cssText;\n\n  for (let i = 0; i < tmpEllement.style.length; ++i) {\n    styles[tmpEllement.style[i]] = tmpEllement.style.getPropertyValue(\n      tmpEllement.style[i]\n    );\n  }\n\n  return styles;\n}\n\n/**\n * Append a string with css styles to an element.\n *\n * @param element - The element that will receive new styles.\n * @param cssText - The styles to be appended.\n */\nexport function addCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const [key, value] of Object.entries(cssStyle)) {\n    element.style.setProperty(key, value);\n  }\n}\n\n/**\n * Remove a string with css styles from an element.\n *\n * @param element - The element from which styles should be removed.\n * @param cssText - The styles to be removed.\n */\nexport function removeCssText(element: HTMLElement, cssText: string): void {\n  const cssStyle = splitCSSText(cssText);\n  for (const key of Object.keys(cssStyle)) {\n    element.style.removeProperty(key);\n  }\n}\n\n/**\n * Convert HSV \\<0, 1\\> into RGB color object.\n *\n * @remarks\n * {@link https://gist.github.com/mjijackson/5311256}\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns RGB color object.\n */\nexport function HSVToRGB(h: number, s: number, v: number): RGB {\n  let r: undefined | number;\n  let g: undefined | number;\n  let b: undefined | number;\n\n  const i = Math.floor(h * 6);\n  const f = h * 6 - i;\n  const p = v * (1 - s);\n  const q = v * (1 - f * s);\n  const t = v * (1 - (1 - f) * s);\n\n  switch (i % 6) {\n    case 0:\n      (r = v), (g = t), (b = p);\n      break;\n    case 1:\n      (r = q), (g = v), (b = p);\n      break;\n    case 2:\n      (r = p), (g = v), (b = t);\n      break;\n    case 3:\n      (r = p), (g = q), (b = v);\n      break;\n    case 4:\n      (r = t), (g = p), (b = v);\n      break;\n    case 5:\n      (r = v), (g = p), (b = q);\n      break;\n  }\n\n  return {\n    r: Math.floor((r as number) * 255),\n    g: Math.floor((g as number) * 255),\n    b: Math.floor((b as number) * 255),\n  };\n}\n\n/**\n * Convert HSV \\<0, 1\\> into hex color string.\n *\n * @param h - Hue.\n * @param s - Saturation.\n * @param v - Value.\n * @returns Hex color string.\n */\nexport function HSVToHex(h: number, s: number, v: number): string {\n  const rgb = HSVToRGB(h, s, v);\n  return RGBToHex(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Convert hex color string into HSV \\<0, 1\\>.\n *\n * @param hex - Hex color string.\n * @returns HSV color object.\n */\nexport function hexToHSV(hex: string): HSV {\n  const rgb = hexToRGB(hex);\n  if (!rgb) {\n    throw new TypeError(`'${hex}' is not a valid color.`);\n  }\n  return RGBToHSV(rgb.r, rgb.g, rgb.b);\n}\n\n/**\n * Validate hex color string.\n *\n * @param hex - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidHex(hex: string): boolean {\n  const isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(hex);\n  return isOk;\n}\n\n/**\n * Validate RGB color string.\n *\n * @param rgb - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGB(rgb: string): boolean {\n  return rgbRE.test(rgb);\n}\n\n/**\n * Validate RGBA color string.\n *\n * @param rgba - Unknown string that may contain a color.\n * @returns True if the string is valid, false otherwise.\n */\nexport function isValidRGBA(rgba: string): boolean {\n  return rgbaRE.test(rgba);\n}\n\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param fields - Names of properties to be bridged.\n * @param referenceObject - The original object.\n * @returns A new object inheriting from the referenceObject.\n */\nexport function selectiveBridgeObject<F extends string, V>(\n  fields: F[],\n  referenceObject: Record<F, V>\n): Record<F, V> | null {\n  if (referenceObject !== null && typeof referenceObject === \"object\") {\n    // !!! typeof null === 'object'\n    const objectTo = Object.create(referenceObject);\n    for (let i = 0; i < fields.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(referenceObject, fields[i])) {\n        if (typeof referenceObject[fields[i]] == \"object\") {\n          objectTo[fields[i]] = bridgeObject(referenceObject[fields[i]]);\n        }\n      }\n    }\n    return objectTo;\n  } else {\n    return null;\n  }\n}\n\nexport function bridgeObject<T extends object>(referenceObject: T): T;\nexport function bridgeObject<T>(referenceObject: T): null;\n/**\n * This recursively redirects the prototype of JSON objects to the referenceObject.\n * This is used for default options.\n *\n * @param referenceObject - The original object.\n * @returns The Element if the referenceObject is an Element, or a new object inheriting from the referenceObject.\n */\nexport function bridgeObject<T extends object | null>(\n  referenceObject: T\n): T | null {\n  if (referenceObject === null || typeof referenceObject !== \"object\") {\n    return null;\n  }\n\n  if (referenceObject instanceof Element) {\n    // Avoid bridging DOM objects\n    return referenceObject;\n  }\n\n  const objectTo = Object.create(referenceObject);\n  for (const i in referenceObject) {\n    if (Object.prototype.hasOwnProperty.call(referenceObject, i)) {\n      if (typeof (referenceObject as any)[i] == \"object\") {\n        objectTo[i] = bridgeObject((referenceObject as any)[i]);\n      }\n    }\n  }\n\n  return objectTo;\n}\n\n/**\n * This method provides a stable sort implementation, very fast for presorted data.\n *\n * @param a - The array to be sorted (in-place).\n * @param compare - An order comparator.\n * @returns The argument a.\n */\nexport function insertSort<T>(a: T[], compare: (a: T, b: T) => number): T[] {\n  for (let i = 0; i < a.length; i++) {\n    const k = a[i];\n    let j;\n    for (j = i; j > 0 && compare(k, a[j - 1]) < 0; j--) {\n      a[j] = a[j - 1];\n    }\n    a[j] = k;\n  }\n  return a;\n}\n\n/**\n * This is used to set the options of subobjects in the options object.\n *\n * A requirement of these subobjects is that they have an 'enabled' element\n * which is optional for the user but mandatory for the program.\n *\n * The added value here of the merge is that option 'enabled' is set as required.\n *\n * @param mergeTarget - Either this.options or the options used for the groups.\n * @param options - Options.\n * @param option - Option key in the options argument.\n * @param globalOptions - Global options, passed in to determine value of option 'enabled'.\n */\nexport function mergeOptions(\n  mergeTarget: any,\n  options: any,\n  option: string,\n  globalOptions: any = {}\n): void {\n  // Local helpers\n  const isPresent = function (obj: any): boolean {\n    return obj !== null && obj !== undefined;\n  };\n\n  const isObject = function (obj: unknown): boolean {\n    return obj !== null && typeof obj === \"object\";\n  };\n\n  // https://stackoverflow.com/a/34491287/1223531\n  const isEmpty = function (obj: object): obj is {} {\n    for (const x in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, x)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  // Guards\n  if (!isObject(mergeTarget)) {\n    throw new Error(\"Parameter mergeTarget must be an object\");\n  }\n\n  if (!isObject(options)) {\n    throw new Error(\"Parameter options must be an object\");\n  }\n\n  if (!isPresent(option)) {\n    throw new Error(\"Parameter option must have a value\");\n  }\n\n  if (!isObject(globalOptions)) {\n    throw new Error(\"Parameter globalOptions must be an object\");\n  }\n\n  //\n  // Actual merge routine, separated from main logic\n  // Only a single level of options is merged. Deeper levels are ref'd. This may actually be an issue.\n  //\n  const doMerge = function (target: any, options: any, option: string): void {\n    if (!isObject(target[option])) {\n      target[option] = {};\n    }\n\n    const src = options[option];\n    const dst = target[option];\n    for (const prop in src) {\n      if (Object.prototype.hasOwnProperty.call(src, prop)) {\n        dst[prop] = src[prop];\n      }\n    }\n  };\n\n  // Local initialization\n  const srcOption = options[option];\n  const globalPassed = isObject(globalOptions) && !isEmpty(globalOptions);\n  const globalOption = globalPassed ? globalOptions[option] : undefined;\n  const globalEnabled = globalOption ? globalOption.enabled : undefined;\n\n  /////////////////////////////////////////\n  // Main routine\n  /////////////////////////////////////////\n  if (srcOption === undefined) {\n    return; // Nothing to do\n  }\n\n  if (typeof srcOption === \"boolean\") {\n    if (!isObject(mergeTarget[option])) {\n      mergeTarget[option] = {};\n    }\n\n    mergeTarget[option].enabled = srcOption;\n    return;\n  }\n\n  if (srcOption === null && !isObject(mergeTarget[option])) {\n    // If possible, explicit copy from globals\n    if (isPresent(globalOption)) {\n      mergeTarget[option] = Object.create(globalOption);\n    } else {\n      return; // Nothing to do\n    }\n  }\n\n  if (!isObject(srcOption)) {\n    return;\n  }\n\n  //\n  // Ensure that 'enabled' is properly set. It is required internally\n  // Note that the value from options will always overwrite the existing value\n  //\n  let enabled = true; // default value\n\n  if (srcOption.enabled !== undefined) {\n    enabled = srcOption.enabled;\n  } else {\n    // Take from globals, if present\n    if (globalEnabled !== undefined) {\n      enabled = globalOption.enabled;\n    }\n  }\n\n  doMerge(mergeTarget, options, option);\n  mergeTarget[option].enabled = enabled;\n}\n\nexport function binarySearchCustom<\n  O extends object,\n  K1 extends keyof O,\n  K2 extends keyof O[K1]\n>(\n  orderedItems: O[],\n  comparator: (v: O[K1][K2]) => -1 | 0 | 1,\n  field: K1,\n  field2: K2\n): number;\nexport function binarySearchCustom<O extends object, K1 extends keyof O>(\n  orderedItems: O[],\n  comparator: (v: O[K1]) => -1 | 0 | 1,\n  field: K1\n): number;\n/**\n * This function does a binary search for a visible item in a sorted list. If we find a visible item, the code that uses\n * this function will then iterate in both directions over this sorted list to find all visible items.\n *\n * @param orderedItems - Items ordered by start.\n * @param comparator - -1 is lower, 0 is equal, 1 is higher.\n * @param field - Property name on an item (That is item[field]).\n * @param field2 - Second property name on an item (That is item[field][field2]).\n * @returns Index of the found item or -1 if nothing was found.\n */\nexport function binarySearchCustom(\n  orderedItems: any[],\n  comparator: (v: unknown) => -1 | 0 | 1,\n  field: string,\n  field2?: string\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n\n  while (low <= high && iteration < maxIterations) {\n    const middle = Math.floor((low + high) / 2);\n\n    const item = orderedItems[middle];\n    const value = field2 === undefined ? item[field] : item[field][field2];\n\n    const searchResult = comparator(value);\n    if (searchResult == 0) {\n      // jihaa, found a visible item!\n      return middle;\n    } else if (searchResult == -1) {\n      // it is too small --> increase low\n      low = middle + 1;\n    } else {\n      // it is too big --> decrease high\n      high = middle - 1;\n    }\n\n    iteration++;\n  }\n\n  return -1;\n}\n\n/**\n * This function does a binary search for a specific value in a sorted array.\n * If it does not exist but is in between of two values, we return either the\n * one before or the one after, depending on user input If it is found, we\n * return the index, else -1.\n *\n * @param orderedItems - Sorted array.\n * @param target - The searched value.\n * @param field - Name of the property in items to be searched.\n * @param sidePreference - If the target is between two values, should the index of the before or the after be returned?\n * @param comparator - An optional comparator, returning -1, 0, 1 for \\<, ===, \\>.\n * @returns The index of found value or -1 if nothing was found.\n */\nexport function binarySearchValue<T extends string>(\n  orderedItems: { [K in T]: number }[],\n  target: number,\n  field: T,\n  sidePreference: \"before\" | \"after\",\n  comparator?: (a: number, b: number) => -1 | 0 | 1\n): number {\n  const maxIterations = 10000;\n  let iteration = 0;\n  let low = 0;\n  let high = orderedItems.length - 1;\n  let prevValue;\n  let value;\n  let nextValue;\n  let middle;\n\n  comparator =\n    comparator != undefined\n      ? comparator\n      : function (a: number, b: number): -1 | 0 | 1 {\n          return a == b ? 0 : a < b ? -1 : 1;\n        };\n\n  while (low <= high && iteration < maxIterations) {\n    // get a new guess\n    middle = Math.floor(0.5 * (high + low));\n    prevValue = orderedItems[Math.max(0, middle - 1)][field];\n    value = orderedItems[middle][field];\n    nextValue =\n      orderedItems[Math.min(orderedItems.length - 1, middle + 1)][field];\n\n    if (comparator(value, target) == 0) {\n      // we found the target\n      return middle;\n    } else if (\n      comparator(prevValue, target) < 0 &&\n      comparator(value, target) > 0\n    ) {\n      // target is in between of the previous and the current\n      return sidePreference == \"before\" ? Math.max(0, middle - 1) : middle;\n    } else if (\n      comparator(value, target) < 0 &&\n      comparator(nextValue, target) > 0\n    ) {\n      // target is in between of the current and the next\n      return sidePreference == \"before\"\n        ? middle\n        : Math.min(orderedItems.length - 1, middle + 1);\n    } else {\n      // didnt find the target, we need to change our boundaries.\n      if (comparator(value, target) < 0) {\n        // it is too small --> increase low\n        low = middle + 1;\n      } else {\n        // it is too big --> decrease high\n        high = middle - 1;\n      }\n    }\n    iteration++;\n  }\n\n  // didnt find anything. Return -1.\n  return -1;\n}\n\n/*\n * Easing Functions.\n * Only considering the t value for the range [0, 1] => [0, 1].\n *\n * Inspiration: from http://gizma.com/easing/\n * https://gist.github.com/gre/1650294\n */\nexport const easingFunctions = {\n  /**\n   * Provides no easing and no acceleration.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  linear(t: number): number {\n    return t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuad(t: number): number {\n    return t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuad(t: number): number {\n    return t * (2 - t);\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuad(t: number): number {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInCubic(t: number): number {\n    return t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutCubic(t: number): number {\n    return --t * t * t + 1;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutCubic(t: number): number {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuart(t: number): number {\n    return t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuart(t: number): number {\n    return 1 - --t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuart(t: number): number {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n  },\n\n  /**\n   * Accelerate from zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInQuint(t: number): number {\n    return t * t * t * t * t;\n  },\n\n  /**\n   * Decelerate to zero velocity.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeOutQuint(t: number): number {\n    return 1 + --t * t * t * t * t;\n  },\n\n  /**\n   * Accelerate until halfway, then decelerate.\n   *\n   * @param t - Time.\n   * @returns Value at time t.\n   */\n  easeInOutQuint(t: number): number {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n  },\n};\n\n/**\n * Experimentaly compute the width of the scrollbar for this browser.\n *\n * @returns The width in pixels.\n */\nexport function getScrollBarWidth(): number {\n  const inner = document.createElement(\"p\");\n  inner.style.width = \"100%\";\n  inner.style.height = \"200px\";\n\n  const outer = document.createElement(\"div\");\n  outer.style.position = \"absolute\";\n  outer.style.top = \"0px\";\n  outer.style.left = \"0px\";\n  outer.style.visibility = \"hidden\";\n  outer.style.width = \"200px\";\n  outer.style.height = \"150px\";\n  outer.style.overflow = \"hidden\";\n  outer.appendChild(inner);\n\n  document.body.appendChild(outer);\n  const w1 = inner.offsetWidth;\n  outer.style.overflow = \"scroll\";\n  let w2 = inner.offsetWidth;\n  if (w1 == w2) {\n    w2 = outer.clientWidth;\n  }\n\n  document.body.removeChild(outer);\n\n  return w1 - w2;\n}\n\n// @TODO: This doesn't work properly.\n// It works only for single property objects,\n// otherwise it combines all of the types in a union.\n// export function topMost<K1 extends string, V1> (\n//   pile: Record<K1, undefined | V1>[],\n//   accessors: K1 | [K1]\n// ): undefined | V1\n// export function topMost<K1 extends string, K2 extends string, V1, V2> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2>>[],\n//   accessors: [K1, K2]\n// ): undefined | V1 | V2\n// export function topMost<K1 extends string, K2 extends string, K3 extends string, V1, V2, V3> (\n//   pile: Record<K1, undefined | V1 | Record<K2, undefined | V2 | Record<K3, undefined | V3>>>[],\n//   accessors: [K1, K2, K3]\n// ): undefined | V1 | V2 | V3\n/**\n * Get the top most property value from a pile of objects.\n *\n * @param pile - Array of objects, no required format.\n * @param accessors - Array of property names.\n * For example `object['foo']['bar']` → `['foo', 'bar']`.\n * @returns Value of the property with given accessors path from the first pile item where it's not undefined.\n */\nexport function topMost(pile: any, accessors: any): any {\n  let candidate;\n  if (!Array.isArray(accessors)) {\n    accessors = [accessors];\n  }\n  for (const member of pile) {\n    if (member) {\n      candidate = member[accessors[0]];\n      for (let i = 1; i < accessors.length; i++) {\n        if (candidate) {\n          candidate = candidate[accessors[i]];\n        }\n      }\n      if (typeof candidate !== \"undefined\") {\n        break;\n      }\n    }\n  }\n  return candidate;\n}\n", "import { Hammer } from \"./hammer\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio=1]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   *\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   *\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\"\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   *\n   * @param {string | object} color\n   * @param {boolean} [setInitial=true]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color)\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   *\n   * @param {boolean} [storePrevious=true]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   *\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   *\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   *\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   *\n   * @param {object} rgba\n   * @param {boolean} [setInitial=true]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   *\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   *\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   *\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   *\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   *\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   *\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   *\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util\";\n\nimport { ColorPicker } from \"./color-picker\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n *\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   *\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   *\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   *\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   *\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   *\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   *\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   *\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   *\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   *\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   *\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   *\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   *\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   *\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   *\n   * @param {object} obj\n   * @param {Array} [path=[]]    | where to look for the actual option\n   * @param {boolean} [checkOnly=false]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   *\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   *\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2))\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options\n      );\n    }\n    return options;\n  }\n}\n", "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   *\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   *\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   *\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". '\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"'\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\"\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   *\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive=false]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op)\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   * http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   * Copyright (c) 2011 Andrei Mackenzie\n   *\n   * Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   *\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1\n            )\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", "import { Activator as ActivatorJS } from \"./activator\";\nimport { ColorPicker as ColorPickerJS } from \"./color-picker\";\nimport { Configurator as ConfiguratorJS } from \"./configurator\";\nimport { Hammer as HammerJS } from \"./hammer\";\nimport { Popup as PopupJS } from \"./popup\";\nimport { VALIDATOR_PRINT_STYLE as VALIDATOR_PRINT_STYLE_JS } from \"./validator\";\nimport { Validator as ValidatorJS } from \"./validator\";\n\nexport const Activator: any = ActivatorJS;\nexport const ColorPicker: any = ColorPickerJS;\nexport const Configurator: any = ConfiguratorJS;\nexport const Hammer: HammerStatic = HammerJS;\nexport const Popup: any = PopupJS;\nexport const VALIDATOR_PRINT_STYLE: string = VALIDATOR_PRINT_STYLE_JS;\nexport const Validator: any = ValidatorJS;\n\nexport * from \"./configurator-types\";\n"], "names": ["Hammer", "Activator", "ColorPicker", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPickerJS", "ConfiguratorJS", "HammerJS", "PopupJS", "VALIDATOR_PRINT_STYLE_JS", "ValidatorJS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEG;MACU,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE;AAoBvC;;;;;;AAMG;SACa,oBAAoB,CAClC,IAAO,EACP,GAAG,OAAwB,EAAA;IAE3B,OAAO,gBAAgB,CAAC,EAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC;AACvD,CAAC;AAUD;;;;;AAKG;AACa,SAAA,gBAAgB,CAAC,GAAG,MAAsB,EAAA;AACxD,IAAA,MAAM,MAAM,GAAG,wBAAwB,CAAC,GAAG,MAAM,CAAC,CAAC;IACnD,WAAW,CAAC,MAAM,CAAC,CAAC;AACpB,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,wBAAwB,CAAC,GAAG,MAAsB,EAAA;AACzD,IAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,QAAA,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AAClB,KAAA;AAAM,SAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAO,wBAAwB,CAC7B,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EACtC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CACnB,CAAC;AACH,KAAA;AAED,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACpB,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAEpB,IAAA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE;QAC1C,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AACvB,QAAA,OAAO,CAAC,CAAC;AACV,KAAA;IAED,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACrC,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAEzD;AAAM,aAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;AAC7B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AAChB,SAAA;AAAM,aAAA,IACL,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,YAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;YAC3B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB;AACA,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD,SAAA;AAAM,aAAA;YACL,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1B,SAAA;AACF,KAAA;AAED,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;AAKG;AACH,SAAS,KAAK,CAAC,CAAM,EAAA;AACnB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,KAAU,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,KAAA;SAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;QAC9C,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;AAC9B,SAAA;AACD,QAAA,OAAO,wBAAwB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACxC,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,CAAC,CAAC;AACV,KAAA;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,WAAW,CAAC,CAAM,EAAA;IACzB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACjC,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;AACtB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AAChB,SAAA;AAAM,aAAA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;AAC1D,YAAA,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACtB,SAAA;AACF,KAAA;AACH;;ACzIA;;;;;;;AAOG;AAqBH;;;;;;AAMG;AACa,SAAA,IAAI,CAAC,GAAG,IAAgB,EAAA;AACtC,IAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED;;;;;AAKG;AACH,SAAS,kBAAkB,CAAC,IAAgB,EAAA;AAC1C,IAAA,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,MAAM,MAAM,GAAQ,MAAa;QAC/B,MAAM,CAAC,GAAG,OAAO,GAAG,EAAE,GAAG,CAAC,GAAG,sBAAsB,CAAC;QACpD,EAAE,GAAG,EAAE,CAAC;QACR,EAAE,GAAG,EAAE,CAAC;AACR,QAAA,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAChC,KAAC,CAAC;AAEF,IAAA,MAAM,CAAC,MAAM,GAAG,MAAc,MAAM,EAAE,GAAG,WAAW,CAAC;IAErD,MAAM,CAAC,OAAO,GAAG,MACf,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,sBAAsB,CAAC;AAElE,IAAA,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC;AAC1B,IAAA,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AACnB,IAAA,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;AAEvB,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;AAMG;AACH,SAAS,QAAQ,CAAC,GAAG,IAAgB,EAAA;AACnC,IAAA,MAAM,IAAI,GAAG,IAAI,EAAE,CAAC;AAEpB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAEnB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC,CAAC;AACT,SAAA;QACD,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC,CAAC;AACT,SAAA;QACD,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC,CAAC;AACT,SAAA;AACF,KAAA;AAED,IAAA,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB,CAAC;AAOD;;;;;AAKG;AACH,SAAS,IAAI,GAAA;IACX,IAAI,CAAC,GAAG,UAAU,CAAC;AAEnB,IAAA,OAAO,UAAU,IAAI,EAAA;AACnB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC/B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC1B,YAAA,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC;AAChC,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACZ,CAAC,IAAI,CAAC,CAAC;YACP,CAAC,IAAI,CAAC,CAAC;AACP,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACZ,CAAC,IAAI,CAAC,CAAC;AACP,YAAA,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;AACtB,SAAA;QACD,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;AAC5C,KAAC,CAAC;AACJ;;AC9HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,GAAG;AACtB,EAAE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC;AACxB;AACA,EAAE,OAAO;AACT,IAAI,EAAE,EAAE,IAAI;AACZ,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,IAAI,EAAE,IAAI;AACd;AACA,IAAI,GAAG,GAAG;AACV,MAAM,OAAO;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD;AACA,MAAMA,QAAM;AACZ,EAAE,OAAO,MAAM,KAAK,WAAW;AAC/B,MAAM,MAAM,CAAC,MAAM,IAAI,UAAU;AACjC,MAAM,YAAY;AAClB;AACA,QAAQ,OAAO,UAAU,EAAE,CAAC;AAC5B,OAAO;;AC7BP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAS,CAAC,SAAS,EAAE;AACrC,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC1B;AACA,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB;AACA,EAAE,IAAI,CAAC,IAAI,GAAG;AACd,IAAI,SAAS;AACb,IAAI,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1C,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACjD;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChE,GAAG,CAAC,CAAC;AACL;AACA,EAAE,MAAM,MAAM,GAAGD,QAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAChC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;AACrB;AACA;AACA,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC5B,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;AAChC,MAAM,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;AACvC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL;AACA;AACA,EAAE,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC/B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AAChD,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,OAAO;AACP,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAClC,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChE,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI;AACJ,MAAM,KAAK,IAAI,KAAK;AACpB,UAAU,KAAK,CAAC,GAAG,KAAK,QAAQ;AAChC,UAAU,KAAK,CAAC,OAAO,KAAK,EAAE;AAC9B,MAAM;AACN,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD;AACA;AACA,OAAO,CAACC,WAAS,CAAC,SAAS,CAAC,CAAC;AAC7B;AACA;AACAA,WAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AACzB;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAC1C,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AACpB;AACA,EAAE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE;AACjE,IAAI,QAAQ,EAAE,CAAC;AACf,GAAG;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC3C;AACA,EAAE,IAAIA,WAAS,CAAC,OAAO,EAAE;AACzB,IAAIA,WAAS,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;AACnC,GAAG;AACH,EAAEA,WAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B;AACA,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC3C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAClD;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxB;AACA;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAC/D,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAC7C,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACtB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACrD,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AAClE;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1B,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,KAAK,EAAE;AACrD;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AAClB,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;AACnC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;AACrC,EAAE,OAAO,OAAO,EAAE;AAClB,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf;;ACrKA;AAEA;AACA;AACA;AACA,MAAM,YAAY,GAAG,oBAAoB,CAAC;AAE1C;AACA,MAAM,SAAS,GAAG,2CAA2C,CAAC;AAC9D,MAAM,UAAU,GAAG,kCAAkC,CAAC;AACtD,MAAM,KAAK,GACT,8GAA8G,CAAC;AACjH,MAAM,MAAM,GACV,kIAAkI,CAAC;AA4DrI;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC9D,CAAC;AAED;;;;AAIG;AACG,SAAU,kBAAkB,CAAC,SAAkC,EAAA;AACnE,IAAA,IAAI,SAAS,EAAE;AACb,QAAA,OAAO,SAAS,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;AACzC,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,CAAC;AACnC,YAAA,IAAI,KAAK,EAAE;gBACT,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAC1B,gBAAA,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC9B,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC9D,CAAC;AAED;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;AACrD,CAAC;AAED;;;;;AAKG;AACG,SAAU,MAAM,CAAC,KAAc,EAAA;IACnC,IAAI,KAAK,YAAY,IAAI,EAAE;AACzB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAAM,SAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;;QAE1B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvC,QAAA,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;aAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;AACpC,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;;;AAUG;AACH,SAAS,YAAY,CACnB,CAAM,EACN,CAAM,EACN,IAAY,EACZ,aAAsB,EAAA;IAEtB,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,QAAA,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;AACxD,KAAA;AAED,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AAChB,KAAA;AAAM,SAAA;QACL,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACnB,KAAA;AACH,CAAC;AAED;;;;;;;;;AASG;AACG,SAAU,aAAa,CAC3B,CAAI,EACJ,CAAa,EACb,aAAa,GAAG,KAAK,EAAA;;;AAIrB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;;gBAEnD,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACtB,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,oBAAA,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;AAC5C,iBAAA;AACF,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED;;;;;;;AAOG;AACU,MAAA,MAAM,GAAG,MAAM,CAAC,OAAO;AAEpC;;;;;;;;;AASG;AACG,SAAU,eAAe,CAC7B,KAAe,EACf,CAAM,EACN,GAAG,MAAa,EAAA;AAEhB,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,QAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;AACzE,KAAA;AAED,IAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAC1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,YAAA,IAAI,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBAC9D,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AACvB,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;;;;;;;AAaG;AACG,SAAU,mBAAmB,CACjC,KAAe,EACf,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;AAGrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AAC/D,KAAA;AAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAC7C,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACd,iBAAA;gBACD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAClC,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;AACpD,iBAAA;AAAM,qBAAA;oBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,iBAAA;AACF,aAAA;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;AACjC,gBAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AAC/D,aAAA;AAAM,iBAAA;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACG,SAAU,sBAAsB,CACpC,cAAwB,EACxB,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;;AAIrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;AAC/D,KAAA;AAED,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;YAClD,SAAS;AACV,SAAA;AACD,QAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACjC,SAAS;AACV,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,gBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACd,aAAA;YACD,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAClC,gBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B,aAAA;AAAM,iBAAA;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,aAAA;AACF,SAAA;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;AACjC,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,gBAAA,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,aAAA;AACF,SAAA;AAAM,aAAA;YACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,UAAU,CACxB,CAAM,EACN,CAAM,EACN,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,KAAK,EAAA;AAErB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,WAAW,KAAK,IAAI,EAAE;AACzE,YAAA,IACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,gBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,gBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;AACA,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;AAChD,iBAAA;AAAM,qBAAA,IACL,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,oBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,oBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;AACA,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;AAC3C,iBAAA;AAAM,qBAAA;oBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,iBAAA;AACF,aAAA;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gBACjC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;AAC3B,aAAA;AAAM,iBAAA;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzC,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;AAMG;AACa,SAAA,UAAU,CAAC,CAAY,EAAE,CAAY,EAAA;AACnD,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;AACzB,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAChB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;AAKG;AACG,SAAU,OAAO,CAAC,MAAe,EAAA;AACrC,IAAA,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;IAE3B,IAAI,IAAI,KAAK,QAAQ,EAAE;QACrB,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;QACD,IAAI,MAAM,YAAY,OAAO,EAAE;AAC7B,YAAA,OAAO,SAAS,CAAC;AAClB,SAAA;QACD,IAAI,MAAM,YAAY,MAAM,EAAE;AAC5B,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;QACD,IAAI,MAAM,YAAY,MAAM,EAAE;AAC5B,YAAA,OAAO,QAAQ,CAAC;AACjB,SAAA;AACD,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,OAAO,OAAO,CAAC;AAChB,SAAA;QACD,IAAI,MAAM,YAAY,IAAI,EAAE;AAC1B,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;IACD,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;IACD,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,OAAO,SAAS,CAAC;AAClB,KAAA;IACD,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;IACD,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAOD;;;;;;AAMG;AACa,SAAA,kBAAkB,CAChC,GAAqB,EACrB,QAAW,EAAA;AAEX,IAAA,OAAO,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;AAKG;AACG,SAAU,SAAS,CAAI,GAAqB,EAAA;AAChD,IAAA,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC;AACrB,CAAC;AAED;;;;;AAKG;AACG,SAAU,eAAe,CAAC,IAAa,EAAA;AAC3C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;AAC3C,CAAC;AAED;;;;;AAKG;AACG,SAAU,gBAAgB,CAAC,IAAa,EAAA;AAC5C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK,CAAC;AAC5C,CAAC;AAED;;;;;AAKG;AACG,SAAU,cAAc,CAAC,IAAa,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;AAC1C,CAAC;AAED;;;;;AAKG;AACa,SAAA,YAAY,CAAC,IAAa,EAAE,UAAkB,EAAA;IAC5D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACzC,OAAO,GAAG,OAAO,CAAC,MAAM,CACtB,UAAU,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;AACnC,QAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;KACrC,CAAC,CACH,CAAC;IACF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC;AAED;;;;;AAKG;AACa,SAAA,eAAe,CAAC,IAAa,EAAE,UAAkB,EAAA;IAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACzC,IAAA,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;AAC1C,QAAA,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AACzC,KAAC,CAAC,CAAC;IACH,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC;AAUD;;;;;;;AAOG;AACa,SAAA,OAAO,CAAC,MAAW,EAAE,QAAa,EAAA;AAChD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;AAEzB,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAChC,SAAA;AACF,KAAA;AAAM,SAAA;;AAEL,QAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AACxB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;gBACrD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;AACpC,aAAA;AACF,SAAA;AACF,KAAA;AACH,CAAC;AAED;;;;;AAKG;AACU,MAAA,OAAO,GAAG,MAAM,CAAC,OAAO;AAErC;;;;;;;AAOG;SACa,cAAc,CAC5B,MAAoB,EACpB,GAAM,EACN,KAAQ,EAAA;AAER,IAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE;AACzB,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACpB,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AACH,CAAC;AAED;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,EAAc,EAAA;IACrC,IAAI,SAAS,GAAG,KAAK,CAAC;AAEtB,IAAA,OAAO,MAAW;QAChB,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,IAAI,CAAC;YACjB,qBAAqB,CAAC,MAAW;gBAC/B,SAAS,GAAG,KAAK,CAAC;AAClB,gBAAA,EAAE,EAAE,CAAC;AACP,aAAC,CAAC,CAAC;AACJ,SAAA;AACH,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACG,SAAU,cAAc,CAAC,KAAwB,EAAA;IACrD,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACtB,KAAA;IAED,IAAI,CAAC,KAAK,EAAE,CAEX;SAAM,IAAI,KAAK,CAAC,cAAc,EAAE;AAC/B,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;AACxB,KAAA;AAAM,SAAA;;AAEJ,QAAA,KAAa,CAAC,WAAW,GAAG,KAAK,CAAC;AACpC,KAAA;AACH,CAAC;AAED;;;;;AAKG;SACa,SAAS,CACvB,KAA2B,GAAA,MAAM,CAAC,KAAK,EAAA;;;IAKvC,IAAI,MAAM,GAAuB,IAAI,CAAC;IACtC,IAAI,CAAC,KAAK,EAAE,CAEX;SAAM,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,QAAA,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AACvB,KAAA;SAAM,IAAI,KAAK,CAAC,UAAU,EAAE;AAC3B,QAAA,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC;AAC3B,KAAA;AAED,IAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AAChC,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IAED,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,EAAE;;AAEnD,QAAA,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;AAC3B,QAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AAChC,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;AACF,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;AAMG;AACa,SAAA,SAAS,CAAC,OAAgB,EAAE,MAAe,EAAA;IACzD,IAAI,IAAI,GAAS,OAAO,CAAC;AAEzB,IAAA,OAAO,IAAI,EAAE;QACX,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;AACxB,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEY,MAAA,MAAM,GAAG;AACpB;;;;;;AAMG;IACH,SAAS,CAAC,KAAc,EAAE,YAAsB,EAAA;AAC9C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,SAAA;QAED,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,KAAK,IAAI,KAAK,CAAC;AACvB,SAAA;QAED,OAAO,YAAY,IAAI,IAAI,CAAC;KAC7B;AAED;;;;;;AAMG;IACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,SAAA;QAED,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,IAAI,CAAC;AAC9C,SAAA;QAED,OAAO,YAAY,IAAI,IAAI,CAAC;KAC7B;AAED;;;;;;AAMG;IACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,SAAA;QAED,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,SAAA;QAED,OAAO,YAAY,IAAI,IAAI,CAAC;KAC7B;AAED;;;;;;AAMG;IACH,MAAM,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC1C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,SAAA;AAED,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,OAAO,KAAK,GAAG,IAAI,CAAC;AACrB,SAAA;AAAM,aAAA;YACL,OAAO,YAAY,IAAI,IAAI,CAAC;AAC7B,SAAA;KACF;AAED;;;;;;AAMG;IACH,SAAS,CACP,KAA4C,EAC5C,YAAe,EAAA;AAEf,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE,CAAC;AACjB,SAAA;AAED,QAAA,OAAO,KAAK,IAAI,YAAY,IAAI,IAAI,CAAC;KACtC;EACD;AAEF;;;;;;;AAOG;AACG,SAAU,QAAQ,CAAC,GAAW,EAAA;AAClC,IAAA,IAAI,MAAM,CAAC;IACX,QAAQ,GAAG,CAAC,MAAM;AAChB,QAAA,KAAK,CAAC,CAAC;AACP,QAAA,KAAK,CAAC;AACJ,YAAA,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9B,YAAA,OAAO,MAAM;AACX,kBAAE;AACE,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACvC,iBAAA;kBACD,IAAI,CAAC;AACX,QAAA,KAAK,CAAC,CAAC;AACP,QAAA,KAAK,CAAC;AACJ,YAAA,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,YAAA,OAAO,MAAM;AACX,kBAAE;oBACE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;oBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;oBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AAC3B,iBAAA;kBACD,IAAI,CAAC;AACX,QAAA;AACE,YAAA,OAAO,IAAI,CAAC;AACf,KAAA;AACH,CAAC;AAED;;;;;;AAMG;AACa,SAAA,eAAe,CAAC,KAAa,EAAE,OAAe,EAAA;AAC5D,IAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAAM,SAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAChC,MAAM,GAAG,GAAG,KAAK;aACd,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9B,aAAA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;aAChB,KAAK,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;AAC7E,KAAA;AAAM,SAAA;AACL,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;YACL,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;AAC1E,SAAA;AACF,KAAA;AACH,CAAC;AAED;;;;;;;AAOG;SACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;AAC/D,IAAA,QACE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAC3E;AACJ,CAAC;AAsCD;;;;;;AAMG;AACa,SAAA,UAAU,CACxB,UAAgC,EAChC,YAA8B,EAAA;AAE9B,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,QAAQ,GAAW,UAAU,CAAC;AAClC,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG,QAAQ;iBACjB,MAAM,CAAC,CAAC,CAAC;iBACT,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC9B,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,UAAU,KAAK,EAAA;AAClB,gBAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,aAAC,CAAC,CAAC;AACL,YAAA,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,SAAA;AACD,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;AACjC,YAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/B,YAAA,MAAM,eAAe,GAAG;gBACtB,CAAC,EAAE,GAAG,CAAC,CAAC;AACR,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;AACd,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;aAC7B,CAAC;AACF,YAAA,MAAM,cAAc,GAAG;gBACrB,CAAC,EAAE,GAAG,CAAC,CAAC;AACR,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC5B,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;aACf,CAAC;AACF,YAAA,MAAM,cAAc,GAAG,QAAQ,CAC7B,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,CACjB,CAAC;AACF,YAAA,MAAM,eAAe,GAAG,QAAQ,CAC9B,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,CAClB,CAAC;YACF,OAAO;AACL,gBAAA,UAAU,EAAE,QAAQ;AACpB,gBAAA,MAAM,EAAE,cAAc;AACtB,gBAAA,SAAS,EAAE;AACT,oBAAA,UAAU,EAAE,eAAe;AAC3B,oBAAA,MAAM,EAAE,cAAc;AACvB,iBAAA;AACD,gBAAA,KAAK,EAAE;AACL,oBAAA,UAAU,EAAE,eAAe;AAC3B,oBAAA,MAAM,EAAE,cAAc;AACvB,iBAAA;aACF,CAAC;AACH,SAAA;AAAM,aAAA;YACL,OAAO;AACL,gBAAA,UAAU,EAAE,QAAQ;AACpB,gBAAA,MAAM,EAAE,QAAQ;AAChB,gBAAA,SAAS,EAAE;AACT,oBAAA,UAAU,EAAE,QAAQ;AACpB,oBAAA,MAAM,EAAE,QAAQ;AACjB,iBAAA;AACD,gBAAA,KAAK,EAAE;AACL,oBAAA,UAAU,EAAE,QAAQ;AACpB,oBAAA,MAAM,EAAE,QAAQ;AACjB,iBAAA;aACF,CAAC;AACH,SAAA;AACF,KAAA;AAAM,SAAA;AACL,QAAA,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,KAAK,GAAoB;AAC7B,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU;AAC5D,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM;AAChD,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;AACvC,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,SAAS;wBAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;AACjC,qBAAA;AACH,sBAAE;wBACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;4BACxD,YAAY,CAAC,SAAS,CAAC,UAAU;wBACnC,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;4BACpD,YAAY,CAAC,SAAS,CAAC,MAAM;AAChC,qBAAA;AACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;AAC/B,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,KAAK;wBACxB,UAAU,EAAE,UAAU,CAAC,KAAK;AAC7B,qBAAA;AACH,sBAAE;wBACE,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM;4BAC5C,YAAY,CAAC,KAAK,CAAC,MAAM;wBAC3B,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU;4BAChD,YAAY,CAAC,KAAK,CAAC,UAAU;AAChC,qBAAA;aACN,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,KAAK,GAAgB;AACzB,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,SAAS;AAC9C,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,SAAS;AACtC,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC;AACvC,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,SAAS;wBAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;AACjC,qBAAA;AACH,sBAAE;wBACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;4BACxD,SAAS;wBACX,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;4BACpD,SAAS;AACZ,qBAAA;AACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC;AAC/B,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,KAAK;wBACxB,UAAU,EAAE,UAAU,CAAC,KAAK;AAC7B,qBAAA;AACH,sBAAE;AACE,wBAAA,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS;AAC5D,wBAAA,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;AACjE,qBAAA;aACN,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACF,KAAA;AACH,CAAC;AAED;;;;;;;;;AASG;SACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;AAC/D,IAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAChB,IAAA,KAAK,GAAG,KAAK,GAAG,GAAG,CAAC;AACpB,IAAA,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;AAClB,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACpD,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;;IAGpD,IAAI,MAAM,KAAK,MAAM,EAAE;AACrB,QAAA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;AAClC,KAAA;;AAGD,IAAA,MAAM,CAAC,GACL,GAAG,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;IAC7E,MAAM,CAAC,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACvD,IAAA,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,CAAC;IACrD,MAAM,UAAU,GAAG,CAAC,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC;IAC9C,MAAM,KAAK,GAAG,MAAM,CAAC;AACrB,IAAA,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AAC7C,CAAC;AAMD;;;;;AAKG;AACH,SAAS,YAAY,CAAC,OAAe,EAAA;IACnC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAElD,MAAM,MAAM,GAAc,EAAE,CAAC;AAE7B,IAAA,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAEpC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAC/D,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CACrB,CAAC;AACH,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACa,SAAA,UAAU,CAAC,OAAoB,EAAE,OAAe,EAAA;AAC9D,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;AACvC,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACnD,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,KAAA;AACH,CAAC;AAED;;;;;AAKG;AACa,SAAA,aAAa,CAAC,OAAoB,EAAE,OAAe,EAAA;AACjE,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACvC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACvC,QAAA,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACnC,KAAA;AACH,CAAC;AAED;;;;;;;;;AASG;SACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;AACtD,IAAA,IAAI,CAAqB,CAAC;AAC1B,IAAA,IAAI,CAAqB,CAAC;AAC1B,IAAA,IAAI,CAAqB,CAAC;IAE1B,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,IAAA,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACtB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAEhC,QAAQ,CAAC,GAAG,CAAC;AACX,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACR,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,MAAM;AACT,KAAA;IAED,OAAO;QACL,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;KACnC,CAAC;AACJ,CAAC;AAED;;;;;;;AAOG;SACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;IACtD,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC;AAED;;;;;AAKG;AACG,SAAU,QAAQ,CAAC,GAAW,EAAA;AAClC,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,MAAM,IAAI,SAAS,CAAC,IAAI,GAAG,CAAA,uBAAA,CAAyB,CAAC,CAAC;AACvD,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,CAAC;AAED;;;;;AAKG;AACG,SAAU,UAAU,CAAC,GAAW,EAAA;IACpC,MAAM,IAAI,GAAG,oCAAoC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5D,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;AAKG;AACG,SAAU,UAAU,CAAC,GAAW,EAAA;AACpC,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED;;;;;AAKG;AACG,SAAU,WAAW,CAAC,IAAY,EAAA;AACtC,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;;AAOG;AACa,SAAA,qBAAqB,CACnC,MAAW,EACX,eAA6B,EAAA;IAE7B,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;;QAEnE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AAChD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;gBACpE,IAAI,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;AACjD,oBAAA,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE,iBAAA;AACF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACH,CAAC;AAID;;;;;;AAMG;AACG,SAAU,YAAY,CAC1B,eAAkB,EAAA;IAElB,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;AACnE,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;IAED,IAAI,eAAe,YAAY,OAAO,EAAE;;AAEtC,QAAA,OAAO,eAAe,CAAC;AACxB,KAAA;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AAChD,IAAA,KAAK,MAAM,CAAC,IAAI,eAAe,EAAE;AAC/B,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE;AAC5D,YAAA,IAAI,OAAQ,eAAuB,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;gBAClD,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAE,eAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,aAAA;AACF,SAAA;AACF,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;AAMG;AACa,SAAA,UAAU,CAAI,CAAM,EAAE,OAA+B,EAAA;AACnE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,QAAA,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,CAAC;QACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACjB,SAAA;AACD,QAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACV,KAAA;AACD,IAAA,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;;;;;;;;;;AAYG;AACG,SAAU,YAAY,CAC1B,WAAgB,EAChB,OAAY,EACZ,MAAc,EACd,aAAA,GAAqB,EAAE,EAAA;;IAGvB,MAAM,SAAS,GAAG,UAAU,GAAQ,EAAA;AAClC,QAAA,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,CAAC;AAC3C,KAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,UAAU,GAAY,EAAA;QACrC,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjD,KAAC,CAAC;;IAGF,MAAM,OAAO,GAAG,UAAU,GAAW,EAAA;AACnC,QAAA,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;AACnB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;AAChD,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACF,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC;;AAGF,IAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC5D,KAAA;AAED,IAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACxD,KAAA;AAED,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACvD,KAAA;AAED,IAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC5B,QAAA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAC9D,KAAA;;;;;AAMD,IAAA,MAAM,OAAO,GAAG,UAAU,MAAW,EAAE,OAAY,EAAE,MAAc,EAAA;QACjE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;AAC7B,YAAA,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AACrB,SAAA;AAED,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5B,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAC3B,QAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AACtB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;gBACnD,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACvB,aAAA;AACF,SAAA;AACH,KAAC,CAAC;;AAGF,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAClC,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxE,IAAA,MAAM,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;AACtE,IAAA,MAAM,aAAa,GAAG,YAAY,GAAG,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;;;;IAKtE,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,QAAA,OAAO;AACR,KAAA;AAED,IAAA,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;AAClC,YAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAC1B,SAAA;AAED,QAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC;QACxC,OAAO;AACR,KAAA;AAED,IAAA,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;;AAExD,QAAA,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;YAC3B,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACnD,SAAA;AAAM,aAAA;AACL,YAAA,OAAO;AACR,SAAA;AACF,KAAA;AAED,IAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACxB,OAAO;AACR,KAAA;;;;;AAMD,IAAA,IAAI,OAAO,GAAG,IAAI,CAAC;AAEnB,IAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;AACnC,QAAA,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;AAC7B,KAAA;AAAM,SAAA;;QAEL,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AAChC,SAAA;AACF,KAAA;AAED,IAAA,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACtC,IAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACxC,CAAC;AAiBD;;;;;;;;;AASG;AACG,SAAU,kBAAkB,CAChC,YAAmB,EACnB,UAAsC,EACtC,KAAa,EACb,MAAe,EAAA;IAEf,MAAM,aAAa,GAAG,KAAK,CAAC;IAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AAEnC,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;AAC/C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AAE5C,QAAA,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AAEvE,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,YAAY,IAAI,CAAC,EAAE;;AAErB,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAAM,aAAA,IAAI,YAAY,IAAI,CAAC,CAAC,EAAE;;AAE7B,YAAA,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC;AAClB,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;AACnB,SAAA;AAED,QAAA,SAAS,EAAE,CAAC;AACb,KAAA;IAED,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED;;;;;;;;;;;;AAYG;AACG,SAAU,iBAAiB,CAC/B,YAAoC,EACpC,MAAc,EACd,KAAQ,EACR,cAAkC,EAClC,UAAiD,EAAA;IAEjD,MAAM,aAAa,GAAG,KAAK,CAAC;IAC5B,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,IAAA,IAAI,SAAS,CAAC;AACd,IAAA,IAAI,KAAK,CAAC;AACV,IAAA,IAAI,SAAS,CAAC;AACd,IAAA,IAAI,MAAM,CAAC;IAEX,UAAU;AACR,QAAA,UAAU,IAAI,SAAS;AACrB,cAAE,UAAU;AACZ,cAAE,UAAU,CAAS,EAAE,CAAS,EAAA;gBAC5B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACrC,aAAC,CAAC;AAER,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;;AAE/C,QAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACxC,QAAA,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACzD,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QACpC,SAAS;AACP,YAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAErE,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;;AAElC,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAAM,aAAA,IACL,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC;AACjC,YAAA,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAC7B;;YAEA,OAAO,cAAc,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;AACtE,SAAA;AAAM,aAAA,IACL,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;AAC7B,YAAA,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EACjC;;YAEA,OAAO,cAAc,IAAI,QAAQ;AAC/B,kBAAE,MAAM;AACR,kBAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;AACnD,SAAA;AAAM,aAAA;;YAEL,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;;AAEjC,gBAAA,GAAG,GAAG,MAAM,GAAG,CAAC,CAAC;AAClB,aAAA;AAAM,iBAAA;;AAEL,gBAAA,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;AACnB,aAAA;AACF,SAAA;AACD,QAAA,SAAS,EAAE,CAAC;AACb,KAAA;;IAGD,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AAED;;;;;;AAMG;AACU,MAAA,eAAe,GAAG;AAC7B;;;;;AAKG;AACH,IAAA,MAAM,CAAC,CAAS,EAAA;AACd,QAAA,OAAO,CAAC,CAAC;KACV;AAED;;;;;AAKG;AACH,IAAA,UAAU,CAAC,CAAS,EAAA;QAClB,OAAO,CAAC,GAAG,CAAC,CAAC;KACd;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACpB;AAED;;;;;AAKG;AACH,IAAA,aAAa,CAAC,CAAS,EAAA;QACrB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACnD;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAClB;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;QACpB,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACxB;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;KAC1E;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACtB;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;QACpB,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC5B;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC9D;AAED;;;;;AAKG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;QACnB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAC1B;AAED;;;;;AAKG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;AACpB,QAAA,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAChC;AAED;;;;;AAKG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACxE;EACD;AAEF;;;;AAIG;SACa,iBAAiB,GAAA;IAC/B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AAC3B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;IAE7B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5C,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AAClC,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,IAAA,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACzB,IAAA,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAClC,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;AAC5B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;AAC7B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAChC,IAAA,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAEzB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACjC,IAAA,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;AAC7B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAChC,IAAA,IAAI,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;IAC3B,IAAI,EAAE,IAAI,EAAE,EAAE;AACZ,QAAA,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC;AACxB,KAAA;AAED,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAEjC,OAAO,EAAE,GAAG,EAAE,CAAC;AACjB,CAAC;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAOG;AACa,SAAA,OAAO,CAAC,IAAS,EAAE,SAAc,EAAA;AAC/C,IAAA,IAAI,SAAS,CAAC;AACd,IAAA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC7B,QAAA,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;AACzB,KAAA;AACD,IAAA,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE;AACzB,QAAA,IAAI,MAAM,EAAE;YACV,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAA,IAAI,SAAS,EAAE;oBACb,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;gBACpC,MAAM;AACP,aAAA;AACF,SAAA;AACF,KAAA;AACD,IAAA,OAAO,SAAS,CAAC;AACnB;;ACxwDA,MAAM,UAAU,GAAG;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,iBAAiB,EAAE,SAAS;AAC9B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,gBAAgB,EAAE,SAAS;AAC7B,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,GAAG,EAAE,SAAS;AAChB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,oBAAoB,EAAE,SAAS;AACjC,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,GAAG,EAAE,SAAS;AAChB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,CAAC,CAAC;AACF;AACA;AACA;AACA;oBACO,MAAM,WAAW,CAAC;AACzB;AACA;AACA;AACA,EAAE,WAAW,CAAC,UAAU,GAAG,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;AACxD,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACpD,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AAC3D,IAAI,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AACnC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB;AACA;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,EAAE,CAAC;AAClC;AACA;AACA,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,SAAS,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;AACnC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AAC5B,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AAC9B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,6EAA6E;AACrF,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,QAAQ,EAAE;AAC7B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;AACpC,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,8EAA8E;AACtF,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,MAAM,OAAO;AACb,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC;AACb;AACA;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,KAAK,GAAG,SAAS,CAAC;AACxB,KAAK;AACL;AACA;AACA,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAClC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AACtC,QAAQ,MAAM,SAAS,GAAG,KAAK;AAC/B,WAAW,MAAM,CAAC,CAAC,CAAC;AACpB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AAC7E,OAAO,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAC9C,QAAQ,MAAM,SAAS,GAAG,KAAK;AAC/B,WAAW,MAAM,CAAC,CAAC,CAAC;AACpB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB,QAAQ,IAAI,GAAG;AACf,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAC7C,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACjE,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,YAAY,MAAM,EAAE;AACnC,QAAQ;AACR,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;AAC/B,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;AAC/B,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;AAC/B,UAAU;AACV,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC;AAChE,UAAU,IAAI,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AAClE,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA;AACA,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;AAC5B,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,+HAA+H;AACvI,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AAC/B,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AACrC,KAAK;AACL;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,EAAE;AAC9B;AACA,IAAI,IAAI,aAAa,KAAK,IAAI,EAAE;AAChC,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACzD,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;AAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7C,KAAK;AACL;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACtC;AACA;AACA;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC5C,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,QAAQ,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;AACvC,OAAO;AACP,KAAK,EAAE,CAAC,CAAC,CAAC;AACV,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE;AACrC;AACA,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;AAC7B,MAAM,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAClD,KAAK;AACL;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD;AACA,IAAI,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAClC,IAAI,MAAM,CAAC;AACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,IAAI,MAAM,CAAC;AACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE;AACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;AACvC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5D,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;AACtC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7D;AACA,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC;AACxB,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE;AACnC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACxD,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AACxC,MAAM,IAAI,CAAC,UAAU;AACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACrC,SAAS,GAAG,CAAC,4BAA4B;AACzC,UAAU,GAAG,CAAC,yBAAyB;AACvC,UAAU,GAAG,CAAC,wBAAwB;AACtC,UAAU,GAAG,CAAC,uBAAuB;AACrC,UAAU,GAAG,CAAC,sBAAsB;AACpC,UAAU,CAAC,CAAC,CAAC;AACb,KAAK;AACL,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACnE;AACA;AACA,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;AACjD,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;AAClD,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9B;AACA,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,IAAI,GAAG,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtD,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;AACf;AACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;AAC9C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG,CAAC;AACV,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;AAC1C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG,CAAC;AACV,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AAChD,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACjD;AACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;AACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;AAC1D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,kBAAkB,CAAC;AAC9C;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC;AACxD,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC9D;AACA,IAAI,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC5D;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;AAC5C,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACrD,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,MAAM,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;AACzC,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACtC,MAAM,QAAQ,CAAC,SAAS,GAAG,kDAAkD,CAAC;AAC9E,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnD,KAAK,MAAM;AACX,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1D,MAAM,IAAI,CAAC,UAAU;AACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACrC,SAAS,GAAG,CAAC,4BAA4B;AACzC,UAAU,GAAG,CAAC,yBAAyB;AACvC,UAAU,GAAG,CAAC,wBAAwB;AACtC,UAAU,GAAG,CAAC,uBAAuB;AACrC,UAAU,GAAG,CAAC,sBAAsB;AACpC,UAAU,CAAC,CAAC,CAAC;AACb,MAAM,IAAI,CAAC,iBAAiB;AAC5B,SAAS,UAAU,CAAC,IAAI,CAAC;AACzB,SAAS,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,KAAK;AACL;AACA,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,WAAW,CAAC;AAChD;AACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,aAAa,CAAC;AAC9C;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,gBAAgB,CAAC;AACpD;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC;AAC1C;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACxD,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;AACvC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;AAClC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC;AACpC,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;AACpC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,WAAW,CAAC;AAC9C;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC3D,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC;AAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG,CAAC;AACrC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,KAAK,CAAC;AACvC,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;AACvC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,WAAW,CAAC;AACjD;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACzD;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,YAAY;AAC5C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,YAAY;AAChD,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,YAAY;AAC/C,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,0BAA0B,CAAC;AAChE,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,aAAa,CAAC;AACnD;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,UAAU,CAAC;AAC7C;AACA,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,eAAe,CAAC;AACjD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;AACvC;AACA,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,mBAAmB,CAAC;AACzD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/C;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB,CAAC;AAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC3C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7D;AACA,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,sBAAsB,CAAC;AACxD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC;AACzC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB,CAAC;AACtD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC;AACvC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD;AACA,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB,CAAC;AACtD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC;AAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACjD;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAID,QAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACnD;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,KAAK,KAAK;AAC9C,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;AACrC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK,KAAK;AAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;AACzC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAClC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1D,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AAC1C,QAAQ,IAAI,CAAC,UAAU;AACvB,UAAU,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACvC,WAAW,GAAG,CAAC,4BAA4B;AAC3C,YAAY,GAAG,CAAC,yBAAyB;AACzC,YAAY,GAAG,CAAC,wBAAwB;AACxC,YAAY,GAAG,CAAC,uBAAuB;AACvC,YAAY,GAAG,CAAC,sBAAsB;AACtC,YAAY,CAAC,CAAC,CAAC;AACf,OAAO;AACP,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE;AACA;AACA,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;AACnD,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;AACpD,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC;AACA;AACA,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;AACzB,MAAM,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAC1D,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACxB,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC;AAC/C,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC;AAC3B,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAC9B,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;AACtC,QAAQ,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;AAC3C,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;AAC5E,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;AAC5E,UAAU,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;AACpD,UAAU,GAAG,CAAC,SAAS,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC3E,UAAU,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/C,SAAS;AACT,OAAO;AACP,MAAM,GAAG,CAAC,WAAW,GAAG,eAAe,CAAC;AACxC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7E,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC;AACnB;AACA,MAAM,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,KAAK,EAAE;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC;AAC7D,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AAC5C,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC1C;AACA,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;AAC3D,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;AAC1D;AACA,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC;AAC7B,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC;AAC5B;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,IAAI,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACtE;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;AACtD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;AACvD;AACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;AACtC,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC;AAClE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;AACvC,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC;AAClE;AACA;AACA,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAC9B,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB;AACA;AACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;AAC9C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG,CAAC;AACV,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;AAC1C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG,CAAC;AACV,GAAG;AACH;;AC9xBA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE;AAC5B,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;AAC9C,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,IAAI,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,GAAG,MAAM;AACT,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;qBACO,MAAM,YAAY,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW;AACb,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,UAAU,GAAG,CAAC;AAClB,IAAI,UAAU,GAAG,MAAM,KAAK;AAC5B,IAAI;AACJ,IAAI,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;AAC/B,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;AACtC,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC;AACA,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,cAAc,GAAG;AAC1B,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,UAAU,EAAE,IAAI;AACtB,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC7C,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAIE,aAAW,CAAC,UAAU,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;AAC/B;AACA,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC7B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B;AACA,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;AACzB,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AACtC,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAC7C,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC9C,QAAQ,IAAI,OAAO,IAAI,IAAI,EAAE;AAC7B,UAAU,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AAC7C,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;AAC1C,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC/C,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE;AAC9C,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACvD,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;AAC3C,UAAU,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACpC,SAAS;AACT,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;AAC/C,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AACnC,QAAQ,OAAO,GAAG,OAAO,CAAC;AAC1B,OAAO,MAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AAChD,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC;AACtC,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;AACzC,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,OAAO;AACP;AACA,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,aAAa,EAAE;AAClC,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;AACvC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AAChD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AAChD,OAAO;AACP,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACrB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB,IAAI,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAChD,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,EAAE;AAC/E,QAAQ,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AACnC,QAAQ,IAAI,GAAG,KAAK,CAAC;AACrB,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACpC,UAAU,IAAI;AACd,YAAY,IAAI;AAChB,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9E,SAAS,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AACrE,UAAU,IAAI,GAAG,IAAI,CAAC;AACtB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;AAC5B,UAAU,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AACpC;AACA;AACA,UAAU,IAAI,OAAO,GAAG,CAAC,EAAE;AAC3B,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AAC/B,WAAW;AACX;AACA,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACnC;AACA;AACA,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtE,SAAS;AACT,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,2BAA2B,CAAC;AACzD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,KAAK;AACL;AACA,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,KAAK;AACL;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AACpC,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B;AACA,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AACvC,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,OAAO,MAAM;AACb,QAAQ,IAAI,GAAG,SAAS,CAAC;AACzB,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,WAAW,EAAE;AAClC,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;AACrC,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,SAAS;AACpB,QAAQ,gDAAgD,GAAG,IAAI,CAAC,MAAM,CAAC;AACvE,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACvC,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,GAAG,CAAC,SAAS,GAAG,qCAAqC,CAAC;AAC1D,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE;AAC9C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,GAAG,CAAC,SAAS;AACjB,MAAM,iDAAiD,GAAG,IAAI,CAAC,MAAM,CAAC;AACtE,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;AAC9B,MAAM,OAAO,GAAG,CAAC,UAAU,EAAE;AAC7B,QAAQ,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACxC,OAAO;AACP,MAAM,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,GAAG,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AAClC,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,SAAS,GAAG,qCAAqC,CAAC;AAC7D,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;AACrC,QAAQ,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK;AACL;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACtD,MAAM,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,IAAI,CAAC,KAAK,aAAa,EAAE;AAC/B,QAAQ,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;AACrC,OAAO;AACP,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACjC,KAAK;AACL;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;AACpB,IAAI,MAAM,CAAC,QAAQ,GAAG,YAAY;AAClC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACnC,KAAK,CAAC;AACN;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACxC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AAC/B,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAClD,IAAI,KAAK,CAAC,SAAS,GAAG,oCAAoC,CAAC;AAC3D,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;AAC3B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB;AACA;AACA,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB;AACA,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC;AACzB,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;AAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;AAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;AACxC,OAAO,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;AACvC,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;AAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;AACxC,OAAO;AACP,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;AAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;AAC9C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;AAC/B,QAAQ,WAAW,GAAG,iBAAiB,CAAC;AACxC,OAAO;AACP,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;AACjC,KAAK;AACL;AACA,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAClD,IAAI,KAAK,CAAC,SAAS,GAAG,yCAAyC,CAAC;AAChE,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAC9B;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;AACpB,IAAI,KAAK,CAAC,QAAQ,GAAG,YAAY;AACjC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3C,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,OAAO,GAAG,YAAY;AAChC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,KAAK,CAAC;AACN;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAChE;AACA;AACA,IAAI,IAAI,WAAW,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;AAC3E,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;AAChD,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;AAC1C,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,MAAM,cAAc,CAAC,SAAS,GAAG,qCAAqC,CAAC;AACvE,MAAM,cAAc,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACpD,MAAM,cAAc,CAAC,OAAO,GAAG,MAAM;AACrC,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,OAAO,CAAC;AACR,MAAM,cAAc,CAAC,WAAW,GAAG,MAAM;AACzC,QAAQ,cAAc,CAAC,SAAS,GAAG,2CAA2C,CAAC;AAC/E,OAAO,CAAC;AACR,MAAM,cAAc,CAAC,UAAU,GAAG,MAAM;AACxC,QAAQ,cAAc,CAAC,SAAS,GAAG,qCAAqC,CAAC;AACzE,OAAO,CAAC;AACR;AACA,MAAM,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS;AACrC,QAAQ,+CAA+C,CAAC;AACxD;AACA,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACnD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE;AAC7B,IAAI;AACJ,MAAM,IAAI,CAAC,WAAW,KAAK,IAAI;AAC/B,MAAM,IAAI,CAAC,aAAa,KAAK,IAAI;AACjC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU;AACzC,MAAM;AACN,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,MAAM,GAAG,CAAC,EAAE,GAAG,yBAAyB,CAAC;AACzC,MAAM,GAAG,CAAC,SAAS,GAAG,yBAAyB,CAAC;AAChD,MAAM,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;AAC7B,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;AAC1B,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;AAC7B,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAClD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpE,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC9C,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAChD,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC1C,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzE,MAAM,MAAM,IAAI,GAAG,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;AAChE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1D,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM;AACnD,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;AAC7C,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM;AACrD,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;AAC3C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,QAAQ,CAAC,IAAI,GAAG,UAAU,CAAC;AAC/B,IAAI,QAAQ,CAAC,SAAS,GAAG,uCAAuC,CAAC;AACjE,IAAI,QAAQ,CAAC,OAAO,GAAG,YAAY,CAAC;AACpC,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;AAC/B,MAAM,IAAI,KAAK,KAAK,YAAY,EAAE;AAClC,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAC9C,UAAU,IAAI,KAAK,KAAK,YAAY,CAAC,OAAO,EAAE;AAC9C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACnE,WAAW;AACX,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;AACpB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;AACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACrC,KAAK,CAAC;AACN;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC;AAC3B,IAAI,QAAQ,CAAC,SAAS,GAAG,mCAAmC,CAAC;AAC7D,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,KAAK,KAAK,YAAY,EAAE;AAChC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC7D,KAAK;AACL;AACA,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC;AACpB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;AACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACnC,KAAK,CAAC;AACN;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AACpC,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC;AACvD;AACA,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,MAAM,GAAG,CAAC,SAAS,GAAG,yCAAyC,CAAC;AAChE,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,SAAS,GAAG,8CAA8C,CAAC;AACrE,KAAK;AACL;AACA,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK,CAAC;AACvD,IAAI,GAAG,CAAC,OAAO,GAAG,MAAM;AACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9C,KAAK,CAAC;AACN;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;AACrC;AACA,IAAI,GAAG,CAAC,OAAO,GAAG,YAAY,EAAE,CAAC;AACjC;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AAC5B;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,KAAK,KAAK;AAClD,MAAM,MAAM,WAAW;AACvB,QAAQ,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAChF,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,WAAW,CAAC;AAC9C,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AACtC,KAAK,CAAC,CAAC;AACP;AACA;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM;AAC5C,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;AAC1B,QAAQ,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAChD,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,GAAG,KAAK,EAAE;AACnD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC;AAC7B,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,EAAE;AAC9B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;AAC7D,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,QAAQ,MAAM,OAAO,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACzD,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACtC;AACA;AACA,UAAU,IAAI,IAAI,KAAK,KAAK,EAAE;AAC9B,YAAY;AACZ,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAClC,cAAc,OAAO,IAAI,KAAK,QAAQ;AACtC,cAAc,OAAO,IAAI,KAAK,SAAS;AACvC,cAAc,IAAI,YAAY,MAAM;AACpC,cAAc;AACd,cAAc,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AACzC,cAAc,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7D,cAAc,IAAI,CAAC,aAAa,GAAG,SAAS,KAAK,KAAK,CAAC;AACvD,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;AAC5B,UAAU,YAAY,GAAG,IAAI,CAAC;AAC9B,UAAU,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAChD;AACA,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACnC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACpD,WAAW,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC/C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACtD,WAAW,MAAM,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;AAChD,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACrD,WAAW,MAAM,IAAI,IAAI,YAAY,MAAM,EAAE;AAC7C;AACA,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;AACpE;AACA,cAAc,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AAC9C,gBAAgB,MAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC3E,gBAAgB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACjE,gBAAgB,IAAI,YAAY,KAAK,IAAI,EAAE;AAC3C,kBAAkB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACvE,kBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACjD,kBAAkB,YAAY;AAC9B,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC;AACtE,iBAAiB,MAAM;AACvB,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAClE,iBAAiB;AACjB,eAAe,MAAM;AACrB,gBAAgB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACrE,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC/C,gBAAgB,YAAY;AAC5B,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY,CAAC;AACpE,eAAe;AACf,aAAa;AACb,WAAW,MAAM;AACjB,YAAY,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC5E,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;AAC1D,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC7C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC/D,OAAO;AACP,KAAK,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACxC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACvE,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;AACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACxD;AACA,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;AACtB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;AAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;AACnC,MAAM;AACN,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE;AAClD,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC;AAC7B;AACA;AACA,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AAC5C,IAAI,KAAK,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAChC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAChC,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,UAAU,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACnC,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACtC;AACA,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;AAC7C,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAC1E,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW;AACrC,MAAM,SAAS,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7E,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,MAAM,IAAI,CAAC,iBAAiB;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK;AACpC,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI;AACnC,QAAQ,OAAO;AACf,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH;;AChyBA;AACA;AACA;cACO,MAAM,KAAK,CAAC;AACnB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,SAAS,EAAE,cAAc,EAAE;AACzC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,KAAK,CAAC;AAClD;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB;AACA;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC;AACzC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,OAAO,YAAY,OAAO,EAAE;AACpC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AACpC,QAAQ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACtD,OAAO;AACP,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACtC,KAAK,MAAM;AACX;AACA;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC;AACrC,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;AAC9B,MAAM,MAAM,GAAG,IAAI,CAAC;AACpB,KAAK;AACL;AACA,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;AACzB,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;AAC7C,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC;AAC3C,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC;AAC3D,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC;AACzD;AACA,MAAM,IAAI,IAAI,GAAG,CAAC;AAClB,QAAQ,GAAG,GAAG,CAAC,CAAC;AAChB;AACA,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,MAAM,EAAE;AACzC,QAAQ,IAAI,MAAM,GAAG,KAAK;AAC1B,UAAU,KAAK,GAAG,IAAI,CAAC;AACvB;AACA,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;AAC5C,UAAU,KAAK,GAAG,KAAK,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE;AACtD,UAAU,MAAM,GAAG,IAAI,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AACxB,SAAS;AACT;AACA,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AACvB,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAC9B,QAAQ,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE;AACrD,UAAU,GAAG,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAClD,SAAS;AACT,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;AAChC,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7B,SAAS;AACT;AACA,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;AACtB,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE;AACpD,UAAU,IAAI,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;AACjD,SAAS;AACT,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;AACjC,UAAU,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,SAAS;AACT,OAAO;AACP;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;AACxC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AAC9C,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;AAChC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClD,GAAG;AACH;;ACrIA,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,IAAI,UAAU,CAAC;AACf;AACO,MAAMC,uBAAqB,GAAG,qCAAqC,CAAC;AAC3E;AACA;AACA;AACA;kBACO,MAAM,SAAS,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE;AACxD,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,IAAI,UAAU,GAAG,gBAAgB,CAAC;AAClC,IAAI,IAAI,WAAW,GAAG,gBAAgB,CAAC;AACvC,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC;AAC9C,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;AAChD,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AACjE,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;AACjE,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACxD,IAAI;AACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;AAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK,SAAS;AAC5C,MAAM;AACN,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;AAC9D,MAAM,OAAO;AACb,KAAK;AACL;AACA,IAAI,IAAI,eAAe,GAAG,MAAM,CAAC;AACjC,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;AACzB;AACA,IAAI;AACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;AAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK,SAAS;AAC5C,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG,SAAS,CAAC;AAClC;AACA;AACA;AACA,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,QAAQ,CAAC;AAClE,KAIK;AACL;AACA,IAAI,IAAI,YAAY,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACzD,IAAI,IAAI,SAAS,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC;AAC3C,KAAK;AACL;AACA,IAAI,SAAS,CAAC,WAAW;AACzB,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,YAAY;AAClB,MAAM,IAAI;AACV,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW;AACpB,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,IAAI;AACR,IAAI;AACJ,IAAI,MAAM,GAAG,GAAG,UAAU,OAAO,EAAE;AACnC,MAAM,OAAO,CAAC,KAAK;AACnB,QAAQ,IAAI,GAAG,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;AAC9D,QAAQA,uBAAqB;AAC7B,OAAO,CAAC;AACR,KAAK,CAAC;AACN;AACA,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1D,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACnD;AACA,IAAI,IAAI,aAAa,KAAK,SAAS,EAAE;AACrC;AACA,MAAM;AACN,QAAQ,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,OAAO;AACpD,QAAQ,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;AACrD,QAAQ;AACR,QAAQ,GAAG;AACX,UAAU,8BAA8B;AACxC,YAAY,MAAM;AAClB,YAAY,IAAI;AAChB,YAAY,sBAAsB;AAClC,YAAY,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;AAC1C,YAAY,QAAQ;AACpB,YAAY,OAAO,CAAC,MAAM,CAAC;AAC3B,YAAY,KAAK;AACjB,SAAS,CAAC;AACV,QAAQ,UAAU,GAAG,IAAI,CAAC;AAC1B,OAAO,MAAM,IAAI,UAAU,KAAK,QAAQ,IAAI,eAAe,KAAK,SAAS,EAAE;AAC3E,QAAQ,IAAI,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAChD,QAAQ,SAAS,CAAC,KAAK;AACvB,UAAU,OAAO,CAAC,MAAM,CAAC;AACzB,UAAU,gBAAgB,CAAC,eAAe,CAAC;AAC3C,UAAU,IAAI;AACd,SAAS,CAAC;AACV,OAAO;AACP,KAAK,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;AAClD;AACA,MAAM,GAAG;AACT,QAAQ,6BAA6B;AACrC,UAAU,MAAM;AAChB,UAAU,eAAe;AACzB,UAAU,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpD,UAAU,cAAc;AACxB,UAAU,UAAU;AACpB,UAAU,KAAK;AACf,UAAU,OAAO,CAAC,MAAM,CAAC;AACzB,UAAU,GAAG;AACb,OAAO,CAAC;AACR,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;AAC/B;AACA,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC3B,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;AAC3B,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,MAAM,YAAY,OAAO,EAAE;AACrC,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;AACpC,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;AACpC,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,OAAO,OAAO,CAAC;AACvB,OAAO;AACP,MAAM,IAAI,MAAM,YAAY,IAAI,EAAE;AAClC,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE;AACzC,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAC5C,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AACnC,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AACnC,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;AAC9C,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC9E,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC/E;AACA,IAAI,MAAM,oBAAoB,GAAG,CAAC,CAAC;AACnC,IAAI,MAAM,qBAAqB,GAAG,CAAC,CAAC;AACpC;AACA,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE;AAC9C,MAAM,GAAG;AACT,QAAQ,MAAM;AACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;AAC7D,QAAQ,4CAA4C;AACpD,QAAQ,WAAW,CAAC,UAAU;AAC9B,QAAQ,QAAQ,CAAC;AACjB,KAAK,MAAM;AACX,MAAM,YAAY,CAAC,QAAQ,IAAI,qBAAqB;AACpD,MAAM,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ;AAClD,MAAM;AACN,MAAM,GAAG;AACT,QAAQ,MAAM;AACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;AAC7D,QAAQ,sDAAsD;AAC9D,QAAQ,SAAS,CAAC,aAAa;AAC/B,UAAU,YAAY,CAAC,IAAI;AAC3B,UAAU,YAAY,CAAC,YAAY;AACnC,UAAU,EAAE;AACZ,SAAS,CAAC;AACV,KAAK,MAAM,IAAI,WAAW,CAAC,QAAQ,IAAI,oBAAoB,EAAE;AAC7D,MAAM,GAAG;AACT,QAAQ,kBAAkB;AAC1B,QAAQ,WAAW,CAAC,YAAY;AAChC,QAAQ,IAAI;AACZ,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC1D,KAAK,MAAM;AACX,MAAM,GAAG;AACT,QAAQ,+BAA+B;AACvC,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAQ,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9C,KAAK;AACL;AACA,IAAI,OAAO,CAAC,KAAK;AACjB,MAAM,8BAA8B,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG;AACzD,MAAMA,uBAAqB;AAC3B,KAAK,CAAC;AACN,IAAI,UAAU,GAAG,IAAI,CAAC;AACtB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE;AACjE,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,IAAI,IAAI,YAAY,GAAG,EAAE,CAAC;AAC1B,IAAI,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAC9B,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AACjD,IAAI,IAAI,UAAU,GAAG,SAAS,CAAC;AAC/B,IAAI,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE;AAC9B,MAAM,IAAI,QAAQ,CAAC;AACnB,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;AACpE,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa;AAC9C,UAAU,MAAM;AAChB,UAAU,OAAO,CAAC,EAAE,CAAC;AACrB,UAAU,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC;AACtC,SAAS,CAAC;AACV,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE;AACnC,UAAU,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAC7C,UAAU,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC;AACzC,UAAU,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC;AAChC,UAAU,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AACzC,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE;AAC9D,UAAU,UAAU,GAAG,EAAE,CAAC;AAC1B,SAAS;AACT,QAAQ,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC7D,QAAQ,IAAI,GAAG,GAAG,QAAQ,EAAE;AAC5B,UAAU,YAAY,GAAG,EAAE,CAAC;AAC5B,UAAU,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAC7C,UAAU,GAAG,GAAG,QAAQ,CAAC;AACzB,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,YAAY;AAChC,MAAM,IAAI,EAAE,gBAAgB;AAC5B,MAAM,QAAQ,EAAE,GAAG;AACnB,MAAM,UAAU,EAAE,UAAU;AAC5B,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,4BAA4B,EAAE;AAC5E,IAAI,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,eAAe,CAAC;AAChD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,GAAG,IAAI,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAC/B,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,GAAG,IAAI,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC;AACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,GAAG,IAAI,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,GAAG,IAAI,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,GAAG,GAAG,MAAM,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC,OAAO,OAAO,CAAC,8BAA8B,EAAE,EAAE,CAAC;AAClD,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC;AACxC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC;AACxC;AACA,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB;AACA;AACA,IAAI,IAAI,CAAC,CAAC;AACV,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtB,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,CAAC;AACV,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,KAAK;AACL;AACA;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAChD,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;AACjC,YAAY,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpC,YAAY,IAAI,CAAC,GAAG;AACpB,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAClC,cAAc,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClC,aAAa;AACb,WAAW,CAAC;AACZ,SAAS;AACT,OAAO;AACP,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACtC,GAAG;AACH;;ACzZO,MAAM,SAAS,GAAQC,YAAY;AACnC,MAAM,WAAW,GAAQC,cAAc;AACvC,MAAM,YAAY,GAAQC,eAAe;AACzC,MAAM,MAAM,GAAiBC,SAAS;AACtC,MAAM,KAAK,GAAQC,QAAQ;AAC3B,MAAM,qBAAqB,GAAWC,wBAAyB;AAC/D,MAAM,SAAS,GAAQC;;;;"}